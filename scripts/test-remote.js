#!/usr/bin/env node

/**
 * Script to test remote module connectivity and cache behavior
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Configuration
const CRM360_URL = process.env.VITE_CRM360_URL || 'https://crmfe.big360.ai';
const REMOTE_ENTRY_PATH = '/assets/remoteEntry.js';

console.log('🔍 Testing Remote Module Connectivity');
console.log('=====================================');
console.log(`Remote URL: ${CRM360_URL}`);
console.log(`Remote Entry: ${CRM360_URL}${REMOTE_ENTRY_PATH}`);
console.log('');

async function testRemoteConnectivity() {
  const remoteUrl = `${CRM360_URL}${REMOTE_ENTRY_PATH}`;
  
  try {
    console.log('📡 Testing remote connectivity...');
    
    const result = await makeRequest(remoteUrl, {
      method: 'HEAD',
      timeout: 10000
    });
    
    console.log('✅ Remote is accessible');
    console.log(`   Status: ${result.statusCode}`);
    console.log(`   Content-Type: ${result.headers['content-type'] || 'N/A'}`);
    console.log(`   Content-Length: ${result.headers['content-length'] || 'N/A'}`);
    console.log(`   Cache-Control: ${result.headers['cache-control'] || 'N/A'}`);
    console.log(`   Last-Modified: ${result.headers['last-modified'] || 'N/A'}`);
    console.log(`   ETag: ${result.headers['etag'] || 'N/A'}`);
    
    return true;
  } catch (error) {
    console.log('❌ Remote is not accessible');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function testCacheBehavior() {
  const remoteUrl = `${CRM360_URL}${REMOTE_ENTRY_PATH}`;
  
  console.log('');
  console.log('🔄 Testing cache behavior...');
  
  try {
    // First request
    console.log('   Making first request...');
    const first = await makeRequest(remoteUrl, { method: 'GET' });
    const firstETag = first.headers['etag'];
    const firstLastModified = first.headers['last-modified'];
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Second request with cache headers
    console.log('   Making second request with cache headers...');
    const headers = {};
    if (firstETag) headers['If-None-Match'] = firstETag;
    if (firstLastModified) headers['If-Modified-Since'] = firstLastModified;
    
    const second = await makeRequest(remoteUrl, { 
      method: 'GET',
      headers 
    });
    
    if (second.statusCode === 304) {
      console.log('✅ Cache validation working (304 Not Modified)');
    } else if (second.statusCode === 200) {
      console.log('⚠️  Cache validation not working (200 OK - should be 304)');
    }
    
    // Third request with cache busting
    console.log('   Making third request with cache busting...');
    const cacheBustedUrl = `${remoteUrl}?v=${Date.now()}`;
    const third = await makeRequest(cacheBustedUrl, { method: 'GET' });
    
    if (third.statusCode === 200) {
      console.log('✅ Cache busting working');
    } else {
      console.log('❌ Cache busting not working');
    }
    
  } catch (error) {
    console.log('❌ Cache behavior test failed');
    console.log(`   Error: ${error.message}`);
  }
}

async function testCorsHeaders() {
  const remoteUrl = `${CRM360_URL}${REMOTE_ENTRY_PATH}`;
  
  console.log('');
  console.log('🌐 Testing CORS headers...');
  
  try {
    const result = await makeRequest(remoteUrl, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://big360.ai',
        'Access-Control-Request-Method': 'GET'
      }
    });
    
    const corsOrigin = result.headers['access-control-allow-origin'];
    const corsMethods = result.headers['access-control-allow-methods'];
    const corsHeaders = result.headers['access-control-allow-headers'];
    
    console.log(`   Access-Control-Allow-Origin: ${corsOrigin || 'N/A'}`);
    console.log(`   Access-Control-Allow-Methods: ${corsMethods || 'N/A'}`);
    console.log(`   Access-Control-Allow-Headers: ${corsHeaders || 'N/A'}`);
    
    if (corsOrigin === '*' || corsOrigin === 'https://big360.ai') {
      console.log('✅ CORS configured correctly');
    } else {
      console.log('⚠️  CORS might not be configured correctly');
    }
    
  } catch (error) {
    console.log('❌ CORS test failed');
    console.log(`   Error: ${error.message}`);
  }
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 5000
    };
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

async function main() {
  const isAccessible = await testRemoteConnectivity();
  
  if (isAccessible) {
    await testCacheBehavior();
    await testCorsHeaders();
  }
  
  console.log('');
  console.log('📋 Recommendations:');
  console.log('===================');
  console.log('1. Ensure remote entry files have proper cache headers');
  console.log('2. Use cache busting parameters for production deployments');
  console.log('3. Configure CORS headers to allow federation requests');
  console.log('4. Monitor remote health in production');
  console.log('5. Implement fallback mechanisms for remote failures');
  console.log('');
  console.log('💡 Use the Remote Debugger component in development for real-time monitoring');
}

main().catch(console.error);
