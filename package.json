{"name": "bg360", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "docker-run": "docker-compose -f docker-compose.yml -f docker-compose-local.yml up -d", "test:remote": "node scripts/test-remote.js"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@hotjar/browser": "^1.0.9", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@reduxjs/toolkit": "^2.2.5", "@remixicon/react": "^4.6.0", "@sentry/react": "^9.12.0", "@tanstack/react-table": "^8.20.5", "@types/leaflet": "^1.9.12", "@types/node": "^20.14.0", "@types/react-cookies": "^0.1.4", "@types/react-slick": "^0.23.13", "ag-grid-community": "^32.0.2", "ag-grid-react": "^32.0.2", "axios": "^1.7.2", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.11", "file-saver": "^2.0.5", "leaflet": "^1.9.4", "lodash": "^4.17.21", "marked": "^15.0.11", "numeral": "^2.0.6", "path": "^0.12.7", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-cookie": "^8.0.1", "react-countup": "^6.5.3", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-hook-form": "^7.53.2", "react-infinite-scroll-component": "^6.1.0", "react-leaflet": "^4.2.1", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-slick": "^0.30.2", "react-sortablejs": "^6.1.4", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.2", "styled-components": "^6.1.11", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "xlsx": "^0.18.5", "yup": "^1.4.0", "zod": "^3.23.8"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.6", "@types/file-saver": "^2.0.5", "@types/jest": "^29.5.12", "@types/lodash": "^4.14.195", "@types/node": "^20.14.0", "@types/numeral": "^2.0.2", "@types/react": "^18.0.37", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "6.7.2", "@typescript-eslint/parser": "^6.0.0 || ^6.0.0-alpha", "@vitejs/plugin-react-swc": "^3.0.0", "autoprefixer": "^10.4.19", "dependency-cruiser": "12.4.0", "eslint": "^8.38.0", "eslint-plugin-no-inline-styles": "^1.0.5", "eslint-plugin-react": "^7.25.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-unused-imports": "^3.0.0", "eslint-plugin-vietnamese": "^1.2.1", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.0.2", "vite": "^4.3.9", "vite-plugin-checker": "^0.6.2"}}