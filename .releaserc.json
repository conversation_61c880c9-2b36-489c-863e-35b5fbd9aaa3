{"branches": [{"name": "main", "prerelease": false}, {"name": "dev", "prerelease": true, "prereleaseSuffix": "dev"}], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"type": "docs", "scope": "README", "release": "patch"}, {"type": "refactor", "release": "patch"}, {"type": "style", "release": "patch"}]}], "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], "@semantic-release/gitlab", ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json"], "message": "chore(release): ${nextRelease.version}\n\n${nextRelease.notes}"}]], "tagFormat": "v${version}"}