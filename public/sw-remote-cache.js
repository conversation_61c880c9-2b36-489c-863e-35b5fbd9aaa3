/**
 * Service Worker for handling remote module cache invalidation
 */

const CACHE_NAME = 'remote-modules-cache-v1';
const REMOTE_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// URLs that should be cached with special handling
const REMOTE_PATTERNS = [
  /\/assets\/remoteEntry\.js/,
  /\/static\/js\/.*\.js$/,
  /crm360/
];

self.addEventListener('install', (event) => {
  console.log('Remote cache service worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Remote cache service worker activating...');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  self.clients.claim();
});

self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // Only handle remote module requests
  const isRemoteRequest = REMOTE_PATTERNS.some(pattern => pattern.test(url.pathname));
  
  if (!isRemoteRequest) {
    return;
  }

  event.respondWith(
    handleRemoteRequest(event.request)
  );
});

async function handleRemoteRequest(request) {
  const url = new URL(request.url);
  const cache = await caches.open(CACHE_NAME);
  
  // Create a cache key without query parameters for consistency
  const cacheKey = new URL(url.pathname, url.origin).href;
  
  try {
    // Check if we have a cached version
    const cachedResponse = await cache.match(cacheKey);
    
    if (cachedResponse) {
      const cacheDate = new Date(cachedResponse.headers.get('sw-cache-date') || 0);
      const now = new Date();
      
      // If cache is still fresh, return it
      if (now - cacheDate < REMOTE_CACHE_DURATION) {
        console.log('Serving from cache:', cacheKey);
        return cachedResponse;
      }
    }
    
    // Fetch fresh version
    console.log('Fetching fresh version:', request.url);
    const response = await fetch(request, {
      cache: 'no-cache',
      headers: {
        ...request.headers,
        'Cache-Control': 'no-cache'
      }
    });
    
    if (response.ok) {
      // Clone the response and add cache metadata
      const responseToCache = response.clone();
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cache-date', new Date().toISOString());
      
      const cachedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });
      
      // Cache the response
      await cache.put(cacheKey, cachedResponse);
      console.log('Cached fresh version:', cacheKey);
    }
    
    return response;
  } catch (error) {
    console.error('Error handling remote request:', error);
    
    // Try to return cached version as fallback
    const cachedResponse = await cache.match(cacheKey);
    if (cachedResponse) {
      console.log('Serving stale cache as fallback:', cacheKey);
      return cachedResponse;
    }
    
    // Return error response
    return new Response('Remote module unavailable', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// Listen for messages from the main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEAR_REMOTE_CACHE') {
    clearRemoteCache();
  } else if (event.data && event.data.type === 'FORCE_REFRESH_REMOTE') {
    forceRefreshRemote(event.data.url);
  }
});

async function clearRemoteCache() {
  try {
    const cache = await caches.open(CACHE_NAME);
    const keys = await cache.keys();
    
    await Promise.all(
      keys.map(key => {
        const url = new URL(key.url);
        const isRemote = REMOTE_PATTERNS.some(pattern => pattern.test(url.pathname));
        if (isRemote) {
          console.log('Clearing cache for:', key.url);
          return cache.delete(key);
        }
      })
    );
    
    console.log('Remote cache cleared');
  } catch (error) {
    console.error('Error clearing remote cache:', error);
  }
}

async function forceRefreshRemote(url) {
  try {
    const cache = await caches.open(CACHE_NAME);
    const cacheKey = new URL(new URL(url).pathname, new URL(url).origin).href;
    
    // Delete from cache
    await cache.delete(cacheKey);
    
    // Fetch fresh version
    const response = await fetch(url, { cache: 'no-cache' });
    
    if (response.ok) {
      const headers = new Headers(response.headers);
      headers.set('sw-cache-date', new Date().toISOString());
      
      const cachedResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: headers
      });
      
      await cache.put(cacheKey, cachedResponse);
      console.log('Force refreshed and cached:', url);
    }
  } catch (error) {
    console.error('Error force refreshing remote:', error);
  }
}
