# Use an official Node.js runtime as the base image for building the React app
FROM node:18-alpine AS build-stage

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package*.json yarn.lock ./

# Install dependencies
RUN yarn install

# Copy the entire project to the working directory
COPY . .

# Build the React app
RUN yarn build

# Use a lightweight Nginx image as the base image for serving the React app
FROM nginx:1.27-alpine

# Copy the built React app from the build-stage to the Nginx web server directory
COPY --from=build-stage /app/build /usr/share/nginx/html

# Copy the Nginx configuration file to enable React app routing
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose the port that the Nginx web server will run on
EXPOSE 80

# Define the command to start the Nginx web server
CMD ["nginx", "-g", "daemon off;"]
