import react from "@vitejs/plugin-react-swc";
import path from "path";
import { defineConfig, loadEnv } from "vite";
import checker from "vite-plugin-checker";
import federation from "@originjs/vite-plugin-federation";

// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
  const CRM360_URL = process.env.VITE_CRM360_URL;

  return {
    plugins: [
      react(),
      federation({
        name: "skycom_react",
        remotes: {
          crm360: CRM360_URL + "/assets/remoteEntry.js",
        },
        filename: "remoteEntry.js",
        exposes: {
          "./reducer": "./src/store",
        },
        shared: ["react", "react-dom", "react-router-dom", "react-redux", "@reduxjs/toolkit"],
      }),
      checker({
        // e.g. use TypeScript check
        typescript: true,
        enableBuild: false,
      }),
    ],
    define: {
      "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV),
    },
    resolve: {
      alias: {
        utils: path.resolve(__dirname, "src/utils"),
        types: path.resolve(__dirname, "src/types"),
        assets: path.resolve(__dirname, "src/assets"),
        apis: path.resolve(__dirname, "src/apis"),
        constants: path.resolve(__dirname, "src/constants"),
        components: path.resolve(__dirname, "src/components"),
        hooks: path.resolve(__dirname, "src/hooks"),
        store: path.resolve(__dirname, "src/store"),
        providers: path.resolve(__dirname, "src/providers"),
        routers: path.resolve(__dirname, "src/routers"),
        layout: path.resolve(__dirname, "src/layout"),
        views: path.resolve(__dirname, "src/views"),
        theme: path.resolve(__dirname, "src/theme"),
        services: path.resolve(__dirname, "src/services"),
        validations: path.resolve(__dirname, "src/validations"),
      },
    },
    build: {
      outDir: "build",
      sourcemap: false,
      modulePreload: false,
      target: "esnext",
      minify: false,
      cssCodeSplit: false,
    },
    envPrefix: "REACT_APP_",
    server: {
      port: 3000,
      cors: true,
    },
  };
});
