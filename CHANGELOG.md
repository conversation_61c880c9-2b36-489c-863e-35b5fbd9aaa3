# [1.1.0](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/compare/v1.0.2...v1.1.0) (2025-06-19)


### Bug Fixes

* add date formatting to contact import log in SegmentLog component ([8e0e31a](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/8e0e31af0ab89be0195f789a283f9a7625d126da))
* add default message and suggestion buttons to ChatBoxContainer ([c7a21c4](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/c7a21c4e71fa6973df0a711b50177b4383f0466a))
* add default welcome message to ChatBoxContainer and initialize message state ([5ba7d18](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/5ba7d1860a3aac15b27fb37620d9d1a0bc14e781))
* adjust column height in DataTable and add height class to column meta ([22b7a6a](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/22b7a6a6a23eac87183048c7864538268bf19402))
* clean up console logs, adjust column styling, and correct CSS height property ([6c57b8d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/6c57b8d279e375d1842972b4353f52ea52ac02bd))
* convert redirect url ([99648b6](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/99648b6d68be27d9dd952ee593dfcb58b2714e0b))
* count down forgot password ([c690153](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/c69015335b82078561ab8653a1106d512c64c82d))
* enhance column definitions with improved layout and sticky behavior in DataTable components ([664b048](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/664b0482b0e66c60c2a53c48966bab961152a1a3))
* enhance column definitions with improved layout and sticky behavior in DataTable components ([3efaf0d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/3efaf0de0dcd9d056404313ccffdd6d0855cb389))
* enhance column styling and adjust sticky behavior in DataTable ([67ee0ea](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/67ee0eaa8b6b0db76b2e307c99982fe6707c039b))
* enhance InvoiceType display with formatted labels ([25ed1b5](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/25ed1b5a67fc7ec5cbc2e227a37ebefa2cfa4425))
* enhance sticky column behavior and adjust table row height in DataTable components ([9fa4e11](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/9fa4e11656e39fcae9decd7f031c8e3171c976ee))
* implement abort controller for API requests in Persona and Social components ([3a0a316](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/3a0a316a2b94d9fd8372f420b333e909b67125e8))
* improve ColorPicker component functionality and enhance loading indicator in ChatBoxContainer ([a56b12d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/a56b12dfbb764e98e4c2d046c0f3fbfecbb29058))
* improve error handling and response processing in AddYourDataModal and socialPersona ([7cc9fec](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/7cc9fecc977a41cb801d484ba8ef66d0266e2eae))
* improve error handling and response processing in AddYourDataModal and socialPersona ([7519ac5](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/7519ac5486c1e5ec519a5d6cdd6d0dfbd6ce0c99))
* improve layout and styling in ChatBoxContainer, normalize escape sequences in ViewChat ([76462f9](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/76462f98a200e56a6edf7b9e4cb73dd4c5c82ebf))
* integrate abort controller for API requests across multiple components ([57071fc](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/57071fcbf4e44496e6569bcf13342a9f4bfc3a01))
* normalize email inputs by transforming to lowercase and trimming whitespace in various schemas and components ([f57d6b0](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/f57d6b0f804893a65d1b2904f35f6d81ac1bbecf))
* redirect public route URL ([1398e87](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/1398e87f466c3e21b2ec1d51204c77b65da965fe))
* redirect public route URL ([facaf7c](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/facaf7cbd6e0a734294da2e906cec4e734d16615))
* redirect public route URL ([4ab753b](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/4ab753b135551ae6f6240906658e69f05bc9e2cb))
* refactor API call in addListContactToCRM to use params for color query ([2215c4c](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/2215c4c38dbd87865b0813e6fb730fcbb707e12d))
* remove console.log statements and improve token check in infinite scroll ([9167319](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/91673198dbf831e604be35b86b017b407019d6a3))
* remove Docker login command from CI configuration ([b081f6b](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/b081f6be096e0e9ad9b4d5abe57d57a189ad77c3))
* remove export segment dataset ([6a5d299](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/6a5d299d3dcdebefaffc4fc377b6fc490c4f82da))
* remove unnecessary console log from ChatBoxContainer for cleaner output ([62770e9](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/62770e9dd5af256fc0243fec9940d62365bb1222))
* simplify handleOpenChange logic and enhance handleColorSelect with event handling ([001e281](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/001e281050b5133aa5ce2392234cf7731f347676))
* tracking ga4 ([59ebc70](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/59ebc70b4c14601d939925a32778eb167296630c))
* update API call to include color as a query parameter in addListContactToCRM ([b26e084](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/b26e084831b2453638d5033971efd3c8b311299e))
* update color selection button style to use rounded-full shape ([1e5d5df](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/1e5d5df12b87e88541cc74500ec75c04efd2166d))
* update contact list API integration and enhance UI styles in AddYourDataModal and SegmentLog components ([07e9f02](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/07e9f02feb814d30741973be6b212dc48891a4d7))
* update error handling for refresh token ([9b5ed8f](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/9b5ed8f831566175bef8bbda29f56639958966ab))
* update error message in ChatBoxContainer for better user experience ([3031397](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/303139785802a1f2374e93771f7988d57ede852b))
* update plan labels and enhance loading state in PlanDetail and ChartWrapper components ([e3eeda6](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/e3eeda6a111a495182e159ba7f60e1ec21d8e657))
* wrap TooltipContent in TooltipPortal for improved rendering in multiple components ([7f555b5](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/7f555b577220df05e1d9fa23ec6a5e8a2e38424b))


### Features

* add AI version selection to ChatAIAgent component ([9a8b3f8](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/9a8b3f83b04fe4a882b495cd2fc84a10ebf7c5a1))
* add animated ShareFolderIcon and enhance SegmentLog error handling ([8486bc6](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/8486bc606ca25bcb64d1842c834f213e52683d7a))
* add applied state to manage referral code submission and disable button accordingly ([d8c434d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/d8c434d0448afad5aaf99d3759cf0e6a2e0822a9))
* add CheckboxController component for controlled checkbox input with validation ([2feafff](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/2feafffedc4024d2519812131ebb62eee1199ca4))
* add error status to badges and enhance tooltip functionality ([4700f43](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/4700f43178231e7d9ecdf74fd65fa871069db9f1))
* add loading indicator and refactor referral code update logic ([e6d72fe](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/e6d72feaa1e72900c32ae45ff01a34cc87e60ea6))
* add M_SUBSCRIPTION credit type and enhance audience download functionality ([48ec8a3](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/48ec8a3eb19fe691a469df8b23db7372bcc8f57e))
* add referral code functionality to signup form and related components ([0640d6d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/0640d6dfd24b5d6ae6ae9f87074d7b7e5480145e))
* add status restore credit ([39f5cf2](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/39f5cf210394da0b8faecd9fcd1f17a32a84c78b))
* add status restore credit ([0aba294](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/0aba294e883dbe167a8c846f32949abdffdc01eb))
* add terms and conditions checkbox to signup form with validation ([cf5be03](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/cf5be030e40c40204c28bc1bc2ff159d16e9ff77))
* add version selection and improve loading indicator in chat ([8259765](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/8259765571cb61a96a7c0667fbdba9b4ca186389))
* adjust popover width and refine padding for better responsiveness ([790b16f](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/790b16f428a651986fe6b95d8f725f7df3af7bfd))
* chat with ai agent ([150ec0a](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/150ec0a6307cb26908f48f9476daa30de0cd9f4d))
* enhance AddYourDataModal with new icon and segment log component ([3410404](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/34104046f13a68565cd5c7b57184f58f7d4f103e))
* enhance chat AI agent with message formatting and user display ([9170967](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/917096790b8fc8649f109848bf2e1d91f08457fd))
* enhance ClearAll component with className prop and improve layout in FilterChipWrapper ([9d5a749](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/9d5a7494d11546d65c33ff61229d6c0c5298bc60))
* enhance Google login flow with redirect URL parameters ([bbdee16](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/bbdee161cae76f907b9d6fa9da08872d364d6db5))
* enhance SegmentLog with status badges and localization ([866386d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/866386dea079d160667c7ee865cb0ba9a7fb18d1))
* implement abort controller for API requests to handle cancellations ([5edc175](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/5edc175c049d2e6a2fd404db7c2f9368da95fdd8))
* implement markdown rendering in chat messages with ViewChat component ([e523046](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/e5230462eec9fc34968f7bf1c81bbbdc62970adf))
* implement segment log functionality and integrate with Redux ([f7a885c](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/f7a885c8116b9a7fa1f83ef3e9c551ef5a2e2f65))
* improve Google login flow by adding session to redirect parameters ([1c11f42](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/1c11f425387d343a28bf633c8e04731cfd0dd963))
* redirect old URL without auth ([866b841](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/866b841f4f316998650f21477ded860236432fb9))
* refactor referral code handling and add utility for data refetching ([a2df596](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/a2df5963ea36ad193cf5b0a6a8dfd1700ec38c34))
* referral code detail ([3405cb0](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/3405cb02c679380f6e7041c00870f92c7a857a2f))
* remove hover table, add icon click request audience fail ([891d156](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/891d156d1a33e247ca8e98eb8c61b9f01d211904))
* replace environment variable checks with IS_DEV constant for consistency ([4ea7496](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/4ea7496e67174ec986803f46f53bbd053b910f52))
* responsive profile collection ([978ab59](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/978ab5975eeedd8137d8ee8f86d7f530ade42878))
* update chat with AI agent, add a new chat page ([bca0326](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/bca03260c104a0f177182ed6c1a22f28af50c089))
* update ChatBoxContainer layout and replace AI agent icon ([ed2c841](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/ed2c841dcfc172af5dcb2a7b016095b5adb59d42))
* update CreditAmount, style signup with referral code ([ff772c9](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/ff772c93219c9ee8c0ddd39270b901f7885dc829))
* update estimated launch date for CRM360 ([e8ea13a](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/e8ea13a4942fe879bb029c6250f56d6e90d25bf5))
* update flow forgot password ([4d76fb2](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/4d76fb2ae47a52b54a1bad50cc39fe5e65e7e937))
* update markdown styles for improved rendering in chat messages ([6dd6e5d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/6dd6e5dbd7c01bc82193b9df159375a9b5b58bb7))
* update responsive styles for audience and social segments ([56f066d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/56f066d487758a457773059ffbff4bc57d9024c9))
* update save params search do redirect when user login ([08a66c0](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/08a66c0994c23e5acec38290f97fe36dd80940cf))

# [1.1.0-dev.1](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/compare/v1.0.1...v1.1.0-dev.1) (2025-05-22)


### Bug Fixes

* add date formatting to contact import log in SegmentLog component ([8e0e31a](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/8e0e31af0ab89be0195f789a283f9a7625d126da))
* add default welcome message to ChatBoxContainer and initialize message state ([5ba7d18](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/5ba7d1860a3aac15b27fb37620d9d1a0bc14e781))
* clean up console logs, adjust column styling, and correct CSS height property ([6c57b8d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/6c57b8d279e375d1842972b4353f52ea52ac02bd))
* convert redirect url ([99648b6](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/99648b6d68be27d9dd952ee593dfcb58b2714e0b))
* implement abort controller for API requests in Persona and Social components ([3a0a316](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/3a0a316a2b94d9fd8372f420b333e909b67125e8))
* improve error handling and response processing in AddYourDataModal and socialPersona ([7519ac5](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/7519ac5486c1e5ec519a5d6cdd6d0dfbd6ce0c99))
* improve layout and styling in ChatBoxContainer, normalize escape sequences in ViewChat ([76462f9](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/76462f98a200e56a6edf7b9e4cb73dd4c5c82ebf))
* integrate abort controller for API requests across multiple components ([57071fc](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/57071fcbf4e44496e6569bcf13342a9f4bfc3a01))
* redirect public route URL ([1398e87](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/1398e87f466c3e21b2ec1d51204c77b65da965fe))
* redirect public route URL ([facaf7c](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/facaf7cbd6e0a734294da2e906cec4e734d16615))
* redirect public route URL ([4ab753b](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/4ab753b135551ae6f6240906658e69f05bc9e2cb))
* remove console.log statements and improve token check in infinite scroll ([9167319](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/91673198dbf831e604be35b86b017b407019d6a3))
* remove Docker login command from CI configuration ([b081f6b](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/b081f6be096e0e9ad9b4d5abe57d57a189ad77c3))
* resend email ([9a47ddc](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/9a47ddc3dfcd4ad08334e69b18b09edd90762d1d))
* tracking ga4 ([59ebc70](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/59ebc70b4c14601d939925a32778eb167296630c))
* update contact list API integration and enhance UI styles in AddYourDataModal and SegmentLog components ([07e9f02](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/07e9f02feb814d30741973be6b212dc48891a4d7))
* update error handling for refresh token ([9b5ed8f](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/9b5ed8f831566175bef8bbda29f56639958966ab))


### Features

* add AI version selection to ChatAIAgent component ([9a8b3f8](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/9a8b3f83b04fe4a882b495cd2fc84a10ebf7c5a1))
* add applied state to manage referral code submission and disable button accordingly ([d8c434d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/d8c434d0448afad5aaf99d3759cf0e6a2e0822a9))
* add CheckboxController component for controlled checkbox input with validation ([2feafff](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/2feafffedc4024d2519812131ebb62eee1199ca4))
* add error status to badges and enhance tooltip functionality ([4700f43](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/4700f43178231e7d9ecdf74fd65fa871069db9f1))
* add loading indicator and refactor referral code update logic ([e6d72fe](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/e6d72feaa1e72900c32ae45ff01a34cc87e60ea6))
* add M_SUBSCRIPTION credit type and enhance audience download functionality ([48ec8a3](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/48ec8a3eb19fe691a469df8b23db7372bcc8f57e))
* add referral code functionality to signup form and related components ([0640d6d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/0640d6dfd24b5d6ae6ae9f87074d7b7e5480145e))
* add status restore credit ([39f5cf2](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/39f5cf210394da0b8faecd9fcd1f17a32a84c78b))
* add status restore credit ([0aba294](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/0aba294e883dbe167a8c846f32949abdffdc01eb))
* add terms and conditions checkbox to signup form with validation ([cf5be03](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/cf5be030e40c40204c28bc1bc2ff159d16e9ff77))
* add version selection and improve loading indicator in chat ([8259765](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/8259765571cb61a96a7c0667fbdba9b4ca186389))
* adjust popover width and refine padding for better responsiveness ([790b16f](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/790b16f428a651986fe6b95d8f725f7df3af7bfd))
* chat with ai agent ([150ec0a](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/150ec0a6307cb26908f48f9476daa30de0cd9f4d))
* enhance AddYourDataModal with new icon and segment log component ([3410404](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/34104046f13a68565cd5c7b57184f58f7d4f103e))
* enhance chat AI agent with message formatting and user display ([9170967](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/917096790b8fc8649f109848bf2e1d91f08457fd))
* enhance ClearAll component with className prop and improve layout in FilterChipWrapper ([9d5a749](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/9d5a7494d11546d65c33ff61229d6c0c5298bc60))
* enhance Google login flow with redirect URL parameters ([bbdee16](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/bbdee161cae76f907b9d6fa9da08872d364d6db5))
* enhance SegmentLog with status badges and localization ([866386d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/866386dea079d160667c7ee865cb0ba9a7fb18d1))
* implement abort controller for API requests to handle cancellations ([5edc175](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/5edc175c049d2e6a2fd404db7c2f9368da95fdd8))
* implement markdown rendering in chat messages with ViewChat component ([e523046](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/e5230462eec9fc34968f7bf1c81bbbdc62970adf))
* implement segment log functionality and integrate with Redux ([f7a885c](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/f7a885c8116b9a7fa1f83ef3e9c551ef5a2e2f65))
* improve Google login flow by adding session to redirect parameters ([1c11f42](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/1c11f425387d343a28bf633c8e04731cfd0dd963))
* redirect old URL without auth ([866b841](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/866b841f4f316998650f21477ded860236432fb9))
* refactor referral code handling and add utility for data refetching ([a2df596](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/a2df5963ea36ad193cf5b0a6a8dfd1700ec38c34))
* referral code detail ([3405cb0](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/3405cb02c679380f6e7041c00870f92c7a857a2f))
* remove hover table, add icon click request audience fail ([891d156](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/891d156d1a33e247ca8e98eb8c61b9f01d211904))
* replace environment variable checks with IS_DEV constant for consistency ([4ea7496](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/4ea7496e67174ec986803f46f53bbd053b910f52))
* responsive profile collection ([978ab59](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/978ab5975eeedd8137d8ee8f86d7f530ade42878))
* update chat with AI agent, add a new chat page ([bca0326](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/bca03260c104a0f177182ed6c1a22f28af50c089))
* update ChatBoxContainer layout and replace AI agent icon ([ed2c841](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/ed2c841dcfc172af5dcb2a7b016095b5adb59d42))
* update CreditAmount, style signup with referral code ([ff772c9](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/ff772c93219c9ee8c0ddd39270b901f7885dc829))
* update estimated launch date for CRM360 ([e8ea13a](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/e8ea13a4942fe879bb029c6250f56d6e90d25bf5))
* update markdown styles for improved rendering in chat messages ([6dd6e5d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/6dd6e5dbd7c01bc82193b9df159375a9b5b58bb7))
* update responsive styles for audience and social segments ([56f066d](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/56f066d487758a457773059ffbff4bc57d9024c9))
* update save params search do redirect when user login ([08a66c0](https://gitlab.skylink.vn/skylink-innovation/big360-services/big360-fe/commit/08a66c0994c23e5acec38290f97fe36dd80940cf))
