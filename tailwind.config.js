/** @type {import('tailwindcss').Config} */

const big360Color = {
  neutral: {
    '00': '#FDFDFD',
    '50': '#F5F5F5',
    '100': '#E6E7E9',
    '200': '#D9DBDE',
    '300': '#C5C8CB',
    '400': '#ADB0B4',
    '500': '#909498',
    '600': '#6F7377',
    '700': '#4E5255',
    '800': '#2E3133',
    '900': '#1E1F21',
    '950': '#141416',
  },
  neutralDark: {
    '00': '#141416',
    '50': '#1A1B1D',
    '100': '#212224',
    '200': '#2A2C2F',
    '300': '#36383C',
    '400': '#46484D',
    '500': '#5A5D63',
    '600': '#707479',
    '700': '#8A8E93',
    '800': '#AEB2B6',
    '900': '#D3D6D9',
    '950': '#FDFDFD',
  },
  brand: {
    '00': '#F4F0FF',
    '50': '#E3DAFF',
    '100': '#CFBEFF',
    '200': '#B89EFF',
    '300': '#A37EFF',
    '400': '#8F5CFF',
    '500': '#7C47E6',
    '600': '#6A3CC2',
    '700': '#582FA0',
    '800': '#45257F',
    '900': '#331B5F',
    '950': '#241344',
  },
  brandAlt: {
    '00': '#341273',
    '50': '#42139D',
    '100': '#4F12C9',
    '200': '#5D14F2',
    '300': '#7537F9',
    '400': '#8F5CFF',
    '500': '#9F75FF',
    '600': '#AF8DFF',
    '700': '#C0A6FF',
    '800': '#D1BFFF',
    '900': '#E2D7FF',
    '950': '#F4F0FF',
  },
  success: {
    '00': '#F3FDF7',
    '50': '#E3F8EF',
    '100': '#C4F1DD',
    '200': '#97E6C4',
    '300': '#6CDCAD',
    '400': '#45D29C',
    '500': '#2BB684',
    '600': '#22996F',
    '700': '#197A59',
    '800': '#135D45',
    '900': '#0E4634',
    '950': '#0A3326',
  },
  successAlt: {
    '00': '#0A3326',
    '50': '#0E4634',
    '100': '#135D45',
    '200': '#197A59',
    '300': '#22996F',
    '400': '#2BB684',
    '500': '#45D29C',
    '600': '#6CDCAD',
    '700': '#97E6C4',
    '800': '#C4F1DD',
    '900': '#E3F8EF',
    '950': '#F3FDF7',
  },
  info: {
    '00': '#F2F8FF',
    '50': '#E4F0FE',
    '100': '#CBE3FD',
    '200': '#A6D3FC',
    '300': '#84C3FB',
    '400': '#69B4FA',
    '500': '#4F9EF0',
    '600': '#4284D0',
    '700': '#366BB0',
    '800': '#2A5490',
    '900': '#1E4071',
    '950': '#152E55',
  },
  infoAlt: {
    '00': '#152E55',
    '50': '#1E4071',
    '100': '#2A5490',
    '200': '#366BB0',
    '300': '#4284D0',
    '400': '#4F9EF0',
    '500': '#69B4FA',
    '600': '#84C3FB',
    '700': '#A6D3FC',
    '800': '#CBE3FD',
    '900': '#E4F0FE',
    '950': '#F2F8FF',
  },
  warning: {
    '00': '#FFFBE8',
    '50': '#FFF5D6',
    '100': '#FFE89E',
    '200': '#FFD75A',
    '300': '#FFC930',
    '400': '#FFBC00',
    '500': '#F5B200',
    '600': '#D89D00',
    '700': '#BB8700',
    '800': '#9E7200',
    '900': '#835D00',
    '950': '#684A00',
  },
  warningAlt: {
    '00': '#684A00',
    '50': '#835D00',
    '100': '#9E7200',
    '200': '#BB8700',
    '300': '#D89D00',
    '400': '#F5B200',
    '500': '#FFBC00',
    '600': '#FFC930',
    '700': '#FFD75A',
    '800': '#FFE89E',
    '900': '#FFF5D6',
    '950': '#FFFBE8',
  },
  danger: {
    '00': '#FFF4F4',
    '50': '#FFE7E7',
    '100': '#FFCECE',
    '200': '#FFA3A3',
    '300': '#FF7A7A',
    '400': '#FF5C5C',
    '500': '#F84242',
    '600': '#DB3838',
    '700': '#BF2D2D',
    '800': '#A32121',
    '900': '#891717',
    '950': '#6E0F0F',
  },
  dangerAlt: {
    '00': '#6E0F0F',
    '50': '#891717',
    '100': '#A32121',
    '200': '#BF2D2D',
    '300': '#DB3838',
    '400': '#F84242',
    '500': '#FF5C5C',
    '600': '#FF7A7A',
    '700': '#FFA3A3',
    '800': '#FFCECE',
    '900': '#FFE7E7',
    '950': '#FFF4F4',
  },
  alphaWhite: {
    '00': '#FDFDFD00',
    '08': '#FDFDFD14',
    '12': '#FDFDFD1F',
    '16': '#FDFDFD29',
    '24': '#FDFDFD3D',
    '32': '#FDFDFD52',
    '48': '#FDFDFD7A',
    '56': '#FDFDFD8F',
    '64': '#FDFDFDA3',
    '72': '#FDFDFDB8',
    '80': '#FDFDFDCC',
    '96': '#FDFDF8F5',
  },
  alphaBlack: {
    '00': '#1E1F2100',
    '08': '#1E1F2114',
    '12': '#1E1F211F',
    '16': '#1E1F2129',
    '24': '#1E1F213D',
    '32': '#1E1F2152',
    '48': '#1E1F217A',
    '56': '#1E1F218F',
    '64': '#1E1F21A3',
    '72': '#1E1F21B8',
    '80': '#1E1F21CC',
    '88': '#1E1F21E0',
  },
  alphaBrand: {
    '08': '#8F5CFF14',
    '12': '#8F5CFF1F',
    '16': '#8F5CFF29',
    '24': '#8F5CFF3D',
    '32': '#8F5CFF52',
    '48': '#8F5CFF7A',
    '56': '#8F5CFF8F',
    '64': '#8F5CFFA3',
    '72': '#8F5CFFB8',
    '80': '#8F5CFFCC',
    '96': '#8F5CFFF5',
  },
};


module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    screens: {
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1400px",
      "3xl": "2560px",
    },
    fontFamily: {
      inter: ["Inter", "sans-serif"],
      sans: ["sans-serif"],
    },
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      backgroundColor: {
        primary: "hsl(var(--background-primary))",
        secondary: "hsl(var(--background-secondary))",
        card: "hsl(var(--card))",
        active: "hsl(var(--background-active))",
        tertiary: "#E1E2E3",
        hover: "#383C4A",
        "primary-hover": "hsl(var(--background-primary-hover))",
        "error-subtitle": "#FFD9D9",
        "error-default": "#F53E3E",
        brand: {
          disabled: "#E2DAFF",
          subtitle: "#E2DAFF",
        },
        success: {
          primary: "#E0F8E3",
        },
        custom: {
          primary: "#FDFDFD",
          secondary: "#F0F0F0",
          tertiary: "#6B7183",
          disable: "#E1E2E3",
        },
      },
      textColor: {
        primary: "hsl(var(--text-primary))",
        secondary: "hsl(var(--text-secondary))",
        tertiary: "hsl(var(--text-tertiary))",
        custom: {
          disable: "#A7AAB1",
          brand: "#FDFDFD",
          tertiary: "#6B7183",
        },
        success: {
          default: "#27923A",
          subtitle: "#205B2B",
        },
      },
      colors: {
        "primary-hover": "hsl(var(--background-primary-hover))",
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: "hsl(var(--background-primary))",
        secondary: "hsl(var(--background-secondary))",
        tertiary: "#6B7183",
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        icon: {
          primary: "hsl(var(--text-secondary))",
        },
        shadow: {
          light: "hsl(var(--color-shadow-light))",
          medium: "hsl(var(--color-shadow-medium))",
        },
        infor: {
          primary: "hsl(208, 100%, 59%, 1)",
          disable: "#a7aab1",
          subtitle: "#EEF9FF",
          text: "#146BE1",
        },
        warning: {
          default: "#FBCA24",
          "subtitle-default": "#FFFCEB",
          "text-strong": "#D98206",
        },
        error: {
          default: "#F53E3E",
          subtitle: "#FFD9D9",
          strong: "#BF1616",
          light: "#FFA2A2",
        },
        brand: {
          strong: "#5A18BF",
          text: "#FDFDFD",
          default: "#8F5CFF",
          light: "#ECDFFB",
        },
        big360Color
      },
      backgroundImage: {
        primary: {
          DEFAULT: "var(--primary-gradients)",
        },
        "custom-gradient":
          "linear-gradient(304.96deg, #A33AD8 -3.24%, #8347CE 48.97%, #5138B9 102.28%)",
      },
      borderColor: {
        normal: "hsl(var(--border))",
        focus: "hsl(var(--border-focus))",
        primary: "hsl(var(--border-primary))",
        secondary: "hsl(var(--border-secondary))",
        tertiary: "hsl(var(--border-tertiary))",
        inverse: "hsl(var(--border-inverse))",
        disable: "hsl(var(--border-disable))",
        error: "#F53E3E",
        default: "#FDFDFD",
        custom: {
          primary: "#E1E2E3",
          success: "#205B2B",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "8px",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "spin-slow": {
          from: { transform: "rotate(0deg)" },
          to: { transform: "rotate(360deg)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "spin-slow": "spin-slow 5s linear infinite",
      },
      fontSize: {
        xl: ["var(--text-xl)", { lineHeight: "var(--text-xl-line-height)" }],
        lg: ["var(--text-lg)", { lineHeight: "var(--text-lg-line-height)" }],
        md: ["var(--text-md)", { lineHeight: "var(--text-md-line-height)" }],
        sm: ["var(--text-sm)", { lineHeight: "var(--text-sm-line-height)" }],
        xs: ["var(--text-xs)", { lineHeight: "var(--text-xs-line-height)" }],
        "dp-2xl": [
          "var(--dp-2xl)",
          {
            lineHeight: "var(--dp-2xl-line-height)",
            letterSpacing: "var(--dp-2xl-letter-spacing)",
          },
        ],
        "dp-xl": [
          "var(--dp-xl)",
          { lineHeight: "var(--dp-xl-line-height)", letterSpacing: "var(--dp-xl-letter-spacing)" },
        ],
        "dp-lg": [
          "var(--dp-lg)",
          { lineHeight: "var(--dp-lg-line-height)", letterSpacing: "var(--dp-lg-letter-spacing)" },
        ],
        "dp-md": [
          "var(--dp-md)",
          { lineHeight: "var(--dp-md-line-height)", letterSpacing: "var(--dp-md-letter-spacing)" },
        ],
        "dp-sm": ["var(--dp-sm)", { lineHeight: "var(--dp-sm-line-height)" }],
        "dp-xs": ["var(--dp-xs)", { lineHeight: "var(--dp-xs-line-height)" }],
      },
      boxShadow: {
        xs: "0px 1px 2px 0px #14151A0D",
        focus: "var(--shadow-focus)",
        chart:
          "rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px",
        btn: "0px 1px 2px 0px #14151A0D",
        toggle: "0px 1px 2px 0px hsla(210, 13%, 62%, 0.16)",
        box: "0px 12px 24px -4px #919EAB1F, 0px 0px 2px 0px #919EAB33",
        // sm: "0px 3px 10px -2px #14151A05, 0px 10px 16px -3px #14151A0D",
        sm: "0px 0px 4px -4px var(--color-shadow-light), 0px 1px 4px 0px var(--color-shadow-medium)",
        medium: "0px 0px 32px 0px rgba(9, 10, 13, 0.05), 0px 4px 20px -8px rgba(9, 10, 13, 0.15)",
        lg: " 0px 0px 32px 0px rgba(9, 10, 13, 0.10), 0px 20px 24px -24px  rgba(9, 10, 13, 0.02)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
