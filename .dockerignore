# Ignore node_modules directory
node_modules/

# Ignore build artifacts
build/
dist/

# Ignore development files
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ignore editor and IDE files
.vscode/
.idea/

# Ignore package manager lock files
# yarn.lock
# package-lock.json

# Ignore test coverage reports
coverage/

# Ignore any other files or directories specific to your project