# Remote Federation Setup & Troubleshooting

## Tổng quan

Dự án này sử dụng Module Federation để tích hợp remote CRM360. Tài liệu này mô tả cách cấu hình và khắc phục sự cố khi remote không load được.

## C<PERSON>u hình hiện tại

### 1. Vite Configuration (vite.config.ts)

```typescript
federation({
  name: "skycom_react",
  remotes: {
    crm360: getCRM360RemoteUrl(), // Với cache busting cho production
  },
  shared: ["react", "react-dom", "react-router-dom", "react-redux", "@reduxjs/toolkit"],
})
```

### 2. Environment Variables

```bash
# Development
VITE_CRM360_URL=http://localhost:5173/dist

# Production
VITE_CRM360_URL=https://crmfe.big360.ai
```

## Tính năng mới

### 1. <PERSON><PERSON> Busting tự động

- **Production**: Tự động thêm timestamp vào URL remote để force reload
- **Development**: Sử dụng URL gốc để dễ debug

### 2. Retry Logic cải tiến

- Retry tối đa 3 lần với delay tăng dần
- Clear cache tự động khi retry
- Fallback về Maintenance component khi fail

### 3. Remote Cache Manager

- Service Worker để quản lý cache remote modules
- API để clear cache và force refresh
- Health check cho remote endpoints

### 4. Remote Debugger Component

- Monitor trạng thái cache và remote health
- Clear cache thủ công
- Force refresh remote modules
- Keyboard shortcut: `Ctrl+Shift+D`

## Cách sử dụng

### 1. Development

```bash
# Bật Remote Debugger
# Thêm ?debug vào URL hoặc dùng Ctrl+Shift+D
http://localhost:3000?debug

# Test remote connectivity
node scripts/test-remote.js
```

### 2. Production

```bash
# Set environment variables
export VITE_CRM360_URL=https://crmfe.big360.ai

# Build với cache busting
npm run build
```

### 3. Troubleshooting

#### Remote không load được:

1. **Kiểm tra connectivity**:
   ```bash
   node scripts/test-remote.js
   ```

2. **Clear cache**:
   - Sử dụng Remote Debugger (Ctrl+Shift+D)
   - Hoặc gọi API: `remoteCacheManager.clearRemoteCache()`

3. **Force refresh remote**:
   ```javascript
   import { remoteCacheManager } from 'utils/remoteCacheManager';
   await remoteCacheManager.forceRefreshRemote(remoteUrl);
   ```

#### Cache issues:

1. **Check cache status**:
   ```javascript
   const status = await remoteCacheManager.getCacheStatus();
   console.log(status);
   ```

2. **Manual cache cleanup**:
   ```javascript
   await remoteCacheManager.clearRemoteCache();
   ```

## API Reference

### remoteCacheManager

```typescript
// Initialize
await remoteCacheManager.init();

// Clear all remote caches
await remoteCacheManager.clearRemoteCache();

// Force refresh specific remote
await remoteCacheManager.forceRefreshRemote(url);

// Check remote health
const isHealthy = await remoteCacheManager.checkRemoteHealth(url);

// Get cache status
const status = await remoteCacheManager.getCacheStatus();
```

### Remote Debugger

```typescript
import RemoteDebugger from 'components/RemoteDebugger';

<RemoteDebugger 
  isVisible={showDebugger}
  onToggle={() => setShowDebugger(!showDebugger)}
/>
```

## Best Practices

### 1. Production Deployment

- Luôn sử dụng cache busting cho production
- Monitor remote health định kỳ
- Implement fallback mechanisms

### 2. Development

- Sử dụng Remote Debugger để debug
- Test connectivity trước khi deploy
- Clear cache khi có vấn đề

### 3. Error Handling

- Luôn có fallback component (Maintenance)
- Log errors để debug
- Implement retry logic

## Monitoring

### 1. Health Checks

```javascript
// Periodic health check
setInterval(async () => {
  const isHealthy = await remoteCacheManager.checkRemoteHealth(remoteUrl);
  if (!isHealthy) {
    console.warn('Remote is not healthy');
  }
}, 60000); // Every minute
```

### 2. Error Tracking

```javascript
// Track remote loading errors
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason?.message?.includes('crm360')) {
    console.error('Remote loading error:', event.reason);
    // Send to monitoring service
  }
});
```

## Troubleshooting Common Issues

### 1. "Remote container not found"

- Kiểm tra VITE_CRM360_URL
- Verify remote server đang chạy
- Check CORS headers

### 2. "Failed to fetch"

- Network connectivity issues
- CORS configuration
- Remote server down

### 3. "Module not found"

- Remote module không expose đúng
- Version mismatch
- Build configuration issues

### 4. Cache không update

- Clear browser cache
- Use cache busting
- Check service worker

## Environment Setup

### Development

```bash
# .env
VITE_CRM360_URL=http://localhost:5173/dist
```

### Production

```bash
# Environment variables in deployment
VITE_CRM360_URL=https://crmfe.big360.ai
```

## Support

Nếu gặp vấn đề:

1. Check Remote Debugger (Ctrl+Shift+D)
2. Run test script: `node scripts/test-remote.js`
3. Check browser console for errors
4. Verify environment variables
5. Test remote URL manually
