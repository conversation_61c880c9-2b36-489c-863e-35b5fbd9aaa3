image:
  name: docker:24.0.5
  entrypoint: [""]

services:
  - docker:24.0.5-dind

stages:
  - build
  - test
  - deploy
  - release

variables:
  DEV_IMAGE_TAG: ${DOCKER_REGISTRY_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/dev:$CI_COMMIT_SHORT_SHA
  PROD_IMAGE_TAG: ${DOCKER_REGISTRY_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/prod:$CI_COMMIT_SHORT_SHA

test:
  stage: test
  script:
    - echo "Stage test is coming up"
  only:
    - dev
    - main
  when: manual

build-dev:
  stage: build
  script:
    - export ENV=dev
    - export IMAGE=${DEV_IMAGE_TAG}_web
    # setup env
    - chmod +x ./k8s/scripts/setup-env.sh
    - sh ./k8s/scripts/setup-env.sh $ENV
    - echo $REACT_APP_API_URL || "null"
    # build and push to gitlab registry
    - DOCKER_FILE=./Dockerfile
    - docker build -t ${IMAGE} --build-arg COMMIT_HASH=${CI_COMMIT_SHORT_SHA} -f ${DOCKER_FILE} .
    - docker images ${IMAGE}
    - docker push ${IMAGE}
  only:
    - dev
  tags:
    - runner-01
  when: manual

deploy-dev:
  stage: deploy
  image:
    name: bitnami/kubectl:1.29
    entrypoint: [""]
  script:
    # Export env and image tag
    - export IMAGE=$DEV_IMAGE_TAG
    # Update image tag in k8s deployment
    - sed -i "s#<IMAGE>#${IMAGE}_web#" ./k8s/dev/app.yml
    # Deploy to k8s
    - kubectl apply -f ./k8s/dev/
  only:
    - dev
  needs:
    - build-dev
  tags:
    - skylink-runner-cluster-dev-v2
  when: manual

build-prod:
  stage: build
  script:
    - export ENV=prod
    - export IMAGE=${PROD_IMAGE_TAG}_web
    # setup env
    - chmod +x ./k8s/scripts/setup-env.sh
    - sh ./k8s/scripts/setup-env.sh $ENV
    - echo $REACT_APP_API_URL || "null"
    # build and push to gitlab registry
    - DOCKER_FILE=./Dockerfile
    - docker build -t ${IMAGE} --build-arg COMMIT_HASH=${CI_COMMIT_SHORT_SHA} -f ${DOCKER_FILE} .
    - docker images ${IMAGE}
    - docker push ${IMAGE}
  only:
    - main
  tags:
    - runner-01
  when: manual

deploy-prod:
  stage: deploy
  image:
    name: bitnami/kubectl:1.29
    entrypoint: [""]
  script:
    # Export env and image tag
    - export IMAGE=$PROD_IMAGE_TAG
    # Update image tag in k8s deployment
    - sed -i "s#<IMAGE>#${IMAGE}_web#" ./k8s/prod/app.yml
    # Deploy to k8s
    - kubectl apply -f ./k8s/prod/
  only:
    - main
  needs:
    - build-prod
  tags:
    - ci-dev
  when: manual

semantic-release:
  image: node:20-alpine
  stage: release
  before_script:
    - apk add --no-cache git
    - npm install -g semantic-release @semantic-release/changelog @semantic-release/git @semantic-release/gitlab conventional-changelog-conventionalcommits
    - git fetch origin $CI_COMMIT_REF_NAME
    - git checkout $CI_COMMIT_REF_NAME
    - git reset --hard origin/$CI_COMMIT_REF_NAME
  script:
    - semantic-release
  only:
    refs:
      - main
      - dev
  tags:
    - runner-01
  when: manual
