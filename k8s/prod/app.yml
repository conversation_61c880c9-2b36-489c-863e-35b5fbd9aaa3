apiVersion: apps/v1
kind: Deployment
metadata:
  name: fe-web-big360-deployment
  namespace: prod-big360
spec:
  selector:
    matchLabels:
      app: fe-web-big360-pod
  template:
    metadata:
      labels:
        app: fe-web-big360-pod
    spec:
      containers:
        - name: fe-web-big360-container
          image: <IMAGE>
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              memory: "256Mi"
              cpu: "500m"
            requests:
              memory: "128Mi"
              cpu: "250m"
          ports:
            - containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 5
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 5
      imagePullSecrets:
        - name: registry-skylink
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: fe-web-big360-service
  namespace: prod-big360
spec:
  selector:
    app: fe-web-big360-pod
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
