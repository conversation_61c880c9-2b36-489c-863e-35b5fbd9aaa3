#!/bin/sh
set -o errexit
set -o pipefail
set -o nounset

# Convert the environment to uppercase
ENV=$(echo "$1" | tr '[:lower:]' '[:upper:]')

# List of environments to check against
ENVS="DEV STAG PROD"

# Check if the provided environment is in the list
if echo "$ENVS" | grep -wq "$ENV"; then
    echo "Setting up environment $ENV"
else
    echo "Invalid environment $ENV"
    exit 1
fi

# Retrieve the variables dynamically
DEBUG=$(eval echo "\${${ENV}_DEBUG}")
REACT_APP_API_URL=$(eval echo "\${${ENV}_REACT_APP_API_URL}")
REACT_APP_DEPLOY_TAG=$(eval echo "\${${ENV}_REACT_APP_DEPLOY_TAG}")
REACT_APP_TRACKING_ID=$(eval echo "\${${ENV}_REACT_APP_TRACKING_ID}")
REACT_APP_HOTJAR_ID=$(eval echo "\${${ENV}_REACT_APP_HOTJAR_ID}")
REACT_APP_FACEBOOK=$(eval echo "\${${ENV}_REACT_APP_FACEBOOK}")
REACT_APP_ZALO=$(eval echo "\${${ENV}_REACT_APP_ZALO}")
REACT_APP_SITE_KEY=$(eval echo "\${${ENV}_REACT_APP_SITE_KEY}")
REACT_APP_USER_MANUAL=$(eval echo "\${${ENV}_REACT_APP_USER_MANUAL}")
REACT_APP_SENTRY_DNS=$(eval echo "\${${ENV}_REACT_APP_SENTRY_DNS}")

VITE_CRM360_URL=$(eval echo "\${${ENV}_VITE_CRM360_URL}")


# Write to .env.production
{
    echo "DEBUG=${DEBUG}"
    echo "REACT_APP_API_URL=${REACT_APP_API_URL}"
    echo "REACT_APP_DEPLOY_TAG=${REACT_APP_DEPLOY_TAG}"
    echo "REACT_APP_TRACKING_ID=${REACT_APP_TRACKING_ID}"
    echo "REACT_APP_HOTJAR_ID=${REACT_APP_HOTJAR_ID}"
    echo "REACT_APP_FACEBOOK=${REACT_APP_FACEBOOK}"
    echo "REACT_APP_ZALO=${REACT_APP_ZALO}"
    echo "REACT_APP_SITE_KEY=${REACT_APP_SITE_KEY}"
    echo "REACT_APP_USER_MANUAL=${REACT_APP_USER_MANUAL}"
    echo "REACT_APP_SENTRY_DNS=${REACT_APP_SENTRY_DNS}"
    echo "VITE_CRM360_URL=${VITE_CRM360_URL}"
    echo "IMAGE=${IMAGE}"
    echo "IMAGE_BUILD_DATE=$(date "+%Y-%m-%d %H:%M:%S %z")"
} >> .env.production
