type Props = {
  text: React.ReactNode;
  required?: boolean;
  isHideIcon?: boolean;
};

const LabelAuth = ({ text, required, isHideIcon = false }: Props) => {
  return (
    <div className="relative w-fit font-normal text-secondary">
      {required && !isHideIcon && (
        <span className="absolute -right-2 text-primary-hover !text-error-default ">*</span>
      )}
      {text}
    </div>
  );
};

export default LabelAuth;
