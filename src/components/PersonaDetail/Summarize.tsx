import BarChartHorizontal from "components/chart/BarChartHorizontal";
import ChartWrapper from "components/chart/ChartWrapper";
import PolarChart from "components/chart/PolarChart";
import AgeGroupChart from "views/Persona/Summarize/AgeGroupChart";

import { cn } from "utils/utils";
import { useAppSelector } from '../../store';

type Props = {
  cityData: { city: string; count: number }[];
  valuesRegion: number[];
  labelsRegion: string[];
  loading: boolean;
  dataAge: any;
  dataGender: any;
  className?: string;
};

const Summarize = ({
  cityData,
  valuesRegion,
  labelsRegion,
  loading,
  dataAge,
  dataGender,
  className,
}: Props) => {
  const { collapse } = useAppSelector((state) => state.sidebar);
  return (
    <div
      className={cn(
        'grid grid-cols-1 my-6 gap-x-7 gap-y-6 xl:grid-cols-3',
        collapse ? 'md:grid-cols-3' : 'md:grid-cols-2',
        className
      )}
    >
      {dataAge && dataGender && (
        <AgeGroupChart dataAge={dataAge} dataGender={dataGender} loading={loading} />
      )}

      <ChartWrapper
        isEmptyData={cityData.length === 0}
        loading={loading}
        isPieChart
        className={cn("col-span-2 lg:col-span-1 xl:col-span-2", collapse ? "col-span-3" : "md:col-span-2")}
        title="City"
      >
        <BarChartHorizontal
          values={cityData.map((item) => item.count) || []}
          labels={cityData.map((item) => item.city) || []}
        />
      </ChartWrapper>
      <ChartWrapper
        isPieChart
        isEmptyData={valuesRegion.length === 0} loading={loading} title="Region"
        className={cn("col-span-2 xl:col-span-1", collapse ? "col-span-3" : "md:col-span-2")}
      >
        <PolarChart labels={labelsRegion} values={valuesRegion} titleTooltip="Region" />
      </ChartWrapper>
    </div>
  );
};
export default Summarize;
