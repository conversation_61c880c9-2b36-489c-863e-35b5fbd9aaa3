import { useMemo } from "react";

import { DataTable } from "components/DataTable/TableCustom";
import Pagination from "components/Pagination";
import ActionAddProfile from "views/YourAudience/components/ActionAddProfile";

import { SortType } from "types";
import { PersonaAudiencePreview } from "types/Persona";
import useFilters from "hooks/useFilters";
import { getPersonaColumns } from "constants/persona/column";

interface Props {
  loading: boolean;
  rowData: PersonaAudiencePreview[];
  count: number;
  isAudienceArchive?: boolean;
  isDataset?: boolean;
}

const UserTable = ({ loading, rowData, count, isAudienceArchive = false, isDataset = false }: Props) => {
  const { searchParams, params, setSearchParams } = useFilters();

  const handleSortChange = (sortType: SortType, field: string) => {
    const sortBy = {
      asc: `${field}`,
      desc: `-${field}`,
    };
    setSearchParams({ ...params, order_by: sortBy[sortType] });
  };

  const memoizedColumns = useMemo(
    () => getPersonaColumns({ buyProfile: false, handleSortChange, isDataset }),
    [handleSortChange, isDataset]
  );
  return (
    <div className="mt-6">
      <DataTable
        loading={loading}
        columns={[
          ...(isAudienceArchive
            ? [
                {
                  accessorKey: "add_profile",
                  header: "",
                  cell: ({ row }: any) => <ActionAddProfile id={row?.original?.uid} />,
                },
              ]
            : []),
          ...memoizedColumns,
        ]}
        data={rowData}
      />
      <Pagination
        className="m-4"
        totalCount={count}
        pageSize={10}
        currentPage={Number(searchParams.get("page")) || 1}
        onPageChange={(page) => setSearchParams({ ...params, page: String(page) })}
      />
    </div>
  );
};

export default UserTable;
