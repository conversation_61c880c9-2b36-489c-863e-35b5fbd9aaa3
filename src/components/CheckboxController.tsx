import { RiErrorWarningLine } from '@remixicon/react';
import { Control, Controller } from 'react-hook-form';

import LabelAuth from './LabelAuth';

import { cn } from 'utils/utils';
import { Checkbox } from './ui/checkbox';
import { Box } from './Box';

type Props = {
  control: Control<any>;
  defaultValue?: string;
  placeholder?: string;
  name: string;
  required?: boolean;
  label?: React.ReactNode;
  error?: string;
  className?: string;
  classNameCheckBox?: string;
  classNameContainer?: string;
  isHideIcon?: boolean;
};

const CheckboxController = (props: Props) => {

  return (
    <Controller
      name={props.name}
      control={props.control}
      defaultValue={props.defaultValue || ''}
      rules={{ required: props.required, minLength: 6 }}
      render={({ field: { onChange, onBlur, value } }) => (
        <div className={cn('flex flex-col gap-1 ', props.classNameContainer)}>

          <Box className="gap-1 justify-start items-center">
            <Checkbox
              name="term"
              checked={value}
              defaultValue={props.defaultValue}
              className={props.className}
              classNameCheckBox={props.classNameCheckBox}
              onCheckedChange={onChange}
              onBlur={onBlur}
            />
            {props.label && <LabelAuth text={props.label} required={props.required} isHideIcon={props.isHideIcon} />}
          </Box>
          {props.error && (
            <div className="text-red-500 text-xs flex items-center gap-1 lowercase">
              <RiErrorWarningLine size={16} color="#F48E2F99" opacity={0.6} /> {props.error}
            </div>
          )}
        </div>
      )}
    />
  );
};

export default CheckboxController;
