import { memo, useContext, useState } from "react";
import { RiLoader2Line } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import { Button } from "components/ui/button";

import LABEL from "constants/label";
import handleCloseModal from "utils/handleCloseModal";

interface Props {
  title: string;
  handleDelete: () => void;
}

const DeleteYourDataModal = ({ title, handleDelete }: Props) => {
  const modal = useContext(ModalContext);
  const [loading, setLoading] = useState<boolean>(false);

  const handleSubmit = async () => {
    setLoading(true);
    await handleDelete();
    setLoading(false);
    handleCloseModal(modal);
  };
  return (
    <>
      <div className="text-lg font-semibold leading-none tracking-tight">{title}</div>
      <div className="w-full flex items-center gap-4 font-medium">
        <Button disabled={loading} onClick={handleSubmit} variant="main" className="flex-1">
          {loading ? <RiLoader2Line size={16} className="animate-spin" /> : "Delete"}
        </Button>
        <Button
          type="button"
          variant="secondary"
          onClick={() => handleCloseModal(modal)}
          className="rounded-md text-secondary"
        >
          {LABEL.cancel}
        </Button>
      </div>
    </>
  );
};
export default memo(DeleteYourDataModal);
