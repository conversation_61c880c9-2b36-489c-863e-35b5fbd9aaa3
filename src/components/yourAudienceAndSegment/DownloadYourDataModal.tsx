import { memo, useContext, useState } from 'react';
import { useAppSelector } from 'store';
import { ModalContext } from 'providers/Modal';
import { RiLoader2Line } from '@remixicon/react';

import { Button } from 'components/ui/button';
import { toast } from 'components/ui/use-toast';
import handleCloseModal from 'utils/handleCloseModal';
import LABEL from 'constants/label';
import httpInstance from '../../apis';
import { Box } from '../Box';
import FileCSV from '../../assets/icons/FileCSV';
import { SEGMENT_LABEL } from '../../constants/Segment/label';

const DownloadYourDataModal = ({ id, name, segment_size, isAudience }: {
  id: string | number,
  name: string,
  segment_size: number,
  isAudience?: boolean,
}) => {
  const modal = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);
  const [loading, setLoading] = useState(false);

  const exportSegments = async () => {
    try {
      const response = await httpInstance.post(`/social-data/segments/${id}/export/`, {}, { headers: { isv2: true } });
      return response.data;
    } catch (error: any) {
      return { error: error, data: null };
    }
  };

  const exportAudience = async () => {
    try {
      const response = await httpInstance.post(`/social-data/user-audiences/${id}/export/?datatype=AUDIENCE`, {}, { headers: { isv2: true } });
      return response.data;
    } catch (error: any) {
      return { error: error, data: null };
    }
  };

  const handleDownloadFiles = () => {
    setLoading(true);
    if (isAudience){
      exportAudience().then(() => {
        toast({
          title: 'Download Successfully',
          status: 'info'
        });
      }).finally(() => {
        setLoading(false);
        handleCloseModal(modal);
      });
      return;
    }else{
      exportSegments().then(() => {
        toast({
          title: 'Download Successfully',
          status: 'info'
        });
      }).finally(() => {
        setLoading(false);
        handleCloseModal(modal);
      });
    }
  };

  return (
    <Box variant="col-start" className="gap-4 w-full">
      <div className="text-xl text-center w-full font-semibold">
        {SEGMENT_LABEL.download_segment}
      </div>

      <Box className="p-4 bg-brand-subtitle text-primary w-full rounded-2xl text-sm font-medium">
        <Box className="gap-1 justify-start">
          <FileCSV />
          <div>
            <div className="text-sm font-medium text-primary">{name}</div>
            <span className="text-xs text-tertiary">
              {SEGMENT_LABEL.segment_size}: {segment_size}
            </span>
          </div>
        </Box>
      </Box>

      <div className="flex flex-col gap-1 w-full">
        <div className="text-secondary text-md font-medium">{SEGMENT_LABEL.download_link}</div>
        <div className="mt-4">
          <div className="relative w-fit font-normal text-secondary text-xs mb-1">
            <span className="absolute -right-2 text-primary-hover !text-error-default ">*</span>
            Email address
          </div>
          <input
            type="email"
            value={user.email}
            disabled
            readOnly
            className="p-2 w-full rounded-lg border outline-none text-sm"
          />
        </div>
      </div>
      <div className="grid grid-cols-2 w-full gap-4">
        <Button
          className="w-full rounded-xl font-medium text-md"
          type="button"
          variant="outline"
          onClick={() => handleCloseModal(modal)}
        >
          {LABEL.cancel}
        </Button>
        <Button
          variant="default"
          className="w-full rounded-xl font-medium text-md"
          onClick={() => handleDownloadFiles()}
          disabled={loading}
          type="button"
        >
          {loading ? <RiLoader2Line className="animate-spin" /> : LABEL.confirm}
        </Button>
      </div>
    </Box>
  );
};
export default memo(DownloadYourDataModal);
