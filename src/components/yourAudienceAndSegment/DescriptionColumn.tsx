import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import useOutsideClick from "hooks/useClickOutSide";
import { useRef, useState } from "react";
import { cn } from '../../utils/utils';
import { TooltipPortal } from '@radix-ui/react-tooltip';

const DescriptionColumn = ({ description, className }: { description: string, className?: string }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const ref = useRef(null);
  const refSecond = useRef(null);

  useOutsideClick(ref, refSecond, () => {
    setIsOpen(false);
  });

  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
        <TooltipTrigger
          ref={ref}
          className={cn('text-center max-w-[250px] w-full truncate text-secondary font-medium]', className)}
          onClick={() => setIsOpen(true)}
        >
          {description}
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent
            ref={refSecond}
            side="bottom"
            align="center"
            sideOffset={5}
            className="max-w-[325px] px-2 py-1"
          >
            <span>{description}</span>
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};

export default DescriptionColumn;
