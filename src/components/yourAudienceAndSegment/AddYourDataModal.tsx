import React, { useContext, useState } from 'react';
import { Box } from '../Box';
import { ModalContext } from 'providers/Modal';
import useResponsive from 'hooks/useResponsive';
import { RiAddLine, RiCloseLine, RiErrorWarningLine } from '@remixicon/react';
import handleCloseModal from 'utils/handleCloseModal';
import { Button } from '../ui/button';
import { PlatFormArr } from 'constants/yourData';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';
import LABEL from 'constants/label';
import { socialPersonaApi } from 'apis/socialPersona';
import { toast } from '../ui/use-toast';
import { CRM_ROUTE } from '../../constants/socialPersona';
import { useNavigate } from 'react-router-dom';
import { handleGetSegmentLog } from '../../store/redux/SegmentLog/slice';
import { useAppDispatch } from '../../store';
import ColorPicker from '../ColorPicker';

type TAddYourDataModalProps = {
  id: number;
  count?: number;
  typeAdd: 'persona' | 'social'
  segmentName: string
}

interface IPlatFromProps {
  id: number;
  type: string;
  icon: React.ReactNode;
  count?: number;
  typeAdd: 'persona' | 'social';
  data: {
    title: string,
    description: string,
    isComingSoon: boolean,
  };
  segmentName: string;
}

interface IConfirmAddProps {
  id: number;
  name: string;
  count?: number;
  type: string;
  title: string;
  typeAdd: 'persona' | 'social';
  segmentName: string;
}

const PlatForm: React.FC<IPlatFromProps> = ({ ...props }: IPlatFromProps) => {
  const { id, icon, count, data, type, typeAdd, segmentName } = props;
  const { title, description, isComingSoon } = data;
  const modal = useContext(ModalContext);
  const context = useContext(ModalContext);
  const { isDesktop } = useResponsive();

  const confirmAddContact = () => {
    handleCloseModal(context);
    modal?.setDataDialog((prev) => ( {
      ...prev,
      className: 'z-[9999] max-w-[calc(100%-32px)] md:max-w-[529px] bg-[#FDFDFD] p-4 lg:p-6',
      isOpen: true,
      isShowTitle: false,
      content: (
        <ConfirmAdd
          id={id}
          name={title}
          count={count}
          segmentName={segmentName}
          title={title}
          type={type}
          typeAdd={typeAdd}
        />
      )
    } ));
  };

  return (
    <Box className="p-2 w-full gap-3 bg-[#FDFDFD] rounded-xl flex-col lg:flex-row items-center">
      {isDesktop && <div className="min-w-[48px]">{icon}</div>}
      <Box variant="col-start" className="gap-1">
        <Box className="gap-2 lg:gap-3">
          {!isDesktop && icon}
          <p className="text-primary text-sm">
            {title}
          </p>
          {isComingSoon && <p className="text-secondary text-xs bg-[#F0F0F0] p-1 rounded-lg">Coming soon</p>}
        </Box>
        <p className="text-secondary text-xs">
          {description}
        </p>
      </Box>
      <div className="w-full lg:w-fit">
        <Button
          onClick={confirmAddContact}
          variant={'outline'}
          disabled={isComingSoon}
          className="w-full lg:w-fit"
        >
          <RiAddLine /> Add
        </Button>
      </div>
    </Box>
  );
};

const ConfirmAdd: React.FC<IConfirmAddProps> = ({ ...props }: IConfirmAddProps) => {
  const { id, name, title, count, type, typeAdd, segmentName } = props;
  const [background, setBackground] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const modal = useContext(ModalContext);
  const context = useContext(ModalContext);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const handleAddContact = () => {
    setLoading(true);
    if (type === 'crm360') {
      socialPersonaApi.addListContactToCRM({
        id,
        typeAdd,
        color: background
      }).then((res) => {
        if (res.status === 200) {
          handleShowToast();
          dispatch(handleGetSegmentLog()).unwrap();
        } else {
          toast({
            description: res.error,
            status: 'error',
            duration: 3000
          });
        }
      }).catch().finally(() => {
        setLoading(false);
        handleCloseModal(context);
      });
    }
  };

  const handleShowToast = () => {
    toast({
      title: `Successfully added ${Number(count).toLocaleString()} contact${count && count > 1 ?
        's' :
        ''} to ${title}.`,
      description: <Button
        variant={'ghost'}
        className="h-auto w-fit p-0 m-0 float-right bg-transparent hover:bg-transparent"
        onClick={() => navigate(CRM_ROUTE.contactList)}
      >Check it out</Button>,
      status: 'success',
      duration: 3000
    });
  };

  return <Box variant="col-start" className="gap-6 justify-center items-center">
    <Box variant="col-start" className="gap-1 justify-center items-center text-sm">
      <RiErrorWarningLine size={67} color={'#8F5CFF'} />
      <p className="text-primary text-lg">Add your Contacts to {name}</p>
      <p className="text-center">
        You are about to add your <strong>({Number(count).toLocaleString()})</strong> contacts from this segment to {name}.
      </p>
      <div className="w-full text-left flex flex-col gap-1">
        <p className="font-semibold">Segment Name: <span className="text-secondary font-normal">{segmentName}</span></p>
        <p className="font-semibold">Total Contact: <span className="text-secondary font-normal">{Number(count).toLocaleString()}</span>
        </p>
        <Box className="gap-1 justify-start">
          <p className="font-semibold">Segment Color<span className="text-[#F53E3E]">*</span>:</p>
          <ColorPicker background={background} setBackground={setBackground} />
        </Box>
        <p className="font-semibold">This action cannot be undone. <span className="text-secondary font-normal">Are you sure you want to proceed?</span>
        </p>
      </div>
    </Box>
    <Box className="w-full gap-4">
      <Button
        variant="secondary"
        className="w-full"
        disabled={loading}
        onClick={() => handleCloseModal(modal)}
      >
        {LABEL.cancel}
      </Button>
      <Button
        variant="main"
        className="w-full"
        disabled={loading || !background}
        onClick={handleAddContact}
      >
        {loading ? <LoadingButtonIcon /> : LABEL.add}
      </Button>
    </Box>
  </Box>;
};

const AddYourDataModal: React.FC<TAddYourDataModalProps> = ({ ...props }: TAddYourDataModalProps) => {
  const { id, count, typeAdd, segmentName } = props;
  const context = useContext(ModalContext);

  const handleRenderIcon = (type: string) => {
    switch (type) {
      case 'crm360':
        return <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-[40px] h-[40px] lg:w-[49px] lg:h-[50px]"
          viewBox="0 0 49 50"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M34.0369 20.3371C35.7235 20.3371 36.8079 21.7715 36.8079 23.9972C36.8079 26.2229 35.7109 27.5753 34.0369 27.5753C32.3629 27.5753 31.3226 26.226 31.3226 23.9877C31.3226 21.7495 32.3503 20.3403 34.0369 20.3403V20.3371ZM28.3939 31.7839C28.3939 32.096 28.4633 32.4081 28.6493 32.7202C29.4468 34.0222 31.332 34.9774 33.854 34.99C37.8388 35.012 40.1685 33.0733 40.1685 29.8546V19.4261C40.1685 18.2817 39.4623 17.7174 38.4945 17.7174C37.5267 17.7174 36.7953 18.2849 36.7953 19.4261V20.1039H36.7291C36.0829 18.7105 34.7367 17.7742 32.9556 17.7742C29.7684 17.7742 27.8769 20.1701 27.8769 23.9468C27.8769 27.7235 29.8188 30.0752 32.8988 30.0752C34.6895 30.0752 36.1301 29.1925 36.7071 27.8622H36.7733V29.8356C36.7733 31.3804 35.6888 32.3955 33.8414 32.3829C32.7223 32.3734 31.8427 31.8974 30.9317 31.0336C30.5471 30.69 30.2034 30.5481 29.7621 30.5481C28.9172 30.5481 28.3876 31.0746 28.3876 31.7807L28.3939 31.7839ZM11.7203 26.3364C11.2821 25.6964 10.8975 25.4505 10.3805 25.4505C9.66489 25.4505 9.13527 25.9927 9.13527 26.7399C9.13527 28.6944 11.5658 30.4914 14.7341 30.4914C18.2681 30.4914 20.7554 28.5147 20.7554 25.6869C20.7554 23.641 19.2895 21.9607 17.3381 21.7715V21.5698C18.9585 21.3333 20.2604 19.7256 20.2604 17.9728C20.2604 15.4539 17.9843 13.6885 14.7436 13.6885C11.6762 13.6885 9.39062 15.4287 9.39062 17.3202C9.39062 18.0863 9.9297 18.619 10.68 18.619C11.2317 18.619 11.6447 18.37 11.9977 17.7805C12.6188 16.7149 13.5236 16.1727 14.6837 16.1727C16.1748 16.1727 17.2088 17.0617 17.2088 18.3637C17.2088 19.6657 16.1527 20.6114 14.7089 20.6114H13.6181C12.9025 20.6114 12.3981 21.1221 12.3981 21.8535C12.3981 22.5849 12.9151 23.1177 13.6181 23.1177H14.7688C16.4932 23.1177 17.6565 24.1107 17.6565 25.5798C17.6565 27.0488 16.5184 28.004 14.7593 28.004C13.4447 28.004 12.3981 27.424 11.7203 26.3364ZM22.4704 30.4914V22.3169C22.4704 20.2646 24.1349 18.6033 26.1872 18.6033V26.7777C26.1872 28.83 24.5226 30.4914 22.4704 30.4914ZM25.6355 14.2276C26.3542 14.9495 26.3542 16.1191 25.6355 16.841C24.9167 17.5598 23.744 17.5598 23.022 16.841C22.3033 16.1191 22.3033 14.9495 23.022 14.2276C23.7408 13.5088 24.9135 13.5088 25.6355 14.2276ZM24.5479 0.810547C37.8041 0.810547 48.5479 11.6394 48.5479 24.9997C48.5479 38.36 37.801 49.1889 24.5479 49.1889C11.2947 49.1889 0.547852 38.36 0.547852 24.9997C0.547852 11.6394 11.2947 0.810547 24.5479 0.810547Z"
            fill="#8F5CFF"
          />
        </svg>;
      case 'saleforce':
        return <svg
          className="w-[40px] h-[23px] lg:w-[49px] lg:h-[36px]"
          viewBox="0 0 49 36"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_3_12)">
            <path
              d="M20.3909 3.92767C21.9712 2.19949 24.1713 1.12762 26.6045 1.12762C29.8391 1.12762 32.6611 3.02064 34.1639 5.83089C35.4699 5.21848 36.9155 4.87779 38.4364 4.87779C44.2703 4.87779 49 9.88515 49 16.0617C49 22.2389 44.2703 27.2463 38.4364 27.2463C37.7376 27.2467 37.0406 27.1738 36.3556 27.0287C35.0323 29.5063 32.5103 31.1803 29.6157 31.1803C28.404 31.1803 27.2578 30.8866 26.2374 30.3642C24.8958 33.6763 21.7701 35.9986 18.1272 35.9986C14.3336 35.9986 11.1004 33.4792 9.85935 29.9459C9.31701 30.0668 8.75511 30.1298 8.17854 30.1298C3.66176 30.1298 0 26.2471 0 21.4566C0 18.2463 1.6452 15.4433 4.08961 13.9437C3.58637 12.7284 3.30645 11.387 3.30645 9.97668C3.30645 4.46754 7.56778 0.00164795 12.8238 0.00164795C15.9096 0.00164795 18.6521 1.54157 20.3909 3.92767Z"
              fill="#00A1E0"
            />
            <path
              d="M7.09719 18.6687C7.06648 18.7529 7.10837 18.7705 7.11813 18.7852C7.21025 18.8555 7.30383 18.9061 7.39806 18.9625C7.89782 19.2408 8.36972 19.3221 8.8632 19.3221C9.86831 19.3221 10.4923 18.761 10.4923 17.8576V17.8401C10.4923 17.0049 9.78807 16.7016 9.12703 16.4826L9.0412 16.4533C8.54281 16.2833 8.11285 16.1368 8.11285 15.7925V15.7742C8.11285 15.4797 8.36413 15.2628 8.75362 15.2628C9.18638 15.2628 9.70012 15.4137 10.031 15.6057C10.031 15.6057 10.128 15.6716 10.1636 15.5727C10.1831 15.5199 10.3507 15.0467 10.3681 14.9954C10.387 14.9397 10.3535 14.8987 10.3193 14.8767C9.94164 14.6357 9.41951 14.4708 8.87929 14.4708L8.77879 14.4715C7.85882 14.4715 7.21667 15.0547 7.21667 15.8906V15.9082C7.21667 16.7895 7.92514 17.0752 8.58897 17.2745L8.69575 17.3089C9.17947 17.465 9.59619 17.599 9.59619 17.9565V17.9741C9.59619 18.3009 9.32539 18.5441 8.88841 18.5441C8.7188 18.5441 8.17784 18.5404 7.59361 18.1528C7.52308 18.1096 7.48193 18.0781 7.42749 18.0437C7.39889 18.0246 7.32698 17.9916 7.29559 18.0913L7.09719 18.6687ZM21.8118 18.6687C21.7811 18.7529 21.823 18.7705 21.8327 18.7852C21.9249 18.8555 22.0184 18.9061 22.1126 18.9625C22.6124 19.2408 23.0843 19.3221 23.5778 19.3221C24.5829 19.3221 25.2069 18.761 25.2069 17.8576V17.8401C25.2069 17.0049 24.5026 16.7016 23.8416 16.4826L23.7558 16.4533C23.2574 16.2833 22.8274 16.1368 22.8274 15.7925V15.7742C22.8274 15.4797 23.0786 15.2628 23.4681 15.2628C23.9009 15.2628 24.4146 15.4137 24.7455 15.6057C24.7455 15.6057 24.8425 15.6716 24.8781 15.5727C24.8976 15.5199 25.0652 15.0467 25.0826 14.9954C25.1015 14.9397 25.068 14.8987 25.0338 14.8767C24.6562 14.6357 24.134 14.4708 23.5938 14.4708L23.4933 14.4715C22.5733 14.4715 21.9311 15.0547 21.9311 15.8906V15.9082C21.9311 16.7895 22.6396 17.0752 23.3034 17.2745L23.4102 17.3089C23.8939 17.465 24.3114 17.599 24.3114 17.9565V17.9741C24.3114 18.3009 24.0399 18.5441 23.6029 18.5441C23.4333 18.5441 22.8924 18.5404 22.3081 18.1528C22.2376 18.1096 22.1958 18.0796 22.1427 18.0437C22.1245 18.0312 22.0394 17.9968 22.0101 18.0913L21.8118 18.6687ZM31.8571 16.8993C31.8571 17.4099 31.7664 17.8121 31.5877 18.0964C31.411 18.3777 31.1437 18.5147 30.7711 18.5147C30.3976 18.5147 30.1317 18.3784 29.9579 18.0964C29.7819 17.8129 29.6927 17.4099 29.6927 16.8993C29.6927 16.3894 29.7819 15.9879 29.9579 15.7066C30.1317 15.4283 30.3976 15.2927 30.7711 15.2927C31.1438 15.2927 31.4111 15.4283 31.5884 15.7066C31.7664 15.9879 31.8571 16.3894 31.8571 16.8993ZM32.6961 15.9528C32.6137 15.6604 32.4853 15.4026 32.3143 15.1879C32.1433 14.9726 31.9269 14.7997 31.67 14.6737C31.4138 14.5484 31.111 14.4847 30.7711 14.4847C30.4304 14.4847 30.1275 14.5484 29.8713 14.6737C29.6144 14.7997 29.3981 14.9726 29.2264 15.1879C29.0561 15.4033 28.9276 15.6612 28.8446 15.9528C28.7629 16.2436 28.7217 16.5615 28.7217 16.8993C28.7217 17.2371 28.7629 17.5557 28.8446 17.8458C28.9276 18.1374 29.0554 18.3953 29.2271 18.6106C29.3981 18.826 29.6152 18.9982 29.8713 19.1205C30.1282 19.2429 30.4304 19.3051 30.7711 19.3051C31.111 19.3051 31.4133 19.2429 31.6701 19.1205C31.9263 18.9982 32.1433 18.826 32.3144 18.6106C32.4854 18.396 32.6138 18.1381 32.6962 17.8458C32.7786 17.555 32.8197 17.2363 32.8197 16.8993C32.8197 16.5623 32.7785 16.2436 32.6961 15.9528ZM39.5859 18.3783C39.558 18.2926 39.4791 18.3248 39.4791 18.3248C39.357 18.3739 39.2271 18.4193 39.0889 18.442C38.9486 18.4647 38.7944 18.4765 38.6289 18.4765C38.2227 18.4765 37.9002 18.3497 37.6692 18.0992C37.4375 17.8486 37.3076 17.4435 37.309 16.8955C37.3104 16.3966 37.4249 16.0215 37.6308 15.7358C37.8353 15.4516 38.1467 15.3058 38.562 15.3058C38.9082 15.3058 39.172 15.3475 39.4484 15.4391C39.4484 15.4391 39.5147 15.4691 39.5461 15.3783C39.6194 15.1644 39.6738 15.0113 39.752 14.7761C39.7743 14.7094 39.7199 14.6809 39.7003 14.6728C39.5914 14.6281 39.3346 14.5556 39.1405 14.5248C38.9591 14.4955 38.7469 14.4801 38.5109 14.4801C38.1584 14.4801 37.8443 14.5432 37.5756 14.6692C37.3076 14.7944 37.08 14.9673 36.9 15.1827C36.7199 15.3981 36.5831 15.6559 36.4916 15.9475C36.4009 16.2383 36.3548 16.5578 36.3548 16.8955C36.3548 17.6259 36.5426 18.2164 36.9133 18.6486C37.2846 19.0824 37.8423 19.3028 38.5697 19.3028C38.9996 19.3028 39.4408 19.2113 39.7577 19.0801C39.7577 19.0801 39.8184 19.0494 39.7919 18.9754L39.5859 18.3783ZM41.0539 16.4101C41.0937 16.1266 41.1684 15.8907 41.2835 15.7069C41.4574 15.4277 41.7226 15.2746 42.0954 15.2746C42.4681 15.2746 42.7145 15.4285 42.8911 15.7069C43.0084 15.8907 43.0593 16.1369 43.0796 16.4101H41.0539ZM43.8787 15.7867C43.8076 15.5046 43.6309 15.2196 43.5151 15.0892C43.3322 14.8826 43.1535 14.7383 42.9762 14.6577C42.7445 14.5537 42.4667 14.4848 42.1624 14.4848C41.8078 14.4848 41.486 14.5471 41.2249 14.676C40.9632 14.805 40.7434 14.9808 40.5709 15.1999C40.3985 15.4182 40.2687 15.6782 40.1863 15.9735C40.1032 16.2673 40.0613 16.5874 40.0613 16.9251C40.0613 17.2687 40.1046 17.5889 40.1905 17.8767C40.2771 18.1669 40.4152 18.4225 40.6023 18.6342C40.7886 18.8475 41.0288 19.0145 41.3164 19.1309C41.6019 19.2467 41.9488 19.3068 42.3473 19.306C43.1675 19.3031 43.5995 19.1111 43.7775 19.0078C43.809 18.9895 43.839 18.9573 43.8012 18.865L43.6156 18.3192C43.5877 18.2379 43.5088 18.2679 43.5088 18.2679C43.3056 18.347 43.0167 18.4892 42.3431 18.4877C41.9027 18.487 41.576 18.3507 41.3714 18.1375C41.1614 17.9192 41.0587 17.5983 41.0406 17.1456L43.8808 17.1485C43.8808 17.1485 43.9554 17.1471 43.9632 17.0708C43.966 17.0387 44.0609 16.4585 43.8787 15.7867ZM18.3081 16.4101C18.3486 16.1266 18.4226 15.8907 18.5378 15.7069C18.7116 15.4277 18.9768 15.2746 19.3495 15.2746C19.7223 15.2746 19.9687 15.4285 20.146 15.7069C20.2625 15.8907 20.3135 16.1369 20.3337 16.4101H18.3081ZM21.1322 15.7867C21.0611 15.5046 20.8852 15.2196 20.7693 15.0892C20.5864 14.8826 20.4077 14.7383 20.2305 14.6577C19.9987 14.5537 19.7209 14.4848 19.4166 14.4848C19.0627 14.4848 18.7402 14.5471 18.4792 14.676C18.2174 14.805 17.9976 14.9808 17.8251 15.1999C17.6527 15.4182 17.5229 15.6782 17.4406 15.9735C17.3582 16.2673 17.3156 16.5874 17.3156 16.9251C17.3156 17.2687 17.3589 17.5889 17.4448 17.8767C17.5313 18.1669 17.6696 18.4225 17.8566 18.6342C18.043 18.8475 18.2831 19.0145 18.5706 19.1309C18.8562 19.2467 19.203 19.3068 19.6016 19.306C20.4218 19.3031 20.8538 19.1111 21.0318 19.0078C21.0633 18.9895 21.0932 18.9573 21.0556 18.865L20.8706 18.3192C20.842 18.2379 20.7631 18.2679 20.7631 18.2679C20.56 18.347 20.2717 18.4892 19.5967 18.4877C19.157 18.487 18.8304 18.3507 18.6258 18.1375C18.4157 17.9192 18.3131 17.5983 18.2949 17.1456L21.1351 17.1485C21.1351 17.1485 21.2098 17.1471 21.2175 17.0708C21.2202 17.0387 21.3151 16.4585 21.1322 15.7867ZM12.1691 18.362C12.0581 18.2689 12.0428 18.2455 12.0051 18.1854C11.9492 18.0938 11.9206 17.9634 11.9206 17.7979C11.9206 17.5356 12.003 17.3473 12.174 17.2206C12.172 17.2214 12.4183 16.9972 12.9976 17.0052C13.4046 17.0111 13.7682 17.0741 13.7682 17.0741V18.4294H13.7689C13.7689 18.4294 13.408 18.5107 13.0018 18.5363C12.4239 18.573 12.167 18.3613 12.1691 18.362ZM13.2991 16.2675C13.184 16.2587 13.0346 16.2535 12.8559 16.2535C12.6123 16.2535 12.3771 16.2858 12.1565 16.348C11.9346 16.4103 11.7349 16.5078 11.5632 16.6366C11.3917 16.7651 11.2508 16.9333 11.1514 17.1282C11.0509 17.3246 10.9999 17.5561 10.9999 17.8154C10.9999 18.0792 11.0432 18.3085 11.1298 18.4961C11.2163 18.6843 11.3413 18.8411 11.5004 18.962C11.6581 19.0829 11.8529 19.1715 12.079 19.225C12.3017 19.2784 12.5543 19.3055 12.8308 19.3055C13.1218 19.3055 13.4122 19.2807 13.6935 19.2301C13.972 19.1802 14.314 19.1077 14.4089 19.085C14.4755 19.0682 14.5418 19.0504 14.6079 19.0315C14.6784 19.0133 14.6728 18.9341 14.6728 18.9341L14.6714 16.2081C14.6714 15.6103 14.5192 15.167 14.2198 14.8923C13.9217 14.6184 13.4827 14.4798 12.9152 14.4798C12.7023 14.4798 12.3596 14.5107 12.1544 14.5539C12.1544 14.5539 11.5339 14.6799 11.2784 14.8894C11.2784 14.8894 11.2226 14.9261 11.2533 15.0081L11.4543 15.5752C11.4794 15.6484 11.5472 15.6235 11.5472 15.6235C11.5472 15.6235 11.5688 15.6147 11.594 15.5993C12.1405 15.2872 12.8315 15.2968 12.8315 15.2968C13.1387 15.2968 13.3746 15.3612 13.5337 15.4894C13.6886 15.6139 13.7675 15.8022 13.7675 16.1993V16.3253C13.5232 16.2887 13.2991 16.2675 13.2991 16.2675ZM36.2065 14.7315C36.2281 14.6641 36.1827 14.6319 36.1638 14.6245C36.1157 14.6048 35.8742 14.5513 35.6878 14.5388C35.3312 14.5161 35.133 14.5791 34.9556 14.6626C34.7797 14.7461 34.5843 14.8809 34.4754 15.0341V14.6714C34.4754 14.6209 34.4412 14.5806 34.3937 14.5806H33.6657C33.6183 14.5806 33.5841 14.6208 33.5841 14.6714V19.1176C33.5841 19.1674 33.6232 19.2084 33.6706 19.2084H34.4168C34.4396 19.2083 34.4615 19.1987 34.4776 19.1816C34.4937 19.1646 34.5027 19.1416 34.5026 19.1176V16.8964C34.5026 16.5982 34.534 16.3008 34.5969 16.114C34.6583 15.9293 34.7421 15.7814 34.8454 15.6751C34.9493 15.5696 35.0673 15.4956 35.1965 15.4539C35.3283 15.4114 35.4742 15.3975 35.5776 15.3975C35.7262 15.3975 35.8896 15.4377 35.8896 15.4377C35.944 15.4443 35.9747 15.4092 35.9928 15.3572C36.0417 15.2209 36.1799 14.8128 36.2065 14.7315Z"
              fill="white"
            />
            <path
              d="M29.2026 12.6709C29.1118 12.6416 29.0295 12.6218 28.9219 12.6005C28.813 12.5801 28.6832 12.5698 28.536 12.5698C28.0222 12.5698 27.6173 12.7221 27.3333 13.0225C27.0506 13.3214 26.8586 13.7764 26.7623 14.3749L26.7273 14.5764H26.0825C26.0825 14.5764 26.0043 14.5734 25.9875 14.6628L25.8821 15.2833C25.8744 15.342 25.8989 15.3793 25.9742 15.3793H26.6018L25.9652 19.1097C25.9156 19.4101 25.8583 19.657 25.7948 19.8445C25.7327 20.0291 25.672 20.1675 25.5966 20.2687C25.524 20.3654 25.4556 20.4371 25.3369 20.4789C25.2392 20.5133 25.1261 20.5295 25.0026 20.5295C24.9342 20.5295 24.8428 20.5177 24.775 20.503C24.708 20.4891 24.6724 20.4738 24.6215 20.451C24.6215 20.451 24.5482 20.4218 24.5189 20.4987C24.4958 20.5624 24.3283 21.0451 24.3081 21.1045C24.2885 21.1638 24.3164 21.21 24.352 21.2239C24.4358 21.2547 24.4979 21.2752 24.6117 21.3038C24.7695 21.3426 24.9027 21.3448 25.0276 21.3448C25.2887 21.3448 25.5274 21.306 25.725 21.2313C25.9232 21.1558 26.0963 21.0246 26.2499 20.8474C26.4153 20.6554 26.5193 20.4547 26.6185 20.18C26.7168 19.9089 26.8014 19.5719 26.8683 19.1793L27.5084 15.3793H28.4437C28.4437 15.3793 28.5225 15.3822 28.5387 15.2921L28.6447 14.6724C28.6517 14.6131 28.628 14.5764 28.5519 14.5764H27.6438C27.6487 14.5552 27.6898 14.2196 27.7939 13.9039C27.8386 13.7699 27.9223 13.6607 27.9928 13.5859C28.0627 13.5127 28.1429 13.4607 28.2309 13.4306C28.3209 13.3998 28.4236 13.3852 28.5359 13.3852C28.6211 13.3852 28.7055 13.3955 28.769 13.4094C28.857 13.4292 28.8911 13.4394 28.9142 13.4468C29.0071 13.4761 29.0196 13.4475 29.0378 13.4007L29.2549 12.775C29.2772 12.7075 29.2221 12.6789 29.2026 12.6709ZM16.5145 19.1178C16.5145 19.1676 16.4804 19.2079 16.4329 19.2079H15.6797C15.6323 19.2079 15.5988 19.1676 15.5988 19.1178V12.7559C15.5988 12.7061 15.6323 12.6658 15.6797 12.6658H16.4329C16.4804 12.6658 16.5145 12.7061 16.5145 12.7559V19.1178Z"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_3_12">
              <rect width="49" height="36" fill="white" />
            </clipPath>
          </defs>
        </svg>;
      case 'hubspot':
        return <svg
          className="w-[40px] h-[40px] lg:w-[49px] lg:h-[50px]"
          viewBox="0 0 49 50"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_3_2)">
            <path
              d="M24.5 50C38.031 50 49 38.8071 49 25C49 11.1929 38.031 0 24.5 0C10.969 0 0 11.1929 0 25C0 38.8071 10.969 50 24.5 50Z"
              fill="#FF7A59"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M29.8498 30.4044C28.0204 30.4044 26.5366 28.9368 26.5366 27.1261C26.5366 25.3154 28.02 23.8473 29.8498 23.8473C31.6796 23.8473 33.164 25.3149 33.164 27.1261C33.164 28.9372 31.6806 30.4044 29.8498 30.4044ZM30.8422 20.814V17.895C31.2303 17.7173 31.5596 17.4324 31.7913 17.0739C32.0229 16.7154 32.1474 16.2982 32.15 15.8714V15.8039C32.15 14.5703 31.1298 13.5616 29.8843 13.5616H29.8158C28.5717 13.5616 27.5501 14.5708 27.5501 15.8039V15.8714C27.5528 16.2982 27.6773 16.7153 27.909 17.0738C28.1406 17.4323 28.4698 17.7172 28.8578 17.895V20.814C27.7405 20.9825 26.6869 21.441 25.802 22.1437L17.7103 15.9149C17.767 15.71 17.7974 15.4987 17.8008 15.2861C17.8008 13.8884 16.6576 12.7538 15.2455 12.75C13.8334 12.7462 12.6878 13.8788 12.6864 15.2771C12.685 16.6753 13.8277 17.8074 15.2398 17.8098C15.6821 17.8079 16.1162 17.691 16.4997 17.4706L24.4612 23.6004C23.745 24.6672 23.3708 25.927 23.3885 27.2119C23.4062 28.4967 23.8149 29.7457 24.5603 30.7924L22.138 33.1898C21.8515 33.1 21.5488 33.0733 21.251 33.1117C20.9531 33.1501 20.6671 33.2527 20.4127 33.4122C20.1582 33.5718 19.9414 33.7846 19.7772 34.0361C19.6129 34.2875 19.5051 34.5715 19.4612 34.8686C19.4173 35.1657 19.4383 35.4688 19.5228 35.757C19.6073 36.0452 19.7533 36.3117 19.9507 36.538C20.1481 36.7644 20.3922 36.9452 20.6662 37.0681C20.9403 37.1911 21.2377 37.2531 21.538 37.25C22.6979 37.25 23.6387 36.3188 23.6387 35.1709C23.6366 34.9691 23.6044 34.7688 23.543 34.5765L25.9355 32.2069C27.0618 33.0606 28.4365 33.5223 29.8498 33.5214C33.42 33.5214 36.3126 30.6585 36.3126 27.1261C36.3126 23.9286 33.9382 21.2882 30.8418 20.814"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_3_2">
              <rect width="49" height="50" fill="white" />
            </clipPath>
          </defs>
        </svg>;
      case 'pipedrive':
        return <svg
          className="w-[40px] h-[40px] lg:w-[49px] lg:h-[50px]"
          viewBox="0 0 49 50"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_3_2)">
            <path
              d="M24.5 50C38.031 50 49 38.8071 49 25C49 11.1929 38.031 0 24.5 0C10.969 0 0 11.1929 0 25C0 38.8071 10.969 50 24.5 50Z"
              fill="#027737"
            />
            <path
              d="M21.4253 21.1943C21.4253 23.7508 22.7222 26.5087 25.5762 26.5087C27.6927 26.5087 29.8326 24.8563 29.8326 21.1568C29.8326 17.9134 28.1504 15.7359 25.6466 15.7359C23.6064 15.7359 21.4253 17.1691 21.4253 21.1943ZM26.652 11C31.7692 11 35.2099 15.0529 35.2099 21.0858C35.2099 27.0239 31.5853 31.1696 26.4036 31.1696C23.933 31.1696 22.3506 30.1114 21.5231 29.3455C21.529 29.5271 21.5329 29.7304 21.5329 29.9476V38H16.2319V16.5453C16.2319 16.2334 16.1321 16.1347 15.8231 16.1347H14V11.4363H18.4482C20.4962 11.4363 21.0204 12.4786 21.1202 13.2821C21.9515 12.3503 23.6748 11 26.652 11Z"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_3_2">
              <rect width="49" height="50" fill="white" />
            </clipPath>
          </defs>
        </svg>
          ;
      default:
        return <></>;
    }
  };

  // const isEnable = useFeatures(FEATURE_PERMISSION_KEY.SCD_VIEW_YOUR_PAGE)?.is_enabled;

  return (
    <Box variant="col-start" className="gap-3 lg:gap-6 w-full">
      <div className="flex justify-between text-sm lg:text-[20px] text-primary font-semibold w-full">
        Choose CRM platform to add your contact
        <RiCloseLine
          className="text-secondary cursor-pointer hover:text-tertiary"
          size={21}
          color={'#20232C'}
          onClick={() => handleCloseModal(context)}
        />
      </div>
      <Box variant="col-start" className="gap-3 w-full h-[500px] lg:h-auto overflow-auto">
        {PlatFormArr.map((item) => (
          <PlatForm
            key={item.type}
            id={id}
            data={item.data}
            type={item.type}
            count={count}
            typeAdd={typeAdd}
            segmentName={segmentName}
            icon={handleRenderIcon(item.type)}
          />
        ))}
      </Box>
    </Box>
  );
};
export default AddYourDataModal;
