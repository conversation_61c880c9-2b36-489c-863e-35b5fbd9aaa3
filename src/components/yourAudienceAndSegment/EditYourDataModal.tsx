import { memo, useContext } from "react";
import { RiLoader2Fill } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import InputController from "components/InputController";
import TextAreaController from "components/TextAreaController";

import LABEL from "constants/label";
import handleCloseModal from "utils/handleCloseModal";

type Props = {
  control: any;
  onSubmit: () => void;
  title: string;
  inputLabel: string;
  textareaLabel: string;
  error: string;
  loading: boolean;
};

const EditYourDataModal = ({
  onSubmit,
  control,
  title,
  textareaLabel,
  loading,
  inputLabel,
  error,
}: Props) => {
  const modal = useContext(ModalContext);
  return (
    <form
      onSubmit={() => {
        onSubmit();
        handleCloseModal(modal);
      }}
    >
      <div className="text-lg font-semibold leading-none tracking-tight">{title}</div>
      <div className="space-y-5 mt-5 text-primary font-medium">
        <InputController
          className="gap-2 text-sm"
          control={control}
          name="name"
          label={inputLabel}
          placeholder="Enter segment name"
          required
          error={error}
        />
        <TextAreaController control={control} label={textareaLabel} name={"description"} />
      </div>
      <Box className="gap-3 mt-4">
        <Button variant={"main"} type="submit" disabled={loading} className="flex-1">
          {loading ? <RiLoader2Fill className="animate-spin" size={14} /> : LABEL.edit}
        </Button>
        <Button
          onClick={() => handleCloseModal(modal)}
          variant={"secondary"}
          className="text-sm text-secondary"
        >
          {LABEL.cancel}
        </Button>
      </Box>
    </form>
  );
};
export default memo(EditYourDataModal);
