import { useState } from 'react';
import { RiAddLine, RiDeleteBin7Line, RiDownload2Line, RiEditLine, RiMore2Line } from '@remixicon/react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";
import LABEL from "constants/label";
import { Button } from '../ui/button';
import { Box } from '../Box';

type Props = {
  isDownload?: boolean;
  isEdit?: boolean;
  isAddContact?: boolean;
  isDelete?: boolean;
  handleDownload: () => void;
  handleEdit?: () => void;
  handleAdd?: () => void;
  handleDelete: () => void;
};

const ActionColumn = ({
  isDownload = true,
  isDelete = true,
  isEdit,
  isAddContact = false,
  handleDownload,
  handleEdit,
  handleAdd,
  handleDelete
}: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleClick = (callBack: () => void) => {
    callBack();
    setIsOpen(false);
  };
  return (
    <Box className='gap-2'>
      {isAddContact && handleAdd ? (
        <>
          <Button
            variant={'secondary'}
            className="flex items-center bg-transparent border-none gap-2 font-semibold text-primary hover:text-secondary hover:bg-[#F0F0F0]"
            onClick={() => handleClick(handleAdd)}
          >
            <RiAddLine size={14} />
            Add To ...
          </Button>
        </>
      ) : <div className="h-10" />}
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger>{<RiMore2Line />}</DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="min-w-30 rounded-sm text-right">
          {isDownload && (
            <>
              <DropdownMenuItem
                onClick={() => handleClick(handleDownload)}
                className="flex items-center gap-2 text-secondary hover:text-primary"
              >
                <RiDownload2Line size={14} />
                {LABEL.download}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </>
          )}

          {isEdit && handleEdit && (
            <>
              <DropdownMenuItem
                onClick={() => handleClick(handleEdit)}
                className="flex items-center gap-2 text-secondary hover:text-primary"
              >
                <RiEditLine size={14} />
                {LABEL.edit}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </>
          )}

          {isDelete && (
            <DropdownMenuItem
              onClick={() => handleClick(handleDelete)}
              className=" cursor-pointer flex items-center gap-2 text-secondary hover:!bg-[#FFD9D9] hover:!text-[#F53E3E] !text-red-400"
            >
              <RiDeleteBin7Line size={14} />
              {LABEL.delete}
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </Box> );
};
export default ActionColumn;
