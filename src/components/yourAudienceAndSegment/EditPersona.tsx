import { useState } from "react";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

import EditYourDataModal from "components/yourAudienceAndSegment/EditYourDataModal";
import { CreateSegmentFormBody, CreateSegmentFormType } from "types/YourData";

type Props = {
  name: string;
  description: string;
  id: string;
  handleEdit: (id: string, payload: CreateSegmentFormType) => void;
};

const EditPersona = ({ name, description, id, handleEdit }: Props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<CreateSegmentFormType>({
    resolver: yupResolver(CreateSegmentFormBody),
    defaultValues: {
      name: name || "",
      description: description || "",
    },
  });
  const onSubmit: SubmitHandler<CreateSegmentFormType> = async (data) => {
    setLoading(true);
    await handleEdit(id, data);
    setLoading(false);
  };
  return (
    <EditYourDataModal
      control={control}
      onSubmit={handleSubmit(onSubmit)}
      title={"Edit Persona Audience"}
      inputLabel={"Segment Name"}
      textareaLabel={"Description"}
      error={errors.name?.message || ""}
      loading={loading}
    />
  );
};
export default EditPersona;
