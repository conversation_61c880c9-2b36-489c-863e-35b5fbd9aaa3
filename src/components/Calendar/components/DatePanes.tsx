import React, { useEffect, useState } from "react";

import SelectedDate from "./SelectedDate";
import { getDateList } from "utils/calender";

type DatePaneProps = {
  selectedDate: string | number | null;
  selectedMonth: string | number | null;
  selectedYear: string | number | null;
  onDateChange: (value: string) => void;
  onMonthChange: (value: string) => void;
  onYearChange: (value: string) => void;
  title: string;
};

const DatePanes: React.FC<DatePaneProps> = ({
  selectedDate,
  selectedMonth,
  selectedYear,
  onDateChange,
  onMonthChange,
  onYearChange,
  title,
}) => {
  const [daysOptions, setDaysOptions] = useState<{ label: string; value: string }[]>(
    getDateList.getDaysInMonth(getDateList.thisMonth, getDateList.thisYear)
  );
  useEffect(() => {
    if (selectedMonth && selectedYear)
      setDaysOptions(getDateList.getDaysInMonth(Number(selectedMonth), Number(selectedYear)));
  }, [selectedMonth, selectedYear]);

  return (
    <>
      <div className="text-sm font-medium text-secondary">{title}:</div>
      <div className="grid grid-cols-3 gap-2 mt-1.5">
        <SelectedDate
          value={selectedDate?.toString() || ""}
          onChange={onDateChange}
          placeholder={"Date"}
          options={daysOptions}
        />

        <SelectedDate
          onChange={onMonthChange}
          options={getDateList.getMonths()}
          placeholder={selectedMonth?.toString() || "Month"}
          value={selectedMonth?.toString() || ""}
        />
        <SelectedDate
          onChange={onYearChange}
          placeholder={selectedYear?.toString() || "Year"}
          value={selectedYear?.toString() || ""}
          options={getDateList.getYears()}
        />
      </div>
    </>
  );
};

export default DatePanes;
