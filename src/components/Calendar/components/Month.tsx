import { useMemo } from "react";

import MultilSelect from "components/MSelect";
import { FILTER_LABEL } from "constants/yourData/label";
import { getDateList } from "utils/calender";
import { TSelectedDate } from "utils/yourAudience/Calender/getLabelValue";
import { keyDOBFilter } from "types/yourAudience";

type Props = {
  keyFilter: keyDOBFilter;
  value: TSelectedDate;
  onChange: (payload: TSelectedDate) => void;
};

const Month = ({ keyFilter, value, onChange }: Props) => {
  const monthOptions = useMemo(() => getDateList.getMonths(), []);

  const handleMonthChange = (selectedValue: { label: string; value: string }[]) => {
    const updatedValue: TSelectedDate = {
      [keyFilter.month]: selectedValue,
      [keyFilter.year]: [],
      [keyFilter.from]: [],
      [keyFilter.to]: [],
    };
    onChange(updatedValue);
  };

  return (
    <>
      <div className="mt-6 text-secondary text-xs font-medium">Choose Month:</div>
      <MultilSelect
        itemShow={4}
        className="!w-full mt-2"
        options={monthOptions}
        isMulti
        onChange={(selectedOptions: { label: string; value: string }[]) =>
          handleMonthChange(selectedOptions)
        }
        defaultValue={value[keyFilter.month] ?? []}
        placeholder={FILTER_LABEL.AGE_MONTH}
      />
    </>
  );
};

export default Month;
