import { RiArrowDownSLine } from "@remixicon/react";
import { useRef, useState } from "react";
import { cn } from "utils/utils";
import useOutsideClick from "hooks/useClickOutSide";

type Props = {
  value: string;
  onChange: (input: string) => void;
  placeholder: string;
  options: { label: string; value: string | number }[];
};

const SelectedDate: React.FC<Props> = ({ value, onChange, placeholder, options }) => {
  const [showOptions, setShowOptions] = useState(false);
  const buttonRef = useRef<any>(null);
  const optionRef = useRef<HTMLDivElement>(null);

  const handleOptionClick = (selectedValue: string) => {
    onChange(selectedValue);
    setShowOptions(false);
  };

  useOutsideClick(buttonRef, optionRef, () => {
    setShowOptions(false);
  });
  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={() => setShowOptions(!showOptions)}
        className="border text-sm border-[#DEE0E3] flex items-center justify-between w-full py-1.5 px-2 rounded-lg shadow-xs"
      >
        {value || placeholder}
        <RiArrowDownSLine size={14} color="#0D112666" />
      </button>
      {showOptions && (
        <div
          ref={optionRef}
          className="absolute border shadow-lg rounded-md p-0.5 border-[#DEE0E3] bg-white z-[9999] mt-0.5 top-full w-full transition-opacity duration-300 ease-out transform opacity-100"
        >
          <div className="h-[200px] p-1 text-sm overflow-x-hidden flex flex-col">
            {options.map((item) => (
              <button
                className={cn(
                  "py-1.5 px-2 text-left rounded transition-colors duration-150 ease-in",
                  String(value) === String(item.value)
                    ? "bg-[#ecdffb] text-[#5314a3]"
                    : "hover:bg-[#0A0F2914]"
                )}
                onClick={() => handleOptionClick(String(item.value))}
                key={item.value}
              >
                {item.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectedDate;
