import { useMemo } from "react";

import MultilSelect from "components/MSelect";

import { getDateList } from "utils/calender";
import { TSelectedDate } from "utils/yourAudience/Calender/getLabelValue";
import { FILTER_LABEL } from "constants/yourData/label";
import { keyDOBFilter } from "types/yourAudience";

type Props = {
  keyFilter: keyDOBFilter;
  value: TSelectedDate;
  onChange: (payload: TSelectedDate) => void;
};

const Year = ({ keyFilter, value, onChange }: Props) => {
  const yearOptions = useMemo(() => getDateList.getYears(), []);

  const handleYearChange = (selectedValue: { label: string; value: string }[]) => {
    const updatedValue: TSelectedDate = {
      [keyFilter.year]: selectedValue,
      [keyFilter.month]: [],
      [keyFilter.from]: [],
      [keyFilter.to]: [],
    };
    onChange(updatedValue);
  };

  return (
    <>
      <div className="mt-6 text-secondary text-xs font-medium">Choose Year:</div>
      <MultilSelect
        itemShow={4}
        className="!w-full mt-2"
        options={yearOptions}
        isMulti
        onChange={(selectedOptions: { label: string; value: string }[]) =>
          handleYearChange(selectedOptions)
        }
        defaultValue={value[keyFilter.year] ?? []}
        placeholder={FILTER_LABEL.AGE_YEAR}
      />
    </>
  );
};

export default Year;
