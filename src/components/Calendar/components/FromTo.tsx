import { FILTER_KEY, FILTER_LABEL } from "constants/yourData/label";
import DatePanes from "./DatePanes";
import { TSelectedDate } from "utils/yourAudience/Calender/getLabelValue";

type Props = {
  value: TSelectedDate;
  onChange: (payload: TSelectedDate) => void;
};

const FromTo = ({ value, onChange }: Props) => {
  const handleChangeValue = (key: string, payload: { value: string; label: string }) => {
    const updatedFilters = {
      ...value,
      [key]: [...(value[key] || []).filter((item) => item.label !== payload.label), payload],
      [FILTER_KEY.AGE_YEAR]: [],
      [FILTER_KEY.AGE_MONTH]: [],
    };

    onChange(updatedFilters);
  };

  const renderDatePanes = (filterKey: string, filterLabel: string) => (
    <DatePanes
      selectedDate={value[filterKey]?.find((item) => item.label === "date")?.value ?? ""}
      selectedMonth={value[filterKey]?.find((item) => item.label === "month")?.value ?? ""}
      selectedYear={value[filterKey]?.find((item) => item.label === "year")?.value ?? ""}
      onDateChange={(dateValue) =>
        handleChangeValue(filterKey, { label: "date", value: dateValue })
      }
      onMonthChange={(monthValue) =>
        handleChangeValue(filterKey, { label: "month", value: monthValue })
      }
      onYearChange={(yearValue) =>
        handleChangeValue(filterKey, { label: "year", value: yearValue })
      }
      title={filterLabel}
    />
  );

  return (
    <>
      {renderDatePanes(FILTER_KEY.DOB_GTE, FILTER_LABEL.DOB_GTE)}
      <div className="h-4" />
      {renderDatePanes(FILTER_KEY.DOB_LTE, FILTER_LABEL.DOB_LTE)}
    </>
  );
};

export default FromTo;
