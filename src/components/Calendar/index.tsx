import { useEffect, useState } from "react";
import { RiCalendar2Line, RiCloseFill } from "@remixicon/react";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import FromTo from "./components/FromTo";
import Month from "./components/Month";
import Year from "./components/Year";

import { keyDOBFilter } from "types/yourAudience";
import { cn } from "utils/utils";
import optionsDate, { TSelectedDate } from "utils/yourAudience/Calender/getLabelValue";
import getValueFilter from "utils/yourAudience/Calender/getFilterValue";
import { DATE_OPTIONS, OptionsDatePicker } from "constants/yourData/label.ts";
import { TSelectedOption } from "types/Select";

interface Props {
  keyFilter: keyDOBFilter;
  defaultValue: any;
  onChange?: (value: any) => void;
  isDisabled: boolean;
  className?: string;
  selectedOption: TSelectedOption;
}
const Calendar = ({
  isDisabled = false,
  keyFilter,
  defaultValue,
  className,
  selectedOption,
  onChange,
}: Props) => {
  const [option, setOption] = useState<DATE_OPTIONS>(DATE_OPTIONS.FROM_TO);
  const [selectedDate, setSelectedDate] = useState<TSelectedDate>(defaultValue);

  useEffect(() => {
    if (onChange && Object.keys(selectedDate).length > 0) {
      onChange(getValueFilter({ keyFilter, option, obj: selectedDate }));
    }
  }, [selectedDate]);

  useEffect(() => {
    if (Object.keys(selectedOption).length == 0) {
      setSelectedDate({});
    }
  }, [selectedOption]);

  const handleClear = () => {
    setSelectedDate({});
    onChange &&
      onChange({
        [keyFilter.from]: undefined,
        [keyFilter.to]: undefined,
        [keyFilter.month]: undefined,
        [keyFilter.year]: undefined,
      });
  };
  //handle format label value
  const valueSelected = optionsDate({ objDate: selectedDate, keyFilter }).find(
    (option) => option.label.trim() !== ""
  );

  return (
    <Popover>
      <PopoverTrigger asChild className="justify-start">
        <Button
          variant="secondary"
          disabled={isDisabled}
          className={cn(
            "border relative min-w-[148px] w-[220px] text-[#82868b] text-xs disabled:opacity-100",
            isDisabled && "bg-[#0A0F290A] pointer-events-none cursor-default",
            className
          )}
        >
          {valueSelected && (
            <Box className="gap-1 p-1 rounded-sm bg-[#ecdffb] text-[#5314a3] text-left">
              <span
                className="max-w-[150px] text-ellipsis overflow-hidden"
                children={valueSelected?.label}
              />
              <RiCloseFill
                color="#14151A"
                size={16}
                opacity={0.6}
                className={cn("hover:fill-[#82868b]", isDisabled && "hidden")}
                onClick={handleClear}
              />
            </Box>
          )}
          <div
            className={cn(
              "flex text-sm items-center left-2 gap-2 transform  duration-300 text-[#82868b] font-normal",
              valueSelected ? "absolute top-[-5px] -translate-y-1/2 bg-white text-xs" : "top-1/2"
            )}
          >
            <RiCalendar2Line size={18} />
            <span className="line-clamp-1" children="Date Of Birth" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="min-w-[400px] border z-[999] bg-white rounded-md p-3">
        <div className="grid grid-cols-3 gap-1.5 mb-5">
          {OptionsDatePicker.map((item) => (
            <Button
              key={'calender' + item.value}
              variant={option === item.value ? "main" : "tertiary"}
              onClick={() => setOption(item.value)}
              className={cn(
                "py-1 rounded-md text-sm font-medium shadow-xs",
                option != item.value && "bg-[#0A0F2914] text-secondary"
              )}
              children={item.value}
            />
          ))}
        </div>
        <CalendarContent
          keyFilter={keyFilter}
          type={option}
          value={selectedDate || {}}
          onChange={(payload) => setSelectedDate(payload)}
        />
      </PopoverContent>
    </Popover>
  );
};
export default Calendar;

type IContext = {
  keyFilter: keyDOBFilter;
  type: DATE_OPTIONS;
  value: TSelectedDate;
  onChange: (payload: TSelectedDate) => void;
};

const CalendarContent = ({ keyFilter, type, value, onChange }: IContext) => {
  const map = {
    [DATE_OPTIONS.FROM_TO]: <FromTo value={value} onChange={onChange} />,
    [DATE_OPTIONS.MONTH]: <Month keyFilter={keyFilter} value={value} onChange={onChange} />,
    [DATE_OPTIONS.YEAR]: <Year keyFilter={keyFilter} value={value} onChange={onChange} />,
  };
  return map[type] || null;
};
