import { RiArrowLeftSLine, RiArrowRightSLine } from "@remixicon/react";
import { Button } from "components/ui/button";

type Props = {
  onPrevClick?: () => void;
  onNextClick?: () => void;
  showLeftButton: boolean;
  showRightButton: boolean;
};

const ActionTabBtn = ({ onNextClick, onPrevClick, showLeftButton, showRightButton }: Props) => {
  return (
    <>
      {showLeftButton && (
        <Button
          onClick={onPrevClick}
          variant="default"
          size="icon"
          className="z-[1] rounded-md w-8 h-8 absolute -left-1 top-0 !bg-white shadow-md p-0"
        >
          <RiArrowLeftSLine color="#0F1324" size={20} opacity={0.6} />
        </Button>
      )}
      {showRightButton && (
        <Button
          onClick={onNextClick}
          variant="default"
          size="icon"
          className="z-[1] rounded-md w-8 h-8 absolute -right-1 top-0 !bg-white shadow-md p-0"
        >
          <RiArrowRightSLine color="#0F1324" size={20} opacity={0.6} />
        </Button>
      )}
    </>
  );
};

export default ActionTabBtn;
