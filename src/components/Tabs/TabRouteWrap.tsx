import React, { useEffect, useMemo, useRef, useState } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import map from "lodash/map";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "components/ui/tabs";
import { optionIconFilter } from "constants/persona";
import { ComponentType } from "types/YourData";
import { cn } from "utils/utils";
import { Badge } from "components/ui/badge";
import { useAppSelector } from "../../store";
import { TNotificationType } from "../../types/notification";
import ActionTabBtn from "./ActionTabBtn";

export interface ITab {
  title: string;
  path: string;
  tab_icon?: ComponentType;
  content?: React.ReactNode | JSX.Element;
  totalItems?: number;
  type?: string;
  classContainer?: string;
}
interface Props {
  tabs: ITab[];
  children?: React.ReactNode | JSX.Element;
  className?: string;
  tabListClass?: string;
  tabTriggerClass?: string;
  tabContentClass?: string;
  classContainer?: string;
}

const TabRouteWrap = ({
  children,
  tabs,
  className,
  tabListClass,
  tabTriggerClass,
  tabContentClass,
  classContainer,
}: Props) => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [activeTab, setActiveTab] = useState<string>(tabs[0].title);
  const { countNoti } = useAppSelector((state) => state.notification);
  const tabsListRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});
  const scrollAmount = 200;

  // Add state to track scroll position
  const [isAtLeftEdge, setIsAtLeftEdge] = useState(true)
  const [isAtRightEdge, setIsAtRightEdge] = useState(false)


  useMemo(() => {
    map(tabs, ({ path, title }) => {
      pathname === path && setActiveTab(title);
    });
  }, [pathname, tabs]);

  useEffect(() => {
    const activeTabElement = tabRefs.current[activeTab];
    if (activeTabElement && tabsListRef.current) {
      activeTabElement.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "center",
      });
    }
  }, [activeTab]);

  const checkScrollPosition = () => {
    if (tabsListRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = tabsListRef.current

      setIsAtLeftEdge(scrollLeft <= 0)

      setIsAtRightEdge(scrollLeft + clientWidth >= scrollWidth - 1)
    }
  }

  useEffect(() => {
    const tabsList = tabsListRef.current
    if (tabsList) {
      checkScrollPosition()

      tabsList.addEventListener("scroll", checkScrollPosition)

      window.addEventListener("resize", checkScrollPosition)

      return () => {
        tabsList.removeEventListener("scroll", checkScrollPosition)
        window.removeEventListener("resize", checkScrollPosition)
      }
    }
  }, [pathname])

  const onPrevClick = () => {
    if (tabsListRef.current) {
      tabsListRef.current.scrollBy({
        left: -scrollAmount,
        behavior: "smooth",
      });
    }
  };

  const onNextClick = () => {
    if (tabsListRef.current) {
      tabsListRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  };

  return (
    <div className={cn("relative w-full", classContainer)}>
      <ActionTabBtn
        onNextClick={onNextClick}
        onPrevClick={onPrevClick}
        showLeftButton={!isAtLeftEdge}
        showRightButton={!isAtRightEdge}
      />
      <Tabs
        className={cn("mt-2 box-content flex flex-col gap-0 relative", className)}
        defaultValue={activeTab}
        value={activeTab}
        key={activeTab}
      >
        <TabsList
          ref={tabsListRef}
          className={cn(
            "justify-start flex-row w-full pt-0 rounded-none border-b border-b-custom-primary overflow-auto scrollbar-hidden",
            tabListClass
          )}
        >
          {tabs.map((tab: ITab) => (
            <TabsTrigger
              ref={(el) => (tabRefs.current[tab.title] = el)}
              className={cn(
                "text-md p-2 md:py-4 md:pr-10 text-tertiary leading-5 box-border",
                tabTriggerClass
              )}
              value={tab.title}
              onClick={() => navigate(tab.path)}
              key={tab.title}
            >
              {tab.tab_icon && (
                <div className="min-w-[16px]">
                  {typeof tab.tab_icon === "function" ? (
                    <tab.tab_icon
                      size={optionIconFilter.size}
                      color={activeTab === tab.title ? optionIconFilter.color_active : optionIconFilter.color}
                    />
                  ) : null}
                </div>
              )}
              <span className="whitespace-nowrap relative">
                {tab.title}
                {tab.type && countNoti && countNoti[tab.type as keyof TNotificationType] ? (
                  <span className="absolute top-0 -right-10 py-0.5 px-1 rounded-sm bg-[#ecdffb] text-[#5A18BF]">
                    +{countNoti[tab.type as keyof TNotificationType]}
                  </span>
                ) : null}
              </span>
              {tab.totalItems && tab.totalItems > 0 ? (
                <Badge className="rounded-md bg-brand-light hover:bg-brand-light text-[#924FE8] px-1 py-0.5 h-5 w-8 justify-center">
                  {tab.totalItems}
                </Badge>
              ) : (
                ""
              )}
            </TabsTrigger>
          ))}
        </TabsList>
        {tabs.map(
          (tab: ITab) =>
            activeTab === tab.title && (
              <TabsContent
                className={cn("w-[-webkit-fill-available] z-1 mt-3 flex-1", tabContentClass)}
                value={tab.title}
                key={tab.title}
              >
                {children ? children : <Outlet />}
              </TabsContent>
            )
        )}
      </Tabs>
    </div>
  );
};

export default TabRouteWrap;
