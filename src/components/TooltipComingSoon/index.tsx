import { Toolt<PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { TooltipPortal } from "@radix-ui/react-tooltip";
interface Props {
  children?: React.ReactNode;
  trigger: React.ReactNode;
}

export const TooltipComingSoon = (props: Props) => {
  const { trigger }= props
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          {trigger}
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent className={'z-50'}>
            <p>Coming soon</p>
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>

  );
};
