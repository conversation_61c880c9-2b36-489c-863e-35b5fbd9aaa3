import React, { useState, useEffect } from 'react';
import { remoteCacheManager } from '../utils/remoteCacheManager';

interface RemoteDebuggerProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

interface CacheStatus {
  supported: boolean;
  serviceWorkerRegistered: boolean;
  caches?: Array<{
    name: string;
    entries: number;
    urls: string[];
  }>;
  error?: string;
}

const RemoteDebugger: React.FC<RemoteDebuggerProps> = ({ 
  isVisible = false, 
  onToggle 
}) => {
  const [cacheStatus, setCacheStatus] = useState<CacheStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [remoteHealth, setRemoteHealth] = useState<Record<string, boolean>>({});

  const CRM360_URL = process.env.VITE_CRM360_URL || '';

  useEffect(() => {
    if (isVisible) {
      refreshStatus();
    }
  }, [isVisible]);

  const refreshStatus = async () => {
    setIsLoading(true);
    try {
      const status = await remoteCacheManager.getCacheStatus();
      setCacheStatus(status);

      // Check remote health
      if (CRM360_URL) {
        const health = await remoteCacheManager.checkRemoteHealth(
          `${CRM360_URL}/assets/remoteEntry.js`
        );
        setRemoteHealth({ crm360: health });
      }
    } catch (error) {
      console.error('Failed to get cache status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearCache = async () => {
    setIsLoading(true);
    try {
      await remoteCacheManager.clearRemoteCache();
      await refreshStatus();
    } catch (error) {
      console.error('Failed to clear cache:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const forceRefreshRemote = async () => {
    if (!CRM360_URL) return;
    
    setIsLoading(true);
    try {
      await remoteCacheManager.forceRefreshRemote(`${CRM360_URL}/assets/remoteEntry.js`);
      await refreshStatus();
    } catch (error) {
      console.error('Failed to force refresh remote:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-blue-500 text-white p-2 rounded-full shadow-lg z-50"
        title="Open Remote Debugger"
      >
        🔧
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Remote Debugger</h3>
        <button
          onClick={onToggle}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      <div className="space-y-4">
        {/* Status */}
        <div>
          <h4 className="font-medium mb-2">Status</h4>
          {isLoading ? (
            <p className="text-gray-500">Loading...</p>
          ) : cacheStatus ? (
            <div className="text-sm space-y-1">
              <p>Cache API: {cacheStatus.supported ? '✅' : '❌'}</p>
              <p>Service Worker: {cacheStatus.serviceWorkerRegistered ? '✅' : '❌'}</p>
              {cacheStatus.error && (
                <p className="text-red-500">Error: {cacheStatus.error}</p>
              )}
            </div>
          ) : (
            <p className="text-gray-500">No status available</p>
          )}
        </div>

        {/* Remote Health */}
        <div>
          <h4 className="font-medium mb-2">Remote Health</h4>
          <div className="text-sm space-y-1">
            {Object.entries(remoteHealth).map(([name, healthy]) => (
              <p key={name}>
                {name}: {healthy ? '✅' : '❌'}
              </p>
            ))}
            {CRM360_URL && (
              <p className="text-xs text-gray-500 break-all">
                URL: {CRM360_URL}
              </p>
            )}
          </div>
        </div>

        {/* Cache Info */}
        {cacheStatus?.caches && (
          <div>
            <h4 className="font-medium mb-2">Cache Info</h4>
            <div className="text-sm space-y-2">
              {cacheStatus.caches.map((cache) => (
                <div key={cache.name} className="border border-gray-200 rounded p-2">
                  <p className="font-medium">{cache.name}</p>
                  <p>Entries: {cache.entries}</p>
                  {cache.urls.length > 0 && (
                    <details className="mt-1">
                      <summary className="cursor-pointer text-blue-500">
                        Show URLs ({cache.urls.length})
                      </summary>
                      <div className="mt-1 max-h-32 overflow-y-auto">
                        {cache.urls.map((url, index) => (
                          <p key={index} className="text-xs text-gray-600 break-all">
                            {url}
                          </p>
                        ))}
                      </div>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="space-y-2">
          <button
            onClick={refreshStatus}
            disabled={isLoading}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Refresh Status
          </button>
          <button
            onClick={clearCache}
            disabled={isLoading}
            className="w-full bg-yellow-500 text-white py-2 px-4 rounded hover:bg-yellow-600 disabled:opacity-50"
          >
            Clear Cache
          </button>
          <button
            onClick={forceRefreshRemote}
            disabled={isLoading || !CRM360_URL}
            className="w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600 disabled:opacity-50"
          >
            Force Refresh Remote
          </button>
        </div>

        {/* Environment Info */}
        <div className="text-xs text-gray-500 border-t pt-2">
          <p>Environment: {process.env.NODE_ENV}</p>
          <p>Build: {import.meta.env.REACT_APP_DEPLOY_TAG || 'unknown'}</p>
        </div>
      </div>
    </div>
  );
};

export default RemoteDebugger;
