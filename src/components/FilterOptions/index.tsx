import Calendar from "components/Calendar";
import MultiSelect from "components/MSelect";
import AgeRangeFilter from "components/AgeRangeFilter";
import DateRangePicker from "components/DateRanger/DateRangerPicker";
import InputFilter from "views/SocialDataDetail/components/InputFilter";

import { TSelectOption } from "types/Select";
import InterestOptions from '../../views/YourAudience/components/InterestOptions';

export interface filterGroupItem {
  type: string;
  key: string;
  placeholder: string;
  options: TSelectOption[];
  selected: string;
  date_from?: undefined;
  date_to?: undefined;
  className?: string;

  onChange?: (value: any) => void;
}
interface Props {
  isDisable?: boolean;
  filterGroup: (filterGroupItem | any | null)[];
  selectedOption?: { [key: string]: string };
}

const FilterOptions = ({ isDisable = false, filterGroup, selectedOption }: Props) => {
  const handleSelectedDefault = (filter: filterGroupItem) => {
    let selectedDefault;

    /**
     * @paramsFilter
     * if isDisable = true -> getDefault(filter.selected) (segment)
     * if isDisable = false -> getDefault = selectedOption (audience)
     */
    if (filter?.key) {
      const paramsFilter = isDisable
        ? (filter.selected || "").split(",")
        : (selectedOption?.[filter?.key] || "").split(",");

      if (paramsFilter.length > 0) {
        selectedDefault = paramsFilter
          .map((value) => (filter.options || []).find((x) => x.value === value))
          .filter(Boolean);
      }
    }
    return selectedDefault || [];
  };

  return filterGroup.map((filter,index) => {
    switch (filter?.type) {
      case "select-multi":
        return (
          <MultiSelect
            key={'multi-select' + index}
            defaultValue={handleSelectedDefault(filter)}
            options={filter.options}
            placeholder={filter.placeholder}
            onChange={(value: TSelectOption[]) => filter.onChange(value)}
            className={filter.className}
            isTruncateLabel={filter.isTruncateLabel ? true : false}
            isMulti={filter.isSingle ? false : true}
            isDisabled={isDisable}
            isSearchable={filter.isSearchable ? true : false}
          />
        );
      case "age-range":
        return (
          <AgeRangeFilter
            key={'age-range' + index}
            defaultValue={filter.defaultValue}
            onChange={(value: any) => filter.onChange(value)}
            isDisabled={isDisable}
            selectedOption={filter.selectedOption}
          />
        );
      case "input-filter":
        return (
          <InputFilter
            key={'input-filter' + index}
            defaultValue={filter.defaultValue}
            onChange={(value: string) => filter.onChange(value)}
            placeholder={filter.placeholder}
            isDisabled={isDisable}
            className={filter.className}
          />
        );
      case "calender":
        return (
          <Calendar
            defaultValue={filter.defaultValue}
            keyFilter={{
              from: filter.key_from,
              to: filter.key_to,
              month: filter.key_month,
              year: filter.key_year,
            }}
            onChange={(value: any) => filter.onChange(value)}
            isDisabled={isDisable}
            className={filter.className}
            selectedOption={filter.selectedOption}
            key={'calender' + index}
          />
        );
      case "date":
        return (
          <DateRangePicker
            key={'date' + index}
            className={filter.className}
            locale="en-GB"
            initialDateFrom={filter.date_from}
            initialDateTo={filter.date_to}
            onChange={(value: any) => filter.onChange(value)}
          />
        );
      case 'interest':
        return (
          <InterestOptions
            key={'interest' + index}
            defaultValue={handleSelectedDefault(filter)}
            selectedOption={filter.selectedOption}
            isDisabled={isDisable}
            isSegmentDetail={isDisable}
            onChange={(value: any) => filter.onChange(value)}
          />
        )
      default:
        break;
    }
  });
};

export default FilterOptions;
