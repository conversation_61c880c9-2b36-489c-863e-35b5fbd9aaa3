import { useRef, useState } from "react";
import * as HoverCard from "@radix-ui/react-hover-card";

import { Badge } from "components/ui/badge";

import useOutsideClick from "hooks/useClickOutSide";
import { TSelectOptionCheckbox } from "types/Select";

import { cn } from "utils/utils";
import SelectGroups from "components/FilterOptions/SelectGroups";
import { RiLockLine } from "@remixicon/react";
import { Box } from "components/Box";

interface Props {
  label: string;
  icon: JSX.Element;
  activeCount: number;
  groupKey?: number;
  groupSelect?: TSelectOptionCheckbox[];
  onClickChangeTab?: any;
  isEnable?: boolean;
  className?: string;
}

const FilterHoverCard = ({
  label,
  icon,
  groupKey,
  groupSelect,
  activeCount,
  onClickChangeTab,
  isEnable = false,
  className
}: Props) => {
  const ref = useRef(null);
  const refSecond = useRef(null);

  const [open, setOpen] = useState<boolean>(false);

  useOutsideClick(ref, refSecond, () => {
    setOpen(false);
  });

  const handleOnClick = () => {
    setOpen((prev) => !prev);
    onClickChangeTab && onClickChangeTab();
  };

  return (
    <HoverCard.Root open={open} openDelay={300} closeDelay={300}>
      <HoverCard.Trigger
        ref={ref}
        asChild
        className={cn(
          "flex items-center gap-2 cursor-pointer border-b-[2px] w-max border-b-[transparent] pb-4",
          open && "border-b-brand-default",
          !isEnable && " text-infor-disable"
        )}
        onClick={isEnable ? handleOnClick : undefined}
      >
        <Box className="gap-1 w-max">
          <span>{icon}</span>
          <p className="m-0 p-0 w-max">{label}</p>
          {activeCount > 0 && isEnable && (
            <Badge className="rounded-md bg-brand-light hover:bg-brand-light text-brand-default px-2">
              {activeCount}
            </Badge>
          )}
          {!isEnable && <RiLockLine size={17} />}
        </Box>
      </HoverCard.Trigger>
      <HoverCard.Portal>
        <HoverCard.Content
          sideOffset={12}
          align="start"
          className="border-none rounded-md bg-white z-[10000]"
          ref={refSecond}
        >
          {groupSelect && (
            <SelectGroups placeholder={label} groupSelect={groupSelect} key={groupKey} className={className}/>
          )}
          <HoverCard.Arrow className="w-3 h-2 fill-brand-default stroke-[#DEE0E3]" />
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
};

export default FilterHoverCard;
