import { Table, TableBody, Table<PERSON>ell, TableHead, Table<PERSON>eader, TableRow } from "components/ui/table";

import { TSelectOptionCheckbox } from "types/Select";
import { cn } from "utils/utils";
// import SearchInput from "components/SearchInput";
// import { PERSONA_LABEL } from "constants/persona/label";
import MultiCheckbox from "components/MCheckbox/MultiCheckbox";
import { RiLockLine } from "@remixicon/react";
import { Box } from "components/Box";
import RangeSlider from "../RangeSlider";

interface ISelect {
  placeholder: string;
  groupSelect: TSelectOptionCheckbox[];
  className?: string;
}
const SelectGroups = (props: ISelect) => {
  const { groupSelect, className } = props;
  return (
    <div
      className={cn(
        "flex items-start gap-4 border border-custom-primary rounded-md w-fit p-3 z-50 min-w-[200px]",
        className
      )}
    >
      <Table>
        <TableHeader>
          <TableRow className="bg-[#F7F7F8] text-sm text-primary border-none">
            {groupSelect.filter((item) => !item.isMobile).map((group: TSelectOptionCheckbox, index: number) => {
              const { placeholderOptions, isEnable = true } = group;
              return (
                <TableHead
                  className={cn(
                    index == 0 && "rounded-tl-[10px]",
                    index == 1 && "rounded-tr-[10px]",
                    groupSelect.length < 2 && "rounded-tr-[10px]"
                  )}
                  key={index}
                >
                  <Box className={cn("gap-1 justify-start ", !isEnable && "text-infor-disable")}>
                    {placeholderOptions}
                    {!isEnable && <RiLockLine size={17} />}
                  </Box>
                </TableHead>
              );
            })}
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow className="bg-transparent hover:bg-transparent p-0 items-start">
            {groupSelect.map((group: TSelectOptionCheckbox, index: number) => {
              const { customOptions, isCustomOptions = false, rangeOption, isMobile = false, isHaveChildren = false, placeholderOptionsChildren } = group;
              return isCustomOptions && customOptions ? (
                <TableCell className="align-top py-3 px-0" key={index}>
                  {!isMobile && rangeOption && rangeOption.rangeSelected && rangeOption.onRangeChange && <div className='mt-2 mb-3'>
                    <RangeSlider
                      minRange={rangeOption.min}
                      maxRange={rangeOption.max}
                      hideInput={rangeOption.hideInput}
                      className={rangeOption.className}
                      rangeSelected={rangeOption.rangeSelected}
                      setRangeSelected={rangeOption.onRangeChange}
                      isRangeEnable={rangeOption.isRangeEnable}
                    />
                  </div>}
                  {!isMobile && customOptions}
                </TableCell>
              ) : (
                <TableCell className="align-top p-0" key={index}>
                  {!isHaveChildren && rangeOption && rangeOption.rangeSelected && rangeOption.onRangeChange && <div className='mt-2 mb-3'>
                    <RangeSlider
                      minRange={rangeOption.min}
                      maxRange={rangeOption.max}
                      hideInput={rangeOption.hideInput}
                      className={rangeOption.className}
                      rangeSelected={rangeOption.rangeSelected}
                      setRangeSelected={rangeOption.onRangeChange}
                      isRangeEnable={rangeOption.isRangeEnable}
                    />
                  </div>}
                  <MultiCheckbox {...group} children={isHaveChildren ?
                    rangeOption && rangeOption.rangeSelected && rangeOption.onRangeChange && <div className="mt-5 mb-3">
                      <RangeSlider
                        minRange={rangeOption.min}
                        maxRange={rangeOption.max}
                        hideInput={rangeOption.hideInput}
                        className={rangeOption.className}
                        rangeSelected={rangeOption.rangeSelected}
                        setRangeSelected={rangeOption.onRangeChange}
                        isRangeEnable={rangeOption.isRangeEnable}
                        title={<div className="absolute -top-4 left-1/2 transform -translate-x-1/2 text-sm text-primary border-none rounded">
                          <Box className={cn("gap-1 justify-center mt-1", !rangeOption.isRangeEnable && "text-infor-disable")}>
                            {placeholderOptionsChildren}
                            {!rangeOption.isRangeEnable && <RiLockLine size={17} />}
                          </Box>
                        </div>}
                      />
                    </div> :
                    <></>}
                  />
                </TableCell>
              );
            })}
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};

export default SelectGroups;
