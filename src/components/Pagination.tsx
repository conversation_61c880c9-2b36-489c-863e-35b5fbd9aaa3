import { cn } from "utils/utils";
import isArray from "lodash/isArray";
import { RiArrowLeftLine, RiArrowRightLine } from "@remixicon/react";

import { DOTS, PaginationProps, usePagination } from "hooks/usePagination";
import "../styles/pagination.css";

const Pagination = (
  props: PaginationProps & { onPageChange: (page: number) => void; className?: string }
) => {
  const {
    onPageChange,
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize,
    className = "",
  } = props;

  const paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize,
  });

  if (currentPage === 0 || (isArray(paginationRange) && paginationRange.length < 2)) {
    return null;
  }

  const onNext = () => {
    onPageChange(currentPage + 1);
  };

  const onPrevious = () => {
    onPageChange(currentPage - 1);
  };

  let lastPage = isArray(paginationRange) && paginationRange[paginationRange.length - 1];

  return (
    <ul className={cn("pagination-container", { [className]: className })}>
      <li
        className={cn("pagination-item", {
          disabled: currentPage === 1,
        })}
        onClick={onPrevious}
      >
        <RiArrowLeftLine color="#0F1324" size={16} opacity={0.6} />
      </li>
      {isArray(paginationRange) &&
        paginationRange.map((pageNumber: number | string, index: any) => {
          if (pageNumber === DOTS) {
            return (
              <li key={"dot" + index} className="pagination-item dots">
                &#8230;
              </li>
            );
          }

          return (
            <li
              key={`${pageNumber + index}`}
              className={cn("pagination-item", {
                selected: pageNumber === currentPage,
              })}
              onClick={() => onPageChange(pageNumber as number)}
            >
              { pageNumber.toLocaleString() }
            </li>
          );
        })}
      <li
        className={cn("pagination-item", {
          disabled: currentPage === lastPage,
        })}
        onClick={onNext}
      >
        <RiArrowRightLine color="#0F1324" size={16} opacity={0.6} />
      </li>
    </ul>
  );
};

export default Pagination;
