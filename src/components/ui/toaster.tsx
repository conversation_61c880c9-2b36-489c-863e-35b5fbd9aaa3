import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastIcon,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "components/ui/toast";
import { useToast } from "components/ui/use-toast";

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(function ({ id, status, title, description, action, className, ...props }) {
        return (
          <Toast key={id} {...props} variant={status} className={className}>
            <div className="flex gap-2">
              <ToastIcon status={status} />
              <div className="grid gap-1 flex-1">
                {title && <ToastTitle>{title}</ToastTitle>}
                {description && <ToastDescription>{description}</ToastDescription>}
              </div>
            </div>
            {action}
            <ToastClose />
          </Toast>
        );
      })}
      <ToastViewport className="lg:max-w-fit" />
    </ToastProvider>
  );
}
