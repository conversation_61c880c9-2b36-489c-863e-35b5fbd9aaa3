import { useEffect, useState } from "react";
import { Progress } from "./progress";

const AnimatedProgress = ({
  targetValue,
  color,
  duration = 500,
}: {
  color: string;
  targetValue: number;
  duration?: number;
}) => {
  const [value, setValue] = useState(0);

  useEffect(() => {
    let startTime = 0;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setValue(Math.floor(progress * targetValue));

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);

    return () => cancelAnimationFrame(animate as any);
  }, [targetValue, duration]);

  return <Progress className="h-1.5" value={value} color={color} />;
};

export default AnimatedProgress;
