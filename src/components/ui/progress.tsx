import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";

import { cn } from "utils/utils";

interface Props {
  maxRange?: number;
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & Props
>(({ className, value, maxRange = 100, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn("relative h-2 w-full overflow-hidden rounded-full bg-secondary", className)}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={cn("h-full w-full flex-1 bg-[#26BD6C] transition-all rounded-full")}
      style={{
        backgroundColor: `${props.color}`,
        transform: `translateX(-${maxRange - (value || 0)}%)`,
      }}
    />
  </ProgressPrimitive.Root>
));
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
