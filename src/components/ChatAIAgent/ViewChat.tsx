import React from 'react';
import './stylesMarkDown.css';
import { marked } from 'marked';

interface ViewChatProps {
  content: string;
}

const ViewChat: React.FC<ViewChatProps> = ({ content }) => {

  const normalizeEscapeSequences = (text: string): string => {
    let prev = '';
    let curr = text;

    while (curr !== prev) {
      prev = curr;
      curr = curr
      .replace(/\\\\/g, '\\')
      .replace(/\\n/g, '\n')
      .replace(/\\r/g, '\r')
      .replace(/\\t/g, '\t')
      .replace(/\\"/g, '"')
      .replace(/\\'/g, "'");
    }

    return curr;
  };

  const convertMDToHTML = (text: string) => {
    const decoded = normalizeEscapeSequences(text);

    const processedText = decoded
    .replace(/<link>(.*?)<\/link>/g, (_, url) => `[${url}](${url})`)
    .replace(/\n/g, '\n\n');

    return marked.parse(processedText);
  };

  return (
    <div
      className="markdown-content"
      dangerouslySetInnerHTML={{ __html: convertMDToHTML(content) }}
    />
  );
};

export default ViewChat;
