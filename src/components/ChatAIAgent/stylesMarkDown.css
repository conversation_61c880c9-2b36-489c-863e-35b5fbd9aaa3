.markdown-content {
  all: unset;
  display: block;
}

.markdown-content img {
  max-width: 100%;
}

.markdown-content * {
  all: revert;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  line-height: initial;
}

.markdown-content p {
  margin: 4px 0;
}

.markdown-content strong {
  font-weight: bold;
}

.markdown-content em {
  font-style: italic;
}

.markdown-content ul {
  list-style-type: disc;
  margin-left: 15px;
  padding-left: 15px;
}

.markdown-content ol {
  list-style-type: decimal;
  margin-left: 15px;
  padding-left: 15px;

}

.markdown-content code {
  font-family: 'Courier New', Courier, monospace;
  background-color: #f4f4f4;
  padding: 2px 4px;
  border-radius: 3px;
}

.markdown-content pre {
  background-color: #f4f4f4;
  padding: 10px;
  border-radius: 5px;
  overflow-x: auto;
  white-space: pre-wrap;
}

.markdown-content a {
  color: #0066cc;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.balls {
  width: 24px;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
}

.balls div {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #6e6e80;
  transform: translateY(-100%);
  animation: wave 0.8s ease-in-out alternate infinite;
}

.balls div:nth-of-type(1) {
  animation-delay: -0.4s;
}

.balls div:nth-of-type(2) {
  animation-delay: -0.2s;
}

@keyframes wave {
  from {
    transform: translateY(-50%);
  }
  to {
    transform: translateY(50%);
  }
}

