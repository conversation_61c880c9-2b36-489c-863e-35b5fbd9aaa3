import { Link } from "react-router-dom";
import { useSelector } from "react-redux";
import { sidebarStore } from "store/redux/sidebar/slice";

import IconFavicon from "assets/icons/IconFavicon";
import LogoSvg from "assets/icons/LogoSvg";
import { cn } from '../utils/utils';

interface Props {
  disabledLink?: boolean;
  hideIcon?: boolean;
  className?: string;
}

const Logo = (props: Props) => {
  const { disabledLink, hideIcon = false, className } = props;
  const { collapse } = useSelector(sidebarStore);
  const logo = (
    <div className="relative items-center gap-2 flex flex-row pt-4 pb-4 pl-2 md:flex">
      {!hideIcon && <IconFavicon />}
      {!collapse && <LogoSvg />}
    </div>
  );
  if (disabledLink) {
    return <>{logo}</>;
  }
  return (
    <div className={cn('w-fit',className)}>
      <Link to="/" >
        {logo}
      </Link>
    </div>
  );
};

export default Logo;
