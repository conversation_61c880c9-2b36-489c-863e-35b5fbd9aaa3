import { useNavigate } from "react-router-dom";

import { Button } from "../ui/button";
import AttachmentIcon from "assets/icons/AttachmentIcon";

import { FB_SERVICE_PATH, ROOT_PATH } from "types/Router";
import { LABEL } from "constants/index";
import { cn } from "utils/utils";

interface Props {
  title?: string;
  className?: string;
  typeButton?:
    | "link"
    | "default"
    | "secondary"
    | "main"
    | "tertiary"
    | "destructive"
    | "outline"
    | "ghost"
    | "success"
    | null
    | undefined;
  colorButton?: string;
}
const ButtonRequest = ({ title, className, typeButton, colorButton }: Props) => {
  const navigate = useNavigate();
  const onNavigate = () => navigate(`${ROOT_PATH}/${FB_SERVICE_PATH.REQUEST_AUDIENCE}`);

  return (
    <Button
      onClick={onNavigate}
      className={cn("rounded-xl w-full h-10 sm:h-9 sm:w-fit", className)}
      size="sm"
      variant={typeButton ? typeButton : "default"}
    >
      <AttachmentIcon className={cn("mr-2")} color={colorButton} />
      {title ? title : LABEL.request_audiences}
    </Button>
  );
};

export default ButtonRequest;
