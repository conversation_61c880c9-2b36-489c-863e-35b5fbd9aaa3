import { RiEdit2Line } from "@remixicon/react";
import { Box } from "components/Box";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";

const ButtonEditUserInfor = ({ CallBack }: { CallBack: () => void }) => {
  return (
    <Box
      className="gap-1 px-1.5 py-1.5 lg:px-3 lg:py-3 text-primary bg-custom-secondary rounded-xl font-medium text-sm cursor-pointer hover:bg-custom-secondary h-8 lg:h-10"
      onClick={CallBack}
    >
      <RiEdit2Line size={20} />
      <span>{USER_PROFILE_LABEL.edit}</span>
    </Box>
  );
};

export default ButtonEditUserInfor;
