import { useAppDispatch, useAppSelector } from 'store';
import { useCallback, useContext, useState } from "react";
import { getMe } from "store/redux/auth/slice";

import { useNavigate } from "react-router-dom";
import { RiLoader2Line } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import AvatarByName from "components/AvatarByName";
import { Button } from "components/ui/button";
import BadgePackage from "components/BadgePackage";
import ShowCreditAudience from "./ShowCreditAudience";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import getCreditCost from "hooks/getCreditCost";
import useAvatar from "hooks/useAvatar";
import { useCancelToken } from "hooks/useCancelToken";
import useShowEnoughCredit from "hooks/useShowEnoughCredit";
import { socialAPI } from "apis/socialData";
import { TSocialDetail } from "types/SocialData";
import { PATH_DASHBOARD } from "types/path";
import { CREDIT_TYPE_ENUM } from "types/Transaction";

import handleCloseModal from "utils/handleCloseModal";
import { fNumberToString } from "utils/number";
import { formatPayloadAvatar } from "utils/utils";
import TRANSACTION_LABEL from "constants/Transaction/label";
import LABEL from "constants/label";
import { maxExceeded } from "constants/socialData";
import { tracking } from '../../utils/Tracking/tracking';

interface Props {
  audience?: TSocialDetail;
  onRefresh?: () => void;
  isDataset: boolean;
}

const BuySocialAudience = ({ audience, isDataset }: Props) => {
  const modal = useContext(ModalContext);
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const { newCancelToken } = useCancelToken();
  const { handleShowEnoughCredit } = useShowEnoughCredit();

  const [loadingBuyAudience, setLoadingBuyAudience] = useState<boolean>(false);

  //get Credit Cost
  const { credit } = getCreditCost({
    params: audience?.size || 0,
    creditType: isDataset ? CREDIT_TYPE_ENUM.BUY_SC_DATASET : CREDIT_TYPE_ENUM.BUY_SC_AUDIENCE,
  });
  const payloadAvatar = formatPayloadAvatar(
    audience?.type || 1,
    audience?.fb_uid || "",
    audience?.actor_id || ""
  );
  const { avatar } = useAvatar(payloadAvatar);

  const handleBuyAudience = useCallback(async () => {
    setLoadingBuyAudience(true);
    const response: any = await socialAPI.addAudiences({
      payload: {
        audience_id: audience?.fb_uid,
        datatype: isDataset ? "DATASET" : "AUDIENCE",
        cancelToken: newCancelToken(),
      },
    });
    if (response && response.data) {
      await dispatch(getMe()).unwrap();
      handleCloseModal(modal);
      if (isDataset) {
        navigate(`/${PATH_DASHBOARD.your_data.your_audience.socialDataset}/${audience?.fb_uid}/`);
      } else {
        navigate(`/${PATH_DASHBOARD.your_data.your_audience.social}/${audience?.fb_uid}/`);
      }
    }
    if (response && response.error?.code == 6005) {
      handleShowEnoughCredit();
    }
    tracking({
      eventName: 'purchase',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          audience_id: audience?.fb_uid,
          audience_type: isDataset ? 'DATASET' : 'AUDIENCE'
        })
      }
    });
    setLoadingBuyAudience(false);
  }, [newCancelToken]);

  const isMaxExceeded = !!(audience?.size && audience?.size > maxExceeded);
  return (
    <>
      {credit.loading && (
        <div className="flex items-center justify-center">
          <LoadingButtonIcon />
        </div>
      )}
      {!credit.loading && (
        <Box variant="col-start" className="w-full justify-center">
          <div className="text-primary text-[20px] font-semibold w-full text-center">
            {isDataset ? LABEL.buy_dataset : LABEL.buy_audience}
          </div>
          <Box className="p-4 bg-brand-subtitle text-primary w-full rounded-2xl text-sm font-medium">
            <Box className="w-2/3 gap-2 justify-start">
              <AvatarByName
                urlImage={avatar.url}
                name={audience?.name || ""}
                className="w-10 h-10"
              />
              <div>
                <div>
                  <span className="mr-2"> {audience?.name}</span>
                  {audience?.package && (
                    <BadgePackage
                      packageValue={audience?.package}
                      className="text-[11px] px-1 py-1"
                    />
                  )}
                </div>
                <div>
                  <span className="mr-1">{TRANSACTION_LABEL.CREDIT.audience_size}</span>
                  <span className="text-secondary font-normal">
                    {audience?.size ? fNumberToString(audience?.size) : 0}
                  </span>
                </div>
              </div>
            </Box>
            {!isMaxExceeded && <ShowCreditAudience cost={credit.cost} />}
          </Box>
          <Box className="grid grid-cols-1 w-full sm:grid-cols-2 gap-3">
            <Button
              variant="secondary"
              className="w-full rounded-xl"
              onClick={() => handleCloseModal(modal)}
            >
              {LABEL.cancel}
            </Button>
            <Button
              variant="default"
              className="w-full rounded-xl"
              onClick={() => handleBuyAudience()}
              disabled={loadingBuyAudience || isMaxExceeded}
            >
              {loadingBuyAudience ? (
                <RiLoader2Line className="animate-spin" size={14} />
              ) : isMaxExceeded ? (
                LABEL.talk_to_sale
              ) : isDataset ? (
                LABEL.buy_dataset
              ) : (
                LABEL.buy_audience
              )}
            </Button>
          </Box>
        </Box>
      )}
    </>
  );
};

export default BuySocialAudience;
