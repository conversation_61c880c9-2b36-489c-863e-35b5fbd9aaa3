import { useAppDispatch, useAppSelector } from 'store';
import { useContext, useEffect, useState } from "react";
import { getMe } from "store/redux/auth/slice";
import { Link } from "react-router-dom";
import { RiLoader2Fill } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import AvatarByName from "components/AvatarByName";
import ShowCreditAudience from "./ShowCreditAudience";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import { creditAPI } from "apis/credit";
import { personaCustomAPI } from "apis/personaCustom";
import useShowEnoughCredit from "hooks/useShowEnoughCredit";
import handleCloseModal from "utils/handleCloseModal";

import { YOUR_AUDIENCES } from "types/Router";
import LABEL from "constants/label";
import { getAudienceProfilesSummarize } from "store/redux/AudienceProfile/slice";
import { tracking } from '../../utils/Tracking/tracking';

interface Props {
  fullName: string;
  phone: string;
  uid: number;
  isBuyProfile?: boolean;
}
const BuyAudienceCustom = ({ uid, fullName, phone, isBuyProfile }: Props) => {
  const dispatch = useAppDispatch();
  const modal = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);
  const { handleShowEnoughCredit } = useShowEnoughCredit();

  const [loading, setLoading] = useState(false);
  const [credit, setCredit] = useState({
    loading: false,
    cost: 0,
  });

  //get Credit Cost
  useEffect(() => {
    handleGetCreditCost();
  }, []);

  const handleGetCreditCost = async () => {
    setCredit((prev) => ({ ...prev, loading: true }));
    const response = await creditAPI.getPersonaCreditCost({
      params: { profile_uid: uid },
    });
    if (response && response.data) {
      setCredit({ loading: false, cost: response.data.credit_cost });
    }
    setCredit((prev) => ({ ...prev, loading: false }));
  };

  const handleBuyAudienceCustom = async () => {
    setLoading(true);

    const response: any = await personaCustomAPI.addProfileCustom(uid);
    if (response && response.data) {
      toast({
        title: "Profile added to your Audience Collection - check it out!",
        description: (
          <Link
            to={`/${YOUR_AUDIENCES.ROOT}/${YOUR_AUDIENCES.PERSONA}/${YOUR_AUDIENCES.ARCHIVE}`}
            className="font-semibold"
            children="VIEW AUDIENCE COLLECTION"
          />
        ),
        status: "success",
        duration: 5000,
        className: "md:max-w-[500px] w-[450px] text-right",
      });
      handleCloseModal(modal);
      await dispatch(getMe()).unwrap();
      await dispatch(getAudienceProfilesSummarize()).unwrap();
    }
    if (response && response.error?.code == 6005) {
      handleShowEnoughCredit();
    }
    if (response && response?.response?.data?.code == 3004) {
      setLoading(false);
      handleCloseModal(modal);
    }
    setLoading(false);
    tracking({
      eventName: 'purchase',
      params:{
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          audience_id: uid,
          audience_type: 'AUDIENCE'
        })
      }
    })
  };

  return (
    <>
      {credit?.loading && (
        <Box className="text-center justify-center">
          <LoadingButtonIcon />
        </Box>
      )}
      {!credit.loading && (
        <div className="flex flex-col w-full gap-4">
          <div className="text-lg text-primary font-semibold text-center w-full">
            {isBuyProfile ? LABEL.buy_profile : LABEL.buy_audience}
          </div>

          <Box className="p-4 bg-brand-subtitle text-primary w-full rounded-2xl text-sm font-medium">
            <Box className="w-2/3 gap-2 justify-start">
              <AvatarByName urlImage={""} name={fullName || ""} className="w-10 h-10" />
              <div>
                <div>{fullName}</div>
                <div className="!font-sans">{phone}</div>
              </div>
            </Box>
            <ShowCreditAudience cost={credit.cost} />
          </Box>
          <div className="grid grid-cols-2 gap-[15px] mt-5">
            <Button
              type="button"
              variant="secondary"
              className="border shadow-xs rounded-xl text-md font-medium"
              onClick={() => handleCloseModal(modal)}
            >
              {LABEL.cancel}
            </Button>
            <Button
              variant="default"
              className="rounded-xl text-md font-medium"
              onClick={handleBuyAudienceCustom}
              disabled={loading}
            >
              {loading ?
                <RiLoader2Fill className="animate-spin" size={14} /> :
                isBuyProfile ? LABEL.buy_profile : LABEL.buy_audience}
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default BuyAudienceCustom;
