import CreditItem from "views/PlanAndPaymentView/components/CreditItem";

import { cn } from "utils/utils";

export interface ICreditValueProps {
  priceValue: IPriceValueProps;
  setPriceValue: React.Dispatch<React.SetStateAction<IPriceValueProps>>;
}

export interface IPriceValueProps {
  price: string | number;
  discount: number;
  price_sale: number | string;
}

const CreditModal = ({ className, classContainer }: { className?: string, classContainer?: string }) => {
  return (
    <div className={cn("w-full flex flex-col gap-6", classContainer)}>
      <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full", className)}>
        <CreditItem />
      </div>
      {/*<SummarizeCredit />*/}
    </div>
  );
};

export default CreditModal;
