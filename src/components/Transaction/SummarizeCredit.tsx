import { useState } from "react";

import { toast } from "components/ui/use-toast";
import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import DiscountTag from "components/DiscountTag";

import { paymentAPI } from "apis/plansAndPayment";
import { calculatePrice } from "constants/Transaction";
import LABEL from "constants/label";
import CustomizeCredit from "./CustomizeCredit";
import { IPriceValueProps } from "./CreditModal";
import { tracking } from '../../utils/Tracking/tracking';
import { useAppSelector } from '../../store';

const SummarizeCredit = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [priceValue, setPriceValue] = useState<IPriceValueProps>({
    price: 600,
    price_sale: 468,
    discount: 22,
  });

  const { user } = useAppSelector((state) => state.auth);

  const handleBuyCredit = async (price: number) => {
    setLoading(true);
    const response = await paymentAPI.createPaymentLink({
      credit_code: `CREDIT_${priceValue.price as number}`,
      type: "credit",
    });
    if (response && response.data) {
      window.open(response.data.url);

    }
    setLoading(false);
    tracking({
      eventName: 'buy_credits_button',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          credits_amount: price,
        })
      }
    });
  };

  const handleTalkToSales = () =>
    toast({
      title:
        "Thank you for reaching out! We've received your request and will get back to you shortly.",
      status: "info",
      duration: 3000,
    });

  const isCustom = priceValue.price == "custom" || priceValue.price == ">5000";
  const disableBuy = (priceValue.price as number) > 500;
  return (
    <div className="bg-white rounded-2xl px-3 lg:px-6 py-4 w-full border">
      <CustomizeCredit priceValue={priceValue} setPriceValue={setPriceValue} />
      <div className="font-medium text-md h-10 leading-10 mt-4">Summary</div>
      <Box className="flex-col lg:flex-row gap-3 lg:gap-6">
        <Box
          variant="col-start"
          className="gap-2 min-h[46px] lg:min-h-[66px] flex-row-reverse lg:flex-col justify-between lg:justify-normal items-end lg:items-start"
        >
          {!isCustom && (
            <>
              <span className="font-medium text-sm">{priceValue.price} Credits</span>
              {priceValue.discount > 0 && (
                <Box className="text-secondary items-start lg:items-center text-sm gap-0 lg:gap-2 flex-col-reverse lg:flex-row">
                  <span className="line-through"> ${priceValue.price}</span>
                  <DiscountTag isShowDiscount percent={priceValue.discount} />
                </Box>
              )}
            </>
          )}
        </Box>
        <Box className="w-full lg:w-auto flex-col lg:flex-row">
          {!isCustom && (
            <Box className="gap-2 items-center w-full lg:w-auto">
              <span className="text-secondary text-base lg:text-md font-medium lg:font-normal">Total:</span>
              <span className="text-2xl lg:text-base font-medium lg:font-normal">${calculatePrice(priceValue?.price, priceValue.discount)}</span>
            </Box>
          )}
          {isCustom || disableBuy ? (
            <Button
              variant="main"
              className="w-full lg:w-[240px] text-md font-medium rounded-xl"
              onClick={handleTalkToSales}
            >
              {LABEL.talk_to_sale}
            </Button>
          ) : (
            <Button
              variant="main"
              className="w-full lg:w-[240px] text-md font-medium rounded-xl"
              disabled={loading}
              onClick={()=>handleBuyCredit(priceValue.price as number)}
            >
              {loading ? <LoadingButtonIcon /> : LABEL.buy}
            </Button>
          )}
        </Box>
      </Box>
    </div>
  );
};

export default SummarizeCredit;
