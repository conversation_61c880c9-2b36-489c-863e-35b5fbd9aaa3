import { Box } from "components/Box";
import { Slider } from "components/ui/slider";
import { ICreditValueProps } from "./CreditModal";

import { cn } from "utils/utils";
import LABEL from "constants/label";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import { calculatePrice, CREDIT_POINTS, formatPointShowSlider, formatValuePrice } from "constants/Transaction";

const CustomizeCredit = ({ priceValue, setPriceValue }: ICreditValueProps) => {
  const handleChange = (val: number[]): void => {
    const newValue = formatValuePrice(val[0]);
    setPriceValue((prev: any) => ( {
      ...prev,
      discount: 22,
      price: newValue,
      price_sale: calculatePrice(priceValue?.price, priceValue.discount)
    } ));
  };

  const price = priceValue?.price as number;

  return (
    <div className="w-full bg-white py-2 px-3 lg:px-4 rounded-sm shadow-sm">
      <Box className="h-10 font-medium text-md text-primary">
        <span>{USER_PROFILE_LABEL.CREDIT.title_card}</span>
        {price <= 500 ? null : price > 500 && price <= 5000 ? (
          <span>${calculatePrice(priceValue?.price, priceValue.discount)}</span>
        ) : (
          <span>{LABEL.talk_to_sale}</span>
        )}
      </Box>

      <div className="relative">
        <Slider
          value={[formatPointShowSlider(priceValue?.price)]}
          min={0}
          max={100}
          step={1}
          onValueChange={handleChange}
          className="w-full h-6"
        />
        <Box className={cn("w-full hidden md:flex")}>
          {CREDIT_POINTS.map((point, index) => (
            <span
              className={cn(
                "text-xs text-center text-secondary w-5 relative",
                index !== 0 && "right-1",
                index === CREDIT_POINTS.length - 1 && "right-5"
              )}
              key={point}
            >
              {point}
            </span>
          ))}
        </Box>
      </div>
    </div>
  );
};

export default CustomizeCredit;
