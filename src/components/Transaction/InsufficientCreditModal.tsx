import { useContext } from "react";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import CreditModal from "./CreditModal";

import LABEL from "constants/label";
import TRANSACTION_LABEL from "constants/Transaction/label";
import handleCloseModal from "utils/handleCloseModal";

const InsufficientCreditModal = () => {
  const modal = useContext(ModalContext);

  const handleOpenBuyCreditModal = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isShowTitle: true,
      title: "Customize Credits",
      content: <CreditModal />,
      className: "max-w-[1110px] bg-custom-secondary",
      footer: "",
    }));
  };

  return (
    <Box variant="col-start" className="w-full justify-center">
      <div className="w-full text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="80"
          height="81"
          viewBox="0 0 80 81"
          fill="none"
          className="text-center w-full"
        >
          <path
            d="M40 7.16675C58.4093 7.16675 73.3333 22.0906 73.3333 40.5001C73.3333 42.9221 73.075 45.2837 72.5843 47.5591L66.6436 41.6184C66.659 41.2474 66.6666 40.8747 66.6666 40.5001C66.6666 25.7725 54.7276 13.8334 40 13.8334C25.2724 13.8334 13.3333 25.7725 13.3333 40.5001C13.3333 55.2278 25.2724 67.1668 40 67.1668C44.341 67.1668 48.4396 66.1294 52.062 64.2894C52.5783 65.1094 53.1933 65.8831 53.9053 66.5947C54.913 67.6024 56.0416 68.4151 57.2446 69.0327C52.214 72.0791 46.312 73.8334 40 73.8334C21.5905 73.8334 6.66663 58.9094 6.66663 40.5001C6.66663 22.0906 21.5905 7.16675 40 7.16675ZM63.3333 47.7388L68.0473 52.4528C70.651 55.0561 70.651 59.2774 68.0473 61.8807C65.444 64.4844 61.2226 64.4844 58.6193 61.8807C56.134 59.3957 56.0213 55.4367 58.2803 52.8174L58.6193 52.4528L63.3333 47.7388ZM40 50.5001C44.888 50.5001 49.2843 52.6041 52.333 55.9561L49.183 58.8204C46.55 57.7751 43.3933 57.1668 40 57.1668C36.6066 57.1668 33.45 57.7751 30.8168 58.8204L27.6669 55.9561C30.7156 52.6041 35.112 50.5001 40 50.5001ZM28.3333 33.8334C31.0947 33.8334 33.3333 36.0721 33.3333 38.8334C33.3333 41.5947 31.0947 43.8334 28.3333 43.8334C25.5719 43.8334 23.3333 41.5947 23.3333 38.8334C23.3333 36.0721 25.5719 33.8334 28.3333 33.8334ZM51.6666 33.8334C54.428 33.8334 56.6666 36.0721 56.6666 38.8334C56.6666 41.5947 54.428 43.8334 51.6666 43.8334C48.9053 43.8334 46.6666 41.5947 46.6666 38.8334C46.6666 36.0721 48.9053 33.8334 51.6666 33.8334Z"
            fill="#8F5CFF"
          />
        </svg>
        <div className="text-primary text-[20px] font-semibold">
          {TRANSACTION_LABEL.CREDIT.insufficient}
        </div>
        <div className="text-secondary text-sm mt-2">
          {TRANSACTION_LABEL.CREDIT.insufficient_message}
        </div>
      </div>
      <Box className="w-full gap-4">
        <Button
          variant="secondary"
          className="w-full rounded-xl"
          onClick={() => handleCloseModal(modal)}
        >
          {LABEL.cancel}
        </Button>
        <Button variant="default" className="rounded-xl w-full" onClick={handleOpenBuyCreditModal}>
          {LABEL.buy_credit}
        </Button>
      </Box>
    </Box>
  );
};

export default InsufficientCreditModal;
