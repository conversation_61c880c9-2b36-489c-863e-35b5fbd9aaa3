import { useContext, useEffect, useState } from "react";
import { RiLoader2Line } from "@remixicon/react";
import { useAppDispatch, useAppSelector } from "store";
import { ModalContext } from "providers/Modal";

import { getMe } from "store/redux/auth/slice";
import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import FileCSV from "assets/icons/FileCSV";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import ShowCreditAudience from "components/Transaction/ShowCreditAudience";

import { FileInfoProps, TypeEnrichmentRecord } from "types/Enrichment";
import { enrichmentAPI } from "apis/enrichment";

import { convertFileSize } from "utils/enrichment";
import handleCloseModal from "utils/handleCloseModal";
import { ENRICHMENT_LABEL } from "constants/Enrichment/label";
import LABEL from "constants/label";
import useShowEnoughCredit from "../../hooks/useShowEnoughCredit";

interface Props extends Partial<Pick<TypeEnrichmentRecord, "file_name" | "date_created" | "id">> {}

const DownloadEnrichModal = ({ id = "", file_name }: Props) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const modal = useContext(ModalContext);
  const { handleShowEnoughCredit } = useShowEnoughCredit();

  const [email, setEmail] = useState(user.email);
  const [loading, setLoading] = useState(false);
  const [fileInfo, setFileInfo] = useState<FileInfoProps>({
    loading: false,
    data: null,
  });

  useEffect(() => {
    handleGetFileInfo();
  }, []);

  const handleGetFileInfo = async () => {
    setFileInfo((prev) => ({ ...prev, loading: true }));

    const response: any = await enrichmentAPI.getFileInfo(id);
    if (response) {
      setFileInfo((prev) => ({ ...prev, data: response }));
    }
    setFileInfo((prev) => ({ ...prev, loading: false }));
  };

  const handleDownload = async () => {
    setLoading(true);
    const response = await enrichmentAPI.downloadEnrichedFile({ id });
    if (response.code == 0) {
      toast({
        title: response.message,
        description: "",
        status: "success",
      });
      await dispatch(getMe()).unwrap();
      handleCloseModal(modal);
    }
    if (response.error?.code == 6005 || response.error?.code == 5004) {
      await handleShowEnoughCredit();
    }
    setLoading(false);
  };

  return fileInfo?.loading ? (
    <Box className="text-center w-full justify-center">
      <LoadingButtonIcon />
    </Box>
  ) : (
    <Box variant="col-start" className="gap-4 w-full">
      <div className="text-xl text-center w-full font-semibold">
        {ENRICHMENT_LABEL.download_enrich}
      </div>

      <Box className="p-4 bg-brand-subtitle text-primary w-full rounded-2xl text-sm font-medium">
        <Box className="gap-1 justify-start">
          <FileCSV />
          <div>
            <div className="text-sm font-medium text-primary">{file_name}</div>
            <span className="text-xs text-tertiary">
              {convertFileSize(fileInfo.data?.file_size!)}
            </span>
          </div>
        </Box>
        {!fileInfo?.data?.is_paid && <ShowCreditAudience cost={fileInfo?.data?.credit_cost || 0} />}
      </Box>

      <div className="flex flex-col gap-1 w-full">
        <div className="text-secondary text-sm font-semibold">{ENRICHMENT_LABEL.download_link}</div>
        <div className="mt-4">
          <div className="relative w-fit text-sm font-semibold mb-1">
            <span className="absolute -right-2 text-primary-hover !text-error-default ">*</span>
            Email address
          </div>
          <input
            type="email"
            value={email}
            disabled
            readOnly
            onChange={(value) => setEmail(value.target.value)}
            className="p-2 w-full rounded-lg border outline-none text-sm"
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 w-full gap-3 md:gap-4">
        <Button
          className="w-full rounded-xl font-medium text-md"
          type="button"
          variant="outline"
          onClick={() => handleCloseModal(modal)}
        >
          {LABEL.cancel}
        </Button>
        <Button
          variant="default"
          className="w-full rounded-xl font-medium text-md"
          onClick={handleDownload}
          disabled={loading}
          type="button"
        >
          {loading ? <RiLoader2Line className="animate-spin" /> : LABEL.confirm}
        </Button>
      </div>
    </Box>
  );
};
export default DownloadEnrichModal;
