import { RiInformation2Line } from "@remixicon/react";
import { Box } from "components/Box";
import { Progress } from "components/ui/progress";

interface Props {
  message: JSX.Element | React.ReactNode;
  used_count: number;
  denominator: number;
}
const NoticeLimitRequest = ({ message, used_count, denominator }: Props) => {
  const value = denominator > 0 ? (used_count / denominator) * 100 : 0;
  return (
    <div className="w-full">
      <Box className="py-1 px-2 md:py-4 md:px-4 bg-infor-subtitle text-primary rounded-lg md:rounded-2xl text-xs gap-2 justify-start items-start ">
        <RiInformation2Line className="text-infor-text" size={20} />
        <div>
          {message}
          <Progress value={value} className="bg-white mt-1" color="#146BE1" max={denominator} />
        </div>
      </Box>
    </div>
  );
};

export default NoticeLimitRequest;
