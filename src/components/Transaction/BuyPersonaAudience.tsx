import { useAppDispatch, useAppSelector } from 'store';
import { useContext, useEffect, useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { getMe } from "store/redux/auth/slice";
import { yupResolver } from "@hookform/resolvers/yup";
import { RiLoader2Fill } from "@remixicon/react";
import { ModalContext } from "providers/Modal";
import { ResultCountProps } from "views/Persona/context/PersonaContext";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import InputController from "components/InputController";
import TextAreaController from "components/TextAreaController";
import ShowCreditAudience from "./ShowCreditAudience";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import { useCancelToken } from "hooks/useCancelToken";
import useShowEnoughCredit from "hooks/useShowEnoughCredit";
import useFilters from "hooks/useFilters";
import { personaAPI } from "apis/persona";
import { CreateSegmentFormBody, CreateSegmentFormType } from "types/YourData";

import handleCloseModal from "utils/handleCloseModal";
import { fNumberToString } from "utils/number";

import LABEL from "constants/label";
import { creditAPI } from "apis/credit";
import { tracking } from '../../utils/Tracking/tracking';

interface Props {
  audienceName: string;
  description: string;
  resultCount: ResultCountProps;
  isDataset?: boolean;
}
const BuyPersonaAudience = ({ resultCount, audienceName, description, isDataset }: Props) => {
  const dispatch = useAppDispatch();
  const modal = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);

  const { params } = useFilters();
  const { newCancelToken } = useCancelToken();
  const { handleShowEnoughCredit } = useShowEnoughCredit();

  const [loading, setLoading] = useState(false);
  const [credit, setCredit] = useState({
    loading: false,
    cost: 0,
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<CreateSegmentFormType>({
    resolver: yupResolver(CreateSegmentFormBody),
    defaultValues: {
      name: audienceName ? `${isDataset ? 'Dataset' : 'Audience'}: ` + audienceName : '',
      description: description,
    },
  });
  //get Credit Cost

  useEffect(() => {
    handleGetCreditCost();
  }, []);

  const handleGetCreditCost = async () => {
    setCredit((prev) => ({ ...prev, loading: true }));
    const response = await creditAPI.getPersonaCreditCost({
      params: { ...params, is_dataset: isDataset }
    });
    if (response && response.data) {
      setCredit({ loading: false, cost: response.data.credit_cost });
    }
    setCredit((prev) => ({ ...prev, loading: false }));
  };

  const handleCreateForm: SubmitHandler<CreateSegmentFormType> = async (data) => {
    setLoading(true);
    const response = await personaAPI.createPersonaAudience({
      audience_name: data.name,
      description: data.description ?? "",
      datatype: isDataset ? 'DATASET' : 'AUDIENCE',
      filters: {
        ...params,
      },
      cancelToken: newCancelToken(),
    });
    if (response && response.data) {
      tracking({
        eventName: 'purchase',
        params: {
          user_id: user?.uuid,
          valueTracking: JSON.stringify({
            audience_id: params,
            audience_type: isDataset ? 'DATASET' : 'AUDIENCE'
          })
        }
      })
      await dispatch(getMe()).unwrap();
      handleCloseModal(modal);
      toast({
        title: response.message,
        status: "success",
        duration: 3000,
      });
    }
    if (response && response.error?.code == 6005) {
      handleShowEnoughCredit();
    }
    if (response && response.error?.code == 3002) {
      toast({
        title: response.error.error,
        status: "error",
        duration: 3000,
      });
      handleCloseModal(modal);
    }
    setLoading(false);
  };
  return (
    <>
      {credit?.loading && (
        <Box className="text-center justify-center">
          <LoadingButtonIcon />
        </Box>
      )}
      {!credit.loading && (
        <form className="flex flex-col w-full gap-4" onSubmit={handleSubmit(handleCreateForm)}>
          <div className="text-lg text-primary font-semibold text-center w-full">
            {isDataset ? LABEL.buy_dataset : LABEL.buy_audience}
          </div>
          <InputController
            className="gap-2 text-sm"
            control={control}
            name="name"
            label={isDataset ? LABEL.dataset_name : LABEL.audience_name}
            placeholder={`Enter ${isDataset ? 'Dataset' : 'Audience'} Name`}
            defaultValue={audienceName ? `${isDataset ? 'Dataset' : 'Audience'}` + audienceName : ""}
            required
            error={errors.name?.message}
          />
          <TextAreaController
            maxLength={200}
            control={control}
            label={LABEL.description}
            name={"description"}
            defaultValue={description}
          />
          <Box className="p-4 bg-brand-subtitle rounded-xl text-md font-medium items-start">
            <div>
              <span className="mr-1">{isDataset ? LABEL.dataset_size : LABEL.audience_size}:</span>
              <span className="text-secondary">{fNumberToString(resultCount?.number)}</span>
            </div>
            <ShowCreditAudience cost={credit.cost} />
          </Box>
          <div className="grid grid-cols-2 gap-4 mt-5">
            <Button
              type="button"
              variant="secondary"
              className="border shadow-xs rounded-xl text-md font-medium"
              onClick={() => handleCloseModal(modal)}
            >
              {LABEL.cancel}
            </Button>
            <Button
              variant="default"
              className="rounded-xl text-md font-medium"
              type="submit"
              disabled={loading}
            >
              {loading ? <RiLoader2Fill className="animate-spin" size={14} /> : isDataset ? LABEL.buy_dataset : LABEL.buy_audience}
            </Button>
          </div>
        </form>
      )}
    </>
  );
};

export default BuyPersonaAudience;
