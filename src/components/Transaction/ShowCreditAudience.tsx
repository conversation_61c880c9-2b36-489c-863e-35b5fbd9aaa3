import { useAppSelector } from "store";
import TRANSACTION_LABEL from "constants/Transaction/label";
import { cn } from "utils/utils";
import { fNumberToString } from "utils/number";

const ShowCreditAudience = ({ cost }: { cost: number }) => {
  const { user } = useAppSelector((state) => state.auth);
  const isHasBuyAudience = user?.credit > cost;

  return (
    <div>
      <div>
        {TRANSACTION_LABEL.CREDIT.credit}
        <div className="text-error-default inline-block mr-1">{fNumberToString(cost)}</div>
        credits
      </div>
      <div>
        {TRANSACTION_LABEL.CREDIT.your_credit}
        <div
          className={cn(
            "pl-0.5 inline-block",
            isHasBuyAudience ? "text-success-subtitle" : "text-error-default"
          )}
        >
          {fNumberToString(user.credit)}
        </div>
      </div>
    </div>
  );
};

export default ShowCreditAudience;
