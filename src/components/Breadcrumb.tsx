import { Fragment, memo } from 'react';
import { NavLink } from 'react-router-dom';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from 'components/ui/breadcrumb';
import { useBreadCrumb } from 'hooks/useBreadCrumb';
import { Skeleton } from './ui/skeleton';

type Props = {
  path?: string;
  loading?: boolean;
  hiddenType?: boolean;
};

const BreadCrumb = ({ path, loading, hiddenType = false }: Props) => {
  const { items, icon, subHeader } = useBreadCrumb(path);

  if (loading) {
    return <Skeleton className="h-5" />;
  }

  return (
    <Breadcrumb className="hidden sm:block">
      <BreadcrumbList>
        {icon && (
          <BreadcrumbItem>
            <span className="w-5 h-5">{icon}</span>
            <BreadcrumbLink>{subHeader.replace(/[^a-zA-Z ]/g, ' ')}</BreadcrumbLink>
          </BreadcrumbItem>
        )}

        {items.length > 0 && icon && <BreadcrumbSeparator />}
        {items.map((item, index) => {
          const lastPath = items.length > 2 && index == items.length - 2;
          return (
            <Fragment key={index}>
              {!( lastPath && hiddenType ) && (
                <>
                  <BreadcrumbItem>
                    {index === items.length - 1 ? (
                      <>
                        {item?.icon && <span className="w-5 h-5">{item.icon}</span>}
                        <span className="capitalize cursor-pointer text-primary">
                        {item.title.replace(/-/g, " ")}
                      </span></>
                    ) : (
                      <>
                        {item?.icon && <span className="w-5 h-5">{item.icon}</span>}
                        <NavLink
                          className="capitalize text-secondary"
                          to={lastPath ? '/' + item.pathname : icon ? item.pathname : ""}
                        >
                          {decodeURIComponent(item.title.replace(/[^a-z A-Z]/g, " "))}
                        </NavLink>
                      </>
                    )}
                  </BreadcrumbItem>
                  {index < items.length - 1 && <BreadcrumbSeparator />}
                </>
              )}
            </Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default memo(BreadCrumb);
