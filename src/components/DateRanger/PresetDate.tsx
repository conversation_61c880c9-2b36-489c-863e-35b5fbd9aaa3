import { useEffect, useState } from "react";

import { Button } from "components/ui/button";

import { cn } from "utils/utils";
import { handleFormatPresetValue } from "utils/datePicker";
import { DateRange, PresetButtonProps, PRESETS_OPTIONS } from "types/DatePicker";

interface Props {
  range: DateRange;
  setRange: React.Dispatch<React.SetStateAction<DateRange>>;
}
interface IDate {
  from: Date;
  to: Date;
}

const PresetButton = ({ preset, label, isSelected, setPreset }: PresetButtonProps): JSX.Element => (
  <Button
    className={cn(
      isSelected && "pointer-events-none bg-[#ecdffb] text-[#5314a3]",
      "w-full items-center justify-start"
    )}
    variant="ghost"
    onClick={() => setPreset(preset)}
    children={label}
  />
);

const PresetDate = ({ range, setRange }: Props) => {
  const [selectedPreset, setSelectedPreset] = useState<string | undefined>(undefined);
  const setPreset = (preset: string): void => {
    const range = handleFormatPresetValue(preset);
    setRange(range);
  };
  const handlePresetValue = (): void => {
    for (const preset of PRESETS_OPTIONS) {
      const presetRange = handleFormatPresetValue(preset.name) as IDate;

      const normalizedRangeFrom = new Date(range.from);
      normalizedRangeFrom.setHours(0, 0, 0, 0);
      const normalizedPresetFrom = new Date(presetRange.from.setHours(0, 0, 0, 0));

      const normalizedRangeTo = new Date(range.to ?? 0);
      normalizedRangeTo.setHours(0, 0, 0, 0);
      const normalizedPresetTo = new Date(presetRange.to?.setHours(0, 0, 0, 0) ?? 0);

      if (
        normalizedRangeFrom.getTime() === normalizedPresetFrom.getTime() &&
        normalizedRangeTo.getTime() === normalizedPresetTo.getTime()
      ) {
        setSelectedPreset(preset.name);
        return;
      }
    }
    setSelectedPreset(undefined);
  };
  useEffect(() => {
    handlePresetValue();
  }, [range]);

  return (
    <div className="flex flex-col items-end gap-1">
      {PRESETS_OPTIONS.map((preset) => (
        <PresetButton
          key={preset.name}
          preset={preset.name}
          label={preset.label}
          isSelected={selectedPreset === preset.name}
          setPreset={setPreset}
        />
      ))}
    </div>
  );
};

export default PresetDate;
