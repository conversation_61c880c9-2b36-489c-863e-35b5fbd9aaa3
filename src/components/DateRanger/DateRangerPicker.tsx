import { useEffect, useRef, useState } from "react";
import { RiArrowDownSLine, RiCalendarLine, RiCloseLine } from "@remixicon/react";

import { Calendar } from "./Calendar";
import PresetDate from "./PresetDate";
import { Box } from "components/Box";
import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";

import useResponsive from "hooks/useResponsive";
import { DateRange, DateRangePickerProps } from "types/DatePicker";
import { cn, formatDate } from "utils/utils";
import { formatDateISOString } from "utils/datePicker";

const DateRangePicker = ({
  initialDateFrom = new Date(new Date().setHours(0, 0, 0, 0)),
  initialDateTo,
  align = "end",
  className,
  disableRemove = false,
  onChange,
}: DateRangePickerProps) => {
  const { isMobile } = useResponsive();
  const [isOpen, setIsOpen] = useState(false);
  const [range, setRange] = useState<DateRange>({
    from: initialDateFrom || "",
    to: initialDateTo ? initialDateTo : initialDateFrom,
  });

  const openedRangeRef = useRef<DateRange | undefined>();

  useEffect(() => {
    if (isOpen) {
      openedRangeRef.current = range;
    }
  }, [isOpen]);

  useEffect(() => {
    if (onChange) {
      if (range.from == "" && range.to == "") {
        onChange({ from: undefined, to: undefined });
      } else {
        onChange({
          from: formatDateISOString(range?.from),
          to: range?.to ? formatDateISOString(range?.to) : formatDateISOString(range?.from),
        });
      }
    }
  }, [range]);

  useEffect(() => {
    setRange({
      from: initialDateFrom,
      to: initialDateTo,
    });
  }, [initialDateFrom, initialDateTo]);

  const handleRemove = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setRange({ from: "", to: "" });
  };

  const DateFrom = formatDate(range?.from);
  const DateTo = range.to ? formatDate(range.to) : DateFrom;

  return (
    <Popover modal={true} open={isOpen} onOpenChange={(open: boolean) => setIsOpen(open)}>
      <PopoverTrigger asChild className="p-0 m-0">
        <Box
          className={cn(
            "flex whitespace-nowrap items-center text-custom-tertiary font-normal",
            className
          )}
        >
          {range.from !== "" && range.to !== "" && (
            <div className="relative z-10">
              <div className="text-xs text-[#5314a3] font-medium flex items-center gap-1 bg-[#ecdffb] px-1 py-0.5 rounded my-2 leading-5">
                {DateFrom + "- " + DateTo}
                {!disableRemove && (
                  <button onClick={(e) => handleRemove(e)}>
                    <RiCloseLine className="" size={14} color="#5314a3" />
                  </button>
                )}
              </div>
            </div>
          )}
          <div
            className={cn(
              " z-[1] absolute flex bg-white items-center gap-1 transform -translate-y-1/2 duration-300  ",
              isOpen || (DateFrom !== "-" && DateTo !== "-") ? "-top-1 left-1 text-xs" : "top-1/2"
            )}
          >
            <RiCalendarLine size={16} />
            <span className="!text-custom-tertiary !font-normal">Date</span>
          </div>
          {!(initialDateFrom && initialDateTo) && (
            <RiArrowDownSLine className="ml-auto" size={16} />
          )}
        </Box>
      </PopoverTrigger>
      <PopoverContent align={align} sideOffset={12} className="w-auto border-custom-primary">
        <div className="flex py-2">
          {!isMobile && <PresetDate range={range} setRange={setRange} />}
          <Box className="items-start">
            <Calendar
              mode="range"
              onSelect={(value: { from?: Date | string; to?: Date | string } | undefined) => {
                if (value?.from != null) {
                  setRange({ from: value.from, to: value?.to });
                }
              }}
              selected={range as any}
              numberOfMonths={isMobile ? 1 : 2}
              defaultMonth={
                new Date(new Date().setMonth(new Date().getMonth() - (isMobile ? 0 : 1)))
              }
            />
          </Box>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default DateRangePicker;
