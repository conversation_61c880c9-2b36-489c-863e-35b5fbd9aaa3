import { SOLIDS_COLOR } from '../../constants';
import { cn } from '../../utils/utils';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Button } from '../ui/button';
import { RiArrowDownSLine } from '@remixicon/react';
import React, { useState, useRef } from 'react';

type TColorPickerProps = {
  background: string;
  className?: string;
  setBackground: (background: string) => void;
  title?: string;
};

const ColorPicker = ({...props}: TColorPickerProps) => {
  const { background, className, setBackground, title } = props;
  const [isOpen, setIsOpen] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
  };

  const handleColorSelect = (color: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setBackground(color);
  };

  const handleTriggerClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild className="h-fit">
        <div>
          {title&&<div className="font-normal text-xs text-secondary flex gap-1 h-[18px] items-end mb-[4px]">
            {title}
          </div>}
          <Button
            ref={triggerRef}
            variant={'outline'}
            className={cn(
              'w-10 h-6 px-1 py-1.5 justify-start text-left font-normal hover:bg-transparent rounded-sm border-[#20232C]',
              !background && 'text-muted-foreground',
              className,
            )}
            type={'button'}
            onClick={handleTriggerClick}
          >
            <div
              className="min-w-3 min-h-3 flex items-center gap-2 rounded-full border border-gray-300"
              style={{ background }}
            />
            <RiArrowDownSLine size={16} color={'#515667'}/>
          </Button>
        </div>
      </PopoverTrigger>
      <PopoverContent
        ref={contentRef}
        className="w-full p-2 z-[9999] pointer-events-auto"
        align={'start'}
        onOpenAutoFocus={(e) => e.preventDefault()}
        onCloseAutoFocus={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => {
          if (triggerRef.current?.contains(e.target as Node)) {
            e.preventDefault();
          }
        }}
        onInteractOutside={(e) => {
          if (contentRef.current?.contains(e.target as Node)) {
            e.preventDefault();
          }
        }}
      >
        <div className="grid grid-cols-10 gap-1 z-[9999]">
          {SOLIDS_COLOR.map((s, index) => (
            <div
              key={`${s}-${index}`}
              style={{ background: s }}
              className={cn(
                'rounded-full h-6 w-6 cursor-pointer active:scale-105',
                s === '#FFFFFF' && 'border border-gray-300',
              )}
              onClick={(e) => handleColorSelect(s, e)}
            />
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};
export default ColorPicker;
