import { Link } from "react-router-dom";
import { cn } from "utils/utils";

interface Props {
  id: string;
  type?: string;
  name: string;
  className?: string;
}
const LinkToFacebook = ({ id, name, className }: Props) => {
  return (
    <Link target="_blank" to={`https://www.facebook.com/${id}`}>
      <h3
        className={cn(
          "text-lg hover:text-primary-hover font-semibold text-primary leading-5 line-clamp-1",
          className
        )}
      >
        {name}
      </h3>
    </Link>
  );
};

export default LinkToFacebook;
