import { GoogleIcon } from '../../assets/icons/GoogleIcon';
import { But<PERSON> } from '../ui/button';
import useRedirectURL from '../../hooks/useRedirectURL';

export const GoogleLogin = () => {
  const { paramsNoReDirect, reDirect } = useRedirectURL();

  const loginWithGoogle =()=>{
    const reDirectURL = reDirect
      ? `${reDirect}?${new URLSearchParams(paramsNoReDirect).toString()}`
      : '/';
    const endpoint = import.meta.env.REACT_APP_API_URL + '/api/v1';
    const safeRedirect = encodeURIComponent(reDirectURL);
    window.location.href = endpoint + `/auth/login/google?redirect=${safeRedirect}`;
  }
  return (
    <>
      <Button
        onClick={loginWithGoogle}
        className="text-foreground bg-white border border-normal rounded-xl shadow-xs hover:bg-white/10"
      >
        <div className="flex items-center gap-2">
          <GoogleIcon /> Login with google
        </div>
      </Button>
    </>
  );
};
