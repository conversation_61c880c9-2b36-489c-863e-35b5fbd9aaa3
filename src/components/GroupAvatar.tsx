import { cn, getLastCharName } from "utils/utils";

import React from "react";

type Props = {
  imgUrls: string[];
  className?: string;
  size?: number;
  type: "horizontal" | "group";
  itemShow?: number;
};

const mapClassName: Record<number, string> = {
  0: "top-0 left-0 z-30",
  1: "top-0 right-0 z-20",
  2: "bottom-0 left-0",
  3: "bottom-0 right-0",
};
const mapClassForTwoImg: Record<number, string> = {
  0: "top-0 left-0 z-30",
  1: "bottom-0 right-0 z-20",
};

type ItemProps = {
  url?: string;
  className?: string;
  name?: string;
  style?: React.CSSProperties;
};

const AvatarItem = ({ url, className, name, style }: ItemProps) => {
  return (
    <>
      {url ? (
        <img style={style} className={cn("", className)} src={url} alt="" />
      ) : (
        <div style={style} className={cn("", className)}>
          {getLastCharName(name!, "first")}
        </div>
      )}
    </>
  );
};

const GroupAvatar = ({ imgUrls, className, size = 30, type, itemShow = 3 }: Props) => {
  const sliceValue = imgUrls.length > itemShow ? itemShow : imgUrls.length;

  const renderGroupAvatar = () => {
    switch (imgUrls.length) {
      case 2:
        return (
          <div
            className={cn("relative flex-shrink-0", className)}
            style={{ width: size + "px", height: size + "px" }}
          >
            {imgUrls.map((url, index) => (
              <AvatarItem
                key={index}
                name="test"
                url={url}
                className={cn(
                  "w-2/3 h-2/3 absolute border-[2px] border-white object-cover rounded-md",
                  mapClassForTwoImg[index],
                  "hover:z-40 transition-all duration-300"
                )}
              />
            ))}
          </div>
        );

      default:
        return (
          <div
            className={cn("relative", className)}
            style={{ width: size + "px", height: size + "px" }}
          >
            {imgUrls.slice(0, sliceValue).map((url, index) => (
              <AvatarItem
                className={cn(
                  "w-3/5 h-3/5 absolute border-[2px] border-white object-cover rounded-full",
                  mapClassName[index],
                  "hover:z-40 transition-all duration-300"
                )}
                key={index}
                url={url}
              />
            ))}
            {imgUrls.length > sliceValue && (
              <div className="bg-slate-500 hover:z-40 transition-all duration-300 border-[2px] text-white rounded-full border-white text-xs font-medium justify-center items-center flex bottom-0 absolute right-0 w-3/5 h-3/5">
                {imgUrls.slice(3).length}
              </div>
            )}
          </div>
        );
    }
  };

  const renderHorizontalAvatar = () => {
    return (
      <div className={cn("flex items-center justify-center", className)}>
        {imgUrls.slice(0, sliceValue).map((url, index) => (
          <button key={index} className="hover:z-10 inline-block mr-[-8px] flex-shrink-0">
            <AvatarItem
              className="rounded-full relative inline-block border-[2px] border-white object-cover hover:scale-110"
              style={{ width: size + "px", height: size + "px" }}
              url={url}
              name={`Your data ${index}`}
            />
          </button>
        ))}
        {imgUrls.length > sliceValue && (
          <button
            className="bg-slate-500 hover:scale-110 flex-shrink-0 border-white border-[2px] text-xs rounded-full flex items-center justify-center text-white hover:z-10"
            style={{ width: size + "px", height: size + "px" }}
          >
            {imgUrls.slice(itemShow).length}
          </button>
        )}
      </div>
    );
  };

  return type === "group" ? renderGroupAvatar() : renderHorizontalAvatar();
};

export default GroupAvatar;
