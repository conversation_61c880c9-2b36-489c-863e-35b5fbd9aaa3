import { RiThumbUpLine, RiUserFollowLine } from "@remixicon/react";
import { Box } from "./Box";
import { Skeleton } from "./ui/skeleton";
import { fNumberToCompact } from "utils/number";
import { cn } from "utils/utils";

interface Props {
  loading: boolean;
  likeCount: number | undefined;
  followCount: number | undefined;
  className?: string;
}
interface IItemProps {
  isLike?: boolean;
  loading: boolean;
  count: number | undefined;
}

const LoadingPulse = ({ loading }: { loading: boolean }) =>
  loading && <Skeleton className="w-1/3 h-4 animate-pulse" />;

const ItemMetric = ({ loading, count, isLike = false }: IItemProps) => (
  <Box className="gap-1">
    {isLike && <RiThumbUpLine size={16} color="#515667" />}
    {!isLike && <RiUserFollowLine size={16} color="#515667" />}
    <LoadingPulse loading={loading} />
    {!loading && (
      <span
        className="text-sm text-secondary font-medium"
        children={count ? fNumberToCompact(count) : "--"}
      />
    )}
  </Box>
);
const LikeAndFollowMetrics = ({ loading, likeCount, followCount, className }: Props) => (
  <Box className={cn("justify-start w-full text-secondary p-0 m-0", className)}>
    <ItemMetric loading={loading} count={likeCount} isLike />
    <ItemMetric loading={loading} count={followCount} />
  </Box>
);
export default LikeAndFollowMetrics;
