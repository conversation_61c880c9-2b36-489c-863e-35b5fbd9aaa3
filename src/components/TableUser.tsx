import DataTable from "components/DataTable";
import { USER_PREVIEW_COLUMNS } from "constants/socialData/column";
import Pagination from "components/Pagination";
import { Fragment } from "react";

type Props = {
  rowData: any[];
  totalCount: number;
  currentPage: number;
  handleChangePage: (page: number) => void;
  loading?: boolean;
};

const TableUser: React.FC<Props> = ({
  rowData,
  totalCount,
  currentPage,
  handleChangePage,
  loading,
}) => {
  return (
    <Fragment>
      <DataTable
        loading={loading}
        column={USER_PREVIEW_COLUMNS}
        rowData={rowData}
        pagination={true}
      />
      <Pagination
        className="mt-4"
        totalCount={totalCount}
        pageSize={10}
        currentPage={currentPage}
        onPageChange={handleChangePage}
      />
    </Fragment>
  );
};

export default TableUser;
