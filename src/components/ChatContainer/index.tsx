import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Button } from '../ui/button';
import ChatAIAgent from '../ChatAIAgent';
import { RiSparklingLine } from '@remixicon/react';

export const ChatContainer = () => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button className="rounded-full w-42 h-42 bg-primary p-4">
          <RiSparklingLine />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" side={'top'} className="z-[9999] w-[350px] h-full bg-brand-subtitle border-brand-default rounded-2xl p-2 shadow-md">
        <ChatAIAgent />
      </PopoverContent>
    </Popover>
  );
};
