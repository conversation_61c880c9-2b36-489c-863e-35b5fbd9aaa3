import { useContext } from "react";
import { RiShoppingCartLine } from "@remixicon/react";
// import useFilters from "hooks/useFilters";
// import useGetPhone from "hooks/useGetPhone";
// import { useModal } from "hooks/useModal";
import { ModalContext } from "providers/Modal";
// import { counterNumberPhone } from "utils/utils";

type Props = {
  id: string;
  endpoint: string;
  phoneNumber: string;
  type: string;
};

const PhoneColumn = ({ phoneNumber, type }: Props) => {
  const modal = useContext(ModalContext);

  /**
   * FEATURE:  call func handleGetPhone để show phoneNumber, Limit show phone number = 5
   * Author: @Kienphan
   * hook use modal bị lỗi re-render trước khi app chạy
   * -> ch<PERSON><PERSON><PERSON> sang sử dụng modal context
   */

  // const { phone, handleGetPhone } = useGetPhone({
  //   defaultValue: phoneNumber,
  //   endpoint: endpoint,
  //   type: type,
  // });
  // const { searchParams } = useFilters();
  // const { onOpen } = useModal();

  // const onShowPhone = useCallback(async () => {
  //   if (Number(searchParams.get("page")) > 5 || counterNumberPhone("#phone_persona") > 4) {
  //     onOpen("BY_AUDIENCE");
  //     return;
  //   }
  //   if (!phone.phone.includes("*")) return;
  //   handleGetPhone(id);
  // }, [phone, id, endpoint, onOpen, phoneNumber, searchParams]);

  const handleOpenModalBuy = () => {
    modal?.setDataDialog((prev) => ({ ...prev, isOpen: true }));
  };

  return (
    <div
      id="phone_persona"
      className="text-center text-sm p-1 rounded-sm bg-brand-subtitle text-brand-default !font-sans flex gap-1 items-center"
    >
      <span
        className="w-20"
        //onClick={onShowPhone}
      >
        {phoneNumber}
      </span>
      {type == "persona" && <RiShoppingCartLine size={16} onClick={handleOpenModalBuy} />}
    </div>
  );
};
export default PhoneColumn;
