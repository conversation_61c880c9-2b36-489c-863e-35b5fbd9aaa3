import { fNumberToString } from "utils/number";

const AudiencesSizeColumn = ({
  size,
  disableProfile = false,
  isCheckStatus = false,
  status,
}: {
  disableProfile?: boolean;
  size: string | number;
  status?: number;
  isCheckStatus?: boolean;
}) => {
  return (
    <>
      {isCheckStatus && status === 2 && (
        <div className="text-xs lg:text-sm flex flex-col text-secondary w-full py-2">
          <span className="text-secondary font-normal text-right ">
            {size ? fNumberToString(size) : 0}
          </span>
        </div>
      )}
      {!isCheckStatus && (
        <div className="text-xs lg:text-sm flex flex-col text-secondary w-full py-2">
          <span className="text-secondary font-normal text-right ">
            {size ? fNumberToString(size) : 0}
          </span>
          {!disableProfile && <span className="text-right">Profiles</span>}
        </div>
      )}
    </>
  );
};

export default AudiencesSizeColumn;
