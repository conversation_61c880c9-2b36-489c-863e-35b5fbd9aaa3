import { useState, useEffect } from "react";
import { cn } from "utils/utils";

const formatTimeLeft = (milliseconds: number) => {
  const totalSeconds = Math.floor(milliseconds / 1000);
  const days = Math.floor(totalSeconds / 86400);
  const hours = Math.floor((totalSeconds % 86400) / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const check = (num: number) => (num < 10 ? `0${num}` : num);
  return `${check(days)} : ${check(hours)} : ${check(minutes)} : ${check(seconds)}`;
};
type Props = {
  className?: string;
  status: number;
  eta_time: string;
  callback?: () => void;
};
const CountdownTimeCol = ({ status, eta_time, className, callback }: Props) => {
  const targetDate = new Date(eta_time).getTime();
  const [timeRemaining, setTimeRemaining] = useState(targetDate - Date.now());

  useEffect(() => {
    if (timeRemaining <= 0 && status === 1) {
      callback && callback();
    }
  }, [timeRemaining, callback]);

  useEffect(() => {
    const interval = setInterval(() => {
      const newTimeRemaining = targetDate - Date.now();
      setTimeRemaining(Math.max(newTimeRemaining, 0));
    }, 1000);

    return () => clearInterval(interval);
  }, [targetDate]);

  return (
    <>
      {status === 1 && timeRemaining > 0 && (
        <div className={cn("text-md text-red-500", className)}>{formatTimeLeft(timeRemaining)}</div>
      )}
    </>
  );
};

export default CountdownTimeCol;
