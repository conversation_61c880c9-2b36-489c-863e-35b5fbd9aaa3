import { ICellRendererParams } from "ag-grid-community";

const EmailColumn: React.FC<ICellRendererParams> = (params) => {
  if (!params.value) return "-";
  return (
    <div className={"text-center rounded-sm line-clamp-1"}>
      <div className="overflow-hidden px-1.5 text-xs py-0.5 text-ellipsis max-w-[180px] bg-[#D1FAE4] text-[#166E3F] font-semibold">
        {params.value}
      </div>
    </div>
  );
};
export default EmailColumn;
