import { useContext } from "react";
import { RiArrowLeftSLine } from "@remixicon/react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Box } from "components/Box";
import useFilters from "hooks/useFilters";

const BackToYourPage = () => {
  const context = useContext(ShowDetailContext);
  const { setSearchParams } = useFilters();

  const handleClick = () => {
    setSearchParams({});
    context?.setShowDetail((prev) => ({ ...prev, isShow: false }));
  };

  return (
    <Box
      className="justify-start gap-1 text-primary cursor-pointer hover:opacity-70 mt-6 px-2 font-medium text-md"
      onClick={() => handleClick()}
    >
      <RiArrowLeftSLine size={20} />
      <span children="Go back" />
    </Box>
  );
};

export default BackToYourPage;
