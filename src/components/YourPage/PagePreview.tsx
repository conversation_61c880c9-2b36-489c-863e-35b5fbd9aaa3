import { useContext, useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { RiEarthLine, RiExternalLinkLine, RiThumbUpLine, RiUserFollowLine } from "@remixicon/react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import { Skeleton } from "components/ui/skeleton";
import { Box } from "components/Box";
import { Button } from "components/ui/button";
import AvatarByName from "components/AvatarByName";
import MetricsCompact from "components/LiveAdsPost/MetricsCompact";

import { pageAPI } from "apis/pagePost";
import { PageFollow } from "types/LiveService";
import { CARD_CLASS } from "constants/index";
import { cn } from "utils/utils";
import useGetCategoryName from "hooks/useGetCategoryName";
import useFilters from "hooks/useFilters";

interface Props {
  page_name: string;
  post_id: string;
  id: string | undefined;
  avatar: string | undefined;
  disablePreview?: boolean;
}
interface IInformation {
  data: PageFollow | undefined;
  loading: boolean;
  open: boolean;
}

const PagePreview = ({ page_name, id, post_id, avatar, disablePreview }: Props) => {
  const [information, setInformation] = useState<IInformation>({
    data: undefined,
    loading: false,
    open: false,
  });
  const categoryName = useGetCategoryName(information?.data?.category);

  useEffect(() => {
    if (information.open && id) {
      fetchData();
    }
  }, [id, information.open]);

  const fetchData = async () => {
    setInformation((prev) => ({ ...prev, loading: true }));
    if (id) {
      const res = await pageAPI.getPageById({
        id: id,
      });
      if (res && res.data) {
        setInformation((prev) => ({
          ...prev,
          data: res.data,
          loading: false,
        }));
      }
    }
    setInformation((prev) => ({ ...prev, loading: false }));
  };

  return (
    <>
      {!disablePreview && (
        <TooltipProvider delayDuration={100}>
          <Tooltip
            open={information.open}
            onOpenChange={(isOpen) => setInformation((prev) => ({ ...prev, open: isOpen }))}
          >
            <TooltipTrigger className="flex-1 ">
              <Trigger
                id={id || ""}
                page_name={page_name}
                className="hover:underline hover:decoration-primary w-fit"
              />
            </TooltipTrigger>
            <TooltipPortal>
              <TooltipContent
                side="bottom"
                sideOffset={5}
                align={"start"}
                className={cn(
                  CARD_CLASS,
                  "relative w-[350px] m-0 text-wrap overflow-hidden p-4 shadow-lg rounded-sm"
                )}
              >
                <Box className="gap-3 items-start font-normal text-sm text-secondary">
                  <AvatarByName className="w-12 h-12" urlImage={avatar} name={page_name || ""} />
                  <Box variant="col-start" className="w-full gap-2">
                    <div
                      className="text-md text-primary text-left"
                      children={information.data?.name}
                    />
                    {categoryName && <span className="font-medium">{categoryName}</span>}
                    {information?.data?.description && (
                      <div className="text-wrap w-full" children={information?.data?.description} />
                    )}
                    <Box className="gap-1 hover:text-tertiary">
                      <RiEarthLine size={20} />
                      {information.loading ? (
                        <Skeleton className="w-1/3 h-4 animate-pulse" />
                      ) : information?.data?.website && information?.data?.website.length > 0 ? (
                        <Link
                          key={information?.data?.website[0]}
                          className="w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                          to={information?.data?.website[0]}
                          children={information?.data?.website[0]}
                          target="_blank"
                        />
                      ) : (
                        <div className="text-xs hover:text-primary-hover" children="--" />
                      )}
                    </Box>
                    <MetricsCompact
                      className="text-sm text-secondary"
                      num={information?.data?.like_count || 0}
                      icon={<RiThumbUpLine size={20} />}
                      content="likes"
                      sizeNumber="sm"
                    />
                    <MetricsCompact
                      className="text-sm text-secondary"
                      num={information?.data?.follow_count || 0}
                      icon={<RiUserFollowLine size={20} />}
                      content="followers"
                      sizeNumber="sm"
                    />
                  </Box>
                </Box>
                <Box className="flex items-center mt-3 gap-3 font-medium">
                  <Link
                    className="w-1/2"
                    to={`https://www.facebook.com/${post_id}`}
                    target="_blank"
                  >
                    <Button
                      className="w-full p-2 text-md text-primary bg-custom-secondary hover:bg-tertiary rounded-sm"
                      children="View post"
                    />
                  </Link>

                  <Link className="w-1/2" target="_blank" to={`https://www.facebook.com/${id}`}>
                    <Button className="w-full p-2 gap-2 text-md text-primary bg-custom-secondary hover:bg-tertiary rounded-sm">
                      <span children="Visit" />
                      <RiExternalLinkLine size={20} />
                    </Button>
                  </Link>
                </Box>
              </TooltipContent>
            </TooltipPortal>
          </Tooltip>
        </TooltipProvider>
      )}
      {disablePreview && id && <Trigger id={id} page_name={page_name} />}
    </>
  );
};

export default PagePreview;

interface TriggerProps {
  page_name: string;
  id: string;
  className?: string;
}
const Trigger = ({ page_name, id, className }: TriggerProps) => {
  const context = useContext(ShowDetailContext);
  const { setSearchParams } = useFilters();

  const handleClick = () => {
    setSearchParams({});
    context?.setShowDetail({ isShow: true, id: id });
  };
  return (
    <div
      className={cn(
        "text-md w-fit font-semibold text-primary flex-1 text-left line-clamp-2 justify-start",
        className
      )}
      children={page_name}
      onClick={() => handleClick()}
    />
  );
};
