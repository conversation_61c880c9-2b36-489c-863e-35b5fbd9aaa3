import { Box } from "components/Box";
import { Skeleton } from "components/ui/skeleton";

const SkeletonPage = () => (
  <Box className="text-primary p-4 mt-0 rounded-2xl shadow-sm border-none  w-full items-start gap-4">
    <Skeleton className="w-20 h-20 rounded-full" />
    <Box variant="col-start" className="gap-1">
      <Skeleton className="w-[300px] h-8 rounded-md" />
      <Skeleton className="w-[500px] h-8 rounded-md" />

      <Box variant="default" className="flex-wrap lg:flex-nowrap l mt-4 gap-4">
        <Skeleton className="w-[300px] h-8 rounded-md" />
        <Skeleton className="w-[200px] h-8 rounded-md" />
        <Skeleton className="w-[200px] h-8 rounded-md" />
      </Box>
    </Box>
  </Box>
);

export default SkeletonPage;
