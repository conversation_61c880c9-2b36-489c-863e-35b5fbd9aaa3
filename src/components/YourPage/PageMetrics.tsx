import { Box } from "components/Box";
import MetricItem from "components/LiveAdsPost/MetricItem";
import { ILivePostItem } from "types/CrawlService";
import { fNumberToString } from "utils/number";
import { cn } from "utils/utils";

interface IPageMetricsProps
  extends Partial<
    Pick<ILivePostItem, "post_id" | "reaction_count" | "share_count" | "comment_count">
  > {
  className?: string;
}

const PageMetrics = ({
  className,
  comment_count,
  reaction_count,
  share_count,
}: IPageMetricsProps) => {
  return (
    <>
      <Box className={cn("w-full justify-between", className)}>
        <MetricItem
          title="Like:"
          content={reaction_count ? fNumberToString(reaction_count) : "--"}
          type="like"
          className="justify-left"
        />
        <MetricItem
          title="Comment:"
          content={comment_count ? fNumberToString(comment_count) : "--"}
          type="comment"
          className="justify-center"
        />
        <MetricItem
          title="Share:"
          content={share_count ? fNumberToString(share_count) : "--"}
          type="share"
          className="justify-end"
        />
      </Box>
    </>
  );
};

export default PageMetrics;
