import { useContext, useEffect, useState } from "react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import AvatarByName from "components/AvatarByName";

import { pageAPI } from "apis/pagePost";
import { PageFollow } from "types/LiveService";
import { cn } from "utils/utils";
import useFilters from "hooks/useFilters";
import useGetCategoryName from "hooks/useGetCategoryName";

interface Props {
  avatar: string;
  className?: string;
  id: string;
}
const PageInformation = (props: Props) => {
  const [data, setData] = useState<PageFollow>();

  const context = useContext(ShowDetailContext);
  const { setSearchParams } = useFilters();
  const categoryName = useGetCategoryName(data?.category);

  useEffect(() => {
    getPageInformation();
  }, []);

  const getPageInformation = async () => {
    const response: any = await pageAPI.getPageById({ id: props.id });
    if (response && response.data) {
      setData(response.data);
    }
  };

  return (
    <Box
      className={cn(
        "p-3 shadow-medium rounded-2xl gap-4 relative items-start w-full flex-col sm:flex-row md:p-4",
        props.className,
        !categoryName && !data?.description && "items-center"
      )}
    >
      <div className="flex gap-4 items-start">
        <AvatarByName
          urlImage={props.avatar}
          name={data?.name || ""}
          className="w-20 h-20 rounded-full"
        />
        <Box variant="col-start" className="gap-2 flex-1 w-auto">
          <h3 className="text-primary text-[20px] font-semibold leading-5" children={data?.name} />
          {categoryName && (
            <span className="text-sm text-secondary font-medium" children={categoryName} />
          )}
          {data?.description && (
            <span
              className="text-sm text-secondary font-normal line-clamp-2"
              children={data?.description}
            />
          )}
        </Box>
      </div>
      <Button
        className="p-3 font-semibold text-sm rounded-xl w-full sm:w-fit"
        variant="default"
        children="View Brand"
        onClick={() => {
          setSearchParams({});
          context?.setShowDetail({ isShow: true, id: data?.page_id || "" });
        }}
      />
    </Box>
  );
};

export default PageInformation;
