import { useContext, useEffect, useState } from "react";
import { Link } from "react-router-dom";
import {
  RiArrowDownSLine,
  RiExternalLinkLine,
  RiLoopLeftLine,
  RiThumbUpLine,
  RiUserFollowLine,
} from "@remixicon/react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Box } from "components/Box";
import { Card, CardDescription, CardTitle } from "components/ui/card";
import AvatarByName from "components/AvatarByName";
import MetricsCompact from "components/LiveAdsPost/MetricsCompact";
import SkeletonPage from "./SkeletonPage";
import ButtonUnFollow from "../../views/YourPageView/components/ButtonCTA/ButtonUnFollow";
import ButtonUpdate from "../../views/YourPageView/components/ButtonCTA/ButtonUpdate";
import ButtonFollow from "../../views/YourPageView/components/ButtonCTA/ButtonFollow";
import SearchAndFilter from "views/LivePostView/components/SearchAndFilter/SearchAndFilter";

import { pageAPI } from "apis/pagePost";
import useAvatar from "hooks/useAvatar";
import useGetCategoryName from "hooks/useGetCategoryName";

import { IFilterOptions } from "types/PostService";
import { PageFollow } from "types/LiveService";
import checkDateExp from "utils/LiveAdsPost/checkDateExp";
import { Button } from "components/ui/button";
import { cn } from "utils/utils";

const PageInfoAndFilter = (props: IFilterOptions) => {
  const [data, setData] = useState<PageFollow>();
  const [loading, setLoading] = useState<boolean>(false);
  const [showMore, setShowMore] = useState<boolean>(false);

  const context = useContext(ShowDetailContext);
  const id = context?.showDetail?.id;

  const { avatar } = useAvatar({ type: "page", uid: data?.page_id || "" });
  const categoryName = useGetCategoryName(data?.category);

  useEffect(() => {
    getPageDetail();
  }, [id]);

  const getPageDetail = async () => {
    setLoading(true);
    const response = await pageAPI.getPageById({ id: id || "" });
    if (response && response.data) {
      setData(response.data);
    }
    setLoading(false);
  };

  const handleUpdateFollow = (follow: boolean) => {
    setData((prev) => {
      if (!prev) return prev;
      return { ...prev, is_following: follow };
    });
  };

  const isExp = checkDateExp(data?.expired_date_follow);
  const toggleShowMore = () => setShowMore((prev) => !prev);

  return loading ? (
    <>
      <SkeletonPage />
    </>
  ) : (
    data && (
      <Card className="text-primary p-4 mt-0 rounded-2xl shadow-sm border-none gap-0 w-full">
        <Box
          variant="row-start"
          className="justify-start flex-1 gap-4 items-start flex flex-col sm:flex-row relative"
        >
          <AvatarByName urlImage={avatar.url} name="Page Name" className="w-20 h-20 rounded-full" />
          <Box variant="col-start" className="gap-3 flex-1 font-medium text-sm">
            <Box>
              <CardTitle className="flex flex-row gap-2 cursor-pointer items-center">
                <div className="text-primary font-semibold text-[20px]" children={data?.name} />
                <Link
                  target="_blank"
                  to={`https://www.facebook.com/${data?.page_id}`}
                  children={
                    <RiExternalLinkLine
                      className="text-primary hover:text-infor-primary"
                      size={20}
                    />
                  }
                />
              </CardTitle>
            </Box>
            {categoryName && <div className="font-medium text-secondary" children={categoryName} />}
            {data?.description && (
              <CardDescription>
                <div
                  className=" text-secondary font-normal line-clamp-3"
                  children={data?.description}
                />
              </CardDescription>
            )}
            {data?.page_id && (
              <div className="text-secondary" children={"Social ID: " + data?.page_id} />
            )}
            <Box className="gap-6">
              <MetricsCompact
                className="text-sm text-secondary"
                num={data.like_count || 0}
                icon={<RiThumbUpLine size={16} />}
                sizeNumber="sm"
                content="likes"
              />
              <MetricsCompact
                className="text-sm text-secondary"
                num={data.follow_count || 0}
                icon={<RiUserFollowLine size={16} />}
                sizeNumber="sm"
                content="followers"
              />
            </Box>
          </Box>
          {data?.is_following ? (
            <Box className="gap-2">
              <ButtonUnFollow id={data?.page_id} callBack={handleUpdateFollow} />
              <ButtonUpdate
                trigger={<RiLoopLeftLine size={20} />}
                isExp={isExp}
                id={data?.page_id}
                className="rounded-xl p-0 w-10"
                setExpDate={setData}
              />
            </Box>
          ) : (
            <>
              <ButtonFollow id={data?.page_id} callBack={handleUpdateFollow} />
            </>
          )}
          <Button
            variant={"ghost"}
            size={"icon"}
            onClick={toggleShowMore}
            className="absolute top-0 right-0 sm:hidden"
          >
            <RiArrowDownSLine size={20} className={cn("duration-300", showMore && "rotate-180")} />
          </Button>
        </Box>

        {/* search and filter in detail */}
        <SearchAndFilter
          {...props}
          typeShow="detail"
          className={cn(!showMore && "hidden", "sm:block")}
        />
      </Card>
    )
  );
};

export default PageInfoAndFilter;
