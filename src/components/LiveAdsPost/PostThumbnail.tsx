import { useRef } from "react";
import Slider from "react-slick";

import { Card } from "components/ui/card";
import { Box } from "components/Box";
import { Button } from "components/ui/button";
import CustomNextArrow from "components/Slider/CustomNextArrow";
import CustomPrevArrow from "components/Slider/CustomPrevArrow";
import PlayIcon from "assets/icons/PlayIcon";

import { IAttachment } from "types/CrawlService";
import { cn } from "utils/utils";
import { FORMAT_TYPE_LABEL } from "constants/postComment";

interface Props {
  title: string;
  att_type: string;
  attachments: IAttachment[];
  slideToShow?: number;
  className?: string;
  classSlider?: string;
}

const img_default = "https://cdn.adslibrary.ai/images/6e888ad1ac401ea5.png";

const PostThumbnail = ({
  att_type,
  title,
  attachments,
  className,
  slideToShow = 1,
  classSlider,
}: Props) => {
  const refVideo: any = useRef(null);
  switch (att_type) {
    case FORMAT_TYPE_LABEL.VIDEO:
      return (
        <div className="relative flex-1">
          <video
            playsInline
            ref={refVideo}
            muted
            className={cn("rounded-xl w-full h-full", className)}
            poster={attachments && attachments[0].url ? attachments[0].url : img_default}
          >
            <source src="#" type="video/mp4" />
          </video>
          <div
            className={cn(
              "absolute w-full h-full z-[2] top-0 left-0 flex items-center justify-center cursor-pointer"
            )}
            children={<PlayIcon />}
          />
        </div>
      );
    case FORMAT_TYPE_LABEL.MULTI_IMAGES:
    case FORMAT_TYPE_LABEL.CAROUSEL:
      return (
        attachments &&
        attachments.length > 0 && (
          <Slider
            infinite={false}
            arrows={true}
            dots={false}
            slidesToShow={slideToShow}
            slidesToScroll={1}
            centerPadding={"5px"}
            nextArrow={<CustomNextArrow />}
            prevArrow={<CustomPrevArrow />}
            swipeToSlide={true}
            className={classSlider}
            // className="post-carousel"
          >
            {attachments.map((attachment) => {
              const { caption, title } = attachment.data;
              return (
                <Card
                  key={title}
                  className="rounded-xl border-none !shadow-sm bg-custom-primary max-h-[343px] sm:max-h-[650px]"
                >
                  <img
                    src={attachment.url}
                    alt={title}
                    className={cn("h-[320px] w-full object-cover rounded-xl", className)}
                  />
                  {att_type == FORMAT_TYPE_LABEL.CAROUSEL && (
                    <Box
                      variant="col-start"
                      className="justify-start items-start gap-3 flex-1 p-4 text-primary"
                    >
                      <div className="text-xs text-tertiary">{caption}</div>
                      <Box className="text-md items-start justify-between w-full text-secondary font-medium">
                        {title?.trim() != "" && (
                          <span className="flex-1 line-clamp-2">{title}</span>
                        )}
                        <Button
                          size="sm"
                          variant="secondary"
                          className="text-primary font-semibold bg-custom-secondary px-2 py-1 border-none hover:bg-custom-secondary"
                          children="Liên hệ"
                        />
                      </Box>
                    </Box>
                  )}
                </Card>
              );
            })}
          </Slider>
        )
      );
    case FORMAT_TYPE_LABEL.IMAGE:
      const imageSrc =
        attachments.length > 0 && !!attachments[0] ? attachments[0].url : img_default;
      return <img className={cn("rounded-xl", className)} src={imageSrc} alt={title} />;
    default:
      return <img className={cn("rounded-xl", className)} src={img_default} alt={title} />;
  }
};

export default PostThumbnail;
