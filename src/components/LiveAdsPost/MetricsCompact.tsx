import { Box } from "components/Box";
import { fNumberToCompact } from "utils/number";
import { cn } from "utils/utils";

interface Props {
  className?: string;
  num: number;
  content?: string;
  icon?: JSX.Element | React.ReactNode;
  sizeNumber?: "xs" | "sm" | "md" | "lg";
}
const MetricsCompact = ({ className, num, content, icon, sizeNumber = "md" }: Props) => (
  <Box className="gap-1">
    {icon}
    {/* if follow/like count null => show text default : "--"  */}
    {num == 0 && "--"}
    {num !== 0 && (
      <>
        <span
          className={cn("text-secondary", `text-${sizeNumber}`)}
          children={fNumberToCompact(num)}
        />
        <span children={content} className={className} />
      </>
    )}
  </Box>
);

export default MetricsCompact;
