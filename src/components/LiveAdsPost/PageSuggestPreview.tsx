import { useContext } from "react";
import { RiThumbUpLine, RiUserFollowLine } from "@remixicon/react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Box } from "components/Box";
import AvatarByName from "components/AvatarByName";
import MetricsCompact from "components/LiveAdsPost/MetricsCompact";

import useAvatar from "hooks/useAvatar";
import useFilters from "hooks/useFilters";
import useGetCategoryName from "hooks/useGetCategoryName";
import { PageSuggest } from "types/LiveService";
import { cn } from "utils/utils";
import useResponsive from "hooks/useResponsive";

const PageSuggestPreview = (props: PageSuggest) => {
  const { name = "", page_id = "", category, like_count, follow_count } = props;

  const { setSearchParams } = useFilters();
  const { avatar } = useAvatar({ type: "page", uid: page_id });
  const context = useContext(ShowDetailContext);
  const categoryName = useGetCategoryName(category || "");
  const { isMobile } = useResponsive();

  return (
    <Box
      className={cn(
        "gap-3 flex-1 w-full",
        props.isSearchModal ? "items-center p-2" : "items-start"
      )}
    >
      <AvatarByName
        urlImage={avatar.url}
        name={name}
        className={cn("w-12 h-12 md:w-20 md:h-20 rounded-full", props.isSearchModal && "w-12 h-12")}
      />
      <Box variant="col-start" className="gap-3 w-full flex-1">
        <div className="flex flex-col items-start">
          {page_id && (
            <Box className="gap-1 items-center">
              <h3
                className={cn(
                  "text-primary hover:text-tertiary text-sm md:text-lg cursor-pointer font-semibold md:font-medium",
                  props.isSearchModal && "font-normal"
                )}
                children={name}
                onClick={() => {
                  setSearchParams({});
                  context?.setShowDetail({ isShow: true, id: page_id });
                }}
              />
            </Box>
          )}
          {categoryName && (
            <span className="text-sm font-semibold text-custom-tertiary">{categoryName}</span>
          )}
        </div>
        {(like_count || follow_count) && (
          <Box className="text-secondary text-xs md:text-sm gap-2 font-normal md:font-semibold">
            {like_count && (
              <MetricsCompact
                num={like_count}
                icon={<RiThumbUpLine size={16} />}
                content="likes"
                className="text-secondary ![&_span]:text-sm"
                sizeNumber={isMobile ? "xs" : "sm"}
              />
            )}
            {follow_count && (
              <MetricsCompact
                num={follow_count}
                icon={<RiUserFollowLine size={16} />}
                content="followers"
                className="text-secondary ![&_span]:text-sm"
                sizeNumber={isMobile ? "xs" : "sm"}
              />
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default PageSuggestPreview;
