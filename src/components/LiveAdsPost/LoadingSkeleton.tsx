import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "components/ui/card";
import { Skeleton } from "components/ui/skeleton";

import { cn } from "utils/utils";
import { CARD_CLASS } from "constants/index";

const LoadingSkeleton = () => {
  return (
    <Card
      className={cn(
        CARD_CLASS,
        "h-fit flex flex-col justify-between gap-[14px] p-[22px_24px_24px_24px] w-full mt-0"
      )}
    >
      <CardHeader className="p-0">
        <CardTitle className="flex items-center justify-between gap-2 mb-3">
          <Skeleton className="w-10 h-10 rounded-full" />
          <Skeleton className="h-[30px] w-2/3 " />
          <Skeleton className="h-[30px] w-[30px] " />
        </CardTitle>
        <div className="flex items-center gap-1">
          <Skeleton className="w-4 h-4 rounded-full" />
          <Skeleton className="h-5 w-2/3" />
        </div>
      </CardHeader>
      <CardContent className="flex-1 p-0">
        <Skeleton className="w-full h-[300px]" />
        <Skeleton className="mt-[14px] line-clamp-3  w-full" />
      </CardContent>
      <CardFooter className="h-fit flex justify-between p-0">
        <Skeleton className="w-full h-12 rounded-md" />
      </CardFooter>
    </Card>
  );
};

export default LoadingSkeleton;
