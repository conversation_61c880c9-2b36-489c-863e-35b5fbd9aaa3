import { PostCommentLabel } from "constants/postComment/label";

const EmptyData = ({ onReset }: { onReset?: () => void }) => {
  return (
    <div className="flex-1 mt-6 items-center justify-center flex flex-col">
      <svg
        className="animate-pulse"
        width="248"
        height="247"
        viewBox="0 0 248 247"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g opacity="0.48">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M43.7929 177.152L46.8013 180.149L49.8194 177.144L51.109 178.428L48.0904 181.433L51.1007 184.429L49.8075 185.716L46.797 182.72L43.7811 185.725L42.4915 184.441L45.5079 181.437L42.4998 178.44L43.7929 177.152Z"
            fill="url(#paint0_linear_21_6647)"
          />
          <path
            opacity="0.48"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M180.292 26.2799L183.3 29.2764L186.318 26.2716L187.608 27.5557L184.589 30.56L187.6 33.5563L186.307 34.8439L183.296 31.8478L180.28 34.8521L178.991 33.5681L182.007 30.5643L178.999 27.5675L180.292 26.2799Z"
            fill="#919EAB"
          />
          <path
            opacity="0.72"
            d="M127.055 215.786C127.621 215.224 128.388 214.908 129.187 214.908C129.987 214.908 130.754 215.224 131.32 215.786L133.467 217.924C133.748 218.203 133.973 218.536 134.126 218.9C134.278 219.266 134.357 219.658 134.357 220.054C134.357 220.45 134.278 220.842 134.126 221.207C133.973 221.572 133.75 221.903 133.469 222.182L131.32 224.321C130.754 224.884 129.987 225.199 129.187 225.199C128.388 225.199 127.621 224.884 127.055 224.321L124.908 222.183C124.626 221.905 124.402 221.572 124.249 221.207C124.097 220.841 124.018 220.449 124.018 220.054C124.018 219.658 124.097 219.266 124.249 218.9C124.402 218.536 124.625 218.204 124.906 217.926L127.055 215.786Z"
            fill="#919EAB"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M199.842 80.3586C199.842 80.3587 199.842 80.3585 199.842 80.3586L197.694 82.4973L196.787 81.5938L197.691 82.5001C197.649 82.5416 197.616 82.591 197.593 82.6455C197.571 82.6999 197.559 82.7582 197.559 82.8171C197.559 82.8761 197.571 82.9344 197.593 82.9888C197.616 83.0432 197.649 83.0927 197.691 83.1342L197.694 83.1369L199.842 85.2756C199.842 85.2755 199.842 85.2758 199.842 85.2756C199.927 85.3599 200.043 85.4076 200.163 85.4076C200.283 85.4076 200.398 85.3603 200.483 85.2761C200.483 85.2763 200.483 85.276 200.483 85.2761L202.634 83.1342C202.676 83.0927 202.709 83.0432 202.732 82.9888C202.755 82.9342 202.766 82.8759 202.766 82.8171C202.766 82.7584 202.755 82.7001 202.732 82.6455C202.709 82.591 202.676 82.5416 202.634 82.5001L202.631 82.4973L200.483 80.3586C200.483 80.3585 200.483 80.3588 200.483 80.3586C200.398 80.2744 200.283 80.2266 200.163 80.2266C200.043 80.2266 199.927 80.2743 199.842 80.3586ZM195.881 80.689L198.03 78.5493C198.596 77.9871 199.363 77.6713 200.163 77.6713C200.962 77.6713 201.729 77.9871 202.295 78.5493L202.296 78.5504L204.442 80.6874C204.443 80.6879 204.443 80.6884 204.444 80.6889C204.725 80.9677 204.948 81.299 205.101 81.6638C205.253 82.0291 205.332 82.4211 205.332 82.8171C205.332 83.2131 205.253 83.6051 205.101 83.9705C204.948 84.3352 204.725 84.6665 204.444 84.9453C204.443 84.9458 204.443 84.9463 204.442 84.9468L202.295 87.085C201.729 87.6472 200.962 87.963 200.163 87.963C199.363 87.963 198.596 87.6472 198.03 87.085L195.883 84.9468C195.882 84.9463 195.882 84.9458 195.881 84.9452C195.6 84.6665 195.377 84.3352 195.224 83.9705C195.072 83.6049 194.993 83.2129 194.993 82.8171C194.993 82.4213 195.072 82.0293 195.224 81.6638C195.377 81.299 195.6 80.9677 195.881 80.689Z"
            fill="#919EAB"
          />
          <path
            opacity="0.48"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M216.074 136.503C216.383 136.424 216.707 136.422 217.017 136.499C217.357 136.583 217.666 136.758 217.913 137.003C218.16 137.249 218.335 137.557 218.419 137.895C218.496 138.204 218.494 138.527 218.414 138.834L217.225 145.197C217.128 145.697 216.812 146.416 216.012 146.661C215.211 146.906 214.545 146.487 214.184 146.128L208.752 140.719C208.384 140.355 207.964 139.689 208.213 138.891C208.461 138.093 209.185 137.78 209.693 137.686C209.693 137.686 209.694 137.686 209.695 137.686L216.074 136.503ZM215.859 139.049L211.402 139.875L215.029 143.486L215.859 139.049Z"
            fill="#919EAB"
          />
          <path
            opacity="0.8"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M211.751 194.031C212.059 193.952 212.383 193.95 212.693 194.027C213.033 194.11 213.342 194.285 213.589 194.531C213.836 194.777 214.011 195.085 214.095 195.422C214.172 195.732 214.17 196.054 214.091 196.362L212.901 202.725C212.804 203.224 212.489 203.944 211.688 204.189C210.887 204.434 210.221 204.015 209.86 203.655L204.428 198.246C204.06 197.883 203.64 197.217 203.889 196.418C204.137 195.621 204.861 195.307 205.369 195.214C205.37 195.214 205.37 195.214 205.371 195.214L211.751 194.031ZM211.535 196.576L207.078 197.402L210.705 201.014L211.535 196.576Z"
            fill="#637381"
          />
          <path
            opacity="0.8"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M25.8341 149.173C25.4583 149.173 25.0909 149.284 24.7783 149.492C24.4656 149.7 24.2222 149.996 24.0785 150.341C23.9348 150.687 23.8972 151.067 23.9705 151.434C24.0437 151.801 24.2246 152.138 24.4903 152.402C24.7559 152.667 25.0944 152.847 25.4633 152.92C25.8324 152.993 26.2145 152.956 26.5614 152.813C26.9084 152.67 27.2052 152.427 27.4141 152.116C27.6228 151.805 27.7342 151.439 27.7342 151.065C27.7342 150.563 27.5341 150.082 27.1778 149.728C26.8216 149.373 26.3382 149.173 25.8341 149.173ZM22.9633 146.786C23.813 146.221 24.812 145.919 25.8341 145.919C27.2044 145.919 28.5188 146.461 29.488 147.426C30.4571 148.391 31.0016 149.7 31.0016 151.065C31.0016 152.083 30.6985 153.078 30.1307 153.924C29.5629 154.77 28.7559 155.43 27.8115 155.819C26.8669 156.209 25.8279 156.31 24.826 156.112C23.8239 155.913 22.9031 155.424 22.1802 154.704C21.4575 153.984 20.9652 153.067 20.7658 152.069C20.5664 151.071 20.6687 150.036 21.0599 149.096C21.4512 148.155 22.1137 147.352 22.9633 146.786Z"
            fill="#637381"
          />
          <path
            opacity="0.8"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M112.721 37.877C112.345 37.877 111.978 37.988 111.665 38.196C111.352 38.404 111.109 38.6993 110.965 39.0447C110.822 39.3903 110.784 39.7707 110.857 40.1376C110.93 40.5044 111.111 40.8414 111.377 41.106C111.643 41.3704 111.981 41.5506 112.35 41.6237C112.719 41.6968 113.101 41.6593 113.448 41.5162C113.795 41.3731 114.092 41.1305 114.301 40.8193C114.51 40.5082 114.621 40.1426 114.621 39.7686C114.621 39.267 114.421 38.7858 114.065 38.4311C113.708 38.0764 113.225 37.877 112.721 37.877ZM109.85 35.4899C110.7 34.9246 111.699 34.6227 112.721 34.6227C114.091 34.6227 115.405 35.1646 116.375 36.1297C117.344 37.0947 117.888 38.4037 117.888 39.7686C117.888 40.7864 117.585 41.7813 117.017 42.6276C116.45 43.4736 115.643 44.1334 114.698 44.5229C113.754 44.9125 112.715 45.0139 111.713 44.8155C110.711 44.617 109.79 44.1272 109.067 43.4074C108.344 42.6878 107.852 41.7709 107.653 40.7726C107.453 39.7744 107.555 38.7394 107.947 37.7991C108.338 36.8586 109 36.0552 109.85 35.4899Z"
            fill="#637381"
          />
        </g>
        <g opacity="0.48">
          <path
            d="M57.8662 101.348H190.133V160.942C190.133 169.603 183.083 176.624 174.387 176.624H73.6122C64.9159 176.624 57.8662 169.603 57.8662 160.942V101.348Z"
            fill="url(#paint1_linear_21_6647)"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M78.3356 80.9611C75.69 80.9611 73.6118 83.0309 73.6118 85.6659C73.6118 88.3002 75.69 90.3706 78.3356 90.3706C80.9813 90.3706 83.0594 88.3002 83.0594 85.6659C83.0594 83.0309 80.9813 80.9611 78.3356 80.9611Z"
            fill="#C4CDD5"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M93.2943 80.9611C90.6487 80.9611 88.5705 83.0309 88.5705 85.6659C88.5705 88.3002 90.6487 90.3706 93.2943 90.3706C95.9399 90.3706 98.0181 88.3002 98.0181 85.6659C98.0181 83.0309 95.9399 80.9611 93.2943 80.9611Z"
            fill="#C4CDD5"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M108.253 80.9611C105.607 80.9611 103.529 83.0309 103.529 85.6659C103.529 88.3002 105.607 90.3706 108.253 90.3706C110.899 90.3706 112.977 88.3002 112.977 85.6659C112.977 83.0309 110.899 80.9611 108.253 80.9611Z"
            fill="#C4CDD5"
          />
          <g filter="url(#filter0_di_21_6647)">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M190.133 82.5296V101.348H57.8662L57.8662 82.5308C57.8662 75.6013 63.5065 69.9833 70.464 69.9833H177.536C184.493 69.9833 190.133 75.6006 190.133 82.5296ZM78.336 80.9611C75.6904 80.9611 73.6122 83.0309 73.6122 85.6658C73.6122 88.3002 75.6904 90.3706 78.336 90.3706C80.9816 90.3706 83.0598 88.3002 83.0598 85.6658C83.0598 83.0309 80.9816 80.9611 78.336 80.9611ZM93.2947 80.9611C90.6491 80.9611 88.5709 83.0309 88.5709 85.6658C88.5709 88.3002 90.6491 90.3706 93.2947 90.3706C95.9403 90.3706 98.0185 88.3002 98.0185 85.6658C98.0185 83.0309 95.9403 80.9611 93.2947 80.9611ZM103.53 85.6658C103.53 83.0309 105.608 80.9611 108.253 80.9611C110.899 80.9611 112.977 83.0309 112.977 85.6658C112.977 88.3002 110.899 90.3706 108.253 90.3706C105.608 90.3706 103.53 88.3002 103.53 85.6658Z"
              fill="#919EAB"
            />
          </g>
          <path
            opacity="0.48"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M170.139 111.274H131.69C129.564 111.274 127.845 112.984 127.845 115.097V160.975C127.845 163.088 129.564 164.798 131.69 164.798H170.139C172.264 164.798 173.984 163.088 173.984 160.975V115.097C173.984 112.984 172.264 111.274 170.139 111.274Z"
            fill="#637381"
          />
          <path
            opacity="0.24"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M77.8606 118.92H93.2404C95.3656 118.92 97.0853 117.21 97.0853 115.097C97.0853 112.984 95.3656 111.274 93.2404 111.274H77.8606C75.7353 111.274 74.0156 112.984 74.0156 115.097C74.0156 117.21 75.7353 118.92 77.8606 118.92ZM74.0156 130.389C74.0156 128.276 75.7353 126.566 77.8606 126.566H116.31C118.435 126.566 120.155 128.276 120.155 130.389C120.155 132.503 118.435 134.213 116.31 134.213H77.8606C75.7353 134.213 74.0156 132.503 74.0156 130.389ZM77.8606 141.859C75.7353 141.859 74.0156 143.569 74.0156 145.682C74.0156 147.795 75.7353 149.505 77.8606 149.505H116.31C118.435 149.505 120.155 147.795 120.155 145.682C120.155 143.569 118.435 141.859 116.31 141.859H77.8606ZM74.0156 160.975C74.0156 158.862 75.7353 157.152 77.8606 157.152H116.31C118.435 157.152 120.155 158.862 120.155 160.975C120.155 163.088 118.435 164.798 116.31 164.798H77.8606C75.7353 164.798 74.0156 163.088 74.0156 160.975Z"
            fill="#637381"
          />
        </g>
        <defs>
          <filter
            id="filter0_di_21_6647"
            x="49.8662"
            y="61.9833"
            width="164.266"
            height="63.365"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="8" dy="8" />
            <feGaussianBlur stdDeviation="8" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.270588 0 0 0 0 0.309804 0 0 0 0 0.356863 0 0 0 0.16 0"
            />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_21_6647" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_21_6647"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="-2" />
            <feGaussianBlur stdDeviation="2" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.270588 0 0 0 0 0.309804 0 0 0 0 0.356863 0 0 0 0.48 0"
            />
            <feBlend mode="normal" in2="shape" result="effect2_innerShadow_21_6647" />
          </filter>
          <linearGradient
            id="paint0_linear_21_6647"
            x1="43.9778"
            y1="185.74"
            x2="52.4914"
            y2="182.389"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="white" stopOpacity="0.16" />
            <stop offset="1" stopColor="white" stopOpacity="0.64" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_21_6647"
            x1="59.5057"
            y1="146.719"
            x2="139.903"
            y2="208.259"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="white" stopOpacity="0.16" />
            <stop offset="1" stopColor="white" stopOpacity="0.64" />
          </linearGradient>
        </defs>
      </svg>
      <div className="w-[286px] flex flex-col justify-center text-center">
        <span className="font-semibold text-base">{PostCommentLabel.empty_data.title}</span>
        <span className="text-secondary text-sm mt-2.5">
          {PostCommentLabel.empty_data.description}
        </span>
        <button
          className="bg-[#924FE8] py-2.5 px-3 rounded-full text-white text-sm font-medium mt-[15px]"
          onClick={onReset}
        >
          {PostCommentLabel.empty_data.button}
        </button>
      </div>
    </div>
  );
};
export default EmptyData;
