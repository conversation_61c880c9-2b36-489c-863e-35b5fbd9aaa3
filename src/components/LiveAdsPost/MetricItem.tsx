import { RiChat3Line, RiShareForwardLine, RiThumbUpLine } from "@remixicon/react";
import { Box } from "components/Box";
import PlatformFormat from "./Platforms";
import { cn } from "utils/utils";

interface IMetricItem {
  title: string;
  content: string | string[];
  type?: "like" | "comment" | "share";
  isPlatform?: boolean;
  className?: string;
}

const MetricItem = ({ title, content, type, isPlatform = false, className }: IMetricItem) => {
  return (
    <Box className={cn("gap-1 justify-start w-full", className)}>
      <span className="text-sm flex items-center gap-1 font-medium">
        {type === "like" && <RiThumbUpLine size={16} />}
        {type === "comment" && <RiChat3Line size={16} />}
        {type === "share" && <RiShareForwardLine size={16} />}
        {title}
      </span>
      <span className="capitalize">
        {isPlatform ? <PlatformFormat platforms={content as string[]} /> : content ? content : "--"}
      </span>
    </Box>
  );
};

export default MetricItem;
