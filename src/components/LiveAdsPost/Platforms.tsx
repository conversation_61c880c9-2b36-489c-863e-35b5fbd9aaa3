import AudienceNetwork from "assets/SocialIcon/AudienceNetwork";
import Facebook from "assets/SocialIcon/Facebook";
import Instagram from "assets/SocialIcon/Instagram";
import Messenger from "assets/SocialIcon/Messenger";
import { Box } from "components/Box";
import { PLATFORM } from "types/CrawlService";

const formatPlatform = (platform: string) => {
  switch (platform) {
    case PLATFORM.MESSENGER:
      return <Messenger />;
    case PLATFORM.INSTAGRAM:
      return <Instagram />;
    case PLATFORM.AUDIENCE_NETWORK:
      return <AudienceNetwork />;
    default:
      return <Facebook />;
  }
};
const PlatformFormat = ({ platforms }: { platforms: string[] }) => (
  <Box className="gap-1">
    {platforms.map((platform) => (
      <div key={platform}>{formatPlatform(platform)}</div>
    ))}
  </Box>
);

export default PlatformFormat;
