import { Box } from "components/Box";
import { Skeleton } from "components/ui/skeleton";
import { cn } from "utils/utils";

interface Props {
  lastElementRef: any;
  isHidden: boolean;
}
const PageSuggestSkeleton = ({ lastElementRef, isHidden }: Props) => (
  <Box
    ref={lastElementRef}
    className={cn("w-full gap-2 mt-2 justify-between", isHidden && "hidden")}
  >
    <Box className="gap-1">
      <Skeleton className="w-12 h-12 rounded-full" />
      <Box variant="col-start" className="justify-start gap-1">
        <Box className="gap-2">
          <Skeleton className="w-36 h-6" />
          <Skeleton className="w-5 h-5 rounded-full" />
        </Box>
        <Skeleton className="w-40 h-6" />
        <Skeleton className="w-[600px] h-6" />
      </Box>
    </Box>
    <Skeleton className="w-20 h-9 rounded-lg pr-6" />
  </Box>
);

export default PageSuggestSkeleton;
