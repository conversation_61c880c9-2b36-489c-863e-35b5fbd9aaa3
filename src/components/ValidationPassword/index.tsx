import { Box } from '../Box';
import ConditionMessage from '../../views/UserProfile/components/ConditionMessage';
import { cn } from '../../utils/utils';

interface IValidationPassword {
  password: string;
  className?: string;
}

export const ValidationPassword = ({ password, className }: IValidationPassword) => {
  return (
    <Box className={cn('flex-wrap gap-2 lg:gap-12 justify-start items-start', className)}>
      <Box variant="col-start" className="gap-2 w-fit">
        <ConditionMessage
          message="8 characters minimum"
          active={password && password?.length >= 8}
        />

        <ConditionMessage
          message="At least one lowercase character"
          active={password && /^(?=.*[a-z])/.test(password)}
        />
        <ConditionMessage
          message="At least one uppercase character"
          active={password && /^(?=.*[A-Z])/.test(password)}
        />
      </Box>
      <Box variant="col-start" className="gap-2 w-fit">
        <ConditionMessage
          message="At least one number"
          active={password && /^(?=.*\d)/.test(password)}
        />
        <ConditionMessage
          message="At least one special character (@, $, !, %, *, ?, &, #)"
          active={password && /^(?=.*[@$!%*?&#])/.test(password)}
        />
      </Box>
    </Box>

  );
};
