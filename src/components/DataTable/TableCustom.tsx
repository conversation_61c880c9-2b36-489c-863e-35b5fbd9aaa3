import { CSSProperties, useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable
} from '@tanstack/react-table';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from 'components/ui/table';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';
import CustomNoRowsOverlay from './CustomNoRowsOverlay';
import { cn } from 'utils/utils';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue & {customField?: string}>[];
  data: TData[];
  isPagination?: any;
  classTable?: string;
  cellClass?: string;
  loading?: boolean;
  isShowTooltip?: boolean;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  cellClass,
  classTable,
  loading = false,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([])
  const table = useReactTable({
    data,
    columns,
    state: { sorting },
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  })

  const getCommonPinningStyles = (index: number, isHeader: boolean): CSSProperties => {
    const columnTable = columns[index]
    const meta = columnTable.meta as { sticky?: string | undefined; position?: number | undefined }
    const sticky = meta?.sticky as "left" | "right" | undefined
    const shadow = '1px 0px 0px 0px #E1E2E3 inset, 0px -1px 0px 0px #E1E2E3 inset';

    return {
      left: sticky === "left" ? `${meta.position ?? 0}px` : undefined,
      right: sticky === "right" ? `${meta.position ?? 0}px` : undefined,
      position: sticky ? "sticky" : "relative",
      zIndex: sticky ? (isHeader ? 2 : 1) : 0,
      boxShadow: sticky === 'left' ? '-' + shadow : sticky === 'right' ? shadow : '0px -1px 0px 0px #E1E2E3 inset',

    }
  }

  const getStickyColumnClass = (isLastRow: boolean): string => {
    const classes = []
    if (!isLastRow) {
      classes.push("border-none border-[#E1E2E3]")
    }

    return classes.join(" ")
  }

  return (
    <div>
      <div
        className={cn(
          "rounded-2xl overflow-hidden shadow-sm border-[2px] border-secondary min-h-[700px] relative",
          classTable,
          table.getRowModel().rows?.length == 0 && !loading && "min-h-[500px]",
        )}
      >
        <Table className="relative">
          <TableHeader className="bg-secondary text-secondary font-medium">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="border-none">
                {headerGroup.headers.map((header, index) => {
                  return (
                    !header.column.columnDef.enableHiding && (
                      <TableHead
                        key={header.id}
                        className={cn("text-center py-4 bg-secondary", header.column.columnDef.meta)}
                        style={{
                          ...getCommonPinningStyles(index, true),
                        }}
                      >
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    )
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          {table.getRowModel().rows?.length > 0 && !loading && (
            <TableBody className="items-start">
              {table.getRowModel().rows.map((row, rowIndex) => {
                const isHighLight = !!(row.original as any)?.isHighLight
                const isLastRow = rowIndex === table.getRowModel().rows.length - 1

                return (
                  <TableRow
                    className={cn("h-fit group", isHighLight ? "!bg-[#eef9ff]" : "")}
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell, index) => {
                      return (
                        !cell.column.columnDef.enableHiding && (
                          <TableCell
                            key={cell.id}
                            className={cn(
                              "bg-white group-hover:bg-muted",
                              isHighLight ? "!bg-[#eef9ff] group-hover:!bg-[#eef9ff]" : "",
                              getStickyColumnClass(isLastRow),
                              cellClass,
                            )}
                            style={{
                              ...getCommonPinningStyles(index, false),
                            }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        )
                      )
                    })}
                  </TableRow>
                )
              })}
            </TableBody>
          )}
        </Table>
        {loading && (
          <div className="flex justify-center items-center absolute w-full h-full text-lg">
            <LoadingButtonIcon />
          </div>
        )}
        {table.getRowModel().rows?.length == 0 && !loading && (
          <div className="flex justify-center items-center absolute w-full h-full text-lg">
            <CustomNoRowsOverlay />
          </div>
        )}
      </div>
    </div>
  );
}
