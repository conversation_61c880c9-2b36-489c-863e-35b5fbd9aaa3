import { IHeaderParams } from "ag-grid-community";
import { useEffect, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import styled from "styled-components";
import { SortType } from "types";

export interface ICustomHeaderParams {
  menuIcon: string;
  handleSortChange: (sortType: SortType, field: string) => void;
}

const CustomHeader = (props: ICustomHeaderParams & IHeaderParams) => {
  // initial wrapper
  let menu = null;
  let sort = null;

  //

  const [ascSort, setAscSort] = useState("inactive");
  const [descSort, setDescSort] = useState("inactive");

  const [searchParams] = useSearchParams();
  const params = Object.fromEntries(searchParams.entries());
  const refButton = useRef(null);

  const onMenuClicked = () => {
    props.showColumnMenu(refButton?.current as any);
  };

  const onSortChanged = () => {
    setAscSort(props.column.isSortAscending() ? "active" : "inactive");
    setDescSort(props.column.isSortDescending() ? "active" : "inactive");
  };

  const onSortRequested = (
    order: SortType,
    id: string,
    event: React.MouseEvent<HTMLDivElement, MouseEvent>
  ) => {
    props.setSort(order, event.shiftKey);
    props.handleSortChange(order, id);
  };

  const handleInitialSort = () => {
    if (params.order_by) {
      const isDesc = params.order_by.includes("-");
      const col_order = isDesc ? params.order_by.split("").splice(1).join("") : params.order_by;
      props.column.getColId() === col_order && props.setSort(isDesc ? "desc" : "asc", false);
    }
  };

  useEffect(() => {
    props.column.addEventListener("sortChanged", onSortChanged);
    handleInitialSort();
    onSortChanged();
  }, []);

  if (props.enableMenu) {
    menu = (
      <div ref={refButton} className="customHeaderMenuButton" onClick={() => onMenuClicked()}>
        <i className={`fa ${props.menuIcon}`}></i>
      </div>
    );
  }

  if (props.enableSorting) {
    sort = (
      <div style={{ display: "inline-block" }}>
        <ButtonSortStyle
          onClick={(event) => onSortRequested("asc", props.column.getColId(), event)}
          className={`customSortDownLabel ${ascSort}`}
        >
          <svg
            width="8"
            height="6"
            viewBox="0 0 12 6"
            fill="#0f13249"
            xmlns="http://www.w3.org/2000/svg"
            className="mb-1 cursor-pointer"
          >
            <path d="M6 0L12 6H0L6 0Z" fill="inherit" />
          </svg>
        </ButtonSortStyle>
        <ButtonSortStyle
          onClick={(event) => onSortRequested("desc", props.column.getColId(), event)}
          className={`customSortUpLabel ${descSort}`}
        >
          <svg
            width="8"
            height="6"
            viewBox="0 0 12 6"
            fill="#0f13249"
            xmlns="http://www.w3.org/2000/svg"
            className="cursor-pointer"
          >
            <path d="M6 6L0 0H12L6 6Z" fill="inherit" />
          </svg>
        </ButtonSortStyle>
      </div>
    );
  }

  return (
    <div className="sortingParent flex items-center">
      {menu}
      <div className="customHeaderLabel text-secondary mr-2">{props.displayName}</div>
      {sort}
    </div>
  );
};

export default CustomHeader;

const ButtonSortStyle = styled("div")({
  position: "relative",
  "&.inactive": {
    "& svg": {
      fill: "#0F132499",
      opacity: "0.7",
    },
  },
  "&.active": {
    "& svg": {
      fill: "#924FE8",
      opacity: "1",
    },
  },
});
