type Props = {
  content?: string;
};

const Separator = (props: Props) => {
  return (
    <div className="border-t border-gray-200 relative">
      {props.content && (
        <div className="absolute w-full transform -translate-y-1/2 text-secondary text-center text-xs">
          <span className="bg-background px-2">{props.content}</span>
        </div>
      )}
    </div>
  );
};
export default Separator;
