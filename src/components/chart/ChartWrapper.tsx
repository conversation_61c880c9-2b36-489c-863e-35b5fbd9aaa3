import { Ri<PERSON><PERSON>der2Line } from "@remixicon/react";

import SelectOptions from "components/SelectOptions";

import { cn } from "utils/utils";
import { PieEmpty } from './components/PieEmpty';
import { BarHorizonEmpty } from './components/BarHorizonEmpty';
import { BarEmpty } from './components/BarEmpty';

type Props = {
  children: React.ReactNode;
  title: string;
  className?: string;
  options?: { label: string; value: string }[];
  onChange?: (value: string) => void;
  loading?: boolean;
  contentRight?: React.ReactNode;
  isEmptyData?: boolean;
  isPieChart?: boolean;
  isBarHorizonChart?: boolean;
  isBarChart?: boolean;
};

const ChartWrapper: React.FC<Props> = ({
  children,
  loading,
  title,
  className,
  options,
  onChange,
  contentRight,
  isEmptyData,
  isPieChart,
  isBarHorizonChart,
  isBarChart
}) => {
  return (
    <div
      className={cn(
        "rounded-2xl p-6 pt-[22px] min-h-[275px] flex flex-col items-center gap-6 shadow-chart",
        className
      )}
    >
      <div className="font-semibold h-6 text-base text-start flex gap-2 justify-start items-center w-full">
        {options && onChange ? (
          <SelectOptions
            className="w-fit"
            placeholder={options[0].label}
            options={options}
            onChange={onChange}
          />
        ) : (
          <>{title}</>
        )}
        {contentRight}
      </div>
      {loading ? (
        <div className="flex-1 w-full h-full flex items-center justify-center">
          <RiLoader2Line className="animate-spin" size={30} />
        </div>
      ) : isEmptyData ? (
        isPieChart ?
          <PieEmpty /> :
          isBarHorizonChart ?
            <BarHorizonEmpty skeletonArray={[100, 60, 75, 90, 100]} /> :
            isBarChart ? <BarEmpty /> :
              <></>
      ) : (
        children
      )}
    </div>
  );
};

export default ChartWrapper;
