import { Pie } from "react-chartjs-2";
import { Chart as ChartJ<PERSON>, RadialLinearScale, ArcElement, Tooltip, Legend } from "chart.js";
import { cn } from "utils/utils";
import { getOrCreateTooltip, polarChartPlugins, singleTooltip } from "./utils";
import { useMemo } from "react";
import { polarPadding } from "utils/chart";
ChartJS.register(RadialLinearScale, ArcElement, Tooltip, Legend);

type Props = {
  values: number[];
  backgroundColors?: string[];
  labels: string[];
  className?: string;
  showTotal?: boolean;
  titleTooltip: string;
  showDots?: boolean;
  isCustomTooltip?: boolean;
  customTooltip?: string;
};
const DEFAULT_COLORS = [
  "hsla(266, 77%, 61%, 1)",
  "hsla(265, 77%, 72%, 1)",
  "hsla(29, 90%, 57%, 1)",
  "hsla(224, 8%, 65%, 1)",
];
const PolarChart = ({
  values,
  backgroundColors,
  labels,
  className,
  titleTooltip,
  isCustomTooltip = false,
  customTooltip,
}: Props) => {
  const data = {
    labels: labels,
    datasets: [
      {
        data: values,
        backgroundColor: backgroundColors ?? DEFAULT_COLORS,
        borderWidth: 0,
      },
    ],
  };
  const total = useMemo(() => values.reduce((a, b) => a + b, 0), [values]);

  return (
    <div className={cn("", className)}>
      <Pie
        data={data}
        options={{
          animation: {
            duration: 2000,
          },
          elements: {
            arc: {
              hoverOffset: 0,
            },
          },

          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              //@ts-ignore
              position: "custom",
              enabled: false,
              external({ chart, tooltip }) {
                const tooltipEl = getOrCreateTooltip(chart, titleTooltip);
                if (tooltip.opacity === 0) {
                  tooltipEl.style.opacity = 0;
                  return;
                }
                if (tooltip.body) {
                  let tooltipHtml;
                  const tableRoot = tooltipEl.querySelector(".chartjs-tooltip > div");
                  const titleLines = tooltip.title || [];
                  tableRoot.innerHTML = "";
                  const valueReverted = values[labels.indexOf(titleLines[0])] ?? 0;
                  titleLines.forEach((title, index) => {
                    const colors = tooltip.labelColors[index];

                    if (title == "Other" && isCustomTooltip) {
                      tooltipHtml = `
                        <div class="min-w-[150px] w-fit flex flex-col gap-0.5 py-2 px-[13px] 3xl:gap-1 z-[9999]">
                          <div class="value-tooltips">
                            <div class="dots-tooltip" style="background-color: ${colors.backgroundColor};"></div>
                            <div class="font-medium text-xs text-secondary 3xl:text-md">${title}</div>
                          </div>
                         ${customTooltip}
                      `;
                    } else {
                      tooltipHtml = singleTooltip(
                        total,
                        colors.backgroundColor,
                        title,
                        valueReverted
                      );
                    }
                    tableRoot.insertAdjacentHTML("beforeend", tooltipHtml);
                  });
                  const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas;
                  tooltipEl.style.opacity = "1";
                  tooltipEl.style.left = `${positionX + tooltip.caretX}px`;
                  tooltipEl.style.top = `${positionY + tooltip.caretY}px`;
                }
              },
            },
          },
          responsive: true,
          layout: {
            padding: polarPadding(values),
          },
        }}
        plugins={polarChartPlugins(values, total)}
      />
    </div>
  );
};
export default PolarChart;
