export const BarEmpty = () => {
  const skeletonCount = 8;
  return (
    <div className="text-left w-full">
      {Array.from({ length: skeletonCount }).map((_, i) => (
        <div key={i}>
          <div className="relative border border-t-[#E1E2E3] w-full h-[1px] mt-1 mb-10">
            <p className="w-10 text-right absolute left-0 -top-2 text-secondary text-xs bg-white pr-2">{( skeletonCount - 1 - i ) * 10} %</p>
          </div>
        </div>
      ))}

    </div>
  );
};
