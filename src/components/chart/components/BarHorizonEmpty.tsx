interface Props {
  skeletonArray?: number[];
}

export const BarHorizonEmpty = (props: Props) => {
  const { skeletonArray } = props;
  return (
    <div className="text-left w-full">
      {skeletonArray?.map((item, i) => <div className="mb-2" key={i}>
        <p className="mb-2 text-xs text-secondary">
          City name
        </p>
        <div
          className="h-[16px] bg-[#F0F0F0] rounded-full"
          style={{
            width: item ? `${item}%` : '100%'
          }}
        ></div>
      </div>)}

    </div>
  );
};
