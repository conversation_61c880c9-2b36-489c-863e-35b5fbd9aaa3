export const PieEmpty = () => {
  return (
    <div>
      <svg width="248" height="248" viewBox="0 0 248 248" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g filter="url(#filter0_dd_53_10532)">
          <path d="M186 186C175.804 196.196 163.251 203.72 149.453 207.906C135.654 212.091 121.036 212.81 106.894 209.996C92.752 207.183 79.5219 200.926 68.3756 191.779C57.2294 182.631 48.5111 170.876 42.9931 157.554C37.4751 144.232 35.3276 129.756 36.741 115.406C38.1543 101.056 43.0848 87.2761 51.0957 75.2869C59.1066 63.2977 69.9507 53.4692 82.6673 46.672C95.384 39.8749 109.581 36.3188 124 36.3188L124 124L186 186Z" fill="#F0F0F0"/>
        </g>
        <g filter="url(#filter1_dd_53_10532)">
          <path d="M124 54.7971C133.088 54.7971 142.087 56.5871 150.483 60.0648C158.879 63.5426 166.508 68.64 172.934 75.0661C179.36 81.4922 184.457 89.1211 187.935 97.5172C191.413 105.913 193.203 114.912 193.203 124C193.203 133.088 191.413 142.087 187.935 150.483C184.457 158.879 179.36 166.508 172.934 172.934C166.508 179.36 158.879 184.457 150.483 187.935C142.087 191.413 133.088 193.203 124 193.203L124 124L124 54.7971Z" fill="#E1E2E3"/>
        </g>
        <g filter="url(#filter2_dd_53_10532)">
          <path d="M73.955 124C73.955 96.361 96.3609 73.9551 124 73.9551C151.639 73.9551 174.045 96.361 174.045 124C174.045 151.639 151.639 174.045 124 174.045C96.3609 174.045 73.955 151.639 73.955 124Z" fill="#FDFDFD" shapeRendering="crispEdges"/>
          <path d="M109.477 119.273V128H108.455L103.699 121.148H103.614V128H102.557V119.273H103.58L108.352 126.142H108.438V119.273H109.477ZM114.115 128.136C113.524 128.136 113.005 127.996 112.559 127.714C112.116 127.433 111.77 127.04 111.52 126.534C111.272 126.028 111.149 125.437 111.149 124.761C111.149 124.08 111.272 123.484 111.52 122.976C111.77 122.467 112.116 122.072 112.559 121.791C113.005 121.51 113.524 121.369 114.115 121.369C114.706 121.369 115.223 121.51 115.666 121.791C116.112 122.072 116.458 122.467 116.706 122.976C116.956 123.484 117.081 124.08 117.081 124.761C117.081 125.437 116.956 126.028 116.706 126.534C116.458 127.04 116.112 127.433 115.666 127.714C115.223 127.996 114.706 128.136 114.115 128.136ZM114.115 127.233C114.564 127.233 114.933 127.118 115.223 126.888C115.512 126.658 115.727 126.355 115.866 125.98C116.005 125.605 116.075 125.199 116.075 124.761C116.075 124.324 116.005 123.916 115.866 123.538C115.727 123.161 115.512 122.855 115.223 122.622C114.933 122.389 114.564 122.273 114.115 122.273C113.666 122.273 113.297 122.389 113.007 122.622C112.717 122.855 112.502 123.161 112.363 123.538C112.224 123.916 112.154 124.324 112.154 124.761C112.154 125.199 112.224 125.605 112.363 125.98C112.502 126.355 112.717 126.658 113.007 126.888C113.297 127.118 113.666 127.233 114.115 127.233ZM124.462 128.136C123.917 128.136 123.435 127.999 123.018 127.723C122.6 127.445 122.273 127.053 122.038 126.547C121.802 126.038 121.684 125.438 121.684 124.744C121.684 124.057 121.802 123.46 122.038 122.955C122.273 122.449 122.602 122.058 123.022 121.783C123.442 121.507 123.928 121.369 124.479 121.369C124.906 121.369 125.242 121.44 125.489 121.582C125.739 121.722 125.93 121.881 126.06 122.06C126.194 122.236 126.298 122.381 126.371 122.494H126.457V119.273H127.462V128H126.491V126.994H126.371C126.298 127.114 126.192 127.264 126.056 127.446C125.92 127.625 125.725 127.786 125.472 127.928C125.219 128.067 124.883 128.136 124.462 128.136ZM124.599 127.233C125.002 127.233 125.343 127.128 125.621 126.918C125.9 126.705 126.112 126.411 126.256 126.036C126.401 125.658 126.474 125.222 126.474 124.727C126.474 124.239 126.403 123.811 126.261 123.445C126.119 123.075 125.908 122.788 125.63 122.584C125.352 122.376 125.008 122.273 124.599 122.273C124.173 122.273 123.817 122.382 123.533 122.601C123.252 122.817 123.04 123.111 122.898 123.483C122.759 123.852 122.69 124.267 122.69 124.727C122.69 125.193 122.761 125.616 122.903 125.997C123.048 126.375 123.261 126.676 123.542 126.901C123.826 127.122 124.178 127.233 124.599 127.233ZM131.37 128.153C130.955 128.153 130.579 128.075 130.241 127.919C129.903 127.76 129.634 127.531 129.435 127.233C129.237 126.932 129.137 126.568 129.137 126.142C129.137 125.767 129.211 125.463 129.359 125.23C129.506 124.994 129.704 124.81 129.951 124.676C130.198 124.543 130.471 124.443 130.769 124.378C131.07 124.31 131.373 124.256 131.677 124.216C132.075 124.165 132.397 124.126 132.644 124.101C132.894 124.072 133.076 124.026 133.19 123.96C133.306 123.895 133.364 123.781 133.364 123.619V123.585C133.364 123.165 133.249 122.838 133.019 122.605C132.792 122.372 132.447 122.256 131.984 122.256C131.504 122.256 131.127 122.361 130.854 122.571C130.582 122.781 130.39 123.006 130.279 123.244L129.325 122.903C129.495 122.506 129.722 122.196 130.006 121.974C130.293 121.75 130.606 121.594 130.944 121.506C131.285 121.415 131.62 121.369 131.95 121.369C132.16 121.369 132.401 121.395 132.674 121.446C132.95 121.494 133.215 121.595 133.471 121.749C133.729 121.902 133.944 122.134 134.114 122.443C134.285 122.753 134.37 123.168 134.37 123.688V128H133.364V127.114H133.313C133.245 127.256 133.131 127.408 132.972 127.57C132.813 127.732 132.602 127.869 132.337 127.983C132.073 128.097 131.751 128.153 131.37 128.153ZM131.523 127.25C131.921 127.25 132.256 127.172 132.529 127.016C132.805 126.859 133.012 126.658 133.151 126.411C133.293 126.163 133.364 125.903 133.364 125.631V124.71C133.322 124.761 133.228 124.808 133.083 124.851C132.941 124.891 132.776 124.926 132.589 124.957C132.404 124.986 132.224 125.011 132.048 125.034C131.874 125.054 131.734 125.071 131.626 125.085C131.364 125.119 131.12 125.175 130.893 125.251C130.668 125.325 130.487 125.437 130.347 125.588C130.211 125.736 130.143 125.937 130.143 126.193C130.143 126.543 130.272 126.807 130.531 126.986C130.792 127.162 131.123 127.25 131.523 127.25ZM139.052 121.455V122.307H135.66V121.455H139.052ZM136.649 119.886H137.654V126.125C137.654 126.409 137.696 126.622 137.778 126.764C137.863 126.903 137.971 126.997 138.102 127.045C138.235 127.091 138.376 127.114 138.524 127.114C138.635 127.114 138.725 127.108 138.797 127.097C138.868 127.082 138.924 127.071 138.967 127.062L139.172 127.966C139.103 127.991 139.008 128.017 138.886 128.043C138.764 128.071 138.609 128.085 138.422 128.085C138.137 128.085 137.859 128.024 137.586 127.902C137.316 127.78 137.092 127.594 136.913 127.344C136.737 127.094 136.649 126.778 136.649 126.398V119.886ZM142.491 128.153C142.076 128.153 141.7 128.075 141.362 127.919C141.024 127.76 140.755 127.531 140.556 127.233C140.358 126.932 140.258 126.568 140.258 126.142C140.258 125.767 140.332 125.463 140.48 125.23C140.627 124.994 140.825 124.81 141.072 124.676C141.319 124.543 141.592 124.443 141.89 124.378C142.191 124.31 142.494 124.256 142.798 124.216C143.196 124.165 143.518 124.126 143.765 124.101C144.015 124.072 144.197 124.026 144.311 123.96C144.427 123.895 144.485 123.781 144.485 123.619V123.585C144.485 123.165 144.37 122.838 144.14 122.605C143.913 122.372 143.568 122.256 143.105 122.256C142.625 122.256 142.248 122.361 141.975 122.571C141.703 122.781 141.511 123.006 141.4 123.244L140.446 122.903C140.616 122.506 140.843 122.196 141.127 121.974C141.414 121.75 141.727 121.594 142.065 121.506C142.406 121.415 142.741 121.369 143.071 121.369C143.281 121.369 143.522 121.395 143.795 121.446C144.071 121.494 144.336 121.595 144.592 121.749C144.85 121.902 145.065 122.134 145.235 122.443C145.406 122.753 145.491 123.168 145.491 123.688V128H144.485V127.114H144.434C144.366 127.256 144.252 127.408 144.093 127.57C143.934 127.732 143.723 127.869 143.458 127.983C143.194 128.097 142.872 128.153 142.491 128.153ZM142.645 127.25C143.042 127.25 143.377 127.172 143.65 127.016C143.926 126.859 144.133 126.658 144.272 126.411C144.414 126.163 144.485 125.903 144.485 125.631V124.71C144.443 124.761 144.349 124.808 144.204 124.851C144.062 124.891 143.897 124.926 143.71 124.957C143.525 124.986 143.345 125.011 143.169 125.034C142.995 125.054 142.855 125.071 142.747 125.085C142.485 125.119 142.241 125.175 142.014 125.251C141.789 125.325 141.608 125.437 141.468 125.588C141.332 125.736 141.264 125.937 141.264 126.193C141.264 126.543 141.393 126.807 141.652 126.986C141.913 127.162 142.244 127.25 142.645 127.25Z" fill="#515667"/>
        </g>
        <defs>
          <filter id="filter0_dd_53_10532" x="32.3187" y="33.3188" width="157.681" height="183.362" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_53_10532"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect2_dropShadow_53_10532"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_53_10532" result="effect2_dropShadow_53_10532"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_53_10532" result="shape"/>
          </filter>
          <filter id="filter1_dd_53_10532" x="120" y="51.7971" width="77.203" height="146.406" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_53_10532"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect2_dropShadow_53_10532"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_53_10532" result="effect2_dropShadow_53_10532"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_53_10532" result="shape"/>
          </filter>
          <filter id="filter2_dd_53_10532" x="41.955" y="41.9551" width="164.09" height="164.09" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="8" operator="erode" in="SourceAlpha" result="effect1_dropShadow_53_10532"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_53_10532"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="16"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_53_10532" result="effect2_dropShadow_53_10532"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_53_10532" result="shape"/>
          </filter>
        </defs>
      </svg>

    </div>
  );
};
