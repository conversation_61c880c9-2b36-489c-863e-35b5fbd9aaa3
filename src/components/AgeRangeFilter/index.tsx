import { useEffect, useState } from "react";
import { RiArrowDownSLine, RiCloseFill } from "@remixicon/react";

import { Box } from "components/Box";
import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import RangeSlider from "components/RangeSlider";
import AgeRangeOptions from "./AgeRangeOptions";

import { cn } from "utils/utils";
import { TSelectedOption } from "types/Select";

export type TRange = { min: number; max: number };
type Props = {
  defaultValue?: TRange;
  onChange: (value: any) => void;
  isDisabled?: boolean;
  selectedOption: TSelectedOption;
};

const AgeRangeFilter = ({ defaultValue, onChange, isDisabled = false, selectedOption }: Props) => {
  const initValue = defaultValue?.min && defaultValue?.max ? defaultValue : { min: 0, max: 100 };

  const [ageRange, setAgeRange] = useState<TRange | undefined>({ min: 0, max: 100 });
  const [rangeSelected, setRangeSelected] = useState<any>(initValue);
  const [isInitialRender, setIsInitialRender] = useState(true);

  useEffect(() => {
    if (!isInitialRender) {
      setAgeRange(rangeSelected);
      onChange({ min: ageRange?.min, max: ageRange?.max });
    } else {
      setIsInitialRender(false);
    }
  }, [rangeSelected]);

  useEffect(() => {
    if (Object.keys(selectedOption).length == 0) {
      setAgeRange(undefined);
    }
  }, [selectedOption]);

  useEffect(() => {
    if (defaultValue) {
      setRangeSelected(defaultValue);
    }
  }, [defaultValue?.min, defaultValue?.max]);

  const handleClear = () => {
    setAgeRange(undefined);
    onChange({ min: undefined, max: undefined });
    setRangeSelected({ min: 0, max: 100 });
    setIsInitialRender(true);
  };
  const labelValue =
    ageRange?.min !== undefined &&
    ageRange?.max !== undefined &&
    `${ageRange?.min}-${ageRange?.max}`;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="relative max-md:w-full">
          <button
            className={cn(
              "flex text-xs px-2.5 py-[5px] h-[39px] rounded-xl justify-between border min-w-28 items-center max-md:w-full",
              isDisabled && "bg-[#0A0F290A] cursor-default pointer-events-none"
            )}
          >
            {labelValue && (
              <span
                className="text-[#5314a3] bg-[#ecdffb] py-0.5 px-1 rounded font-medium"
                children={
                  <Box className="gap-1">
                    {labelValue}

                    <RiCloseFill
                      color="#14151A"
                      size={16}
                      opacity={0.6}
                      className={cn("hover:fill-[#82868b]", isDisabled && "hidden")}
                      onClick={() => handleClear()}
                    />
                  </Box>
                }
              />
            )}

            <RiArrowDownSLine className="ml-auto text-secondary" size={20} />
          </button>
          <div
            className={cn(
              "absolute left-3 transform text-[#82868b] -translate-y-1/2 duration-300 flex text-sm",
              labelValue ? "top-[-3px] text-xs bg-white" : "top-1/2"
            )}
            children="Age"
          />
        </div>
      </PopoverTrigger>
      {!isDisabled && (
        <PopoverContent className="min-w-96">
          <AgeRangeOptions
            ageRange={ageRange}
            setAgeRange={setAgeRange}
            setRangeSelected={setRangeSelected}
          />
          <RangeSlider rangeSelected={rangeSelected} setRangeSelected={setRangeSelected} />
        </PopoverContent>
      )}
    </Popover>
  );
};

export default AgeRangeFilter;
