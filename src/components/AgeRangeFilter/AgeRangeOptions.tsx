import React from "react";
import { AGE_RANGE_OPTIONS } from "constants/persona";
import { cn } from "utils/utils";
import { TRange } from ".";

interface Props {
  ageRange?: TRange;
  setAgeRange: React.Dispatch<React.SetStateAction<TRange | undefined>>;
  setRangeSelected: React.Dispatch<React.SetStateAction<TRange>>;
}

const AgeRangeOptions = ({ ageRange, setAgeRange, setRangeSelected }: Props) => {
  const handleChangeAge = (value: TRange) => {
    setAgeRange({
      min: value.min,
      max: value.max,
    });
    setRangeSelected({
      min: value.min,
      max: value.max,
    });
  };
  return (
    <div className="grid grid-cols-3 gap-1">
      {AGE_RANGE_OPTIONS.map((item) => {
        const isActive = ageRange?.max === item.value.max && ageRange?.min === item.value.min;
        return (
          <button
            key={item.label}
            className={cn(
              "px-2.5 py-1.5 border mb-2 font-medium rounded-md text-sm min-w-20 shadow-xs flex items-center text-[12px] justify-center",
              isActive ? "bg-[#924FE8] text-white" : "text-secondary bg-white"
            )}
            onClick={() => handleChangeAge(item.value)}
            children={item.label}
          />
        );
      })}
    </div>
  );
};

export default AgeRangeOptions;
