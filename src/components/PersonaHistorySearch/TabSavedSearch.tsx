import React, { useContext, useEffect, useState } from 'react';
import { ModalContext } from 'providers/Modal';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from 'components/ui/table';
import { Button } from 'components/ui/button';
import { Box } from 'components/Box';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';
import FilterItemLabel from './FilterItemLabel';
import ModalDeleteFilter from './ModalDeleteFilter';

import useFilters from 'hooks/useFilters';
import { personaAPI } from 'apis/persona';
import LABEL from 'constants/label';
import { useAppSelector } from '../../store';
import { useAbortController } from '../../hooks/useAbortController';

export interface IFiterItem {
  id: string | number;
  filter_name: string;
  filters: {};
}

interface Props {
  setOpenHistory: React.Dispatch<React.SetStateAction<boolean>>;
  type: 'work' | 'social';
}

const TabSavedSearch = ({ setOpenHistory, type }: Props) => {
  const modal = useContext(ModalContext);
  const { data: categories } = useAppSelector((state) => state.category);
  const { setSearchParams } = useFilters();
  const { newAbortController } = useAbortController();

  const [data, setData] = useState({
    data: [],
    loading: false
  });

  useEffect(() => {
    getSavedSearch();
  }, []);

  const getSavedSearch = async () => {
    setData({ ...data, loading: true });
    const controller = newAbortController();
    const res = await personaAPI.get({
      endpoint: 'filter-histories/',
      params: { type },
      signal: controller.signal
    });
    if (res.data) {
      setData({ data: res.data.items, loading: false });
    } else {
      setData({ ...data, loading: true });
    }
  };

  const handleOpenModalDelete = async (item: IFiterItem) => {
    modal?.setDataDialog((prev) => ( {
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <ModalDeleteFilter item={item} getSavedSearch={getSavedSearch} />
    } ));
  };

  const DesktopView = () => (
    <div className="table-container">
      <Table>
        <TableHeader>
          <TableRow className="bg-custom-secondary text-sm text-secondary font-medium border-none">
            <TableHead className="w-[200px] sticky top-0 z-10 bg-custom-secondary">Name</TableHead>
            <TableHead className="w-[300px] sticky top-0 z-10 bg-custom-secondary">Filter</TableHead>
            <TableHead className="w-[150px] sticky top-0 z-10 bg-custom-secondary text-right">
              <span className="sr-only">Actions</span>
            </TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div className="table-body min-h-[354px] max-h-[354px] overflow-y-auto">
        {data.loading ? (
          <Box className="justify-center items-center h-full">
            <LoadingButtonIcon />
          </Box>
        ) : data.data.length > 0 ? (
          <Table>
            <TableBody>
              {data.data.map((item: IFiterItem) => {
                return (
                  <TableRow key={item.id}>
                    <TableCell className="w-[200px] font-sm align-top text-secondary font-medium">
                      {item.filter_name}
                    </TableCell>
                    <TableCell
                      className="cursor-pointer flex gap-2 flex-wrap"
                      onClick={() => {
                        setSearchParams((prev) => ( { ...prev, ...item.filters } ));
                        setOpenHistory(false);
                      }}
                    >
                      {FilterItemLabel(item.filters, categories.items)}
                    </TableCell>
                    <TableCell id={item.id as string} className="w-[50px] align-top text-right cursor-pointer">
                      <Button
                        className="text-sm px-1 py-0.5 rounded-sm font-medium h-7 text-error-default border border-[#F53E3E] hover:text-error-default"
                        variant="ghost"
                        onClick={() => handleOpenModalDelete(item)}
                      >
                        {LABEL.delete}
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        ) : (
          <Box className="justify-center items-center h-full">No filter to show</Box>
        )}
      </div>
    </div>
  );

  const MobileView = () => (
    <div className="px-4 py-2">
      <div className="grid grid-cols-2 gap-4 mb-2 bg-gray-100 rounded-t-lg p-3">
        <div className="font-medium text-gray-700">Name</div>
        <div className="font-medium text-gray-700">Filter</div>
      </div>

      <div className="max-h-[500px] overflow-y-auto">
        {data.loading ? (
          <Box className="justify-center items-center h-[200px]">
            <LoadingButtonIcon />
          </Box>
        ) : data.data.length > 0 ? (
          <div className="space-y-4">
            {data.data.map((item: IFiterItem) => (
              <div key={item.id} className="bg-gray-50 rounded-lg p-4 shadow-sm border border-gray-100">
                <div className="flex justify-between items-start mb-3">
                  <div className="text-gray-700 font-medium">{item.filter_name}</div>
                  <Button
                    className="text-xs px-3 py-1 rounded-full font-medium h-8 text-error-default border border-[#F53E3E] hover:text-error-default bg-white"
                    variant="ghost"
                    onClick={() => handleOpenModalDelete(item)}
                  >
                    {LABEL.delete}
                  </Button>
                </div>

                <div
                  className="cursor-pointer space-y-2"
                  onClick={() => {
                    setSearchParams((prev) => ( { ...prev, ...item.filters } ));
                    setOpenHistory(false);
                  }}
                >
                  {FilterItemLabel(item.filters, categories.items)}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Box className="justify-center items-center h-[200px]">No filter to show</Box>
        )}
      </div>
    </div>
  );

  return (
    <div className="mt-4 rounded-2xl overflow-hidden shadow-md border">
      <div className="hidden md:block">
        <DesktopView />
      </div>

      <div className="block md:hidden">
        <MobileView />
      </div>
    </div>
  );
};

export default TabSavedSearch;
