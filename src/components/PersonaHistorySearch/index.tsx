import React, { useEffect, useState } from "react";
import isEqual from "lodash/isEqual";

import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "components/ui/tabs";
import { Button } from "components/ui/button";
import TabSavedSearch from "./TabSavedSearch";
import TableRecentFilter from "./TableRecentFilter";

import usePrevious from "hooks/usePrevious";
import useFilters from "hooks/useFilters";
import { PERSONA_LABEL } from "constants/persona/label";
import useResponsive from '../../hooks/useResponsive';
import { FilterHistoryIcon } from '../../assets/icons/FilterHistoryIcon';

type TFilterPersonaHistory = {
  type: 'work' | 'social';
}

const FilterPersonaHistory:React.FC<TFilterPersonaHistory> = ({...props}:TFilterPersonaHistory) => {
  const { type } = props;
  const [activeTab, setActiveTab] = useState<string>("recent");
  const [openHistory, setOpenHistory] = useState<boolean>(false);
  const { isMobile } = useResponsive()

  const { params, paramsNoPage, searchParams } = useFilters();
  const prevParams = usePrevious(paramsNoPage);
  const recentFilters = JSON.parse(localStorage.getItem(type === 'work' ?
    'recentFilters' :
    'recentFiltersSocial') || '[]');

  useEffect(() => {
    if (searchParams.size > 0) {
      saveRecentFilter();
    }
  }, [params, prevParams]);

  const handleChangeDialog = (open: boolean) => {
    // close open -> reset state tab
    if (!open) {
      setActiveTab("recent");
    }
    setOpenHistory(open);
  };
  const saveRecentFilter = () => {
    const recentFilterItem = {
      date_created: `${new Date().toLocaleDateString("en-GB")} ${new Date().toLocaleTimeString(
        "en-GB",
        { hour12: false }
      )}`,
      filters: { ...paramsNoPage },
    };
    if (!isEqual(paramsNoPage, prevParams)) {
      recentFilters.unshift(recentFilterItem);
    }
    recentFilters.length > 5 && recentFilters.pop();
    localStorage.setItem(type === 'work' ? 'recentFilters' : 'recentFiltersSocial', JSON.stringify(recentFilters));
  };

  return (
    <Dialog open={openHistory} onOpenChange={(open) => handleChangeDialog(open)}>
      <DialogTrigger asChild>
        {isMobile ? <Button variant={'secondary'} className="text-secondary w-fit">
            <FilterHistoryIcon />
          </Button> :
          <Button variant={'secondary'} className="text-secondary text-sm max-lg:w-full">
            {PERSONA_LABEL.filter_history}
          </Button>}
      </DialogTrigger>
      <DialogContent className="max-w-[calc(100%-16px)] md:max-w-2xl lg:max-w-3xl xl:max-w-4xl h-auto rounded-2xl p-4 md:p-6 overflow-hidden">
        <DialogTitle className="text-center hidden"></DialogTitle>
        <Tabs defaultValue="recent" value={activeTab}>
          <div className="flex items-start justify-between">
            <TabsList className="pt-0 w-full border-b border-custom-primary justify-start rounded-none">
              <TabsTrigger
                onClick={() => setActiveTab("recent")}
                value="recent"
                className="text-md px-2 md:px-6 py-2 md:py-4"
              >
                {PERSONA_LABEL.recent_search}
              </TabsTrigger>
              <TabsTrigger
                onClick={() => setActiveTab("saved")}
                value="saved"
                className="text-md px-2 md:px-6 py-2 md:py-4"
              >
                {PERSONA_LABEL.saved_search}
              </TabsTrigger>
            </TabsList>
          </div>
          <TabsContent className="w-full" value="recent">
            <TableRecentFilter
              setOpenHistory={setOpenHistory}
              setActiveTab={setActiveTab}
              data={recentFilters || []}
              type={type}
            />
          </TabsContent>
          <TabsContent className="w-full h-full" value="saved">
            <TabSavedSearch setOpenHistory={setOpenHistory} type={type} />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default FilterPersonaHistory;
