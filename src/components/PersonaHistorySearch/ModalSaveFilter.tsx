import { useContext, useState } from "react";
import { ModalContext } from "providers/Modal";

import { Button } from "components/ui/button";
import { Input } from "components/ui/input";
import { Label } from "components/ui/label";
import { toast } from "components/ui/use-toast";
import { IFiterItem } from "./TabSavedSearch";

import { personaAPI } from "apis/persona";
import handleCloseModal from "utils/handleCloseModal";
import { PERSONA_LABEL } from "constants/persona/label";
import LABEL from "constants/label";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

interface Props {
  item?: IFiterItem;
  type: "work" | "social";
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
}
const ModalSaveFilter = ({ item, setActiveTab, type }: Props) => {
  const modal = useContext(ModalContext);
  const [searchName, setSearchName] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);

  const handleSavedFilter = async (filters: {}) => {
    setLoading(true);
    if (searchName === "") {
      toast({
        title: "Please enter search name !!",
        status: "error",
        duration: 3000,
      });
    } else {
      await personaAPI.savedFilter({
        payload: {
          filter_name: searchName,
          filters: filters,
          type: type,
        },
      });
      handleCloseModal(modal);
      setActiveTab("saved");
    }
    setLoading(false);
  };

  const handleOnkeyUpEnter = (e: React.KeyboardEvent<HTMLElement>, filters: {}) => {
    if (e.keyCode === 13) {
      handleSavedFilter(filters);
    }
  };
  return (
    <>
      <div>
        <div className="text-lg text-primary font-medium">{PERSONA_LABEL.save_new_search}</div>
        <div className="grid w-full items-center gap-1 mt-2">
          <Label htmlFor="picture" className="font-medium text-sm text-secondary">
            {PERSONA_LABEL.search_name_label}
          </Label>
          <Input
            className="w-full focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:ring-opacity-0 border-custom-primary"
            id="search_name"
            placeholder="Enter filter name"
            type="text"
            required
            onKeyUp={(e) => handleOnkeyUpEnter(e, item?.filters || {})}
            onChange={(e) => setSearchName(e.target.value)}
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <Button variant="secondary" onClick={() => handleCloseModal(modal)}>
          {LABEL.cancel}
        </Button>
        <Button
          type="submit"
          variant="main"
          disabled={loading}
          onClick={() => handleSavedFilter(item?.filters || {})}
        >
          {loading ? <LoadingButtonIcon /> : LABEL.save}
        </Button>
      </div>
    </>
  );
};

export default ModalSaveFilter;
