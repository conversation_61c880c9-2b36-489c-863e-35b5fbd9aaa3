import { FILTER_TITLE } from "constants/persona";
import formatFilterHistory from "utils/persona/formatFilterHistory";

const FilterItemLabel = (filters: any, categories: any[]) => {
  const filterValues = formatFilterHistory(filters, FILTER_TITLE, categories);
  return filterValues.map((value) => {
    return (
      <div className="text-brand-strong bg-brand-subtitle px-1 py-0.5 rounded-sm w-fit">
        <span className="font-semibold">{value.name}: </span>
        <span>{value.value}</span>
      </div>
    );
  });
};
export default FilterItemLabel;
