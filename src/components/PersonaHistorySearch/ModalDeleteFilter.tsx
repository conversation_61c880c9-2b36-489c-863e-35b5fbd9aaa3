import { useContext, useState } from 'react';
import { ModalContext } from "providers/Modal";

import { Button } from "components/ui/button";
import { Label } from "components/ui/label";
import { personaAPI } from "apis/persona";
import { PERSONA_LABEL } from "constants/persona/label";
import { IFiterItem } from "./TabSavedSearch";

import handleCloseModal from "utils/handleCloseModal";
import LABEL from "constants/label";
import { toast } from "components/ui/use-toast";
import { LoadingButtonIcon } from '../../assets/icons/LoadingButtonIcon';

interface Props {
  item?: IFiterItem;
  getSavedSearch: () => Promise<void>;
}
const ModalDeleteFilter = ({ item, getSavedSearch }: Props) => {
  const modal = useContext(ModalContext);
  const [loading, setLoading] = useState(false);

  const handleDeleteFilter = async (id: string | number) => {
    setLoading(true);
    await personaAPI.deleteFilter({
      id: id,
    });
    await getSavedSearch();
    toast({
      title: "Delete success!",
      status: "error",
      duration: 3000,
    });
    setLoading(false);
    handleCloseModal(modal);
  };

  return (
    <>
      <div className="grid w-full items-center gap-1.5">
        <Label>{PERSONA_LABEL.confirm_delete}</Label>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <Button variant="secondary" onClick={() => handleCloseModal(modal)}>
          {LABEL.cancel}
        </Button>
        <Button
          type="submit"
          variant="destructive"
          disabled={loading}
          onClick={() => handleDeleteFilter(item?.id || "")}
        >
          {loading ? <LoadingButtonIcon /> : LABEL.delete}
        </Button>
      </div>
    </>
  );
};

export default ModalDeleteFilter;
