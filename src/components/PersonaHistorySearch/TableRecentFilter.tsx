import React, { useContext } from "react";
import { ModalContext } from "providers/Modal";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "components/ui/table";
import { Button } from "components/ui/button";
import { IFiterItem } from "./TabSavedSearch";
import ModalSaveFilter from "./ModalSaveFilter";

import LABEL from "constants/label";
import FilterItemLabel from "./FilterItemLabel";
import { Box } from "components/Box";
import { useAppSelector } from '../../store';
import useFilters from '../../hooks/useFilters';

interface Props {
  data: [];
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
  setOpenHistory: React.Dispatch<React.SetStateAction<boolean>>;
  type: 'work' | 'social';
}

const TableFilterHistory = ({ data, setActiveTab, setOpenHistory, type }: Props) => {
  const modal = useContext(ModalContext)
  const { data: categories } = useAppSelector((state) => state.category)
  const { setSearchParams } = useFilters()

  const handleOpenModalSave = (item: IFiterItem) => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      className: "gap-6 px-6 py-4",
      content: <ModalSaveFilter item={item} setActiveTab={setActiveTab} type={type} />,
    }))
  }

  const DesktopView = () => (
    <div className="table-container">
      <Table>
        <TableHeader>
          <TableRow className="bg-custom-secondary text-sm text-secondary font-medium border-none">
            <TableHead className="w-[200px] sticky top-0 z-10 bg-custom-secondary">Time</TableHead>
            <TableHead className="w-[300px] sticky top-0 z-10 bg-custom-secondary">Filter</TableHead>
            <TableHead className="w-[150px] sticky top-0 z-10 bg-custom-secondary text-right">
              <span className="sr-only">Actions</span>
            </TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div className="table-body min-h-[354px] max-h-[354px] overflow-y-auto">
        {data.length > 0 ? (
          <Table>
            <TableBody>
              {data.map((item: any) => (
                <TableRow key={item.id}>
                  <TableCell className="w-[200px] font-sm align-top text-secondary font-medium">
                    {item.date_created}
                  </TableCell>
                  <TableCell
                    className="cursor-pointer flex gap-2 flex-wrap"
                    onClick={() => {
                      setSearchParams((prev) => ({ ...prev, ...item.filters }))
                      setOpenHistory(false)
                    }}
                  >
                    {FilterItemLabel(item.filters, categories.items)}
                  </TableCell>
                  <TableCell className="w-[150px] align-top text-right">
                    <Button
                      className="text-sm px-1 py-0.5 rounded-sm font-medium h-7"
                      variant="default"
                      onClick={() => handleOpenModalSave(item)}
                    >
                      {LABEL.save}
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Box className="justify-center items-center h-full">No recent filter to show</Box>
        )}
      </div>
    </div>
  )

  const MobileView = () => (
    <div className="px-4 py-2">
      <div className="grid grid-cols-2 gap-4 mb-2 bg-gray-100 rounded-t-lg p-3">
        <div className="font-medium text-gray-700">Time</div>
        <div className="font-medium text-gray-700">Filter</div>
      </div>

      <div className="max-h-[500px] overflow-y-auto">
        {data.length > 0 ? (
          <div className="space-y-4">
            {data.map((item: any) => (
              <div key={item.id} className="bg-gray-50 rounded-lg p-4 shadow-sm border border-gray-100">
                <div className="flex justify-between items-start mb-3">
                  <div className="text-gray-700 font-medium">{item.date_created}</div>
                  <Button
                    className="text-xs px-3 py-1 rounded-full font-medium h-8 bg-black text-white hover:bg-gray-800"
                    onClick={() => handleOpenModalSave(item)}
                  >
                    {LABEL.save}
                  </Button>
                </div>

                <div
                  className="cursor-pointer space-y-2"
                  onClick={() => {
                    setSearchParams((prev) => ({ ...prev, ...item.filters }))
                    setOpenHistory(false)
                  }}
                >
                  {FilterItemLabel(item.filters, categories.items)}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Box className="justify-center items-center h-[200px]">No recent filter to show</Box>
        )}
      </div>
    </div>
  )

  return (
    <div className="mt-4 rounded-2xl overflow-hidden shadow-md border">
      <div className="hidden md:block">
        <DesktopView />
      </div>

      <div className="block md:hidden">
        <MobileView />
      </div>
    </div>
  )
}

export default TableFilterHistory
