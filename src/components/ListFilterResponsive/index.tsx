import { RiArrowLeftSLine, RiArrowRightSLine } from '@remixicon/react';

import * as React from 'react';
import { useEffect, useRef, useState } from 'react';
import { Button } from '../ui/button';
import { cn } from '../../utils/utils';

interface IProps {
  children: React.ReactNode;
  className?: string;
}

export default function HorizontalScrollNav(props: IProps) {
  const { children, className } = props;
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);

  const checkScroll = () => {
    if (!scrollContainerRef.current) {
      return;
    }

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;

    setShowLeftArrow(scrollLeft > 0);

    setShowRightArrow(scrollLeft + clientWidth < scrollWidth - 5); // 5px buffer
  };

  const scrollLeft = () => {
    if (!scrollContainerRef.current) {
      return;
    }
    scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
  };

  const scrollRight = () => {
    if (!scrollContainerRef.current) {
      return;
    }
    scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
  };

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) {
      return;
    }

    checkScroll();

    const handleResize = () => {
      checkScroll();
    };

    const handleScroll = () => {
      checkScroll();
    };

    window.addEventListener('resize', handleResize);
    scrollContainer.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('resize', handleResize);
      scrollContainer.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div className="relative w-full">
      {showLeftArrow && (
        <Button
          variant="default"
          size="icon"
          className="absolute left-0 top-1/2 -translate-y-1/2 z-10 !bg-white shadow-md p-0 h-8 w-8"
          onClick={scrollLeft}
        >
          <RiArrowLeftSLine color="#0F1324" size={20} opacity={0.6} />
        </Button>
      )}

      {/* Scrollable Container */}
      <div
        ref={scrollContainerRef}
        className={cn('flex items-center gap-y-1 gap-4 relative mt-6 border-b w-full overflow-x-auto scrollbar-hide', className)}
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {children}
      </div>

      {showRightArrow && (
        <Button
          variant="default"
          size="icon"
          className="absolute right-0 top-1/2 -translate-y-1/2 z-10 !bg-white shadow-md p-0 h-8 w-8"
          onClick={scrollRight}
        >
          <RiArrowRightSLine color="#0F1324" size={20} opacity={0.6} />
        </Button>
      )}
    </div>
  )
}
