import GroupMix from "assets/icons/GroupMix";
import AvatarByName from "./AvatarByName";

type ItemProps = {
  url: string;
  name: string;
};

const Item: React.FC<ItemProps> = ({ url, name }) => {
  return (
    <button className="flex items-center gap-1 px-2 py-1 rounded hover:bg-[#ecdffb]">
      <AvatarByName name={name} urlImage={url} className="w-6 h-6" />
      <span className="text-xs text-[#5314a3] font-medium">{name}</span>
    </button>
  );
};

const SourceGroup: React.FC = () => {
  const group = [
    "https://i.pinimg.com/236x/12/fb/8d/12fb8d61e0211d6dcf6aa532d5c7a4a5.jpg",
    "https://i.pinimg.com/236x/2c/90/91/2c909186ee4e5c9dbd3860190fdefb75.jpg",
    "https://i.pinimg.com/236x/12/fb/8d/12fb8d61e0211d6dcf6aa532d5c7a4a5.jpg",
  ];
  return (
    <div className="flex items-center gap-4">
      <div className="flex flex-col gap-1">
        {group.map((item, index) => (
          <Item key={index} name={`your data ${index}`} url={item} />
        ))}
      </div>
      <GroupMix />
      <div className="flex flex-col gap-1">
        {group.map((item, index) => (
          <Item key={index} name={`your data ${index}`} url={item} />
        ))}
      </div>
    </div>
  );
};

export default SourceGroup;
