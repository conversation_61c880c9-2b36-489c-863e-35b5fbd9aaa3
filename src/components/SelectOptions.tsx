import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";
import { SelectProps, TSelectOption } from "types/Select";
import { cn } from "utils/utils";

const SelectOptions = ({
  placeholder,
  onChange,
  options,
  selected = "",
  className,
  defaultValue,
  disabled,
}: SelectProps) => {
  return (
    <Select defaultValue={selected} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger
        className={cn(
          "md:w-fit w-[75px] focus:ring-offset-0 bg-white focus:none border border-secondary",
          className
        )}
      >
        {defaultValue || <SelectValue placeholder={placeholder} />}
      </SelectTrigger>
      <SelectContent className="max-h-[500px] w-full overflow-y-auto px-1 z-[9999]">
        {options.map((option: TSelectOption) => (
          <SelectItem className="w-full" key={option.value} value={"" + option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default SelectOptions;
