import { RiFilter2Line } from "@remixicon/react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "components/ui/accordion";

type Props = {
  children: React.ReactNode;
};

const AccordionFilter = ({ children }: Props) => (
  <Accordion className="border rounded-xl my-3 md:hidden" type="single" collapsible>
    <AccordionItem value="1">
      <AccordionTrigger className="[&_svg]:h-6 [&_svg]:w-6 text-primary px-2 py-2.5">
        <div className="flex items-center gap-2 font-semibold">
          <RiFilter2Line size={20} /> Filter
        </div>
      </AccordionTrigger>
      <AccordionContent className="p-2">{children}</AccordionContent>
    </AccordionItem>
  </Accordion>
);
export default AccordionFilter;
