import { RiArrowUpSLine } from "@remixicon/react";
import { useEffect, useState } from "react";
import { cn } from "utils/utils";
import { IS_DEV } from '../layout/SidebarConfig';

const ScrollButton = () => {
  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowButton(true);
      } else {
        setShowButton(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    showButton && (
      <div className={IS_DEV ? 'fixed bottom-[90px] right-[28px] z-50' : 'fixed bottom-4 right-4 z-50'}>
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
          className={cn("p-[14px] rounded-full border bg-card shadow-md")}
        >
          <RiArrowUpSLine size={20} />
        </button>
      </div>
    )
  );
};
export default ScrollButton;
