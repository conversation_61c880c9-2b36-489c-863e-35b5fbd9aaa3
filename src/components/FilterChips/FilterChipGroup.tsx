import { styled } from "styled-components";
import map from "lodash/map";

import ReadMoreChips from "./ReadMoreChips";

import useFilters from "hooks/useFilters";
import { fMapRadius, getTwoDecimal } from "utils/number";
import { FILTER_KEY } from "constants/persona";
import { formatDate } from "utils/utils";

interface Props {
  chipLabel: {};
  limit_badge?: number;
}
const FilterChipGroup = ({ chipLabel, limit_badge = 3 }: Props) => {
  const { searchSchema } = useFilters();

  return map(searchSchema, (searchParams: string[], key: keyof typeof chipLabel) => {
    switch (true) {
      case key === FILTER_KEY.map_radius:
        const newSearchParams = searchParams.map((item) => getTwoDecimal(+item));
        const mapValue = [
          newSearchParams
            .slice(0, 2)
            .join(" - ")
            .concat(` - ${fMapRadius(newSearchParams[2])}km`),
        ];
        return (
          <GroupWrapper key={`${key}-1`} className="relative">
            <BadgeTitle>{chipLabel[key]}:</BadgeTitle>
            {ReadMoreChips({ filters: mapValue, key, limit: limit_badge })}
          </GroupWrapper>
        );
      // format date time picker
      case key === "publish_time__gte":
        const newValue = searchParams.map(
          (item) => formatDate(item) + `- ${formatDate(searchSchema["publish_time__lte"])}`
        );
        return (
          <GroupWrapper key={`${key}-1`} className="relative">
            <BadgeTitle>{chipLabel[key]}:</BadgeTitle>
            {ReadMoreChips({ filters: newValue, key: key, limit: limit_badge })}
          </GroupWrapper>
        );
      default:
        return (
          chipLabel[key] &&
          key !== "page" && (
            <GroupWrapper key={`${key}-1`} className="relative">
              <BadgeTitle>{chipLabel[key]}:</BadgeTitle>
              {ReadMoreChips({ filters: searchParams, key: key, limit: limit_badge })}
            </GroupWrapper>
          )
        );
    }
  });
};

export default FilterChipGroup;

const GroupWrapper = styled("div")({
  margin: "4px 0",
  border: "1px dotted #DEE0E3",
  display: "flex",
  justifyContent: "flex-start",
  alignItems: "center",
  gap: "10px",
  boxShadow: "0 1 2 0 #14151A0D",
  width: "fit-content",
  borderRadius: "12px",
  padding: "5px 6px",
  fontSize: "14px",
});
const BadgeTitle = styled("span")({
  fontWeight: "600",
  color: "#14151A",
});
