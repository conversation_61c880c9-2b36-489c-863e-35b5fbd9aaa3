import { RiDeleteBinLine } from '@remixicon/react';
import { useSearchParams } from 'react-router-dom';
import LABEL from 'constants/label';
import { cn } from '../../utils/utils';

const ClearAll = ({ className }: {className?: string}) => {
  const [_, setSearchParams] = useSearchParams();
  return (
    <div
      className={cn('flex items-center gap-1 text-sm font-medium cursor-pointer w-fit mt-2', className)}
      onClick={() => setSearchParams(() => ( {} ))}
    >
      <RiDeleteBinLine size={16} color="#E6483D" />
      <span className="text-[#E6483D]">{LABEL.clear_all}</span>
    </div>
  );
};

export default ClearAll;
