import { Toolt<PERSON>, Too<PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON><PERSON>ider, TooltipTrigger } from "components/ui/tooltip";
import Chip<PERSON>abe<PERSON> from "./ChipLabel";
import { Badge } from "components/ui/badge";
import { TooltipPortal } from '@radix-ui/react-tooltip';

interface Props {
  filters: string[];
  key: never;
  limit: number;
}
const ReadMoreChips = ({ filters, key, limit }: Props) => {
  if (filters.length > limit) {
    const remainingCount = filters.length - limit;
    return (
      <>
        <ChipLabel filters={filters.slice(0, limit)} keyFilter={key} />
        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger className="duration-100">
              <Badge className="px-1.5 bg-[#0A0F2914] text-primary font-normal rounded-sm hover:bg-[#0A0F2914] hover:opacity-75 cursor-pointer">
                +{remainingCount} more
              </Badge>
            </TooltipTrigger>
            <TooltipPortal>
              <TooltipContent className="gap-1 duration-100">
                <ChipLabel filters={filters.slice(limit)} keyFilter={key} />
              </TooltipContent>
            </TooltipPortal>
          </Tooltip>
        </TooltipProvider>
      </>
    );
  } else {
    return <ChipLabel filters={filters} keyFilter={key} />;
  }
};

export default ReadMoreChips;
