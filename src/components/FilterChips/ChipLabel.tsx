import { useAppSelector } from "store";
import { RiCloseCircleFill } from "@remixicon/react";
import map from "lodash/map";

import { Badge } from "components/ui/badge";

import useFilters from "hooks/useFilters";
import formatLabelChip from "utils/persona/formatChipLabel";
import { FILTER_KEY } from "constants/persona";
import omit from "lodash/omit";
import { TAttribute } from '../../types/Attribute';
import { ScoreOptionsAtt } from '../../constants/socialPersona';
import { convertRangeToValue, removeDuplicateScore } from '../../utils/socialPersona';

const ChipLabel = ({ keyFilter, filters }: { keyFilter: string; filters: string[] }) => {
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const searchSchemaValue:string = searchSchema[FILTER_KEY.score]?.[0] ?? '';
  const { min, max } = convertRangeToValue(searchSchemaValue);
  const { data: categories } = useAppSelector((state) => state.category);
  const categoriesFilter: TAttribute[] = searchSchemaValue
    ? [{ name: `${min / 10}-${max / 10}`, id: `${min}-${max}` }]
    : [];

  const handleRemoveItem = (value: string, key: string) => {
    const index = searchSchema[key].indexOf(value);
    switch (true) {
      case key === FILTER_KEY.map_radius:
        setSearchParams(() => ({
          ...paramsNoPage,
          [key]: [],
          unit: [],
        }));
        break;
      case key === "publish_time__gte":
        setSearchParams(() => ({
          ...paramsNoPage,
          ["publish_time__gte"]: [],
          ["publish_time__lte"]: [],
        }));
        break;
      case key === "q":
        const updatedParams = omit(paramsNoPage, ["q"]);
        setSearchParams(() => ({
          ...updatedParams,
        }));
        break;
      case key === "category":
        setSearchParams(() => ({
          ...paramsNoPage,
          ["category"]: [],
          ["score_range"]: [],
        }));
        break;
      case key === 'score_range':
        setSearchParams(() => ({
          ...paramsNoPage,
          ["category"]: [],
          ["score_range"]: [],
        }));
        break;
      case index > -1:
        searchSchema[key].splice(index, 1);
        setSearchParams(() => ({
          ...paramsNoPage,
          [key]: searchSchema[key].length > 0 ? searchSchema[key].join(",") : [],
        }));
        break;
      default:
        break;
    }
  };

  const categoryRanking = removeDuplicateScore([...ScoreOptionsAtt, ...categoriesFilter]);

  return map(filters, (item: string, idx: number) => (
    <Badge
      key={`${item}-${idx}`}
      className="px-1.5 bg-[#31333d14] text-primary font-normal rounded-sm hover:bg-[#0A0F2914] hover:opacity-75 cursor-pointer mx-1"
      onClick={() => handleRemoveItem(item, keyFilter)}
    >
      {formatLabelChip({
        value: item,
        categoryOption: categories.items,
        categoryRanking,
        keyFilter
      })}
      <RiCloseCircleFill className="ml-1" size={14} color="#0D112666" />
    </Badge>
  ));
};

export default ChipLabel;
