import { cn } from "utils/utils";

interface Props {
  isShowDiscount?: boolean;
  percent: number;
  className?: string;
}
const DiscountTag = ({ isShowDiscount, percent, className }: Props) => {
  return (
    <div
      className={cn(
        "bg-error-default text-white px-2 py-1 font-bold text-xs rounded-full",
        className
      )}
    >
      {isShowDiscount && "Discount"}
      <span className="ml-1">
        {!isShowDiscount && "-"} {percent}%
      </span>
    </div>
  );
};

export default DiscountTag;
