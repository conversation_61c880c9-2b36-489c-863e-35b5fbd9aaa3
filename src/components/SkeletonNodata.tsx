const SkeletonNodata = () => {
  return (
    <div className="min-h-[100px] absolute left-0 top-0 z-1 w-full h-full flex items-center justify-center gap-[17px] animate-pulse">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="67"
        height="53"
        viewBox="0 0 67 53"
        fill="none"
      >
        <rect opacity="0.24" width="67.0007" height="14.1054" rx="7.05271" fill="#919EAB" />
        <path
          opacity="0.48"
          d="M10.9469 6.53845H8.88986V4.48141C8.88986 4.19739 8.65962 3.96715 8.3756 3.96715C8.09158 3.96715 7.86134 4.19739 7.86134 4.48141V6.53845H5.8043C5.52028 6.53845 5.29004 6.76869 5.29004 7.05271C5.29004 7.33673 5.52028 7.56697 5.8043 7.56697H7.86134V9.62401C7.86134 9.90803 8.09158 10.1383 8.3756 10.1383C8.65962 10.1383 8.88986 9.90803 8.88986 9.62401V7.56697H10.9469C11.2309 7.56697 11.4612 7.33673 11.4612 7.05271C11.4612 6.76869 11.2309 6.53845 10.9469 6.53845Z"
          fill="#919EAB"
        />
        <rect x="16.751" y="4.84872" width="35.2635" height="4.40794" rx="2.20397" fill="#919EAB" />
        <rect
          opacity="0.24"
          x="5.28906"
          y="19.395"
          width="61.7112"
          height="14.1054"
          rx="7.05271"
          fill="#919EAB"
        />
        <path
          opacity="0.48"
          d="M16.235 27.1089H11.0924C10.8084 27.1089 10.5781 26.8622 10.5781 26.5579C10.5781 26.2536 10.8084 26.0069 11.0924 26.0069H16.235C16.519 26.0069 16.7492 26.2536 16.7492 26.5579C16.7492 26.8622 16.519 27.1089 16.235 27.1089Z"
          fill="#919EAB"
        />
        <rect
          opacity="0.48"
          x="22.0391"
          y="24.2437"
          width="29.974"
          height="4.40794"
          rx="2.20397"
          fill="#919EAB"
        />
        <rect
          opacity="0.24"
          x="5.28906"
          y="38.7899"
          width="61.7112"
          height="14.1054"
          rx="7.05271"
          fill="#919EAB"
        />
        <path
          opacity="0.48"
          d="M16.235 46.5038H11.0924C10.8084 46.5038 10.5781 46.2571 10.5781 45.9528C10.5781 45.6485 10.8084 45.4018 11.0924 45.4018H16.235C16.519 45.4018 16.7492 45.6485 16.7492 45.9528C16.7492 46.2571 16.519 46.5038 16.235 46.5038Z"
          fill="#919EAB"
        />
        <rect
          opacity="0.24"
          x="22.0391"
          y="43.6387"
          width="29.974"
          height="4.40794"
          rx="2.20397"
          fill="#919EAB"
        />
      </svg>
      <span className="text-lg text-[#0A0F2940] ">Not found</span>
    </div>
  );
};

export default SkeletonNodata;
