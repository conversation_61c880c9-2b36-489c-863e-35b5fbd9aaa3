import { useEffect, useRef } from "react";

import { toast } from "components/ui/use-toast";
import { TRange } from "components/AgeRangeFilter";
import InputRange from "./InputRange";
import getPercent from "./getPercent";

import { cn } from "utils/utils";
import "./style.css";

type Props = {
  minRange?: number;
  maxRange?: number;
  rangeSelected: TRange;
  isRangeEnable?: boolean;
  setRangeSelected: (value: TRange) => void;
  hideInput?: boolean;
  className?: string;
  title?: React.ReactNode;
};

const RangeSlider = ({ minRange = 0, maxRange = 100, rangeSelected, setRangeSelected, isRangeEnable = true, hideInput = false, className, title }: Props) => {
  const minValRef = useRef<HTMLInputElement>(null);
  const maxValRef = useRef<HTMLInputElement>(null);
  const range = useRef<HTMLDivElement>(null);

  useEffect(() => {
    updateRange();
    // setAgeRange(rangeSelected);
  }, [rangeSelected.min, rangeSelected.max]);

  const updateRange = async () => {
    if (minValRef.current && maxValRef.current && range.current) {
      const minPercent = getPercent({ value: rangeSelected.min, minRange, maxRange });
      const maxPercent = getPercent({ value: rangeSelected.max, minRange, maxRange });
      range.current.style.left = `${minPercent}%`;
      range.current.style.width = `${maxPercent - minPercent}%`;
    }
  };

  const handleMinChange = (value: number) => {
    const updateRange: TRange = { ...rangeSelected, min: Math.min(value, rangeSelected?.max || 100 - 1) };
    setRangeSelected(updateRange);
  };

  const handleMaxChange = (value: number) => {
    if (!isRangeEnable) return;
    if (value > maxRange) {
      toast({
        status: "error",
        description: `Max value must be less than ${maxRange}`,
      });
      return;
    } else {
      const updateRange: TRange = { ...rangeSelected, max: value  };
      setRangeSelected(updateRange);
    }
  };

  return (
    <div className="relative flex gap-1 justify-center">
      {hideInput ?
        <div className="absolute -top-4 left-0 text-secondary min-w-[30px] h-[30px] m-auto text-sm font-medium flex items-center justify-center">{rangeSelected?.min || minRange}</div> :
        <InputRange value={rangeSelected?.min || minRange} onChange={handleMinChange} isRangeEnable={!isRangeEnable} />}
      {title}
      <div className="container p-0 h-[30px] flex items-center justify-center">
        <input
          type="range"
          min={minRange}
          max={maxRange}
          defaultValue={rangeSelected?.min || minRange}
          value={rangeSelected.min ? rangeSelected.min : minRange}
          ref={minValRef}
          onChange={(event) => handleMinChange(event.target.valueAsNumber)}
          className={cn('thumb thumb--zindex-3', className, {
            "thumb--zindex-5": rangeSelected.min > maxRange - 100,
          })}
          disabled={!isRangeEnable}
        />
        <input
          type="range"
          min={minRange}
          max={maxRange}
          defaultValue={rangeSelected?.max || maxRange}
          value={rangeSelected.max ? rangeSelected.max : maxRange}
          ref={maxValRef}
          onChange={(event) => handleMaxChange(event.target.valueAsNumber)}
          className={cn('thumb thumb--zindex-4', className)}
        />
        <div className={cn('relative slider', className)}>
          <div className="slider__track" />
          <div ref={range} className="slider__range" />
        </div>
      </div>
      {hideInput ?
        <div className="absolute -top-4 right-0 text-secondary min-w-[30px] h-[30px] m-auto text-sm font-medium flex items-center justify-center">{rangeSelected?.max || maxRange}</div> :
        <InputRange value={rangeSelected?.max || maxRange} onChange={handleMaxChange} isRangeEnable={!isRangeEnable} />}
    </div>
  );
};

export default RangeSlider;
