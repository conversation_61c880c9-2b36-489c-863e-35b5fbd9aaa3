import { useEffect, useState } from "react";

interface Props {
  value: number;
  isRangeEnable?: boolean;
  onChange: (value: number) => void;
}
const InputRange = ({ value, isRangeEnable, onChange }: Props) => {
  const [inputValue, setInputValue] = useState<number>(value);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.key === "Enter" && onChange(inputValue);
  };

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <input
      className="w-10 text-center outline-none border rounded-md text-xs font-medium"
      type="number"
      value={inputValue}
      disabled={isRangeEnable}
      onChange={(e) => setInputValue(parseFloat(e.target.value))}
      onBlur={() => onChange(inputValue)}
      onKeyDown={(e) => handleKeyDown(e)}
    />
  );
};

export default InputRange;
