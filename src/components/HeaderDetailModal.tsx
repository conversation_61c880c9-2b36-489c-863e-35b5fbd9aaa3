import React from "react";
import { Link } from "react-router-dom";
import {
  RiAdvertisementLine,
  RiEarthLine,
  RiInformation2Line,
  RiLiveLine,
  RiLock2Line,
} from "@remixicon/react";

import { Box } from "./Box";
import { Skeleton } from "./ui/skeleton";
import GroupAvatar from "./GroupAvatar";
import AvatarByName from "./AvatarByName";
import BadgePackage from "./BadgePackage";
import { SubTag } from "views/RequestAudienceView/components/column/TypeColumn";

import { cn } from "utils/utils";
import { fNumberToString } from "utils/number";
import { REQUEST_AUDIENCE_LABEL } from "constants/requestAudience/label";
import { SUB_TYPE_SOCIAL, SUB_TYPE_SOCIAL_LABEL, TYPE_SOCIAL } from "constants/requestAudience";

type Props = {
  contentRight?: React.ReactElement | JSX.Element;
  name: string;
  loading?: boolean;
  avatarUrl?: string | string[];
  uid?: string;
  size?: number;
  description?: string;
  packageValue?: string;
  className?: string;
  type?: string;
  typeAudience?: TYPE_SOCIAL;
  subTypeAudience?: SUB_TYPE_SOCIAL;
  category?: string;
  rightAvatarChild?: React.ReactNode;
  classRightContent?: string;
};

const HeaderDetailModal = (props: Props) => {
  const {
    contentRight,
    name,
    loading,
    avatarUrl,
    size,
    description,
    uid,
    packageValue,
    type,
    typeAudience,
    subTypeAudience,
    className,
    category,
    rightAvatarChild,
    classRightContent
  } = props;

  const isSingleAvatar =
    (Array.isArray(avatarUrl) && avatarUrl.length === 1) ||
    (!Array.isArray(avatarUrl) && avatarUrl);

  const isGroupAvatar = Array.isArray(avatarUrl) && avatarUrl.length > 1;

  /**
   *  show badge by subtype : "PRIVATE" | "PUBLIC" | "LIVE" | "ADS"
   */
  const renderSubTag = () => {
    const style = "py-1 px-2 gap-1 capitalize font-semibold rounded-sm text-secondary";
    switch (subTypeAudience) {
      case SUB_TYPE_SOCIAL.ad_post:
        return (
          <SubTag
            icon={<RiAdvertisementLine size={16} />}
            text={REQUEST_AUDIENCE_LABEL.ads_post}
            className={style}
          />
        );
      case SUB_TYPE_SOCIAL.live_post:
        return (
          <SubTag
            icon={<RiLiveLine size={16} />}
            text={REQUEST_AUDIENCE_LABEL.live_post}
            className={style}
          />
        );
      case SUB_TYPE_SOCIAL.private_group:
        return (
          <SubTag
            icon={<RiLock2Line size={16} />}
            text={SUB_TYPE_SOCIAL_LABEL[0].name}
            className={style}
          />
        );
      case SUB_TYPE_SOCIAL.public_group:
        return <SubTag icon={<RiEarthLine size={16} />} text={SUB_TYPE_SOCIAL_LABEL[1].name} />;
      default:
        if (typeAudience === TYPE_SOCIAL.post) {
          return <SubTag icon={<RiInformation2Line size={16} />} text="Post" />;
        }
        break;
    }
  };
  return (
    <Box className={cn("w-full shadow-sm p-4 mt-6 rounded-2xl justify-start items-start max-md:gap-4 md:gap-6 max-md:flex-col", className)}>
      {!loading && (
        <>
          <Box className={cn(rightAvatarChild && "justify-between")}>
            {isSingleAvatar && (
              <AvatarByName
                urlImage={Array.isArray(avatarUrl) ? avatarUrl[0] : avatarUrl}
                className={"min-w-12 w-12 h-12 max-md:min-w-16 max-md:w-16 max-md:h-16  text-xl"}
                name={name}
                position="first"
                type={typeAudience}
              />
            )}
            {isGroupAvatar && <GroupAvatar imgUrls={avatarUrl} type={"group"} size={60} />}
            {!isGroupAvatar && !isSingleAvatar && (
              <AvatarByName className={"min-w-12 w-12 h-12 max-md:min-w-16 max-md:w-16 max-md:h-16  text-xl"} name={name} type={typeAudience} />
            )}
            <Box className="flex md:hidden gap-0 flex-wrap mb-2 xl:flex-nowrap xl:gap-2 xl:mb-0">
              {uid ? (
                <h1>
                  <Link
                    to={type == "ads_post" ? "" : `https://www.facebook.com/${uid}`}
                    className="font-semibold text-start text-base md:text-lg tracking-[-0.3px] text-primary line-clamp-1 mr-2 max-w-[500px]"
                    target="_blank"
                  >
                    {name}
                  </Link>
                </h1>
              ) : (
                <h1>
                  <div
                    className="font-semibold text-start text-lg tracking-[-0.3px] text-primary line-clamp-1 mr-2 max-w-[500px]"
                  >
                    {name}
                  </div>
                </h1>
              )}

              <Box className="gap-2 mt-2 md:mt-0 ">
                <div className="w-max text-secondary font-semibold text-xs border border-custom-primary p-1 rounded-sm">
                  <span>Total lead: </span>
                  <span> {size ? fNumberToString(size) : 0}</span>
                </div>
                {packageValue && <BadgePackage packageValue={packageValue} />}
                {renderSubTag()}
              </Box>

            </Box>
          </Box>
          <Box variant="col-start" className="gap-1 items-start flex-1">
            <Box className="hidden md:flex gap-0 flex-wrap mb-2 xl:flex-nowrap xl:gap-2 xl:mb-0">
              {uid ? (
                <Link
                  to={type == "ads_post" ? "" : `https://www.facebook.com/${uid}`}
                  className="font-semibold text-start text-base md:text-lg tracking-[-0.3px] text-primary line-clamp-1 mr-2 max-w-[500px]"
                  target="_blank"
                >
                  {name}
                </Link>
              ) : (
                <div
                  className="font-semibold text-start text-lg tracking-[-0.3px] text-primary line-clamp-1 mr-2 max-w-[500px]"
                >
                  {name}
                </div>
              )}

              <Box className="gap-2 mt-2 md:mt-0 ">
                <div className="w-max text-secondary font-semibold text-xs border border-custom-primary p-1 rounded-sm">
                  <span>Total lead: </span>
                  <span> {size ? fNumberToString(size) : 0}</span>
                </div>
                {packageValue && <BadgePackage packageValue={packageValue} />}
                {renderSubTag()}
              </Box>

            </Box>
            <div className="text-sm gap-12 items-center text-secondary capitalize line-clamp-2 max-w-full md:max-w-[90%]">
              <span>{description ? description : ""}</span>
            </div>
            {category && <span className="text-sm text-tertiary" >{category}</span>}
          </Box>
          <div className={cn("flex items-center gap-2 max-md:flex-row max-lg:flex-col", classRightContent)}>{contentRight}</div>
        </>
      )}
      {loading && (
        <Box>
          <Skeleton className="w-[55px] h-[55px] rounded-md" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-4 w-full" />
        </Box>
      )}
    </Box>
  );
};

export default HeaderDetailModal;
