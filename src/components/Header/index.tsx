import Breadcrumb from "components/Breadcrumb";
import LeftHeader from "./LeftHeader";
import { RightHeader } from "./RightHeader";

import { cn } from "utils/utils";

interface Props {
  leftChildren?: {
    title?: React.ReactNode;
    highlight?: string | undefined;
    titleSize?: "sm" | "md" | "lg" | "xl" | "xxl";
    subTitle?: string;
  };
  rightChildren?: React.ReactNode | JSX.Element;
  isShowBreadcrumb?: boolean;
  className?: string;
  breadcrumbPath?: string;
}
const HeaderWrapper = ({
  rightChildren,
  leftChildren,
  isShowBreadcrumb = false,
  breadcrumbPath,
  ...props
}: Props) => {
  const { title, titleSize, subTitle, highlight } = leftChildren || {};

  return (
    <>
      {isShowBreadcrumb &&
        (breadcrumbPath ? (
          <Breadcrumb hiddenType path={`brand/${breadcrumbPath}`} />
        ) : (
          <Breadcrumb />
        ))}
      <div
        className={cn(
          "flex flex-wrap gap-2 lg:gap-0 items-center md:items-start mt-6 w-full",
          props.className
        )}
      >
        <LeftHeader title={title} highlight={highlight} titleSize={titleSize} subTitle={subTitle} />
        <RightHeader rightChildren={rightChildren} />
      </div>
    </>
  );
};

export default HeaderWrapper;
