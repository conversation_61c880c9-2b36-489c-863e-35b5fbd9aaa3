import { useContext } from "react";
import { RiCloseLine } from "@remixicon/react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "components/ui/dialog";
import { Box } from "./Box";
import { Button } from "components/ui/button";
import { ModalContext } from "providers/Modal";
import { cn } from "utils/utils";
import handleCloseModal from "utils/handleCloseModal";

const Modal = () => {
  const context = useContext(ModalContext);
  const ButtonSubmit = context?.dataDialog?.buttonSubmit;

  const handleSubmit = () => {
    context?.dataDialog?.handleSubmit && context?.dataDialog?.handleSubmit();
    context?.dataDialog?.handleCancel && context?.dataDialog?.handleCancel();
  };

  const handleCancel = () => {
    context?.dataDialog?.handleCancel && context?.dataDialog?.handleCancel();
    handleCloseModal(context);
  };
  return (
    <Dialog open={context?.dataDialog?.isOpen}>
      <DialogContent
        className={cn(
          "max-w-[425px] !rounded-2xl overflow-visible sm:p-6",
          context?.dataDialog?.className
        )}
        isShowCloseIcon={false}
      >
        {context?.dataDialog?.isShowTitle ? (
          <DialogHeader className="text-secondary">
            <Box className="justify-between py-2">
              <DialogTitle className="font-medium">{context?.dataDialog?.title}</DialogTitle>
              <RiCloseLine
                className="text-secondary cursor-pointer hover:text-tertiary"
                size={20}
                onClick={() => handleCancel()}
              />
            </Box>
            {context?.dataDialog?.message && (
              <DialogDescription>{context?.dataDialog?.message}</DialogDescription>
            )}
          </DialogHeader>
        ) : (
          <DialogTitle className="hidden"></DialogTitle>
        )}
        {context?.dataDialog?.content}
        {context?.dataDialog?.footer && context?.dataDialog?.footer}
        {!context?.dataDialog?.footer && ButtonSubmit && (
          <DialogFooter className="mt-0">
            <ButtonSubmit
              variant={context?.dataDialog?.buttonVariant}
              children={context?.dataDialog?.buttonText}
              onClick={() => handleSubmit}
            />
            <Button variant="secondary" children="Cancel" onClick={() => handleCancel()} />
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default Modal;
