import { RiArrowRightSLine } from "@remixicon/react";
import { cn } from "utils/utils";

const CustomNextArrow = (props: any) => {
  const { className, onClick, currentSlide, slideCount } = props;
  return (
    <div className={cn(className, "relative z-[2] before:hidden")}>
      <RiArrowRightSLine
        onClick={onClick}
        className={cn(
          "absolute top-0 right-8 w-8 h-8 p-1 bg-white border rounded-full shadow-medium",
          currentSlide == slideCount - 1 && "hidden"
        )}
        color="#20232C"
        size={16}
      />
    </div>
  );
};

export default CustomNextArrow;
