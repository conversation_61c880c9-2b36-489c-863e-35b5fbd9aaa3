import { RiArrowLeftSLine } from "@remixicon/react";
import { cn } from "utils/utils";

const CustomPrevArrow = (props: any) => {
  const { className, onClick, currentSlide } = props;
  return (
    <div className={cn(className, "relative z-[2] before:hidden")}>
      <RiArrowLeftSLine
        onClick={onClick}
        className={cn(
          "absolute top-0 left-8 w-8 h-8 p-1 bg-white border rounded-full shadow-medium",
          currentSlide == 0 && "hidden"
        )}
        color="#20232C"
        size={16}
      />
    </div>
  );
};

export default CustomPrevArrow;
