import React, { useCallback, useEffect, useState } from 'react';
import { RiExternalLinkLine, RiLoader2Fill } from '@remixicon/react';
import {
  NotificationAction,
  TNotificationData,
  TNotificationResponse,
} from '../../types/notification';
import { notificationApi } from '../../apis/notification';
import InfiniteScroll from 'react-infinite-scroll-component';
import { cn, formatDate } from '../../utils/utils';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  getCountUnread,
  getCountUnreadType, handleOpenDrawer,
  handleSaveRef, setIsNewNoti
} from '../../store/redux/notification/slice';
import { useNavigate } from 'react-router-dom';
import { DrawerClose } from '../ui/drawer';

type TNotificationContent = {
  isRead?: boolean;
  onRefetch: (ref: () => void) => void;
}

export const NotificationContent: React.FC<TNotificationContent> = ({ ...props }: TNotificationContent) => {
  const { isRead, onRefetch } = props;
  const dispatch = useAppDispatch();

  const [listNoti, setListNoti] = useState<TNotificationData[]>([]);
  const [page, setPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const { isNewNoti } = useAppSelector((state) => state.notification);


  const fetchNotifications = useCallback(async (currentPage: number) => {
    if (!hasMore) {
      return;
    }
    try {
      await handleFetchNoti(currentPage, false, isRead);
    } catch (error) {
      console.error(error);
    }
  }, [hasMore, isRead, page]);

  const handleFetchNoti = async (page: number, isRefetchUR: boolean, is_read?: boolean,) => {
    setLoading(true);
    if (!hasMore && !isRefetchUR){
      return;
    }
    const res = await notificationApi.get<TNotificationResponse>({
      params: {
        page: page,
        limit: 10,
        is_read: is_read
      }
    });
    const data = res?.data?.items ?? [];
    if (data.length < 10) {
      setHasMore(false);
    }
    if (isRefetchUR) {
      setListNoti(data);
    } else {
      setListNoti((prev) => [...prev, ...data]);
    }

    setLoading(false);
  };

  useEffect(() => {
    fetchNotifications(page);
  }, [page]);

  useEffect(() => {
    if (onRefetch) {
      onRefetch(() => handleFetchNoti(1, true,undefined));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onRefetch]);

  useEffect(()=>{
    if (isNewNoti){
      handleFetchNoti(1, true,undefined).finally(()=>{
        dispatch(setIsNewNoti(false));
      });
    }
  },[isNewNoti])

  const fetchMoreData = () => {
    setPage((prev) => prev + 1);
  };

  return (
    <>
      <div className="overflow-auto max-md:h-[calc(100vh-200px)] min-[769px]:h-[calc(100vh-130px)]" id="notification-content">
        {listNoti.length > 0 ? <InfiniteScroll
          dataLength={listNoti.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={undefined}
          scrollableTarget="notification-content"
        >
          {listNoti.map((noti) =>
            <NotificationItem
              key={noti.id}
              noti={noti}
              refetchNoti={handleFetchNoti}
            />
          )}
        </InfiniteScroll> : <></>}

        {loading && <div className="flex items-center justify-center h-14"><RiLoader2Fill color={'#20232C'} size={20} className="animate-spin" /></div>}
      </div>
    </>
  );
};
type TNotificationItem = {
  noti: TNotificationData,
  refetchNoti: (page: number, isRefetchUR: boolean, is_read?: boolean) => Promise<any>;
}

export const NotificationItem = ({ ...props }: TNotificationItem) => {
  const { noti, refetchNoti } = props;
  const { id, ref_id, ref_name, title, action, created_at, is_read, meta } = noti;
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const handleViewDetail = async () => {
    handleValidRoute();
    return await notificationApi.handleMarkAsRead(id).then((res) => {
      refetchNoti(1, true, false).finally(() => {
        dispatch(getCountUnreadType()).unwrap();
        dispatch(getCountUnread()).unwrap();
      });
      return res;
    })
  };

  const handleValidRoute = () => {
    dispatch(handleSaveRef({
      id: ref_id,
      type: action
    }));
    dispatch(handleOpenDrawer(false));
    const routeYourAudience = '/your-audience'
    const routeYourSegment = '/your-segments'
    const isdataset = meta.datatype === 'DATASET';
    switch (action) {
      case NotificationAction.DATA_ENRICHMENT:
        navigate('/enrichment');
        break;
      case NotificationAction.REQUEST_AUDIENCE:
        navigate('/request-audience');
        break;
      case NotificationAction.YOUR_AUDIENCE_SOCIAL:
        navigate(`${routeYourAudience}/social-${isdataset ? 'dataset' : 'audience'}/${ref_id}`);
        break;
      case NotificationAction.YOUR_AUDIENCE_WORK:
        navigate(`${routeYourAudience}/persona-audience/${ref_id}`);
        break;
      case NotificationAction.YOUR_SEGMENT_SOCIAL:
        navigate(`${routeYourSegment}/social-segment/${ref_id}`);
        break;
      case NotificationAction.YOUR_SEGMENT_WORK:
        navigate(`${routeYourSegment}/persona-segment/${ref_id}`);
        break;
      default:
        break;
    }
  };

  const handleViewMessage =()=>{
    switch (action){
      case NotificationAction.DATA_ENRICHMENT:
        return <span>Enrich <strong>{ref_name}</strong> has been successfully processed.</span>;
      case NotificationAction.REQUEST_AUDIENCE:
        return <span>Request <strong>{ref_name}</strong> has been successfully processed.</span>;
      case NotificationAction.YOUR_AUDIENCE_SOCIAL:
      case NotificationAction.YOUR_AUDIENCE_WORK:
      case NotificationAction.YOUR_SEGMENT_SOCIAL:
      case NotificationAction.YOUR_SEGMENT_WORK:
        return <span>Buy <strong>{ref_name}</strong> successfully</span>;
      default:
        return '';
    }
  }


  return <div className={cn('p-4 border-t border-[#E1E2E3]',!is_read&&'bg-[#F0F0F0]')}>
    <p className="text-primary font-medium text-sm mb-2 ">
      {title}
    </p>
    <p className="text-secondary text-sm mb-2">
      {handleViewMessage()}
    </p>
    <p className="text-xs text-secondary mb-2">
      {formatDate(created_at, true)}
    </p>
    <div className="border-b border-[#2C9EFF] w-fit">
      <DrawerClose className="text-sm p-0 m-0 w-auto h-auto hover:bg-transparent flex-1 text-[#2C9EFF] hover:text-[#2C9EFF]"  onClick={handleViewDetail}>
        <div className="flex gap-1">
          View detail
          <RiExternalLinkLine size={16} />
        </div>
      </DrawerClose>
    </div>
  </div>;
};
