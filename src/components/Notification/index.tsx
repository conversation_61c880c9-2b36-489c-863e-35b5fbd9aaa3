import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs";
import React, { useRef, useState } from "react";
import { NotificationContent } from "./NotificationContent";
import { Button } from "../ui/button";
import { RiCheckDoubleLine, RiLoader2Fill } from "@remixicon/react";
import { Badge } from "../ui/badge";
import { useAppDispatch, useAppSelector } from "../../store";
import { notificationApi } from "../../apis/notification";
import { resetNoti } from "../../store/redux/notification/slice";

type TNofiticationTab = {
  title: string;
  type: string;
};

const tabs: TNofiticationTab[] = [
  {
    title: "All",
    type: "all",
  },
  {
    title: "Unread",
    type: "unread",
  },
];

export const Notification: React.FC = () => {
  const [activeTab, setActiveTab] = useState("all");
  const { unreadCount } = useAppSelector((state) => state.notification);
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();
  const resetrefetchNotiRef = useRef<(() => void) | null>(null);

  const handleMarkAllAsRead = async () => {
    setLoading(true);
    await notificationApi
      .handleMarkAllAsRead()
      .then(() => {
        dispatch(resetNoti());
        handleRefetch();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleRefetch = () => {
    if (resetrefetchNotiRef.current) {
      resetrefetchNotiRef.current();
      setActiveTab("all");
    }
  };

  return (
    <div className="w-screen md:w-[472px]">
      <div className="flex items-center pt-[7px] mr-[48px] justify-between px-4">
        <p className="font-medium text-md text-primary-crm">Notification</p>
        {unreadCount > 0 && (
          <Button
            variant={"icon"}
            size={"icon"}
            className="h-auto"
            disabled={loading}
            onClick={handleMarkAllAsRead}
          >
            {loading ? (
              <RiLoader2Fill color={"#20232C"} size={20} className="animate-spin" />
            ) : (
              <RiCheckDoubleLine color={"#20232C"} size={20} />
            )}
          </Button>
        )}
      </div>
      <Tabs
        className="md:mt-2 box-content flex flex-col gap-0 "
        defaultValue={activeTab}
        value={activeTab}
        key={activeTab}
      >
        <TabsList className="justify-start w-full pt-0 rounded-none border-b border-b-custom-primary px-4">
          {tabs.map((tab: TNofiticationTab) => (
            <TabsTrigger
              className="flex-1 text-md py-2 md:py-4 pl-2 pr-2 md:pr-10 text-tertiary leading-5 box-border"
              value={tab.type}
              key={tab.type}
              onClick={() => setActiveTab(tab.type)}
            >
              <p className="w-max">{tab.title}</p>
              {tab.type === "unread" && unreadCount > 0 ? (
                <Badge className="rounded-md bg-brand-light hover:bg-brand-light text-[#924FE8] px-1 py-0.5 h-5 w-8 justify-center">
                  {unreadCount}
                </Badge>
              ) : (
                <></>
              )}
            </TabsTrigger>
          ))}
        </TabsList>
        {tabs.map((tab) => (
          <TabsContent className="w-full z-1 flex-1" value={tab.type} key={tab.type}>
            <NotificationContent
              isRead={tab.type === "all" ? undefined : false}
              onRefetch={(ref) => (resetrefetchNotiRef.current = ref)}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};
