import { Box } from '../Box';
import IconFavicon from '../../assets/icons/IconFavicon';
import LogoSvg from '../../assets/icons/LogoSvg';
import { Button } from '../ui/button';
import { RiArrowLeftLine } from '@remixicon/react';
import { Link, useNavigate } from 'react-router-dom';
import { PATH_DASHBOARD } from '../../types/path';
import useResponsive from '../../hooks/useResponsive';
import { useEffect, useRef, useState } from 'react';

export const Maintenance = () => {
  const navigate = useNavigate();
  const { isTablet,width } = useResponsive();
  const svgRef = useRef<SVGSVGElement>(null);
  const [resolution, setResolution] = useState({
    width: isTablet ? 343 : 775,
    height: isTablet ? 134 : 259
  });
  useEffect(() => {
    if (svgRef.current) {
      const widthSvg = ( width - 32 ) < 775 ? ( width - 32 ) : 775;
      setResolution({
        width: widthSvg,
        height: +( widthSvg / 2.5 ).toFixed(0)
      });
    }
  }, [width]);
  return (
    <div className='h-[500px] min-h-[500px]'>
      <Box variant="col-start" className="relative justify-center items-center gap-12 h-full">
        <div className="sm:absolute top-1/2 left-1/2  sm:transform sm:-translate-x-1/2 sm:-translate-y-1/2">
          <svg width={resolution.width} height={resolution.height} viewBox="0 0 775 259" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.2528 190.324H15.9933L38.8967 246.266H39.6865L62.5898 190.324H72.3304V257.718H64.6959V206.514H64.0378L42.9772 257.718H35.606L14.5454 206.514H13.8873V257.718H6.2528V190.324Z" fill="#F0F0F0"/>
            <path d="M101.651 257.718H93.0951L117.841 190.324H126.265L151.012 257.718H142.456L122.317 200.986H121.79L101.651 257.718ZM104.81 231.392H139.297V238.632H104.81V231.392Z" fill="#F0F0F0"/>
            <path d="M179.863 190.324V257.718H171.702V190.324H179.863Z" fill="#F0F0F0"/>
            <path d="M259.861 190.324V257.718H251.963L215.239 204.803H214.581V257.718H206.42V190.324H214.317L251.173 243.37H251.832V190.324H259.861Z" fill="#F0F0F0"/>
            <path d="M282.699 197.564V190.324H333.244V197.564H312.052V257.718H303.891V197.564H282.699Z" fill="#F0F0F0"/>
            <path d="M356.033 257.718V190.324H396.706V197.564H364.194V220.335H394.6V227.575H364.194V250.478H397.232V257.718H356.033Z" fill="#F0F0F0"/>
            <path d="M475.05 190.324V257.718H467.152L430.428 204.803H429.77V257.718H421.609V190.324H429.507L466.362 243.37H467.021V190.324H475.05Z" fill="#F0F0F0"/>
            <path d="M504.338 257.718H495.782L520.528 190.324H528.952L553.698 257.718H545.143L525.003 200.986H524.477L504.338 257.718ZM507.497 231.392H541.983V238.632H507.497V231.392Z" fill="#F0F0F0"/>
            <path d="M627.83 190.324V257.718H619.933L583.208 204.803H582.55V257.718H574.389V190.324H582.287L619.143 243.37H619.801V190.324H627.83Z" fill="#F0F0F0"/>
            <path d="M708.585 211.385H700.424C699.941 209.037 699.096 206.975 697.89 205.198C696.705 203.421 695.257 201.929 693.546 200.723C691.857 199.494 689.981 198.573 687.919 197.958C685.857 197.344 683.707 197.037 681.469 197.037C677.389 197.037 673.692 198.068 670.38 200.13C667.089 202.193 664.467 205.231 662.515 209.246C660.584 213.26 659.619 218.185 659.619 224.021C659.619 229.856 660.584 234.782 662.515 238.796C664.467 242.811 667.089 245.849 670.38 247.912C673.692 249.974 677.389 251.005 681.469 251.005C683.707 251.005 685.857 250.698 687.919 250.083C689.981 249.469 691.857 248.559 693.546 247.352C695.257 246.124 696.705 244.621 697.89 242.844C699.096 241.045 699.941 238.983 700.424 236.657H708.585C707.97 240.102 706.852 243.184 705.228 245.904C703.605 248.625 701.586 250.939 699.173 252.848C696.76 254.734 694.051 256.171 691.045 257.158C688.062 258.146 684.87 258.639 681.469 258.639C675.721 258.639 670.61 257.235 666.134 254.427C661.659 251.619 658.138 247.626 655.571 242.449C653.005 237.272 651.721 231.129 651.721 224.021C651.721 216.913 653.005 210.77 655.571 205.593C658.138 200.416 661.659 196.423 666.134 193.615C670.61 190.807 675.721 189.403 681.469 189.403C684.87 189.403 688.062 189.896 691.045 190.883C694.051 191.871 696.76 193.319 699.173 195.227C701.586 197.114 703.605 199.417 705.228 202.138C706.852 204.836 707.97 207.918 708.585 211.385Z" fill="#F0F0F0"/>
            <path d="M731.966 257.718V190.324H772.639V197.564H740.127V220.335H770.533V227.575H740.127V250.478H773.165V257.718H731.966Z" fill="#F0F0F0"/>
            <path d="M105.864 0.775879H136.195V110.165C136.195 122.155 133.361 132.702 127.692 141.804C122.078 150.906 114.175 158.019 103.983 163.142C93.7911 168.211 81.882 170.745 68.2561 170.745C54.5757 170.745 42.6394 168.211 32.4473 163.142C22.2551 158.019 14.3521 150.906 8.73819 141.804C3.12432 132.702 0.317383 122.155 0.317383 110.165V0.775879H30.6486V107.63C30.6486 114.607 32.1747 120.82 35.227 126.27C38.3337 131.721 42.6939 135.999 48.3078 139.106C53.9217 142.158 60.5711 143.684 68.2561 143.684C75.9412 143.684 82.5906 142.158 88.2045 139.106C93.8728 135.999 98.2331 131.721 101.285 126.27C104.338 120.82 105.864 114.607 105.864 107.63V0.775879Z" fill="#F0F0F0"/>
            <path d="M306.757 0.775879V168.211H279.778L200.884 54.1622H199.494V168.211H169.163V0.775879H196.306L275.118 114.906H276.589V0.775879H306.757Z" fill="#F0F0F0"/>
            <path d="M396.545 168.211H339.807V0.775879H397.69C414.313 0.775879 428.593 4.12785 440.529 10.8318C452.52 17.4812 461.731 27.0466 468.163 39.528C474.594 52.0093 477.81 66.9433 477.81 84.3299C477.81 101.771 474.567 116.76 468.081 129.295C461.65 141.831 452.357 151.451 440.202 158.155C428.103 164.859 413.55 168.211 396.545 168.211ZM370.138 141.968H395.073C406.737 141.968 416.466 139.842 424.26 135.591C432.054 131.285 437.913 124.881 441.838 116.378C445.762 107.821 447.724 97.1383 447.724 84.3299C447.724 71.5216 445.762 60.8934 441.838 52.4453C437.913 43.9427 432.109 37.5931 424.424 33.3963C416.793 29.145 407.31 27.0194 395.973 27.0194H370.138V141.968Z" fill="#F0F0F0"/>
            <path d="M506.629 168.211V0.775879H615.527V26.2018H536.96V71.6578H609.886V97.0838H536.96V142.785H616.181V168.211H506.629Z" fill="#F0F0F0"/>
            <path d="M646.471 168.211V0.775879H709.26C722.122 0.775879 732.914 3.01053 741.635 7.47982C750.41 11.9491 757.032 18.217 761.501 26.2836C766.025 34.2956 768.287 43.643 768.287 54.3257C768.287 65.0629 765.998 74.383 761.42 82.286C756.896 90.1346 750.219 96.2117 741.389 100.518C732.56 104.769 721.714 106.894 708.851 106.894H664.131V81.7138H704.763C712.285 81.7138 718.443 80.6782 723.24 78.607C728.036 76.4814 731.579 73.4019 733.868 69.3687C736.212 65.2809 737.383 60.2666 737.383 54.3257C737.383 48.3848 736.212 43.316 733.868 39.1192C731.524 34.8679 727.954 31.6522 723.158 29.472C718.362 27.2374 712.176 26.1201 704.6 26.1201H676.803V168.211H646.471ZM732.969 92.342L774.419 168.211H740.572L699.858 92.342H732.969Z" fill="#F0F0F0"/>
          </svg>
        </div>
        <Box variant="col-start" className="gap-3 w-full items-center z-10">
          <div className="flex gap-2 p-[1px] mb-3 items-center sm:px-[66px]">
            <IconFavicon />
            <LogoSvg color="hsla(229, 41%, 10%, 0.6)" />
          </div>
          <div className="text-primary text-2xl md:text-4xl font-medium">We are tidying up!</div>
          <p className="text-secondary text-sm md:text-base font-normal text-center whitespace-pre-line md:whitespace-nowrap">
            We’re working hard to improve the user experinence.
            <br />
            Stay tuned!
          </p>
          <Box className="flex-col lg:flex-row w-full lg:w-fit gap-3">
            <Button
              variant="outline"
              className="w-full lg:w-fit"
              onClick={() => navigate(-1)}
            ><RiArrowLeftLine size={21} /> Go back</Button>
            <Link to={`${PATH_DASHBOARD.audience_finder.social}`} className="w-full lg:w-fit">
              <Button variant="main" className="w-full lg:w-fit">Go Home</Button>
            </Link>
          </Box>
        </Box>
      </Box>
    </div>
  );
};
