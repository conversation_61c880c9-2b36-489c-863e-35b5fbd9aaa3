import { RiArrowDownSLine } from "@remixicon/react";
import { styled } from "styled-components";
import { TSelectOption } from "types/Select";
import React, { useEffect, useState } from "react";
import { cn } from "utils/utils";

interface Props {
  node: TSelectOption;
  isSingleSelect?: boolean;
  onBoxChecked: (event: React.ChangeEvent<HTMLInputElement>, node: TSelectOption) => void;
}

const TreeItem = ({ ...props }: Props) => {
  const { node, isSingleSelect = false, onBoxChecked } = props;
  const { label, checked, children, value = undefined } = node;
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const hasChildren = children && children.length > 0;

  // Automatically open the node based on the parent's checked status
  useEffect(() => {
    if (hasChildren && checked) {
      setIsOpen(true);
    }
    if (hasChildren) {
      children.map((child) => child.checked && setIsOpen(true));
    }
  }, [checked, hasChildren]);

  return (
    <>
      <TreeItemWrapper key={value} className='max-[768px]:!p-1'>
        <div
          className="flex-1"
          onClick={() => onBoxChecked({ target: { name: label, checked: !checked } } as any, node)}
        >
          {isSingleSelect ?
            <ItemRadio type="radio" name={label} value={value} checked={checked} readOnly /> :
            <ItemCheckbox type="checkbox" name={label} value={value} checked={checked} readOnly />}

          <label className="cursor-pointer" htmlFor={value as string} children={label} />
        </div>
        {hasChildren && (
          <RiArrowDownSLine
            size={20}
            color="#0F1324"
            opacity={0.6}
            onClick={() => setIsOpen(!isOpen)}
            className={cn(
              "transition duration-500 cursor-pointer",
              isOpen ? "rotate-180 " : "rotate-0 "
            )}
          />
        )}
      </TreeItemWrapper>
      {hasChildren && isOpen && (
        <div className="pl-6">
          {children.map((child) => (
            <TreeItem key={child.value} node={{ ...child }} onBoxChecked={onBoxChecked} />
          ))}
        </div>
      )}
    </>
  );
};

export default TreeItem;

const TreeItemWrapper = styled("li")(() => ({
  width: "100%",
  padding: "12px",
  display: "flex",
  alignItems: "center",
  gap: "8px",
  flexDirection: "row",
  justifyContent: "space-between",
  cursor: "pointer",
  "&:hover": { backgroundColor: "#0a0f2914", borderRadius: "5px" },
}));

const ItemCheckbox = styled.input`
  position: relative;
  cursor: pointer;
  margin: 5px 8px -2.5px 0;
  width: 16px;
  height: 16px;
  appearance: none;
  &:before {
    content: "";
    display: block;
    position: absolute;
    width: 16px;
    height: 16px;
    border: 1px solid #dee0e3;
    border-radius: 4px;
    background-color: white;
    z-index: 10;
    box-shadow: 0 1px 2px 0 #14151a0d;
  }
  &:checked:after {
    content: "";
    display: block;
    width: 5px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    position: absolute;
    top: 3px;
    left: 5px;
    border-radius: 1px;
    z-index: 10;
  }
  &:checked:before {
    background-color: #924fe8;
  }
`;
const ItemRadio = styled.input`
  position: relative;
  cursor: pointer;
  margin: 5px 8px -2.5px 0;
  width: 16px;
  height: 16px;
  appearance: none;
  &:before {
    content: "";
    display: block;
    position: absolute;
    min-width: 16px;
    min-height: 16px;
    width: 16px;
    height: 16px;
    border: 1px solid #dee0e3;
    border-radius: 30px;
    background-color: white;
    z-index: 10;
    box-shadow: 0 1px 2px 0 #14151a0d;
  }
  &:checked:after {
    content: "";
    display: block;
    min-width: 6px;
    min-height: 6px;
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
  &:checked:before {
    background-color: #924fe8;
    border: 1px solid #924fe8;
  }
`;
