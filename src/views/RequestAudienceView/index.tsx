import Breadcrumb from 'components/Breadcrumb';
import HeaderWrapper from 'components/Header';
import RequestTable from './components/RequestTable';
import SearchPreviewURL from './components/SearchPreviewURL';
import { useContext, useRef } from 'react';
import { REQUEST_AUDIENCE_LABEL } from 'constants/requestAudience/label';
import { TutorialIcon } from '../../assets/icons/TutorialIcon';
import { Button } from '../../components/ui/button';
import { ModalContext } from '../../providers/Modal';
import handleCloseModal from 'utils/handleCloseModal';
import { RiCloseLine } from '@remixicon/react';
import useOutsideClick from '../../hooks/useClickOutSide';

const RequestAudienceView = () => {
  const modal = useContext(ModalContext);
  const ref = useRef(null);
  const refSecond = useRef(null);

  useOutsideClick(ref, refSecond, () => {
    if (!!modal?.dataDialog?.isPopupVideo) {
      handleCloseModal(modal);
    }
  });
  const handleOpenTutorial =()=>{
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      isPopupVideo: true,
      content: (
        <div ref={refSecond}>
          <div className="text-right">
            <Button
              onClick={() => handleCloseModal(modal)}
              className="p-[10px] text-sm font-semibold mb-1 bg-transparent hover:bg-transparent text-primary"
            >
              <RiCloseLine size={20} />
              Close
            </Button>
          </div>
          <iframe
            src="https://www.youtube.com/embed/elMOPjboz5U?autoplay=1"
            title="Big360 - Request Audience"
            className="rounded-2xl w-full max-w-[920px] aspect-video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
        </div>
      ),
      className: 'max-w-[936px] !p-2 max-lg:max-w-[calc(100%-12px)]'
    } ))
  }

  return (
    <>
      <Breadcrumb />
      <HeaderWrapper
        leftChildren={{
          title: <div className="flex gap-2" ref={ref}>
            <h1>{REQUEST_AUDIENCE_LABEL.title}</h1>
            <Button onClick={()=>handleOpenTutorial()} className='flex items-center justify-center bg-[#F0F0F0] h-[36px] gap-1 p-2 rounded-xl	hover:bg-[#F0F0F0]'>
              <TutorialIcon /><p className="text-[#8F5CFF] text-xs mt-[1px]">Guide</p>
            </Button>
          </div>,
          subTitle: REQUEST_AUDIENCE_LABEL.sub_title,
        }}
      />
      <SearchPreviewURL />
      <RequestTable />
    </>
  );
};

export default RequestAudienceView;
