import { createContext, ReactNode, useEffect, useState } from "react";
import { socialAPI } from "apis/socialData";
import { toPaginationParams } from "utils/pagination";
import { TableType } from "types/Table";
import { TDataResponse } from "types/RequestAudience";
import useFilters from "hooks/useFilters";
import { useAbortController } from '../../hooks/useAbortController';

type RequestAudienceContextProps = {
  data: TableType<TDataResponse>;
  getData: () => Promise<void>;
  setData: React.Dispatch<React.SetStateAction<TableType<TDataResponse>>>;
} | null;

const RequestAudienceContext = createContext<RequestAudienceContextProps>(null);

const RequestAudienceProvider = ({ children }: { children: ReactNode }) => {
  const { params } = useFilters();
  const { newAbortController } = useAbortController();
  const [data, setData] = useState<TableType<TDataResponse>>({
    data: [],
    count: 0,
    loading: false,
  });

  useEffect(() => {
    getData();
  }, [params]);

  const getData = async () => {
    setData((prev) => ({ ...prev, loading: true }));
    const controller = newAbortController();
    const res = await socialAPI.get({
      endpoint: "request-audience/",
      params: toPaginationParams(params),
      signal: controller.signal
    });

    if (res.data) {
      const { data = {} } = res;
      setData({ data: data.items, count: data.count, loading: false });
    } else {
      setData({ ...data, loading: false });
    }
  };

  return (
    <RequestAudienceContext.Provider value={{ data, getData, setData }}>
      {children}
    </RequestAudienceContext.Provider>
  );
};
export { RequestAudienceProvider, RequestAudienceContext };
