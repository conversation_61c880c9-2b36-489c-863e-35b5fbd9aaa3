import { REQUEST_AUDIENCE_LABEL } from "constants/requestAudience/label.ts";
import { RiCloseLine, RiErrorWarningLine } from "@remixicon/react";
import handleCloseModal from "utils/handleCloseModal.ts";
import { Box } from "components/Box.tsx";
import { useContext } from "react";
import { ModalContext } from "providers/Modal.tsx";

export const RequestInformation = ({ message }: { message?: string }) => {
  const modal = useContext(ModalContext);
  return (
    <>
      <Box className="justify-between items-start w-full">
        <RiErrorWarningLine size={40} color="#8F5CFF" className='min-w-[40px]'/>
        <RiCloseLine className="cursor-pointer" onClick={() => handleCloseModal(modal)} />
      </Box>
      <span
        className="text-sm text-left text-secondary"
        children={message ? message : REQUEST_AUDIENCE_LABEL.notice_message}
      />
    </>
  );
};
