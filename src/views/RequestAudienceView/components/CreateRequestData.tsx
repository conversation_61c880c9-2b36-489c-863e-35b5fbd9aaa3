import { useAppDispatch, useAppSelector } from 'store';
import { useSelector } from "react-redux";
import { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ModalContext } from "providers/Modal";
import { getMe } from "store/redux/auth/slice";
import { subscriptionStore } from "store/redux/subscription/slice";

import { toast } from "components/ui/use-toast";
import { Button } from "components/ui/button";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import useFeatures from "hooks/useFeatures";
import usePreviewFB from "hooks/usePreviewFB";
import useShowEnoughCredit from "hooks/useShowEnoughCredit";
import { requestAudienceApi } from "apis/requestAudience";

import { PATH_DASHBOARD } from "types/path";
import { AUDIENCE_FINDER_PATH } from "types/Router";
import { IAudienceExist, IRequestData } from "types/RequestAudience";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import handleGetDataRequest from "utils/Transaction/handleGetDataRequest";
import { getFeatures } from "store/redux/features/slice";
import { tracking } from '../../../utils/Tracking/tracking';
import { audienceType } from '../../../utils/typeSocial';

interface Props {
  data: IRequestData | undefined;
  url: string;
  onRefresh?: () => Promise<void>;
}
const CreateRequestData = ({ data, url, onRefresh }: Props) => {
  const context = useContext(ModalContext);
  const { last_sub } = useSelector(subscriptionStore);
  const { user } = useAppSelector((state) => state.auth);


  const { setRequestData, setRequestURL } = usePreviewFB({ type: "request" });
  const { handleShowEnoughCredit } = useShowEnoughCredit();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [isAudienceExists, setIsAudienceExists] = useState<IAudienceExist>({
    isExist: undefined,
    loading: false,
  });

  const checkAudienceExists = async () => {
    setIsAudienceExists((prev) => ({ ...prev, loading: true }));
    if (data?.uid) {
      const res = await requestAudienceApi.checkAudienceExists({
        audience_id: data.uid,
      });
      /**
       * Nếu audience đã tồn tại --> handleAudienceExist
       * Nếu audience chưa tồn tại --> handleCreateRequest
       */
      res === false ? await handleCreateRequest() : handleAudienceExist(data);
      setIsAudienceExists({ loading: false, isExist: undefined });
    }
    setIsAudienceExists((prev) => ({ ...prev, loading: false }));
  };

  const handleAudienceExist = (data: IRequestData) => {
    context?.setDataDialog((prev) => ({ ...prev, isOpen: false }));
    /**
     * Nếu audience đã tồn tại đang processing @param status == 1 ==> Show toast thông báo
     * Nếu @param status == 2  ==> Redirect  về audience detail
     */
    if (data?.status === 1) {
      toast({
        title: "Audience request is already in progress. Please wait while we process it.",
        status: "info",
        duration: 3000,
        className: "border bg-infor-subtitle text-infor-text border-infor-text",
      });
    } else {
      navigate(
        `${PATH_DASHBOARD.audience_finder.social}/${AUDIENCE_FINDER_PATH.SOCIAL_DETAIL}/${data.uid}`
      );
    }
  };
  const handleCreateRequest = async () => {
    const payload = {
      ...data,
      url: url,
    };
    const response: any = await requestAudienceApi.createRequest({ payload: payload });
    if (response && response.data) {
      await dispatch(getMe());
      await dispatch(getFeatures());
      onRefresh && onRefresh();
      context?.setDataDialog((prev) => ({ ...prev, isOpen: false }));
    }

    if (response.error?.code == 6005 || response.error?.code == 5004) {
      handleShowEnoughCredit();
    }

    setRequestURL("");
    setRequestData({ data: undefined, loading: false });
    tracking({
      eventName: 'request_data',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          type: audienceType(data?.type ?? 0)?.name
        })
      }
    });
  };

  const is_overage = useFeatures(FEATURE_PERMISSION_KEY.SCD_REQUEST_AUDIENCE)?.is_overage;

  return (
    <Button
      variant="main"
      className="text-md p-[10px] border  rounded-xl focus-visible:ring-0 font-medium h-12"
      children={isAudienceExists.loading ? <LoadingButtonIcon /> : "Get data"}
      disabled={isAudienceExists.loading}
      onClick={() =>
        handleGetDataRequest({
          context,
          loading: isAudienceExists.loading,
          is_overage: is_overage || false,
          is_trial: last_sub?.plan_code == "TRIAL",
          callBack: checkAudienceExists,
        })
      }
    />
  );
};

export default CreateRequestData;
