import { RiEarthLine, RiInformation2Line, RiLock2Line } from "@remixicon/react";
import { SUB_TYPE_SOCIAL } from "constants/requestAudience";

const AudienceTypeIcon = ({ subType, size = 20 }: { subType?: SUB_TYPE_SOCIAL, size?: number }) => {
  switch (subType && subType) {
    case SUB_TYPE_SOCIAL.public_group:
      return <RiEarthLine size={size} />;
    case SUB_TYPE_SOCIAL.private_group:
      return <RiLock2Line size={size} />;
    default:
      return <RiInformation2Line size={size} />;
  }
};

export default AudienceTypeIcon;
