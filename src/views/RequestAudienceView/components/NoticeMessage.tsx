import { RiErrorWarningLine } from '@remixicon/react';
import { Box } from 'components/Box';
import { REQUEST_AUDIENCE_LABEL } from 'constants/requestAudience/label';
import { cn } from '../../../utils/utils';
import React from 'react';

const NoticeMessage = ({ message, colorIcon = '#FAC881',className }: {
  message?: React.ReactNode,
  colorIcon?: string,
  className?: string
}) => (
  <Box className="gap-2 justify-start">
    <RiErrorWarningLine size={16} color={colorIcon} className="min-w-[16px]" />
    <span
      className={cn('inline-block text-sm text-left text-secondary', className)}
      children={message ? message : REQUEST_AUDIENCE_LABEL.notice_message}
    />
  </Box>
);
export default NoticeMessage;
