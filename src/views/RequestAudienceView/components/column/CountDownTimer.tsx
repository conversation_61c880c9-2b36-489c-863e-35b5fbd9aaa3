import { useState, useEffect } from "react";

const formatTimeLeft = (milliseconds: number) => {
  const totalSeconds = Math.floor(milliseconds / 1000);
  const days = Math.floor(totalSeconds / 86400);
  const hours = Math.floor((totalSeconds % 86400) / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const check = (num: number) => (num < 10 ? `0${num}` : num);
  return `${check(days)} : ${check(hours)} : ${check(minutes)} : ${check(seconds)}`;
};

const CountdownTimer = ({ status, eta_time }: { status: number; eta_time: string }) => {
  const targetDate = new Date(eta_time).getTime();
  const [timeRemaining, setTimeRemaining] = useState(targetDate - Date.now());

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeRemaining(targetDate - Date.now());
    }, 1000);

    timeRemaining < 1 && setTimeRemaining(0);
    return () => clearInterval(interval);
  }, [targetDate]);

  return (
    status == 1 &&
    timeRemaining > 0 && (
      <div className="text-xs md:text-sm text-[#F53E3E]">{formatTimeLeft(timeRemaining)}</div>
    )
  );
};

export default CountdownTimer;
