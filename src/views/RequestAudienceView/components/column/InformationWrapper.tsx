import { Link } from "react-router-dom";
import { Box } from "components/Box";
import { Badge } from "components/ui/badge";
import VisitAuthor from "../VisitAuthor";
import AvatarByName from "components/AvatarByName";

import { TDataResponse } from "types/RequestAudience";
import useAvatar from "hooks/useAvatar";
import { cn, formatPayloadAvatar } from "utils/utils";
import { PATH_DASHBOARD } from "types/path";
import { REQUEST_AUDIENCE_LABEL } from "constants/requestAudience/label";
import { AUDIENCE_FINDER_PATH } from "types/Router";
import { getCountUnread, getCountUnreadType } from '../../../../store/redux/notification/slice';
import { notificationApi } from '../../../../apis/notification';
import { useAppDispatch, useAppSelector } from '../../../../store';
import { audienceType } from '../../../../utils/typeSocial';

const InformationWrapper = (params: TDataResponse) => {
  const { url, audience, status, uid, is_update, id } = params;
  const type = params?.audience?.type;

  const payload = formatPayloadAvatar(audience?.type || 1, audience?.fb_uid, audience?.actor_id);
  const { avatar } = useAvatar(payload);
  const dispatch = useAppDispatch();
  const { unreadByType } = useAppSelector((state) => state.notification);


  const handleViewDetail = async () => {
    const findNoti = unreadByType?.find((item) => item.ref_id === id.toString());
    if (!findNoti) {
      return;
    }
    return await notificationApi.handleMarkAsRead(findNoti?.id).then((res) => {
      dispatch(getCountUnreadType()).unwrap();
      dispatch(getCountUnread()).unwrap();
      return res;
    });
  };

  return audience ? (
    <Box className="gap-5 flex-shrink-0 w-full relative  px-0 items-start justify-start">
      <div className="relative !w-full !h-12 max-w-12 max-h-12">
        {is_update && (
          <Badge className="bg-custom-secondary text-primary absolute right-0 top-0 -translate-y-1/2 translate-x-1/2 text-[9px] rounded-sm py-0.5 px-1 hover:bg-custom-secondary">
            Update
          </Badge>
        )}
        <Link
          to={`${PATH_DASHBOARD.audience_finder.social}/${AUDIENCE_FINDER_PATH.SOCIAL_DETAIL}/${audience?.fb_uid}`}
          className={cn(
            ( status == 1 || status == 3 ) && 'pointer-events-none'
          )}
        >
          <AvatarByName
            name={audience.name}
            urlImage={avatar.url ?? ''}
            type={audience?.type}
            className="max-w-12 max-h-12 w-12 h-12"
          />
        </Link>
      </div>

      <Box variant="col-start" className="gap-1 text-secondary text-xs md:text-sm font-normal items-start">
        <Link
          to={`${PATH_DASHBOARD.audience_finder.social}/${AUDIENCE_FINDER_PATH.SOCIAL_DETAIL}/${audience?.fb_uid}`}
          className={cn(
            'text-xs md:text-md font-semibold text-primary text-wrap max-w-[410px] line-clamp-1 hover:text-tertiary ',
            ( status == 1 || status == 3 ) && 'pointer-events-none'
          )}

        >
          <p onClick={handleViewDetail}>
            {audience?.name}
          </p>
        </Link>
        {audience?.description && (
          <Link
            to={`${PATH_DASHBOARD.audience_finder.social}/${AUDIENCE_FINDER_PATH.SOCIAL_DETAIL}/${audience?.fb_uid}`}
            className={cn(
              'max-w-100% text-wrap line-clamp-2 max-w-[380px] ',
              ( status == 1 || status == 3 ) && 'pointer-events-none'
            )}

          >
            {audience?.description}
          </Link>
        )}
        <VisitAuthor
          facebookURL={audience?.type == 4 ? `https://www.facebook.com/${uid}` : url}
          content={<span children={<>{REQUEST_AUDIENCE_LABEL.visit_page} {audienceType(type)?.name}</>} />}
        />
      </Box>
    </Box>
  ) : (
    <div className="flex-1 text-center py-2 gap-2 w-20">--</div>
  );
};

export default InformationWrapper;
