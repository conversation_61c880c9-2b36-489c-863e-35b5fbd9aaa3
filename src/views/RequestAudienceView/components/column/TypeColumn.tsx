import { RiAdvertisementLine, Ri<PERSON><PERSON><PERSON><PERSON>ine, Ri<PERSON><PERSON><PERSON>ine, RiLock2Line } from "@remixicon/react";

import { Box } from "components/Box";

import { audienceType } from "utils/typeSocial";
import { SUB_TYPE_SOCIAL, SUB_TYPE_SOCIAL_LABEL } from "constants/requestAudience";
import { REQUEST_AUDIENCE_LABEL } from "constants/requestAudience/label";
import { cn } from "utils/utils";

export const SubTag = ({
  icon,
  text,
  className,
}: {
  icon: JSX.Element | React.ReactNode;
  text: string;
  className?: string;
}) => (
  <Box className={cn("gap-1 text-[12px] font-semibold p-1 bg-secondary rounded-sm", className)}>
    <span> {icon}</span>
    <span className="capitalize" children={text} />
  </Box>
);
const TypeColumn = (params: any) => {
  const type = params?.audience?.type;
  const subtype = params?.audience?.subtype;
  return (
    <div className="text-center">
      {(type || params?.audience) && (
        <Box variant="col-start" className="text-secondary gap-0 items-center py-2">
          <div className="uppercase text-xs lg:text-sm font-semibold" children={audienceType(type)?.name} />
          {subtype && (
            <>
              {subtype == SUB_TYPE_SOCIAL.ad_post && (
                <SubTag
                  icon={<RiAdvertisementLine size={16} />}
                  text={REQUEST_AUDIENCE_LABEL.ads_post}
                />
              )}
              {subtype == SUB_TYPE_SOCIAL.live_post && (
                <SubTag icon={<RiLiveLine size={16} />} text={REQUEST_AUDIENCE_LABEL.live_post} />
              )}
              {subtype == SUB_TYPE_SOCIAL.private_group && (
                <SubTag
                  icon={<RiLock2Line size={16} />}
                  text={SUB_TYPE_SOCIAL_LABEL[subtype - 1].name}
                />
              )}
              {subtype == SUB_TYPE_SOCIAL.public_group && (
                <SubTag
                  icon={<RiEarthLine size={16} />}
                  text={SUB_TYPE_SOCIAL_LABEL[subtype - 1].name}
                />
              )}
            </>
          )}
        </Box>
      )}
      {(!type || params?.audience == null) && "--"}
    </div>
  );
};

export default TypeColumn;
