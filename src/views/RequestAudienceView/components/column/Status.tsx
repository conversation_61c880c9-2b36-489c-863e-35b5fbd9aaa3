import { Badge } from 'components/ui/badge';
import { Button } from '../../../../components/ui/button';
import { RiCloseLine, RiErrorWarningLine } from '@remixicon/react';
import { useContext } from 'react';
import { REQUEST_AUDIENCE_LABEL } from 'constants/requestAudience/label.ts';
import handleCloseModal from 'utils/handleCloseModal.ts';
import { Box } from 'components/Box.tsx';
import { ModalContext } from 'providers/Modal.tsx';

const Status = ({ status }: { status: number }) => {
  const style = "p-2 rounded-xl border-none text-xs md:text-sm font-normal";
  const context = useContext(ModalContext);
  const handlePopup = () => {
    context?.setDataDialog((prev) => ( {
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <RequestErrorInformation message={'There was an error while processing your request'}/>,
      className: "max-w-[200px] md:max-w-[400px] p-4 gap-2 rounded-2xl",
      handleCancel: () => {
        handleCloseModal(context);
      }
    } ));
  };
  switch (status) {
    case 1:
      return <Badge className={style} variant="process" children="Processing" />
    case 2:
      return <Badge className={style} variant="success" children="Completed" />
    case 3:
      return <Box className="gap-1">
        <Badge className={style} variant="error" children="Failed" />
        <Button
          variant={'link'}
          className='p-0 h-auto underline text-secondary'
          onClick={() => handlePopup()}
        >
          <RiErrorWarningLine size={16} color="#515667"/>
        </Button>
      </Box>
  }
};

export default Status;

const RequestErrorInformation = ({ message }: { message?: string }) => {
  const modal = useContext(ModalContext);
  return (
    <>
      <Box className="justify-between items-start w-full">
        <RiErrorWarningLine size={40} color="#f53e3e" className='min-w-[40px]'/>
        <RiCloseLine className="cursor-pointer" onClick={() => handleCloseModal(modal)} />
      </Box>
      <span
        className="text-sm text-left text-secondary"
        children={message ? message : REQUEST_AUDIENCE_LABEL.notice_message}
      />
    </>
  );
};
