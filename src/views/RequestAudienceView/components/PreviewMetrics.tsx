import { Ri<PERSON><PERSON><PERSON><PERSON>, Ri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RiUserFollowLine } from "@remixicon/react";

import { Box } from "components/Box";

import { IRequestData } from "types/RequestAudience";
import { fNumberToCompact } from "utils/number";
import { TYPE_SOCIAL } from "constants/requestAudience";

const PreviewMetrics = (props: IRequestData) => {
  switch (props?.type) {
    case TYPE_SOCIAL.fanpage:
      return (
        <Box className="justify-between text-sm text-secondary flex items-center ">
          {props?.like_count && (
            <Box className="gap-1">
              <RiThumbUpLine size={16} />
              <span children={fNumberToCompact(props?.like_count) + " likes"} />
            </Box>
          )}
          {props?.follow_count && (
            <Box className="gap-1">
              <RiUserFollowLine size={16} />
              <span children={fNumberToCompact(props?.follow_count) + " follows"} />
            </Box>
          )}
        </Box>
      );
    case TYPE_SOCIAL.group:
      return (
        props?.member_count && (
          <Box className="gap-1">
            <RiGroupLine size={16} />
            <span children={fNumberToCompact(props?.member_count) + " members"} />
          </Box>
        )
      );
  }
};

export default PreviewMetrics;
