import { Box } from "components/Box";
import useFeatures from "hooks/useFeatures";

import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { REQUEST_AUDIENCE_LABEL } from "constants/requestAudience/label";

const RequestLimitModal = ({ isTrial }: { isTrial?: boolean }) => {
  const cost = useFeatures(FEATURE_PERMISSION_KEY.SCD_REQUEST_AUDIENCE)?.excess_usage_credit;
  const limit = useFeatures(FEATURE_PERMISSION_KEY.SCD_REQUEST_AUDIENCE)?.usage_limit;
  return (
    <Box variant="col-start" className="w-full gap-2">
      <div className="w-full text-center text-xl font-semibold">
        {REQUEST_AUDIENCE_LABEL.limit_title}
      </div>
      <div className="text-sm text-secondary text-center">
        {isTrial ? (
          `Your current plan allows up to ${limit} data requests from Facebook links. Upgrade now to increase your request limit and access more data!`
        ) : (
          <>
            You’ve reached your limit of {limit} data requests from Facebook links under your
            current plan. Purchase additional requests for
            <strong className="text-error-default ml-1">{cost}</strong> credits each.
          </>
        )}
      </div>
    </Box>
  );
};

export default RequestLimitModal;
