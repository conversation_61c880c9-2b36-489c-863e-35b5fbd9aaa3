import { useContext, useEffect } from 'react';
import { RequestAudienceContext } from "../RequestContext";

import { REQUEST_AUDIENCE_COLUMN } from "constants/requestAudience/column";
import { DataTable } from "components/DataTable/TableCustom";
import Pagination from "components/Pagination";
import useFilters from "hooks/useFilters";
import { useAppDispatch, useAppSelector } from '../../../store';
import {
  getUnreadType,
  TUnReadByType
} from '../../../store/redux/notification/slice';

const RequestTable = () => {
  const { params, setSearchParams } = useFilters();
  const data = useContext(RequestAudienceContext)?.data;
  const onRefreshTable = useContext(RequestAudienceContext)?.getData;
  const dispatch = useAppDispatch();
  const { unreadByType, timeReadNoti } = useAppSelector((state) => state.notification);

  useEffect(() => {
    dispatch(getUnreadType({
      type: 'request_audience'
    }));
  }, [timeReadNoti]);

  useEffect(() => {
    if (onRefreshTable && timeReadNoti) {
      onRefreshTable().finally(() => {
      });
    }
  }, [timeReadNoti]);

  const handleUpdateData = () => {
    if (!data){return [];}
    return data.data.map((request) => {
      if (unreadByType?.some((item: TUnReadByType) => item.ref_id === request.id.toString())) {
        return { ...request, isHighLight: true };
      }
      return request;
    });
  };

  return (
    <div className="mt-6">
      <DataTable
        columns={REQUEST_AUDIENCE_COLUMN}
        data={handleUpdateData()}
        loading={data?.loading}
      />
      <Pagination
        className="mt-4"
        currentPage={Number(params["page"]) || 1}
        pageSize={10}
        totalCount={data?.count || 0}
        onPageChange={(page) => setSearchParams((prev) => ({ ...prev, page: String(page) }))}
      />
    </div>
  );
};

export default RequestTable;
