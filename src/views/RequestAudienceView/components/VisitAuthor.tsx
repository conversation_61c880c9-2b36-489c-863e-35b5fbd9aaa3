import { RiExternalLinkLine } from "@remixicon/react";
import { Link } from "react-router-dom";

interface Props {
  facebookURL: string;
  content: React.ReactNode | JSX.Element;
}

const VisitAuthor = ({ facebookURL, content }: Props) => (
  <Link
    className="underline flex items-center gap-1 hover:text-infor-primary line-clamp-3 text-ellipsis max-w-[410px] text-wrap"
    to={facebookURL}
    target="_blank"
  >
    {content}
    <RiExternalLinkLine size={16} />
  </Link>
);

export default VisitAuthor;
