import { Box } from "components/Box";
import AvatarByName from "components/AvatarByName";
import PreviewMetrics from "./PreviewMetrics";
import AudienceTypeIcon from "./AudienceTypeIcon";

import { IRequestData } from "types/RequestAudience";
import { audienceSubType, audienceType } from "utils/typeSocial";
import { TYPE_SOCIAL } from "constants/requestAudience";
import { cn } from "utils/utils";
import { RiCloseLine } from "@remixicon/react";
import { useContext } from "react";
import { ModalContext } from "providers/Modal";
import handleCloseModal from "utils/handleCloseModal";
import useResponsive from "hooks/useResponsive.tsx";

const RequestPreview = ({ data }: { data: IRequestData | undefined }) => {
  const modal = useContext(ModalContext);
  const { isMobile } = useResponsive();
  const audienceTypeFormat = data?.subtype
    ? audienceSubType(data?.subtype)?.name
    : audienceType(data?.type || 1)?.name;

  return (
    data && (
      <>
        <Box className="justify-between h-8 w-full">
          <div className="text-secondary text-md font-medium">Preview Link</div>
          <RiCloseLine className="cursor-pointer" onClick={() => handleCloseModal(modal)} />
        </Box>
        <Box
          variant="col-start"
          className="p-4 rounded-2xl bg-white w-full gap-4 lg:max-h-[500px] xl:max-h-[600px] overflow-y-auto"
        >
          <Box
            className={cn("gap-4 items-start ", data?.type == TYPE_SOCIAL.post && "items-center", "items-start md:items-center")}
          >
            <AvatarByName
              urlImage={data?.img}
              name={data?.name}
              className="w-[32px] h-[32px] md:w-20 md:h-20 "
              type={data?.type}
            />
            <Box
              variant="col-start"
              className="gap-[7px] md:gap-3 w-full flex-1 text-sm text-secondary font-medium"
            >
              <h4 className="text-primary text-sm md:text-lg leading-6" children={data?.name} />
              {data?.type !== TYPE_SOCIAL.post && (
                <div className="text-sm font-normal" children={data?.description} />
              )}
              <Box className="gap-2 md:gap-4 text-xs md:text-sm">
                <Box className="gap-1">
                  <AudienceTypeIcon subType={data?.subtype} size={isMobile ? 16 : 20} />
                  <span children={audienceTypeFormat} />
                  {data?.category && <span children={"- " + data?.category} />}
                </Box>
                <Box className="gap-1">
                  <span children={"Social ID: " + data?.uid || data?.actor_id} />
                </Box>
              </Box>
              <PreviewMetrics {...data} />
            </Box>
          </Box>
          {data?.type === TYPE_SOCIAL.post && (
            <Box variant="col-start" className="w-full gap-4">
              <div className="text-sm text-secondary line-clamp-3" children={data?.description} />
              {data?.attachments && data?.attachments?.length > 0 && (
                <img
                  src={data?.attachments[0]}
                  className="w-full object-contain 2xl:max-h-[500px] max-h-[300px] lg:max-h-[400px] !rounded-2xl"
                />
              )}
            </Box>
          )}
        </Box>
      </>
    )
  );
};

export default RequestPreview;
