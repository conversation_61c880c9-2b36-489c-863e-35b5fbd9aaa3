import { Title } from 'views/AuthenticationView/components/HeaderModal';
import { Button } from 'components/ui/button';
import { Link, useNavigate } from 'react-router-dom';
import { useCallback, useEffect, useState } from 'react';
import { AUTHEN_LABEL } from 'constants/label';
import { resendAcctVerification } from '../../../../apis/auth';
import { toast } from 'components/ui/use-toast';
import { Toaster } from '../../../../components/ui/toaster';

type IVerifyStatus = {
  status: 'success' | 'error' | 'verified' | 'invalid' | null;
  callback: () => void;
};

const VerificationEmail = ({ status, callback }: IVerifyStatus) => {
  const [countdown, setCountdown] = useState(3);
  const [resendCountdown, setResendCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)
  const [loading, setLoading] = useState(false);
  const resendEmail = localStorage.getItem('resendEmail');
  const navigate = useNavigate();
  useEffect(() => {
    if (status === null || status === 'error' || status === 'invalid') {
      return;
    }
    const timer = setTimeout(() => {
      navigate('/login');
    }, 3000);
    return () => clearTimeout(timer);
  }, [status]);
  useEffect(() => {
    if (status === null || status === 'error' || status === 'invalid') {
      return;
    }
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  useEffect(() => {
    if (resendCountdown > 0) {
      const timerId = setTimeout(() => {
        setResendCountdown(resendCountdown - 1)
      }, 1000)
      setCanResend(false)
      return () => clearTimeout(timerId)
    } else {
      setCanResend(true)
    }
  }, [resendCountdown])

  const handleResendVerification = useCallback(async () => {
    if (!resendEmail) {
      toast({
        title: "Invalid email",
        status: "error",
        duration: 3000,
      })
    } else {
      setLoading(true)
      try {
        await resendAcctVerification(resendEmail).finally(() => {
          setLoading(false)
        })
        setResendCountdown(60)
        setCanResend(false)
        toast({
          title: "Verification email sent",
          status: "success",
          duration: 3000,
        })
      } catch (error: any) {
        toast({
          title: "Failed to send verification email",
          status: "error",
          duration: 3000,
        })
      }
      setLoading(false)
    }
  }, [resendEmail])

  switch (status) {
    case 'success':
      return (
        <div className="h-full flex flex-col gap-4 justify-center">
          <Title
            title={AUTHEN_LABEL.verifyEmail.success.title}
            subTitle={AUTHEN_LABEL.verifyEmail.success.subTitle}
          />
          <div className="flex text-sm gap-1 justify-center">
            <span className="text-secondary">Back to</span>
            <Link to="/login" className="text-primary hover:underline">
              Log in
            </Link>
          </div>
          <div className="flex text-sm gap-1 justify-center">
            Auto redirect to login in {countdown}
          </div>
        </div>
      );
    case 'verified':
      return (
        <div className="h-full flex flex-col gap-4 justify-center">
          <Title
            title={AUTHEN_LABEL.verifyEmail.verified.title}
            subTitle={AUTHEN_LABEL.verifyEmail.verified.subTitle}
          />
          <div className="flex text-sm gap-1 justify-center">
            <span className="text-secondary">Back to</span>
            <Link to="/login" className="text-primary hover:underline">
              Log in
            </Link>
          </div>
          <div className="flex text-sm gap-1 justify-center">
            Auto redirect to login in {countdown}
          </div>
        </div>
      );
    case 'error':
      return (
        <div className="h-full flex flex-col gap-4 justify-center">
          <Title
            title={AUTHEN_LABEL.verifyEmail.error.title}
            subTitle={AUTHEN_LABEL.verifyEmail.error.subTitle}
          />

          <Button
            onClick={callback}
            type="submit"
            children={'Resend verification'}
            className="text-white bg-primary rounded-xl shadow-xs hover:bg-primary-hover"
          />
          <div className="flex text-sm gap-1 justify-center">
            <span className="text-secondary">Back to</span>
            <Link to="/login" className="text-primary hover:underline">
              Log in
            </Link>
          </div>
        </div>
      );

    case 'invalid':
      return (
        <div className="h-full flex flex-col gap-4 justify-center">
          <Title title={AUTHEN_LABEL.verifyEmail.invalid.title} />
          <div className="flex text-sm gap-1 justify-center">
            <span className="text-secondary">Back to</span>
            <Link to="/login" className="text-primary hover:underline">
              Log in
            </Link>
          </div>
        </div>
      );
    case null:
      return (
        <div className="h-full flex flex-col gap-4 justify-center">
          <Title
            title={AUTHEN_LABEL.verifyEmail.null.title}
            subTitle={AUTHEN_LABEL.verifyEmail.null.subTitle}
          />
          <div className="flex text-sm gap-1 justify-center">
            <span className="text-secondary">Back to</span>
            <Link to="/login" className="text-primary hover:underline">
              Log in
            </Link>
          </div>
          <Button
            disabled={loading || !canResend}
            onClick={() => handleResendVerification()}
            className="text-white bg-primary rounded-xl shadow-xs hover:bg-primary-hover"
          >
            {canResend ? "Resend verification" : `Resend verification (${resendCountdown}s)`}
          </Button>
          <Toaster />
        </div>
      );
  }
};

export default VerificationEmail;
