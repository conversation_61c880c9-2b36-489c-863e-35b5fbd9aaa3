import { yupR<PERSON>olver } from '@hookform/resolvers/yup';
import { resendForgotPassword } from 'apis/auth';
import InputController from 'components/InputController';
import { Button } from 'components/ui/button';
import { toast } from 'components/ui/use-toast';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ForgotPasswordBody, ForgotPasswordBodyType } from 'validations/account';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';
import { ValidationPassword } from '../../../../components/ValidationPassword';

type Props = {
  token?: string;
  setStatus: (status: 'request' | 'pending' | 'success' | 'updated') => void;
};

const ChangePassword = ({ token, setStatus }: Props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const {
    handleSubmit,
    control,
    watch,
    formState: { errors }
  } = useForm<Omit<ForgotPasswordBodyType, 'email'>>({
    resolver: yupResolver(ForgotPasswordBody.omit(['email'])),
    defaultValues: {
      password: ''
    }
  });
  const onSubmit: SubmitHandler<Omit<ForgotPasswordBodyType, 'email'>> = async (data) => {
    if (!token || token === '0') {
      return;
    }
    setLoading(true);
    const res = await resendForgotPassword(data.password, token);
    if (res.code === 1010) {
      toast({
        title: 'Invalid token',
        description: res.message,
        status: 'error',
        duration: 3000
      });
      navigate('/authen/forgot-password/0/');
    } else {
      toast({
        title: 'Password changed',
        description: res.message,
        status: 'success',
        duration: 3000
      });
      setStatus('updated');
    }
    setLoading(false);
  };

  const newPassword = watch('password');

  return (
    <form className="flex flex-col gap-2" onSubmit={handleSubmit(onSubmit)}>
      <InputController
        control={control}
        label="New password"
        name="password"
        placeholder="Enter your new password"
        error={errors.password?.message}
        required={true}
        type="password"
      />
      <div className="py-2">
        <ValidationPassword password={newPassword} className={'gap-2 lg:gap-2 flex-col'} />
      </div>
      <Button
        type="submit"
        disabled={loading}
        children={loading ? <LoadingButtonIcon /> : 'Change password'}
        className="text-white bg-primary rounded-xl shadow-xs hover:bg-primary-hover"
      />
    </form>
  );
};
export default ChangePassword;
