import { useAppDispatch, useAppSelector } from "store";
import { <PERSON>mit<PERSON>and<PERSON>, useForm } from "react-hook-form";
import { Link, useNavigate } from 'react-router-dom';
import { LoginBody, LoginBodyType } from "validations/account";
import { login } from "store/redux/auth/slice";

import { Button } from "components/ui/button";
import Separator from "components/Separator";
import InputController from "components/InputController";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import { Title } from "views/AuthenticationView/components/HeaderModal";

import { yupResolver } from "@hookform/resolvers/yup";
import getUserInfor from "hooks/getUserInfor";
import { setTokensToLS } from "utils/localStorage";
import { AUTHEN_LABEL } from "constants/label";
import { Toaster } from '../../components/ui/toaster';
import { GoogleLogin } from '../../components/GoogleLogin';

import { CloudFlareVerify } from './CloudFlareVerify';
import { useCookies } from 'react-cookie';
import useRedirectURL from '../../hooks/useRedirectURL';

const Login = () => {
  const [cookies, setCookie] = useCookies(['captchaVerified']);
  const verified = cookies.captchaVerified;
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { paramsNoReDirect, reDirect } = useRedirectURL();

  const { loading, user } = useAppSelector((state) => state.auth);
  const { isReady } = getUserInfor();
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<LoginBodyType>({
    resolver: yupResolver(LoginBody),
    defaultValues: {
      username: user?.email || "",
      password: "",
    },
  });

  const onSubmit: SubmitHandler<LoginBodyType> = async (data) => {
    const reDirectURL = reDirect
      ? `${reDirect}?${new URLSearchParams(paramsNoReDirect).toString()}`
      : null;
    dispatch(login(data)).unwrap().then((value) => {
      setTokensToLS(value.data.access_token, value.data.refresh_token);
      if (isReady) {
        // navigate("/dashboard");
        navigate(reDirectURL ?? '/social-data');
      }
    }).catch((e) => {
      return e;
    });
  };

  const handleViewVerify = () => {
    const expireDate = new Date();
    expireDate.setDate(expireDate.getDate() + 30);

    setCookie('captchaVerified', 'true', {
      path: '/',
      expires: expireDate,
      secure: true,
      sameSite: 'strict',
    });

    setTimeout(() => {
      navigate(0);
    }, 1500);
  };

  if (!verified){
    return <CloudFlareVerify handleViewVerify={handleViewVerify} />
  }
  return (
    <div className="flex justify-center my-auto flex-col gap-8 h-full">
      <Title title={AUTHEN_LABEL.login.title} subTitle={AUTHEN_LABEL.login.subTitle} />
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(onSubmit)}>
        <InputController
          error={errors.username?.message}
          required={true}
          name="username"
          placeholder="Email or username"
          control={control}
        />
        <InputController
          error={errors.password?.message}
          required={true}
          name="password"
          type="password"
          placeholder="Password"
          control={control}
        />
        <Button
          disabled={loading}
          children={!loading ? "Log in" : <LoadingButtonIcon />}
          className="text-white bg-primary rounded-xl shadow-xs hover:bg-primary-hover"
        />
      </form>
      <Separator content="Or authorize with" />
      <GoogleLogin />
      <div className="text-center">
        <Link
          to="/authen/forgot-password/0/"
          className="text-center text-sm text-[#924FE8] hover:text-primary-hover"
        >
          {AUTHEN_LABEL.forgotPassword.title}
        </Link>
      </div>
      <div className="flex justify-center text-sm gap-1">
        <span className="text-secondary">{AUTHEN_LABEL.no_have_account}</span>
        <Link to="/signup" className="text-[#924FE8] hover:underline hover:tex-primary-hover font-medium">
          {AUTHEN_LABEL.sign_up}
        </Link>
      </div>
      <Toaster />
    </div>
  );
};

export default Login;
