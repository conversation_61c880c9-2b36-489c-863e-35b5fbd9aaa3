import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import LoadingFavicon from '../../components/Loading/LoadingFavicon';
import { useCallback, useEffect } from 'react';
import { exchangeSession } from '../../apis/auth';
import { HttpStatusCode } from 'axios';
import { clearLS, setTokensToLS } from '../../utils/localStorage';
import { useAppDispatch } from '../../store';
import { getMe } from '../../store/redux/auth/slice';
import { getCategories } from '../../store/redux/category/slice';
import { getFeatures } from '../../store/redux/features/slice';
import { fetchActiveSubscription, fetchLastSubscription } from '../../store/redux/subscription/slice';

export const LoginGoogle = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const session = Object.fromEntries(searchParams.entries())['session'] ?? '';
  const dispatch = useAppDispatch();
  const handleVerify = useCallback(async () => {
    if (!session) {
      return;
    }
    try {
      const redirectValue = location.search.split('redirect=');
      const res: any = await exchangeSession(session);
      if (res.status === HttpStatusCode.Ok) {
        const { access_token, refresh_token } = res.data.data;
        setTokensToLS(access_token, refresh_token);
        await Promise.all([
          dispatch(getMe()).unwrap(),
          dispatch(getCategories()).unwrap(),
          dispatch(getFeatures()).unwrap(),
          dispatch(fetchLastSubscription()).unwrap(),
          dispatch(fetchActiveSubscription()).unwrap()
        ]);
        if (redirectValue.length === 2) {
          navigate(redirectValue[1] ?? '/');
        } else {
          navigate('/');
        }
      }
    } catch (error) {
      navigate('/login');
      clearLS();
    }
  }, [session]);

  useEffect(() => {
    if (!session) {
      navigate('/login');
      return;
    }
    handleVerify();
  }, [session]);

  return (
    <div className="w-full h-full flex items-center justify-center">
      <LoadingFavicon />
    </div>
  );
};
