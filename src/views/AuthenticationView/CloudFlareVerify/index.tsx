import { Turnstile } from '@marsidev/react-turnstile';
import IconFavicon from '../../../assets/icons/IconFavicon';

interface Props {
  handleViewVerify: () => void;
}
const siteKey = import.meta.env.REACT_APP_SITE_KEY??''
export const CloudFlareVerify = (props:Props) => {
  const { handleViewVerify } = props;
  return (
    <div className="bg-[#222] text-[#d9d9d9] h-screen w-full absolute top-0 left-0 overflow-hidden">
      <div className="max-w-[60rem] px-6 py-32 mx-auto">
        <h1 className="flex items-center gap-2 text-4xl font-medium leading-[3.75rem]">
          <IconFavicon />
          app.big360.ai
        </h1>
        <p className="text-2xl mb-8 font-medium">
          Verify that you are human by completing the action below.
        </p>
        <Turnstile
          siteKey={siteKey}
          as="aside"
          options={{
            theme: 'auto',
            size: 'normal',
            language: 'en',
          }}
          onSuccess={handleViewVerify}
        />
        <p className="mt-16">
          app.big360.ai Your connection's security needs to be evaluated before proceeding.
        </p>
      </div>
    </div>
  );
};
