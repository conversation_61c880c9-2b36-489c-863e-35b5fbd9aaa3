import { Submit<PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { RegisterBody, RegisterBodyType } from 'validations/account';
import { useMemo, useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useCookies } from 'react-cookie';
import { toast } from 'components/ui/use-toast';

import InputController from 'components/InputController';
import CheckboxController from 'components/CheckboxController';
import { Button } from 'components/ui/button';
import { Toaster } from 'components/ui/toaster';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';
import { Title } from 'views/AuthenticationView/components/HeaderModal';
import { callRegister } from 'apis/auth';
import { referralApi } from 'apis/referral';
import { CloudFlareVerify } from './CloudFlareVerify';
import useFilters from 'hooks/useFilters';
import { Box } from 'components/Box';
import NoticeMessage from '../RequestAudienceView/components/NoticeMessage';
import { Checkbox } from 'components/ui/checkbox';
import { AUTHEN_LABEL } from 'constants/label';
import { RiCheckDoubleLine, RiLoaderLine } from '@remixicon/react';
import { cn } from 'utils/utils';

const Signup = () => {
  const navigate = useNavigate();
  const { params } = useFilters();
  const referralCodeDefault = useMemo(() => params.referralCode ?? '', [params]);

  const [cookies, setCookie] = useCookies(['captchaVerified']);
  const verified = cookies.captchaVerified;

  const [referralStatus, setReferralStatus] = useState({
    referralCode: referralCodeDefault,
    showReferralInput: !!referralCodeDefault,
    loading: false,
    loadingVerify: false,
    isReferralValid: !!referralCodeDefault,
    hasReferralError: false
  });

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<RegisterBodyType>({
    resolver: yupResolver(RegisterBody),
    defaultValues: {
      email: '',
      password: '',
      full_name: '',
      confirmPassword: '',
      term: false
    },
    mode: 'onChange'
  });

  const onSubmit: SubmitHandler<RegisterBodyType> = async (payload) => {
    setReferralStatus((prev) => ( { ...prev, loading: true } ));
    try {
      const normalizedEmail = payload.email.toLowerCase().trim();
      const payloadRegister = {
        ...payload,
        email: normalizedEmail,
        ...( referralStatus.referralCode && { referral_code: referralStatus.referralCode } )
      };
      localStorage.setItem('resendEmail', normalizedEmail);

      const res = await callRegister(payloadRegister);
      if (res.code === 0) {
        toast({
          title: 'Registration successful',
          description: 'You have successfully registered',
          status: 'success',
          duration: 3000
        });
        navigate('/authen/verify-email/0/');
      }
    } catch (err) {
      console.error(err);
    } finally {
      setReferralStatus((prev) => ( { ...prev, loading: false } ));
    }
  };

  const verifyReferralCode = async () => {
    setReferralStatus((prev) => ( { ...prev, loadingVerify: true } ));
    try {
      const res = await referralApi.checkValidReferral({ referral_code: referralStatus.referralCode });
      const valid = res.data.valid;
      setReferralStatus((prev) => ( {
        ...prev,
        isReferralValid: valid,
        hasReferralError: !valid
      } ));
    } finally {
      setReferralStatus((prev) => ( { ...prev, loadingVerify: false } ));
    }
  };

  const handleCaptchaSuccess = () => {
    const expireDate = new Date();
    expireDate.setDate(expireDate.getDate() + 30);
    setCookie('captchaVerified', 'true', {
      path: '/',
      expires: expireDate,
      secure: true,
      sameSite: 'strict'
    });

    setTimeout(() => navigate(0), 1500);
  };

  if (!verified) {
    return <CloudFlareVerify handleViewVerify={handleCaptchaSuccess} />;
  }

  return (
    <div className="flex flex-col justify-center gap-8 h-full my-auto">
      <Title title={AUTHEN_LABEL.register.title} subTitle={AUTHEN_LABEL.register.subTitle} />
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(onSubmit)}>
        <InputController
          label="Full Name"
          name="full_name"
          placeholder="Enter Full Name"
          control={control}
          required
          error={errors.full_name?.message}
        />
        <InputController
          label="Email"
          name="email"
          placeholder="Enter your email address"
          control={control}
          required
          error={errors.email?.message}
        />
        <InputController
          label="Password"
          name="password"
          type="password"
          placeholder="Enter password"
          control={control}
          required
          error={errors.password?.message}
        />
        <InputController
          label="Confirm Password"
          name="confirmPassword"
          type="password"
          placeholder="Confirm password"
          control={control}
          required
          error={errors.confirmPassword?.message}
        />

        <Box className="gap-1 justify-start">
          <Checkbox
            className="w-4 h-4"
            classNameCheckBox="w-3 h-3"
            name="showReferralInput"
            checked={referralStatus.showReferralInput}
            onCheckedChange={(checked) =>
              setReferralStatus((prev) => ( { ...prev, showReferralInput: checked as boolean } ))
            }
          />
          <span className="text-sm font-normal text-secondary">I have a referral code</span>
        </Box>

        {referralStatus.showReferralInput && (
          <Box className="gap-1">
            <input
              value={referralStatus.referralCode}
              onChange={(e) =>
                setReferralStatus((prev) => ( {
                  ...prev,
                  referralCode: e.target.value,
                  isReferralValid: false,
                  hasReferralError: false
                } ))
              }
              placeholder="Enter Referral Code"
              className={cn(
                'p-3 text-tertiary border-b border-custom-primary w-full rounded-none focus:outline-none h-10',
                referralStatus.hasReferralError && 'border-[#F53E3E]'
              )}
            />
            <Button
              type="button"
              disabled={!referralStatus.referralCode || referralStatus.loadingVerify || referralStatus.isReferralValid}
              className={cn(
                'm-0 p-0 h-10 bg-transparent text-[#8F5CFF] font-medium hover:bg-transparent',
                referralStatus.isReferralValid && 'text-[#27923A]',
                referralStatus.loadingVerify && 'text-[#A7AAB1]'
              )}
              onClick={verifyReferralCode}
            >
              {referralStatus.loadingVerify && <RiLoaderLine className="animate-spin" />}
              {referralStatus.isReferralValid && <RiCheckDoubleLine />}
              {referralStatus.isReferralValid ? 'Verified' : 'Verify'}
            </Button>
          </Box>
        )}

        {referralStatus.hasReferralError && (
          <NoticeMessage colorIcon="#F53E3E" message="Invalid code." className="text-[#F53E3E]" />
        )}

        <CheckboxController
          control={control}
          required
          name="term"
          isHideIcon={true}
          label={
            <span className="text-sm">
              I Agree with{' '}
              <a className="text-[#924FE8] hover:underline" href="https://big360.ai/term-of-service" target="_blank" rel="noreferrer">
                Term of Service
              </a>
            </span>
          }
          error={errors.term?.message}
          className="w-4 h-4"
          classNameCheckBox="w-3 h-3"
        />

        <Button
          type="submit"
          disabled={referralStatus.loading || ( referralStatus.showReferralInput && !referralStatus.isReferralValid )}
          className="text-white bg-primary rounded-xl shadow-xs hover:bg-primary-hover"
        >
          {referralStatus.loading ? <LoadingButtonIcon /> : 'Sign up'}
        </Button>
      </form>
      {/*<Separator content="Or authorize with" />*/}
      {/*<GoogleLogin />*/}
      <div className="flex justify-center text-sm gap-1">
        <span className="text-secondary">Already have an account? </span>
        <Link to="/login" className="text-[#924FE8] hover:underline hover:tex-primary-hover font-medium">
          Log in
        </Link>
      </div>
      <Toaster />
    </div>
  );
};

export default Signup;
