import { useParams } from "react-router-dom";
import { lazy, useCallback, useEffect, useState } from "react";

import { resendAcctVerification, verifyEmail } from "apis/auth";
import { useAppDispatch } from "store";
import { setEmailVerify } from "store/redux/auth/slice";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import { HttpStatusCode } from "axios";
const VerificationEmail = lazy(
  () => import("views/AuthenticationView/components/form/VerficationEmail")
);

const Verify = () => {
  const dispatch = useAppDispatch();
  const { token } = useParams<{ token: string }>();
  const [loading, setLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<"success" | "error" | "verified" | "invalid" | null>(null);
  const [email, setEmail] = useState<string>("");

  const handleVerify = useCallback(async () => {
    if (!token) return;
    setLoading(true);
    const res: any = await verifyEmail({ token });
    const statusCode = res.status ?? res.response.status;
    switch (statusCode) {
      case HttpStatusCode.Ok:
        setStatus("success");
        dispatch(setEmailVerify(res.data.email));
        break;
      case HttpStatusCode.BadRequest:
        const emailVerify = res.response.data.data.email;
        if (emailVerify) {
          setEmail(emailVerify);
          setStatus("error");
        } else {
          setStatus("invalid");
        }
        break;
      default:
        break;
    }
    setLoading(false);
  }, [token]);

  const handleResendVerification = useCallback(async () => {
    setLoading(true);
    try {
      const res = await resendAcctVerification(email);
      res.code === 0 && setStatus(null);
      setLoading(false);
    } catch (error: any) {
      if (error.code === 1010) {
        setStatus("invalid");
      }
    }
    setLoading(false);
  }, [email]);

  useEffect(() => {
    if (token == "0") {
      setStatus(null);
      return;
    }
    handleVerify();
  }, [token]);

  if (loading)
    return (
      <div className="w-full h-full flex items-center justify-center">
        <LoadingButtonIcon width="80" height="80" />
      </div>
    );
  return <VerificationEmail status={status} callback={handleResendVerification} />;
};

export default Verify;
