import { yupResolver } from '@hookform/resolvers/yup';
import { SubmitHandler, useForm } from 'react-hook-form';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { forgotPassword } from 'apis/auth';

import ChangePassword from 'views/AuthenticationView/components/form/ChangePassword';
import { ForgotPasswordBody, ForgotPasswordBodyType } from 'validations/account';
import InputController from 'components/InputController';
import { Button } from 'components/ui/button';
import { Title } from 'views/AuthenticationView/components/HeaderModal';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';
import { toast } from 'components/ui/use-toast';
import { AUTHEN_LABEL } from 'constants/label';
import { Toaster } from '../../components/ui/toaster';
import { RiCheckboxCircleFill, RiMailOpenLine } from '@remixicon/react';

const ForgotPassword = () => {
  const { token } = useParams<{token: string}>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [resendCountdown, setResendCountdown] = useState(60);
  const canResend = resendCountdown === 0;

  const {
    handleSubmit,
    control,
    formState: { errors }
  } = useForm<Omit<ForgotPasswordBodyType, 'password'>>({
    resolver: yupResolver(ForgotPasswordBody.omit(['password'])),
    defaultValues: {
      email: ''
    }
  });
  const [status, setStatus] = useState<'request' | 'pending' | 'success' | 'updated'>('request');

  useEffect(() => {
    if (token === "0") {
      setStatus("request");
      return;
    } else setStatus("success");
  }, [token]);

  useEffect(() => {
    if (status === 'pending' && resendCountdown > 0) {
      const timerId = setTimeout(() => {
        setResendCountdown((prev) => prev - 1);
      }, 1000);

      return () => clearTimeout(timerId);
    }
  }, [resendCountdown, status]);

  const onSubmit: SubmitHandler<Omit<ForgotPasswordBodyType, 'password'>> = async (data) => {
    setLoading(true);
    try {
      const res = await forgotPassword(data.email);
      if (res.code === 0) {
        toast({ title: res.message, status: "success" });
        setStatus("pending");
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      return { error };
    }
  };

  if (status === 'pending') {
    return (
      <div className="flex flex-col justify-between md:justify-center w-full h-full gap-4">
        <div>
          <div className="mx-auto flex justify-center items-center rounded-full w-[112px] h-[112px] bg-[#F5F5F5]">
            <RiMailOpenLine size={49} color={'#909498'} />
          </div>
          <div className="text-center">
            <p className="text-lg font-semibold mb-2">{AUTHEN_LABEL.forgotPassword.checkEmail}</p>
            <p className="text-secondary">
              <span className="text-base">{AUTHEN_LABEL.forgotPassword.sentPassword}</span> <br />
              <span className="text-base">{AUTHEN_LABEL.forgotPassword.checkYourInbox}</span>
            </p>
          </div>
          {!canResend &&
            <div className="w-full text-center mt-[32px] mb-0"><span className="font-semibold">{resendCountdown}</span>
            </div>}
        </div>
        <p className="text-primary flex justify-center items-center mt-auto md:mt-11 text-sm gap-1">
          <span className="font-medium text-primary">{AUTHEN_LABEL.forgotPassword.didntReceiveEmail}</span>
          <Button
            variant={'ghost'}
            className="block text-sm p-0 m-0 h-auto text-brand-default"
            onClick={() => setStatus('request')}
            disabled={!canResend}
          >{AUTHEN_LABEL.forgotPassword.resend}</Button>
        </p>
        <Toaster />
      </div>
    );
  }
  if (status === 'updated') {
    return (
      <div className="flex flex-col justify-center w-full h-full gap-4">
        <div className="mx-auto flex justify-center items-center rounded-full w-[112px] h-[112px] bg-[#E3F8EF]">
          <RiCheckboxCircleFill size={49} color={'#2BB684'} />
        </div>
        <div className="text-center">
          <p className="text-lg font-semibold mb-2">{AUTHEN_LABEL.forgotPassword.passwordReset}</p>
          <p className="text-secondary">
            <span className="text-base">{AUTHEN_LABEL.forgotPassword.yourPasswordHasBeenSuccess}</span> <br />
            <span className="text-base">{AUTHEN_LABEL.forgotPassword.youCanNowLogin}</span>
          </p>
        </div>

        <Button
          variant={'main'}
          className="h-10 text-sm"
          onClick={() => navigate('/login')}
        >
          {AUTHEN_LABEL.forgotPassword.goToLogin}
        </Button>
        <Toaster />
      </div>
    );
  }
  return (
    <div className="flex flex-col justify-center w-full h-full gap-4">
      <Title
        title={AUTHEN_LABEL.forgotPassword.title}
        subTitle={
          status === 'success'
            ? AUTHEN_LABEL.forgotPassword.subtitle_change
            : AUTHEN_LABEL.forgotPassword.subTitle
        }
      />
      {status === 'request' && (
        <form className="flex flex-col gap-2" onSubmit={handleSubmit(onSubmit)}>
          <InputController
            name="email"
            control={control}
            error={errors.email?.message}
            required={true}
            label="Email Address"
            placeholder="Enter your email address"
          />
          <Button
            type="submit"
            disabled={loading}
            children={!loading ? 'Next' : <LoadingButtonIcon />}
            className="text-white bg-primary rounded-xl shadow-xs hover:bg-primary-hover"
          />
        </form>
      )}
      {status === 'success' && <ChangePassword token={token} setStatus={setStatus} />}
      <div className="flex text-sm gap-1">
        <span className="text-secondary">Back to</span>
        {loading ? <span className="text-primary">Log in</span> :
          <Link to="/login" className="text-primary hover:underline">
            Log in
          </Link>}
      </div>
      <Toaster />
    </div>
  );
};

export default ForgotPassword;
