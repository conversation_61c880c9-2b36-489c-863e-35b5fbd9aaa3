import { useAppSelector } from "store";

import { Box } from "components/Box";
import GroupControl from "./GroupControl";
import ButtonEditUserInfor from "components/Button/ButtonEditUserInfor";

interface Props {
  title: string;
  isEmail?: boolean;
  isFullName?: boolean;
  isPhoneNumber?: boolean;
  isGender?: boolean;
  onClick: () => void;
}
const HeadingAndEdit = (props: Props) => {
  const { title, isEmail, isFullName, isGender, isPhoneNumber, onClick } = props;
  const { user } = useAppSelector((state) => state.auth);
  return (
    <div className="w-full bg-custom-primary border border-custom-primary p-3 lg:p-6 rounded-2xl h-full">
      <Box variant="col-start" className="w-full gap-3 lg:gap-6">
        <Box className="justify-between w-full">
          <h4 className="text-base lg:text-[20px] text-primary font-semibold" children={title} />
          <ButtonEditUserInfor CallBack={onClick} />
        </Box>
        <Box className="w-full gap-3 lg:gap-6">
          {isFullName && isGender && (
            <Box className="w-full justify-between items-center">
              <GroupControl
                label="Full name"
                value={user.full_name}
                isGender
                gender={user.gender}
              />
            </Box>
          )}
          {isEmail && <GroupControl label="Email" value={user.email} />}
          {isPhoneNumber && (
            <GroupControl
              label="Phone Number"
              value={user.phone_number ? user.phone_number : "--"}
            />
          )}
        </Box>
      </Box>
    </div>
  );
};
export default HeadingAndEdit;
