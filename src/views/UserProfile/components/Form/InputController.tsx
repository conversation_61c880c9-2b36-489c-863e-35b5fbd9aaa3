import { useState } from "react";
import { Control, Controller } from "react-hook-form";
import { RiErrorWarningLine, RiEye2Line, RiEyeCloseLine } from "@remixicon/react";

import { Box } from "components/Box";
import { cn } from "utils/utils";

interface Props {
  name: string;
  control: Control<any>;
  errors?: string;
  className?: string;
  isPassword?: boolean;
}
const InputController = ({ name, control, errors, className, isPassword = false }: Props) => {
  const [showPassword, setShowPassword] = useState(false);

  // const preventCopyPaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
  //   event.preventDefault();
  // };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <>
          <Box
            className={cn(
              "w-full border border-custom-primary text-md text-secondary bg-custom-primary py-2 px-3 focus-visible:ring-0 outline-none box-border rounded-xl",
              errors && "border-error border",
              className
            )}
          >
            <input
              {...field}
              className="p-0 outline-none flex-1"
              type={isPassword && !showPassword ? "password" : "text"}
              // onCopy={preventCopyPaste}
              // onPaste={preventCopyPaste}
              // onCut={preventCopyPaste}
            />
            {isPassword && (
              <button onClick={() => setShowPassword(!showPassword)}>
                {showPassword ? (
                  <RiEye2Line color="#0D1126" size={20} />
                ) : (
                  <RiEyeCloseLine color="#0D1126" size={20} />
                )}
              </button>
            )}
          </Box>
          {errors && (
            <Box className="text-error-default text-xs gap-1 justify-start mt-2">
              <RiErrorWarningLine size={16} opacity={0.6} />
              {errors}
            </Box>
          )}
        </>
      )}
    />
  );
};

export default InputController;
