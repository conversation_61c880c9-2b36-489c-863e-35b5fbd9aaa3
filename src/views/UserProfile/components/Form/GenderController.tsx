import { <PERSON>, Controller } from "react-hook-form";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";

export const GENDER_OPTIONS = [
  {
    label: "Female",
    value: "female",
  },
  {
    label: "Male",
    value: "male",
  },
  {
    label: "Other",
    value: "other",
  },
];

const GenderController = ({ control }: { control: Control<any> }) => (
  <Controller
    name="gender"
    control={control}
    render={({ field }) => (
      <Select onValueChange={field.onChange}>
        <SelectTrigger className="!w-full lg:!w-[100px] border-custom-primary text-tertiary gap-2 text-md rounded-xl">
          <SelectValue placeholder="Gender" className="border-custom-primary" />
        </SelectTrigger>
        <SelectContent className="p-2">
          <SelectGroup>
            {GENDER_OPTIONS.map((option) => (
              <SelectItem
                className="hover:bg-purple-100 text-secondary text-md text-left p-2"
                value={option.value}
                children={option.label}
              />
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    )}
  />
);

export default GenderController;
