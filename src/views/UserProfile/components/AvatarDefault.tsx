const AvatarDefault = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="120"
      height="120"
      viewBox="0 0 120 120"
      fill="none"
    >
      <g clipPath="url(#clip0_89_6664)">
        <rect width="120" height="120" rx="60" fill="#E2DAFF" />
        <circle
          cx="59.9997"
          cy="140.176"
          r="66.9412"
          transform="rotate(90 59.9997 140.176)"
          fill="url(#paint0_radial_89_6664)"
        />
        <g filter="url(#filter0_dd_89_6664)">
          <circle cx="59.9999" cy="43.337" r="25.8981" fill="url(#paint1_radial_89_6664)" />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_dd_89_6664"
          x="-5.89819"
          y="-6.56116"
          width="131.796"
          height="131.796"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="16" />
          <feGaussianBlur stdDeviation="20" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.886275 0 0 0 0 0.854902 0 0 0 0 1 0 0 0 1 0"
          />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_89_6664" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="8" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.647059 0 0 0 0 0.521569 0 0 0 0 1 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_89_6664"
            result="effect2_dropShadow_89_6664"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_89_6664"
            result="shape"
          />
        </filter>
        <radialGradient
          id="paint0_radial_89_6664"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(198.109 236.436) rotate(-139.716) scale(252.408)"
        >
          <stop offset="0.24" stopColor="#E2DAFF" />
          <stop offset="1" stopColor="white" />
        </radialGradient>
        <radialGradient
          id="paint1_radial_89_6664"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(113.432 80.578) rotate(-139.716) scale(97.6511)"
        >
          <stop offset="0.24" stopColor="#E2DAFF" />
          <stop offset="1" stopColor="white" />
        </radialGradient>
        <clipPath id="clip0_89_6664">
          <rect width="120" height="120" rx="60" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default AvatarDefault;
