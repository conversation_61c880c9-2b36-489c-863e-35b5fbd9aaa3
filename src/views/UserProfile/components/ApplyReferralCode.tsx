import { Label } from '../../../components/ui/label';
import NoticeMessage from '../../RequestAudienceView/components/NoticeMessage';
import { Box } from '../../../components/Box';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { referralApi } from '../../../apis/referral';
import { useEffect, useState } from 'react';
import { TReferral } from '../../../validations/account';
import { useAppDispatch } from '../../../store';
import { toast } from '../../../components/ui/use-toast';
import { RiLoader2Line } from '@remixicon/react';
import { refetchAfterReferral } from '../../../utils/refetchAfterReferral';

export const ApplyReferralCode = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [referralCode, setReferralCode] = useState<string>('');
  const [referralData, setReferralData] = useState<TReferral>();
  const [applied, setApplied] = useState<boolean>(false);

  const dispatch = useAppDispatch();

  const handleGetReferralCode = async () => {
    setLoading(true);
    return await referralApi.getReferralCode();
  };

  useEffect(() => {
    handleUpdateReferralCode();
  }, []);

  const handleUpdateReferralCode = () => {
    handleGetReferralCode().then((res) => {
      if (res.data) {
        setReferralData(res.data);
        setApplied(true)
      }
    }).finally(() => {
      setLoading(false);
    });
  }

  const handleSubmitReferralCode = async () => {
    await referralApi.checkValidReferral({ referral_code: referralCode }).then(async (res) => {
      const valid = res.data.valid;
      if (valid) {
        await referralApi.appleReferralCode({ referral_code: referralCode }).then(async () => {
          toast({
            description: 'Referral code applied',
            status: 'success',
            duration: 3000
          });
          handleUpdateReferralCode();
          await refetchAfterReferral(dispatch);
        });
      } else {
        toast({
          description: 'Referral code is invalid',
          status: 'error',
          duration: 3000
        });
      }
    });
  };

  return (
    <div className="w-full bg-secondary border border-secondary p-3 lg:p-6 rounded-2xl h-full space-y-1">
      <Label
        htmlFor={'Referral Code'}
        className="text-secondary text-sm lg:text-xs font-semibold pl-1"
        children={'Referral Code'}
      />
      <Box className="gap-4">
        <Input
          className="p-3 rounded-xl"
          id={'Referral Code'}
          placeholder="--"
          onChange={(e) => setReferralCode(e.target.value)}
          disabled={!!referralData?.code || loading || applied}
          value={referralData?.code ?? referralCode}
        />
        {( !referralData?.code && !applied ) &&
          <Button variant={'main'} className="h-10" disabled={loading} onClick={() => handleSubmitReferralCode()}>
            Submit
          </Button>}
      </Box>
      {loading && <RiLoader2Line className="animate-spin" size={20} />}
      {!loading &&  <NoticeMessage
        colorIcon={'#515667'}
        message={referralData?.code ?
          <>The referral code has been successfully applied with <span className="text-[#8f5cff]">{referralData?.benefit_credits} credits</span>{referralData.benefit_trial_days > 0 ?
            <> and an additional <span
              className="text-[#8f5cff]"
            >{referralData?.benefit_trial_days} day{referralData?.benefit_trial_days > 1 ?
              's' :
              ''}{' '}</span> trial.</> :
            '.'}</> :
          'Enter referral code to receive reward.'}
      />}
    </div>
  );
};
