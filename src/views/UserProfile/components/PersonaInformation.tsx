import { useContext } from "react";
import { ModalContext } from "providers/Modal";

import HeadingAndEdit from "./HeadingAndEdit";
import FormUpdateInformation from "./FormUpdateInformation";

import { USER_PROFILE_LABEL } from "constants/UserProfile/label";

const PersonaInformation = () => {
  const context = useContext(ModalContext);
  const handleEditFullName = () => {
    context?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      title: "",
      className: "max-w-[calc(100%-32px)] lg:max-w-[825px] rounded-sm p-6",
      content: (
        <FormUpdateInformation
          formType="full_name_gender"
          title={USER_PROFILE_LABEL.PROFILE.personal_edit_title}
        />
      ),
      footer: "",
    }));
  };

  return (
    <HeadingAndEdit
      title={USER_PROFILE_LABEL.PROFILE.persona_title}
      isFullName
      isGender
      onClick={handleEditFullName}
    />
  );
};

export default PersonaInformation;
