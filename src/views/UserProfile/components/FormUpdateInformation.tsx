import { useContext, useState } from "react";
import { useAppDispatch, useAppSelector } from "store";
import { useForm, SubmitHandler } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { getMe } from "store/redux/auth/slice";
import { RiCloseLine, RiErrorWarningLine } from '@remixicon/react';
import { ModalContext } from "providers/Modal";
import { UpdateAccountSchema, UpdateAccountSchemaType } from "validations/updateUserInfo";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import { Label } from "components/ui/label";
import GroupControl from "./GroupControl";
import GenderController from "./Form/GenderController";
import InputController from "./Form/InputController";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import { userAPI } from "apis/user";
import { cn } from "utils/utils";
import handleCloseModal from "utils/handleCloseModal";
import useResponsive from '../../../hooks/useResponsive';

interface Props {
  title?: string;
  formType: "full_name_gender" | "phone" | "change_password";
  // onSubmit: SubmitHandler<UpdateAccountSchemaType>;
}

function FormUpdateInformation(props: Props) {
  const dispatch = useAppDispatch();
  const { isDesktop } = useResponsive();
  const context = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);
  const { title, formType } = props;

  const [loading, setLoading] = useState<boolean>(false);

  const {
    control,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm<UpdateAccountSchemaType>({
    resolver: yupResolver(UpdateAccountSchema),
    defaultValues: {
      formType,
      full_name: user.full_name,
      gender: user.gender,
      phone_number: user.phone_number,
    },
    mode: "onChange",
  });
  const onSubmit: SubmitHandler<UpdateAccountSchemaType> = async (data) => {
    setLoading(true);
    const response: any = await userAPI.updateUserProfile({
      payload: {
        full_name: data.full_name,
        gender: data?.gender || undefined,
        phone_number: data?.phone_number || undefined,
      },
    });
    if (response && response.data) {
      await dispatch(getMe());
      handleCloseModal(context);
      toast({
        title: "Your change saved successfully.",
        status: "success",
        duration: 3000,
        className: "bg-success-primary text-success-subtitle border border-custom-success",
      });
      setLoading(false);
    } else {
      setLoading(false);
    }
    reset();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="w-full">
      <Box variant="col-start" className={cn("w-full", formType == "phone" ? "gap-3 lg:gap-6" : "gap-3 lg:gap-12")}>
        <div className="flex justify-between lg:justify-center text-sm lg:text-[20px] text-primary font-semibold text-center w-full">
          {title}
          {!isDesktop && <RiCloseLine
            className="text-secondary cursor-pointer hover:text-tertiary"
            size={21}
            onClick={() => handleCloseModal(context)}
          />}
        </div>

        {formType === "full_name_gender" && (
          <div className="w-full">
            <Label className="text-xs text-secondary font-semibold">Full Name</Label>
            <Box className="flex-1 w-full gap-4 flex-col lg:flex-row">
              <InputController
                name="full_name"
                control={control}
                className={errors?.full_name?.message && "border-error border flex-1 rounded-xl"}
              />
              <div className="w-full lg:w-[100px]">
                <GenderController control={control} />
              </div>
            </Box>
            {errors?.full_name?.message && (
              <Box className="text-error-default text-xs gap-1 justify-start mt-2">
                <RiErrorWarningLine size={16} opacity={0.6} />
                {errors?.full_name?.message}
              </Box>
            )}
          </div>
        )}
        {formType === "phone" && (
          <>
            <GroupControl label="Email" value={user.email} />
            <div className="w-full">
              <Label className="text-xs text-secondary font-semibold">Phone Number</Label>
              <InputController
                name="phone_number"
                control={control}
                errors={errors?.phone_number?.message}
              />
            </div>
          </>
        )}

        <Box className="grid grid-cols-1 lg:grid-cols-2 text-md font-semibold w-full">
          {isDesktop && <Button
            className="text-md font-semibold text-primary border-custom-primary rounded-xl"
            variant="secondary"
            children="Cancel"
            type="button"
            onClick={() => handleCloseModal(context)}
          />}
          <Button
            className="text-md font-semibold text-custom-brand rounded-xl w-full lg:w-auto"
            variant="main"
            type="submit"
            disabled={loading}
            children={loading ? <LoadingButtonIcon /> : "Submit"}
          />
        </Box>
      </Box>
    </form>
  );
}
export default FormUpdateInformation;
