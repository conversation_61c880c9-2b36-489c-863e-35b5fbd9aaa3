import { useContext } from "react";
import { RiLock2Line } from "@remixicon/react";
import { useAppSelector } from "store";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Badge } from "components/ui/badge";
import { But<PERSON> } from "components/ui/button";
import AvatarProfile from "./AvatarProfile";
import ChangePassword from "./ChangePassword";

import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import { cn, formatDate } from "utils/utils";
import { useSelector } from "react-redux";
import { subscriptionStore } from "store/redux/subscription/slice";

const UserInformation = () => {
  const { user } = useAppSelector((state) => state.auth);
  const { last_sub, is_active } = useSelector(subscriptionStore);

  const context = useContext(ModalContext);

  const handleChangePassword = () => {
    context?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      title: "",
      className: "max-w-[calc(100%-32px)] lg:max-w-[825px] rounded-2xl p-6",
      content: <ChangePassword />,
      footer: "",
    }));
  };

  const startDate = last_sub?.start_date ? formatDate(last_sub?.start_date) : "--";
  const endDate = last_sub?.end_date ? formatDate(last_sub?.end_date) : "--";

  return (
    <Box
      variant="col-center"
      className="col-span-4 lg:col-span-2 p-3 lg:p-6 rounded-2xl border border-custom-primary bg-custom-primary items-center gap-3 lg:gap-6"
    >
      <AvatarProfile />
      <Box variant="col-center" className="gap-1 items-center justify-start w-full flex-1">
        <Badge className="px-1 bg-success-primary text-success-subtitle rounded-2xl hover:bg-success-primary capitalize">
          {last_sub?.plan_code.toLocaleLowerCase() || "--"}
        </Badge>
        <h4 className="text-black font-semibold text-lg lg:text-xl text-center" children={user.full_name} />
        <div className="text-secondary text-sm" children={user.email} />
        <Box
          variant="col-center"
          className={cn(
            "mt-3 lg:mt-6 items-center text-primary text-sm  bg-custom-secondary w-full p-2 rounded-2xl gap-1",
            is_active == false && "bg-error-subtitle text-error-strong"
          )}
        >
          <span>Start date: {startDate}</span>
          <span>Expiry date: {endDate}</span>
        </Box>
      </Box>
      <Button
        variant="ghost"
        className="bg-custom-primary w-full text-primary font-medium text-sm lg:text-md border border-custom-primary rounded-2xl gap-1 p-3 leading-6 h-10"
        onClick={handleChangePassword}
      >
        <RiLock2Line size={18} />
        <span children={USER_PROFILE_LABEL.PROFILE.change_password} />
      </Button>
    </Box>
  );
};

export default UserInformation;
