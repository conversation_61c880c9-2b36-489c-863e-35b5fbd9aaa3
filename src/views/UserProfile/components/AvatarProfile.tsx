import { useContext } from "react";
import { useAppSelector } from "store";
import { RiCameraLine } from "@remixicon/react";
import { ModalContext } from "providers/Modal";
import UploadAvatar from "./UploadAvatar";
import AvatarDefault from "./AvatarDefault";

const AvatarProfile = () => {
  const context = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);

  const handleUploadAvatar = () => {
    context?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      title: "",
      className: "max-w-[825px] rounded-sm p-6",
      content: <UploadAvatar />,
      footer: "",
    }));
  };

  return (
    <div className="relative cursor-pointer" onClick={handleUploadAvatar}>
      {user.avatar ? (
        <img className="w-[120px] h-[120px] rounded-full  object-cover" src={user.avatar} />
      ) : (
        <AvatarDefault />
      )}
      <RiCameraLine
        className="fill-[#20232C] absolute right-2 bottom-0 rounded-sm border border-custom-primary p-1 bg-white"
        size={28}
      />
    </div>
  );
};

export default AvatarProfile;
