import { useContext, useState } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { ModalContext } from 'providers/Modal';
import { UpdatePasswordSchema, UpdatePasswordSchemaType } from 'validations/updateUserInfo';

import { Box } from 'components/Box';
import { toast } from 'components/ui/use-toast';
import { Button } from 'components/ui/button';
import { Label } from 'components/ui/label';
import InputController from './Form/InputController';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';

import { ChangePasswordProps, userAPI } from 'apis/user';
import handleCloseModal from 'utils/handleCloseModal';
import { USER_PROFILE_LABEL } from 'constants/UserProfile/label';
import useResponsive from '../../../hooks/useResponsive';
import { RiCloseLine } from '@remixicon/react';
import { ValidationPassword } from '../../../components/ValidationPassword';

const ChangePassword = () => {
  const context = useContext(ModalContext);
  const { isDesktop } = useResponsive();

  const [loading, setLoading] = useState<boolean>(false);
  const {
    control,
    watch,
    handleSubmit,
    reset,
    setError,
    formState: { errors },
  } = useForm<UpdatePasswordSchemaType>({
    resolver: yupResolver(UpdatePasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    mode: "onChange",
  });



  const onSubmit: SubmitHandler<UpdatePasswordSchemaType> = async (data) => {
    setLoading(true);
    const payload: ChangePasswordProps = {
      password: data.newPassword,
      old_password: data.currentPassword,
    };
    const response: any = await userAPI.changePassword({ payload: payload });
    if (response && response.data) {
      toast({
        title: "Your password changed successfully.",
        status: "success",
        duration: 3000,
        className: "bg-success-primary text-success-subtitle border border-custom-success",
      });
      reset();
      handleCloseModal(context);
    }
    if (response.code == 1008) {
      setError("currentPassword", {
        type: "manual",
        message: "Wrong password. Please try again.",
      });
    }
    setLoading(false);
  };

  const newPassword = watch("newPassword");

  return (
    <Box variant="col-start" className="w-full gap-1">
      <form onSubmit={handleSubmit(onSubmit)} className="w-full">
        <Box variant="col-start" className="gap-3 lg:gap-6 w-full">
          <div className="flex justify-between lg:justify-center text-sm lg:text-[20px] text-primary font-semibold w-full">
            {USER_PROFILE_LABEL.PROFILE.change_password}
            {!isDesktop && <RiCloseLine
              className="text-secondary cursor-pointer hover:text-tertiary"
              size={21}
              onClick={() => handleCloseModal(context)}
            />}
          </div>
          <div className="w-full">
            <Label className="text-xs text-secondary font-semibold">
              {USER_PROFILE_LABEL.PROFILE.current_password}
              <strong className="text-error-default">*</strong>
            </Label>
            <InputController
              name="currentPassword"
              control={control}
              errors={errors?.currentPassword?.message}
              isPassword
            />
          </div>
          <Box variant="col-start" className="gap-3">
            <div className="w-full">
              <Label className="text-xs text-secondary font-semibold">
                {USER_PROFILE_LABEL.PROFILE.new_password}
                <strong className="text-error-default">*</strong>
              </Label>
              <InputController name="newPassword" control={control} isPassword />
            </div>
            <ValidationPassword password={newPassword}/>
          </Box>
          <div className="w-full">
            <Label className="text-xs text-secondary font-semibold">
              {USER_PROFILE_LABEL.PROFILE.confirm_password}
              <strong className="text-error-default">*</strong>
            </Label>
            <InputController
              name="confirmPassword"
              control={control}
              errors={errors?.confirmPassword?.message}
              isPassword
            />
          </div>
          <Box className="grid grid-cols-1 lg:grid-cols-2 text-md font-semibold w-full">
            {isDesktop && <Button
              className="text-md font-semibold text-primary border-custom-primary rounded-xl"
              variant="secondary"
              children="Cancel"
              type="button"
              onClick={() => handleCloseModal(context)}
            />}
            <Button
              className="text-md font-semibold text-custom-brand rounded-xl"
              variant="main"
              type="submit"
              disabled={loading}
              children={loading ? <LoadingButtonIcon /> : 'Save change'}
            />
          </Box>
        </Box>
      </form>
    </Box>
  );
};

export default ChangePassword;
