import { Box } from "components/Box";
import { Input } from "components/ui/input";
import { Label } from "components/ui/label";
import { Select, SelectTrigger, SelectValue } from "components/ui/select";

interface Props {
  label: string;
  value: string;
  gender?: "Male" | "Female" | "Other";
  isGender?: boolean;
  isDisabled?: boolean;
}
const GroupControl = ({ label, value, isGender = false, gender, isDisabled = true }: Props) => (
  <Box variant="col-start" className="gap-1 flex-1 w-full">
    <Label htmlFor={label} className="text-secondary text-sm lg:text-xs font-semibold pl-1" children={label} />
    {isGender ? (
      <Box className="w-full lg:gap-6 gap-3">
        <Input
          className="bg-custom-primary border border-custom-primary p-3 text-sm text-secondary rounded-xl"
          id={label}
          placeholder="--"
          disabled={isDisabled}
          value={value}
        />
        <Select>
          <SelectTrigger
            className="w-[95px] border-custom-primary capitalize rounded-xl"
            disabled={isDisabled}
          >
            <SelectValue
              placeholder={gender ? gender : "Gender"}
              className="border-custom-primary capitalize"
            />
          </SelectTrigger>
        </Select>
      </Box>
    ) : (
      <Input
        className="bg-custom-primary border border-custom-primary rounded-xl p-3 text-md text-secondary "
        id={label}
        placeholder="--"
        disabled={isDisabled}
        value={value}
      />
    )}
  </Box>
);

export default GroupControl;
