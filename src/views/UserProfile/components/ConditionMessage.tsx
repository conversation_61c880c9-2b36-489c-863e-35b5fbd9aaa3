import { Box } from "components/Box";

interface Props {
  message: string;
  active: string | boolean | undefined;
}
const ConditionMessage = ({ message, active }: Props) => (
  <Box className="gap-1 text-secondary text-sm justify-start">
    {active ? <CheckBoxIcon /> : <CheckFailIcon />}
    <span>{message}</span>
  </Box>
);
const CheckBoxIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 25" fill="none">
    <path
      d="M12 22.5C6.47715 22.5 2 18.0228 2 12.5C2 6.97715 6.47715 2.5 12 2.5C17.5228 2.5 22 6.97715 22 12.5C22 18.0228 17.5228 22.5 12 22.5ZM12 20.5C16.4183 20.5 20 16.9183 20 12.5C20 8.08172 16.4183 4.5 12 4.5C7.58172 4.5 4 8.08172 4 12.5C4 16.9183 7.58172 20.5 12 20.5ZM11.0026 16.5L6.75999 12.2574L8.17421 10.8431L11.0026 13.6716L16.6595 8.01472L18.0737 9.42893L11.0026 16.5Z"
      fill="#27923A"
    />
  </svg>
);

const CheckFailIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 25" fill="none">
    <path
      d="M12 22.5C6.47715 22.5 2 18.0228 2 12.5C2 6.97715 6.47715 2.5 12 2.5C17.5228 2.5 22 6.97715 22 12.5C22 18.0228 17.5228 22.5 12 22.5ZM12 20.5C16.4183 20.5 20 16.9183 20 12.5C20 8.08172 16.4183 4.5 12 4.5C7.58172 4.5 4 8.08172 4 12.5C4 16.9183 7.58172 20.5 12 20.5ZM12 11.0858L14.8284 8.25736L16.2426 9.67157L13.4142 12.5L16.2426 15.3284L14.8284 16.7426L12 13.9142L9.17157 16.7426L7.75736 15.3284L10.5858 12.5L7.75736 9.67157L9.17157 8.25736L12 11.0858Z"
      fill="#F53E3E"
    />
  </svg>
);

export default ConditionMessage;
