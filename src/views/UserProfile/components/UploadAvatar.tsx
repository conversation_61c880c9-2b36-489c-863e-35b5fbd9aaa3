import { useAppDispatch } from "store";
import React, { useContext, useState } from "react";
import { getMe } from "store/redux/auth/slice";
import { ModalContext } from "providers/Modal";
import { RiCloseLine, RiFileImageLine, RiUploadCloud2Line } from "@remixicon/react";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import UploadAvatarSkeleton from "assets/icons/UploadAvatarSkeleton";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import { userAPI } from "apis/user";
import handleCloseModal from "utils/handleCloseModal";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

const UploadAvatar = () => {
  const dispatch = useAppDispatch();
  const context = useContext(ModalContext);

  const [dragging, setDragging] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(false);

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const droppedFile = files[0];
      validateFile(droppedFile);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(true);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const selectedFile = files[0];
      validateFile(selectedFile);
    }
  };

  const validateFile = (file: File) => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setError("Invalid file type. Allowed types: .jpeg, .jpg, .png, .webp.");
      setFile(null);
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      setError("File size exceeds the 10MB limit.");
      setFile(null);
      return;
    }
    setError(null);
    setFile(file);
  };
  const handleUpload = async () => {
    setLoading(true);
    if (!file) {
      return;
    }
    const form = new FormData();
    form.append("avatar", file);
    const response: any = await userAPI.uploadAvatar({ formData: form });

    if (response && response.data) {
      await dispatch(getMe());
      handleCloseModal(context);
      toast({
        title: "Your change saved successfully.",
        status: "success",
        duration: 3000,
        className: "bg-success-primary text-success-subtitle border border-custom-success",
      });
      setLoading(false);
    } else {
      handleCloseModal(context);
      setLoading(false);
    }
  };
  return (
    <Box variant="col-start" className="w-full gap-6">
      <Box className="justify-between w-full text-secondary text-md font-semibold">
        <span>{USER_PROFILE_LABEL.PROFILE.upload_img}</span>
        <RiCloseLine className="cursor-pointer" onClick={() => handleCloseModal(context)} />
      </Box>
      <Box
        variant="col-start"
        className={`w-full p-6 bg-custom-primary rounded-2xl border border-custom-primary items-center gap-2 ${
          dragging ? "border-dashed border-2 border-primary" : ""
        }`}
        onDragOver={handleDragOver}
        onDragLeave={() => setDragging(false)}
        onDrop={handleDrop}
      >
        <UploadAvatarSkeleton />
        <Box variant="row-start" className="gap-1 text-primary text-sm">
          <span>{USER_PROFILE_LABEL.PROFILE.upload_text_before}</span>
          <label
            htmlFor="file-input"
            className="underline font-semibold cursor-pointer text-brand-default"
          >
            {USER_PROFILE_LABEL.PROFILE.browse}
          </label>
          <span> {USER_PROFILE_LABEL.PROFILE.update_text_after}</span>
        </Box>
        <input id="file-input" type="file" className="hidden" onChange={handleFileChange} />
        <span className="text-secondary text-xs font-semibold">
          {USER_PROFILE_LABEL.PROFILE.upload_notice}
        </span>
        {file && (
          <Box className=" text-secondary text-md font-medium gap-1">
            <RiFileImageLine />
            <p> {file.name}</p>
          </Box>
        )}
        {error && (
          <Box className="mt-2 text-red-500 text-xs">
            <p>{error}</p>
          </Box>
        )}
      </Box>
      <Button
        variant="main"
        className="p-1 w-full gap-1 text-md font-medium rounded-xl"
        disabled={loading || !file}
        onClick={handleUpload}
      >
        {!loading && (
          <>
            <RiUploadCloud2Line />
            <span>Upload</span>
          </>
        )}
        {loading && <LoadingButtonIcon />}
      </Button>
    </Box>
  );
};

export default UploadAvatar;
