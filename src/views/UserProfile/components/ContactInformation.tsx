import { useContext } from "react";
import { ModalContext } from "providers/Modal";

import FormUpdateInformation from "./FormUpdateInformation";
import HeadingAndEdit from "./HeadingAndEdit";

import { USER_PROFILE_LABEL } from "constants/UserProfile/label";

const ContactInformation = () => {
  const context = useContext(ModalContext);

  const handleEditPhone = () => {
    context?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      title: "",
      className: "max-w-[calc(100%-32px)] lg:max-w-[825px] rounded-sm p-6",
      content: (
        <FormUpdateInformation
          formType="phone"
          title={USER_PROFILE_LABEL.PROFILE.contact_edit_title}
        />
      ),
      footer: "",
    }));
  };

  return (
    <HeadingAndEdit
      title={USER_PROFILE_LABEL.PROFILE.contact_title}
      isEmail
      isPhoneNumber
      onClick={handleEditPhone}
    />
  );
};

export default ContactInformation;
