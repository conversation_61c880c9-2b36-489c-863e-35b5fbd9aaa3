import Breadcrumb from "components/Breadcrumb";

import { Box } from "components/Box";
import UserTabs from "components/Tabs/UserTabs";
import HeaderWrapper from "components/Header";
import UserInformation from "./components/UserInformation";
import PersonaInformation from "./components/PersonaInformation";
import ContactInformation from "./components/ContactInformation";

import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import { RiAccountCircleLine } from '@remixicon/react';
import { ApplyReferralCode } from './components/ApplyReferralCode';

const UserProfileLayout = () => (
  <div className="grid lg:grid-cols-6 md:grid-cols-2 gap-3 lg:gap-6">
    <UserInformation />
    <Box variant="col-start" className="col-span-4 gap-3 lg:gap-6">
      <PersonaInformation />
      <ContactInformation />
      <ApplyReferralCode />
    </Box>
  </div>
);

const UserProfileView = () => (
  <>
    <Box className="gap-2 justify-start hidden md:flex">
      <RiAccountCircleLine color={'#515667'} size={20} /><Breadcrumb />
    </Box>
    <HeaderWrapper
      className="items-center md:items-center"
      leftChildren={{
        title: USER_PROFILE_LABEL.title,
        subTitle: USER_PROFILE_LABEL.sub_title,
      }}
    />
    <UserTabs />
    <UserProfileLayout />
  </>
);

export default UserProfileView;
