import { useParams } from "react-router-dom";

import HeaderDetailModal from "components/HeaderDetailModal";
import ChartSummarize from "./components/ChartSummarize";
import UpdateAndBuyAudience from "./components/UpdateAndBuyAudience";
import TablePreview from "./components/TablePreview";
import Breadcrumb from "components/Breadcrumb";

import useFilters from "hooks/useFilters";
import useAvatar from "hooks/useAvatar";
import useGetAudiencePreview from "hooks/useGetAudiencePreview";
import useGetAudienceSummarize from "hooks/useGetAudienceSummarize";
import { formatPayloadAvatar } from "utils/utils";

const SocialDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { params } = useFilters();

  const previewData = useGetAudiencePreview({
    id: id || "",
    params: params,
    page: params.page ?? 1,
  });
  const { summarize, loading } = useGetAudienceSummarize(id || "");

  const { avatar } = useAvatar(
    formatPayloadAvatar(summarize.type || 1, summarize.fb_uid ?? "", summarize.actor_id ?? "")
  );

  return (
    <>
      <Breadcrumb path={summarize.name} loading={loading} />
      <HeaderDetailModal
        name={summarize.name || ""}
        loading={loading}
        avatarUrl={avatar.url}
        size={summarize.size}
        description={summarize.description}
        uid={summarize.fb_uid}
        packageValue={summarize.package || ""}
        contentRight={
          <UpdateAndBuyAudience
            audience={summarize}
            disableBuyAudience={summarize.is_aud_added}
            disableBuyDataset={summarize.is_ds_added}
            disableUpdate={summarize.system}
          />
        }
        typeAudience={summarize.type || 1}
        subTypeAudience={summarize.subtype}
        className={summarize.description ? "items-start" : "items-center"}
      />
      <div className="my-6">
        <ChartSummarize loading={loading} data={summarize.summarize || {}} />
      </div>
      {previewData && <TablePreview data={previewData} />}
    </>
  );
};

export default SocialDetail;
