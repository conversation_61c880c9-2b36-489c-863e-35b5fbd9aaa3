import { useContext } from "react";
import { RiDatabase2Line } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import { Button } from "components/ui/button";
import BuyAudienceIcon from "assets/icons/BuyAudienceIcon";
import BuySocialAudience from "components/Transaction/BuySocialAudience";

import { TSocialDetail } from "types/SocialData";
import LABEL from "constants/label";
import { toast } from "components/ui/use-toast";
import { SOCIAL_DATA_LABEL } from "constants/socialData/label";
import UpdateButton from "./UpdateButton";
import { tracking } from '../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../store';

interface Props {
  disableBuyDataset?: boolean;
  disableBuyAudience?: boolean;
  disableUpdate?: boolean;
  audience?: TSocialDetail;
}
const UpdateAndBuyAudience = ({
  disableBuyAudience = false,
  disableBuyDataset = false,
  disableUpdate = false,
  audience,
}: Props) => {
  const modal = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);

  const handleOpenModal = (isDataset: boolean) => {
    !audience?.size
      ? toast({
          title: SOCIAL_DATA_LABEL.message_audience_empty,
          status: "warning",
          duration: 3000,
        })
      : modal?.setDataDialog((prev) => ({
          ...prev,
          isOpen: true,
          title: "",
          content: <BuySocialAudience audience={audience} isDataset={isDataset} />,
          footer: "",
          className: "max-w-[680px]",
          isShowTitle: false,
        }));
  };


  const trackingPurchase = (isDataset: boolean) => {
    tracking({
      eventName: 'purchase_preview',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          audience_id: audience?.fb_uid,
          audience_type: isDataset ? 'DATASET' : 'AUDIENCE'
        })
      }
    });
  };
  return (
    <>
      {!disableUpdate && <UpdateButton className="hidden md:flex" />}
      <Button
        onClick={() => {
          handleOpenModal(true);
          trackingPurchase(true)
        }}
        variant="secondary"
        className="flex items-center gap-0.5 px-[10px] shadow-xs rounded-lg py-1.5 font-medium text-sm text-primary bg-[#F0F0F0]"
        disabled={disableBuyDataset}
      >
        <RiDatabase2Line size={20} />
        {LABEL.buy_dataset}
      </Button>
      <Button
        onClick={() => {
          handleOpenModal(false);
          trackingPurchase(false)
        }}
        className="flex items-center min-w-[130px] h-10 gap-1 px-[10px] shadow-xs rounded-xl py-1.5 text-white font-normal text-sm bg-custom-gradient"
        disabled={disableBuyAudience}
      >
        <BuyAudienceIcon />
        {LABEL.buy_audience}
      </Button>
    </>
  );
};

export default UpdateAndBuyAudience;
