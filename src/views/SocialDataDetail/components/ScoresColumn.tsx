import useGetCategoryName from 'hooks/useGetCategoryName';

import BadgeScore from './BadgeScore';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from 'components/ui/tooltip';
import ScoreDetail from './ScoreDetail';
import { useRef, useState } from 'react';
import useOutsideClick from 'hooks/useClickOutSide';
import { TooltipPortal } from '@radix-ui/react-tooltip';

type Data = {
  [key: string]: number;
};
export default function ScoresColumn({ scores }: {scores: Data}) {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const ref = useRef(null);
  const refSecond = useRef(null);

  useOutsideClick(ref, refSecond, () => {
    setIsOpen(false);
  });
  const categoryNames = scores
    ? Object.keys(scores).reduce((acc: any, key: string) => {
      acc[key] = useGetCategoryName(key || '');
      return acc;
    }, {})
    : null;

  const sortedData = scores
    ? Object.entries(scores).sort(([, a], [, b]) => b - a).map(([key, value]) => ( { key: categoryNames[key], value } ))
    : [];

  return scores ? (
    <TooltipProvider delayDuration={100}>
      <Tooltip open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
        <TooltipTrigger ref={ref} onClick={() => setIsOpen(true)}>
          <BadgeScore data={sortedData} maxWidth={250} />
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent
            ref={refSecond}
            side="bottom"
            sideOffset={5}
            className="z-30  text-sm bg-custom-secondary"
          >
            <ScoreDetail data={sortedData} />
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  ) : (
    '--'
  );
}
