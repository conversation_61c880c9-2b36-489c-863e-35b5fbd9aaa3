import { useHiddenPhone } from "hooks/useHiddenPhone";
// import { useModal } from "hooks/useModal";
import { useAppSelector } from "store";

type Props = {
  phoneNumber: string;
  rowIndex: number;
};

const HiddenPhoneNumber: React.FC<Props> = ({ phoneNumber, rowIndex }) => {
  const { current_page } = useAppSelector((state) => state.detailSocial);
  // const { onOpen } = useModal();
  const handleHiddenNumber = () => {
    if (rowIndex > 4 || current_page > 5) {
      // onOpen("BY_AUDIENCE");
      return;
    }
  };
  return phoneNumber ? (
    <button onClick={handleHiddenNumber}>
      {useHiddenPhone(phoneNumber, rowIndex, current_page).number}
    </button>
  ) : (
    "--"
  );
};

export default HiddenPhoneNumber;
