import { useEffect, useRef, useState } from "react";

import { cn } from "utils/utils";

type Props = {
  placeholder?: string;
  defaultValue?: string;
  onChange: (value: string) => void;
  isDisabled?: boolean;
  className?: string;
};

const InputFilter = ({
  placeholder,
  defaultValue,
  onChange,
  isDisabled = false,
  className,
}: Props) => {
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>();
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setSearchTerm(defaultValue);
  }, [defaultValue]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    onChange(e.target.value);
  };

  return (
    <div className="relative max-lg:w-full">
      <input
        ref={inputRef}
        className={cn(
          "h-[39px] border text-xs rounded-lg group outline-none pl-3 disabled:bg-transparent max-lg:w-full",
          isDisabled && "!bg-[#0A0F290A] cursor-default",
          className
        )}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onChange={(e) => handleChange(e)}
        disabled={isDisabled}
        defaultValue={defaultValue || searchTerm}
        value={searchTerm ? searchTerm : ""}
      />
      <div
        className={cn(
          "absolute pointer-events-none left-3 transform text-[#82868b] duration-300 flex text-sm px-1",
          isFocused || defaultValue || searchTerm
            ? "-top-3 text-xs bg-white"
            : "top-1/2 -translate-y-1/2"
        )}
        children={placeholder}
      />
    </div>
  );
};

export default InputFilter;
