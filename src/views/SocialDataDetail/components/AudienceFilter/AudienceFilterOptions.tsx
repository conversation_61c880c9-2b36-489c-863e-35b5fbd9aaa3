import React, { useState } from 'react';
import { Box } from 'components/Box';
import ChartSummarize from '../ChartSummarize';
import FilterOptions from 'components/FilterOptions';
import FilterHeader from 'views/YourAudience/components/FilterHeader';
import FilterInformation from 'views/YourAudience/components/FilterInformation';
import useFilters from 'hooks/useFilters';
import useDelayRenderSummarize from 'hooks/useDelayRenderSummarize';
import { Summarize } from 'types/SocialData';
import { TSelectedOption, TSelectOption } from 'types/Select';
import { cn } from 'utils/utils';
import filterOptions, { TSocialAudienceFilter } from 'utils/yourAudience/socialFilterOption';
import { getCityData } from '../../../../utils/persona';

interface Props extends TSocialAudienceFilter {
  summarize: Summarize;
  loading: boolean;
  contentRight?: React.ReactElement | JSX.Element;
  contentLeft?: React.ReactElement | JSX.Element;
  className?: string;
  isSocialPersona?:boolean
}

const AudienceFilterOptions = (props: Props) => {
  const { summarize } = props;
  const { params, paramsNoPage } = useFilters();
  const { isVisible, isRendered } = useDelayRenderSummarize(paramsNoPage, props.isDisable);
  const [selectedOption, setSelectedOption] = useState<TSelectedOption>(params);
  const cityData = summarize?.city && getCityData(summarize?.city, 'all');
  const handleConvertCity = () => {
    return cityData?.map((item) => {
      return {
        label: item?.city,
        value: item?.city
      };
    });
  };

  const handleChange = (key: string, value: TSelectOption[]) => {
    const newValue = value.map((item: any) => item?.value).join(',');
    setSelectedOption((prev: any) => ( { ...prev, [key]: newValue ? newValue : undefined } ));
  };

  const filterGroup = filterOptions({
    ...props,
    params,
    city: handleConvertCity() || [],
    selectedOption: selectedOption,
    onSetParams: handleChange,
    setSelectedOption
  });

  return (
    <>
      <Box variant="col-start" className="p-4 border rounded-2xl gap-4 my-6 relative">
        {!props.isDisable && (
          <FilterHeader
            isDisableFilter={props.isDisable}
            selectedOption={selectedOption}
            setSelectedOption={setSelectedOption}
          />
        )}
        <Box className="gap-3 flex-wrap 2xl:flex-nowrap items-start xl:items-center justify-start xl:justify-between">
          <FilterOptions
            isDisable={props.isDisable}
            filterGroup={filterGroup}
            selectedOption={selectedOption}
          />
        </Box>
        {props.isDisable && (
          <div className="absolute top-1 right-2">
            <FilterInformation />
          </div>
        )}
      </Box>

      {/* show chart summarize */}
      {props.contentRight}
      <div
        className={cn(
          'transition-all duration-500 ease-in-out',
          isVisible ? 'xl:max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        {isRendered && <ChartSummarize data={props.summarize || {}} loading={props.loading} />}
      </div>
      <div className={cn(!isVisible && '-mt-[56px]')}>{props.contentLeft}</div>
    </>
  );
};

export default AudienceFilterOptions;
