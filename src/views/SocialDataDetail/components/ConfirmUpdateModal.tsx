import { useContext, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useAppDispatch } from "store";
import { getMe } from "store/redux/auth/slice";
import { getFeatures } from "store/redux/features/slice";
import { subscriptionStore } from "store/redux/subscription/slice";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Button } from "components/ui/button";

import { FB_SERVICE_PATH, ROOT_PATH } from "types/Router";
import { socialAPI } from "apis/socialData";
import useFeatures from "hooks/useFeatures";

import LABEL from "constants/label";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import handleCloseModal from "utils/handleCloseModal";
import handleGetDataRequest from "utils/Transaction/handleGetDataRequest";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

const ConfirmUpdateModal = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { last_sub } = useSelector(subscriptionStore);
  const { id } = useParams<{ id: string }>();

  const modal = useContext(ModalContext);
  const is_overage = useFeatures(FEATURE_PERMISSION_KEY.SCD_REQUEST_AUDIENCE)?.is_overage;

  const [loading, setLoading] = useState<boolean>(false);

  const handleUpdateAudienceDetail = async () => {
    setLoading(true);
    const response = await socialAPI.updateData({
      id: id || "",
    });
    if (response && response.data) {
      await dispatch(getFeatures()).unwrap();
      await dispatch(getMe()).unwrap();
      navigate(`${ROOT_PATH}/${FB_SERVICE_PATH.REQUEST_AUDIENCE}`);
    }
    setLoading(false);
    handleCloseModal(modal);
  };

  return (
    <Box variant="col-start" className="w-full">
      <div className="w-full text-center text-xl font-semibold leading-6">Confirm Update</div>
      <Box variant="col-center" className="text-sm text-secondary text-center w-full gap-1">
        <span> Are you sure you want to update this audience?</span>
        <span>Each update will be counted as an audience request.</span>
      </Box>
      <div className="grid grid-cols-2 w-full gap-4">
        <Button
          className="w-full rounded-xl font-medium text-md"
          type="button"
          variant="outline"
          onClick={() => handleCloseModal(modal)}
        >
          {LABEL.cancel}
        </Button>
        <Button
          type="button"
          variant="default"
          className="w-full rounded-xl font-medium text-md"
          disabled={loading}
          onClick={() =>
            handleGetDataRequest({
              context: modal,
              loading: loading,
              is_overage: is_overage || false,
              is_trial: last_sub?.plan_code == "TRIAL",
              callBack: handleUpdateAudienceDetail,
            })
          }
        >
          {loading ? <LoadingButtonIcon /> : LABEL.confirm}
        </Button>
      </div>
    </Box>
  );
};

export default ConfirmUpdateModal;
