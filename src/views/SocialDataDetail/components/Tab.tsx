import styled from "styled-components";
import { cn } from "utils/utils";
type Props = {
  items: { icon: React.ReactNode; value: string; backgroundColor: string }[];
  selected: string | string[];
  setSelected: (item: string) => void;
  className?: string;
};

const Button = styled.button<{ backgroundColor: string }>`
  background-color: ${(props) => props.backgroundColor};
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 8px;
  border: 1px solid #dee0e3;
  box-shadow: 0px 1px 2px 0px #14151a0d;
  color: white;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const Tab: React.FC<Props> = ({ items, selected, setSelected, className }) => {
  return (
    <div className="flex items-center gap-2">
      {items.map((item) => (
        <Button
          onClick={() => setSelected(item.value)}
          className={cn(
            className,
            !selected.includes(item.value) ? "!text-primary !stroke-black" : "stroke-white"
          )}
          backgroundColor={selected.includes(item.value) ? item.backgroundColor : ""}
          key={item.value}
        >
          {item.icon} {item.value}
        </Button>
      ))}
    </div>
  );
};

export default Tab;
