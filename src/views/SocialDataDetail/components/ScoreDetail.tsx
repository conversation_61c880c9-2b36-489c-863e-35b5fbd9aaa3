import { Box } from "components/Box";
import { Progress } from "components/ui/progress";

interface IDATA {
  key: any;
  value: number;
}
const ScoreDetail = ({ data }: { data: IDATA[] }) => (
  <Box variant="col-start" className="min-w-[250px] p-2 text-secondary gap-2 ">
    {data.map((item) => {
      return (
        <Box className="gap-2 flex-1 w-full" key={item.key}>
          <div className="flex-1">{item.key}</div>
          <Box className="gap-2 justify-start">
            <Progress className="bg-brand-subtitle w-[150px]" color="#5A18BF" value={item.value} maxRange={50} />
            <span className="font-semibold w-8 text-primary">{item.value/10}</span>
          </Box>
        </Box>
      );
    })}
  </Box>
);

export default ScoreDetail;
