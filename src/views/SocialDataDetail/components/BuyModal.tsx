import { But<PERSON> } from "components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "components/ui/dialog";
import * as DialogPrimitive from "@radix-ui/react-dialog";

import { memo } from "react";
// import { useModal } from "hooks/useModal";

const BuyModal: React.FC = () => {
  // const { open, onOpen } = useModal();
  return (
    <Dialog
    // open={open === "BY_AUDIENCE"}
    // onOpenChange={() => onOpen(open === "BY_AUDIENCE" ? "NULL" : "BY_AUDIENCE")}
    >
      <DialogTrigger asChild></DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>You can see limit 5 number phone</DialogTitle>
          <DialogDescription>Can you buy audience to see more !!!</DialogDescription>
        </DialogHeader>
        <DialogPrimitive.Close>
          <Button
            className="w-full bg-primary text-white hover:bg-primary-hover"
            variant="secondary"
          >
            Buy Audience
          </Button>
        </DialogPrimitive.Close>
      </DialogContent>
    </Dialog>
  );
};
export default memo(BuyModal);
