import ChartWrapper from "components/chart/ChartWrapper";
import Polar<PERSON>hart from "components/chart/PolarChart";
import { useState } from "react";
import { useAppSelector } from "store";
import { cn } from "utils/utils";

const SpecialChart = ({}: {}) => {
  const { social_detail, loading } = useAppSelector((state) => state.detailSocial);
  const { collapse } = useAppSelector((state) => state.sidebar);
  const [select, setSelect] = useState<"Gender" | "Relationships">("Relationships");

  const data: { type: string; labels: string[]; value: number[] }[] = [
    {
      type: "Gender",
      labels: ["Female", "Male"],
      value: [social_detail?.summarize.gender.F ?? 0, social_detail?.summarize.gender.M ?? 0],
    },
  ];

  const selectedData = data.find((item) => item.type === select) || {
    labels: [],
    value: [],
  };

  return (
    <ChartWrapper
      loading={loading.social_detail}
      className={cn("lg:col-span-1", collapse ? "md:col-span-1" : "md:col-span-2")}
      title={select}
      options={[
        { label: "Relationships", value: "Relationships" },
        { label: "Gender", value: "Gender" },
      ]}
      isPieChart
      onChange={(input) => setSelect(input as "Gender" | "Relationships")}
    >
      <PolarChart
        labels={selectedData.labels}
        values={selectedData.value}
        showTotal={true}
        titleTooltip={select}
      />
    </ChartWrapper>
  );
};

export default SpecialChart;
