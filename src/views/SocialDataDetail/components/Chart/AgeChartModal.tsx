import React, { useMemo } from "react";
import { getAgeValue } from "utils/utils";
import { GENDER } from "constants/label";
import AgeChart from "components/AgeChart";
import ManIcon from "assets/icons/ManIcon";
import WomanIcon from "assets/icons/WomanIcon";
import { AgeGroup } from "types/ResponseApi";

const AgeChartModal = ({ ageGenderData, loading = false }: {ageGenderData: AgeGroup, loading?: boolean}) => {
  const genderData = useMemo(() => {
    return Object.keys(ageGenderData)
      .map((ageRange) => {
        const ageData = ageGenderData[ageRange] || {};
        return {
          [ageRange]: {
            F: ageData["F"] || 0,
            M: ageData["M"] || 0,
            NULL: ageData["NULL"] || 0,
          },
        };
      })
      .filter((item) => {
        const key = Object.keys(item)[0];
        return key !== "NULL";
      })
      .sort((a, b) => getAgeValue(Object.keys(a)[0]) - getAgeValue(Object.keys(b)[0]));
  }, [ageGenderData]);

  const labels = useMemo(() => genderData.map((item) => Object.keys(item)[0]), [genderData]);
  const femaleData = useMemo(
    () => genderData.map((item) => item[Object.keys(item)[0]].F),
    [genderData]
  );
  const maleData = useMemo(
    () => genderData.map((item) => item[Object.keys(item)[0]].M),
    [genderData]
  );

  return (
    <AgeChart
      loading={loading}
      data={[
        {
          label: GENDER.female,
          values: femaleData,
          backgroundColor: "#924FE8",
          icon: WomanIcon,
        },
        {
          label: GENDER.male,
          values: maleData,
          backgroundColor: "#F48E2F",
          icon: ManIcon,
        },
      ]}
      labels={labels}
    />
  );
};

export default React.memo(AgeChartModal);
