import { Skeleton } from "components/ui/skeleton";
import { useAppSelector } from "store";
import { cn } from "utils/utils";
import CountUp from "react-countup";
import React from "react";
import useResponsive from "../../../../hooks/useResponsive.tsx";

type Props = {
  className?: string;
  total: number;
  icon: React.ReactNode;
  title: string;
  titleColor?: string;
};

const AnalystCard: React.FC<Props> = ({ className, title, total, icon, titleColor }) => {
  const { social_detail } = useAppSelector((state) => state.detailSocial.loading);
  const { width } = useResponsive();

  const handleScaleDownText = () => {
    const isBilTotal = total > 100_000_000;
    return width < 1366 && width >= 1280
      ? isBilTotal
        ? "text-base"
        : "text-lg"
      : width < 576
      ? "text-xl"
      : isBilTotal
      ? "text-[16px]"
      : "text-[18px]";
  };

  return (
    <div
      className={cn(
        "rounded-2xl col-span-1 shadow-chart p-4 gap-3 flex items-start text-white flex-col md:flex-row md:p-6 md:items-center md:gap-4",
        className
      )}
    >
      {social_detail ? <Skeleton className="h-[50px] w-[50px] rounded-md" /> : icon}
      <div className="flex flex-col flex-1 h-[50px]">
        {social_detail! ? (
          <>
            <Skeleton className="h-[12px] w-1/3" />
            <Skeleton className="h-9 w-2/3 mt-1" />
          </>
        ) : (
          <>
            <span className={cn("text-xs font-semibold", titleColor)}>{title}</span>
            <p className={cn("font-semibold transition-all duration-300", handleScaleDownText())}>
              <CountUp
                className="whitespace-nowrap"
                start={0}
                end={total}
                duration={2.75}
                separator=","
              />
            </p>
          </>
        )}
      </div>
    </div>
  );
};
export default AnalystCard;
