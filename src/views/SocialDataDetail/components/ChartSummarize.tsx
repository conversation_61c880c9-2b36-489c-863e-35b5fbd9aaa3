import { useAppSelector } from "store";

import ChartWrapper from "components/chart/ChartWrapper";
import PolarChart from "components/chart/PolarChart";
import BarChartHorizontal from "components/chart/BarChartHorizontal";
import AgeChartModal from "./Chart/AgeChartModal";

import { CityValue } from "types/Persona";
import { Summarize } from "types/SocialData";
import { getCityData, getGenderData } from 'utils/persona';
import { cn } from "utils/utils";
import ListCardAnalyst from "./Chart/ListCardAnalyst";
import { formatRelationShipsData } from "utils/socialData/formatRelationShips";
import { fNumberToString } from "utils/number";

interface Props {
  data: Summarize;
  loading: boolean;
  isShowListCard?: boolean;
}
const ChartSummarize = ({ data, loading, isShowListCard = true }: Props) => {
  const { collapse } = useAppSelector((state) => state.sidebar);

  const cityData = data.city && getCityData(data.city, "social");

  const relationships = formatRelationShipsData(data.relationships);

  const genderData = getGenderData(data.gender)

  return (
    <>
      {isShowListCard && <ListCardAnalyst data={data.overview} />}
      <div
        className={cn(
          "grid grid-cols-1 my-6 gap-x-7 gap-y-6 xl:grid-cols-3",
          collapse ? "md:grid-cols-3" : "md:grid-cols-2"
        )}
      >
        <AgeChartModal ageGenderData={data.age_gender || {}} />
        <ChartWrapper
          loading={loading}
          className={cn("col-span-2 lg:col-span-1", collapse ? "md:col-span-1" : "md:col-span-2")}
          title="Gender"
          isPieChart
          isEmptyData={genderData == undefined || Object.keys(genderData).length == 0}
        >
          <PolarChart
            labels={["Female", "Male"]}
            values={[data.gender?.F ?? 0, data.gender?.M ?? 0]}
            showTotal={true}
            backgroundColors={["#924FE8", "#F48E2F"]}
            titleTooltip="Gender"
          />
        </ChartWrapper>
        <ChartWrapper
          loading={loading}
          className={cn(
            "col-span-2 lg:col-span-1 xl:col-span-2",
            collapse ? "col-span-3" : "md:col-span-2"
          )}
          title="Top cities"
          isBarHorizonChart
          isEmptyData={!cityData || cityData?.length == 0}
        >
          <BarChartHorizontal
            values={cityData?.map((item: CityValue) => item.count) || []}
            labels={cityData?.map((item: CityValue) => item.city) || []}
          />
        </ChartWrapper>
        <ChartWrapper
          loading={loading}
          className={cn("col-span-2 xl:col-span-1", collapse ? "col-span-3" : "md:col-span-2")}
          title="Relationships"
          isPieChart
          isEmptyData={relationships.total == 0}
        >
          <PolarChart
            labels={relationships.labels}
            values={[...relationships.value]}
            showTotal={true}
            titleTooltip="Relationships"
            isCustomTooltip={true}
            customTooltip={CustomOtherTooltip(relationships.otherItems, relationships.total)}
          />
        </ChartWrapper>
      </div>
    </>
  );
};

export default ChartSummarize;

interface otherItems {
  label: string;
  value: number;
}
const CustomOtherTooltip = (items: otherItems[], total: number) => `
 ${items
   .map(
     (item) =>
       `<div class="flex gap-1 justify-start w-[250px]">
            <div class= "font-medium text-xs text-secondary 3xl:text-md ml-1 capitalize">${
              item.label
            }:</div>
            <div class = "font-medium text-xs text-primary 3xl:text-md">${
              fNumberToString(item.value) + " - " + ((item.value / total) * 100).toFixed(2)
            }%</div>
        </div>`
   )
   .join("")}
`;
