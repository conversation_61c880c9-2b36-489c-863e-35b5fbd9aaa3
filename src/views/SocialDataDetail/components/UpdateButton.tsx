import { ModalContext } from "providers/Modal";
import { useContext } from "react";
import ConfirmUpdateModal from "./ConfirmUpdateModal";
import { Button } from "components/ui/button";
import { RiLoopLeftLine } from "@remixicon/react";
import LABEL from "constants/label";
import { cn } from "utils/utils";

type Props = {
  className?: string;
};

const UpdateButton = ({ className }: Props) => {
  const modal = useContext(ModalContext);

  const handleConfirmUpdate = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      title: "",
      content: <ConfirmUpdateModal />,
      footer: "",
      className: "max-w-[680px]",
      isShowTitle: false,
    }));
  };
  return (
    <Button
      size={"default"}
      className={cn(
        "bg-transparent text-primary font-medium gap-1 py-1.5 px-[10px] rounded-xl hover:bg-gray-100",
        className
      )}
      onClick={handleConfirmUpdate}
      children={
        <>
          <RiLoopLeftLine size={20} color="#0F1324" />
          {LABEL.update}
        </>
      }
    />
  );
};
export default UpdateButton;
