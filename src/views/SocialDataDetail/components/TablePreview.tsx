import { useMemo } from "react";

import Pagination from "components/Pagination";
import { DataTable } from "components/DataTable/TableCustom";

import useFilters from "hooks/useFilters";
import { TDataPreview } from "types/SocialData";
import { cn } from "utils/utils";
import { getSocialAudienceColumns } from 'constants/socialData/column';

const TablePreview = ({ data, isDataset }: {data: TDataPreview, isDataset?: boolean}) => {
  const { params, setSearchParams } = useFilters();
  const memoizedColumns = useMemo(() => getSocialAudienceColumns({
    isDataset: isDataset
  }), [isDataset]);
  return (
    <>
      <DataTable
        columns={memoizedColumns}
        data={data.data || []}
        loading={data.loading}
        classTable={cn("mt-6 min-h-[930px]", data.total > 0 && !data.loading && "min-h-fit [&_tbody>tr]:h-[87px]")}
      />
      <Pagination
        className="mt-4"
        totalCount={data.total}
        pageSize={10}
        currentPage={Number(params.page) || 1}
        onPageChange={(page) =>
          setSearchParams({
            ...params,
            page: page.toString(),
          })
        }
      />
    </>
  );
};

export default TablePreview;
