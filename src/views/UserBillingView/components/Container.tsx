import { useEffect, useState } from "react";

import { Box } from "components/Box";
import InvoicesSkeleton from "./InvoicesSkeleton";
import InvoicesTable from "./InvoicesTable";

import { paymentAPI } from "apis/plansAndPayment";
import { InvoicesResponse } from "types/Payment";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import { toPaginationParams } from "utils/pagination";
import useFilters from "hooks/useFilters";
import LoadingSkeleton from "components/LiveAdsPost/LoadingSkeleton";
import AutoRenew from "./AutoRenew";
import BillingInformation from "./BillingInformation";
import { useAppDispatch, useAppSelector } from '../../../store';
import { getMe } from '../../../store/redux/auth/slice';

interface IData {
  data: InvoicesResponse[];
  loading: boolean;
  count: number;
}
const Container = () => {
  const { params } = useFilters();
  const [invoices, setInvoices] = useState<IData>({
    data: [],
    count: 0,
    loading: false,
  });
  const dispatch = useAppDispatch();
  const { isNewNoti } = useAppSelector((state) => state.notification);

  useEffect(() => {
    getListInvoices();
  }, [params]);

  useEffect(() => {
    if (isNewNoti) {
      getListInvoices();
      dispatch(getMe()).unwrap();
    }
  }, [isNewNoti]);

  const getListInvoices = async () => {
    setInvoices((prev) => ({ ...prev, loading: true }));
    const response = await paymentAPI.getListInvoices({ params: toPaginationParams(params) });

    if (response && response.data) {
      setInvoices((prev) => ({ ...prev, data: response.data.items, count: response.data.count }));
    }
    setInvoices((prev) => ({ ...prev, loading: false }));
  };

  return (
    <Box variant="col-start" className="w-full gap-3 lg:gap-6">
      <BillingInformation />
      <Box
        variant="col-start"
        className="p-3 lg:p-6 border border-custom-primary bg-custom-primary rounded-2xl w-full gap-[11px] lg:gap-6"
      >
        <div className="text-primary text-base lg:text-[20px] font-medium">
          {USER_PROFILE_LABEL.BILLING.title_invoice}
        </div>
        <div className="w-full">
          {invoices.loading && <LoadingSkeleton />}
          {!invoices.loading && (
            <>
              {invoices.data.length > 0 ? (
                <InvoicesTable total={invoices.count} data={invoices.data} />
              ) : (
                <InvoicesSkeleton />
              )}
            </>
          )}
        </div>
      </Box>
      <AutoRenew />
    </Box>
  );
};

export default Container;
