import { DataTable } from "components/DataTable/TableCustom";
import { INVOICES_COLUMN } from "constants/PlanPayment/column";
import { InvoicesResponse } from "types/Payment";
import Pagination from "components/Pagination";
import useFilters from "hooks/useFilters";
import { cn } from "utils/utils";

const InvoicesTable = ({ data, total }: { data: InvoicesResponse[]; total: number }) => {
  const { params, setSearchParams } = useFilters();

  return (
    <div className="w-full">
      <DataTable
        columns={INVOICES_COLUMN}
        data={data || []}
        classTable={cn("!min-h-fit")}
        cellClass="text-center first:text-left text-secondary text-sm capitalize"
      />
      <Pagination
        className="mt-4 "
        currentPage={Number(params["page"]) || 1}
        pageSize={10}
        totalCount={total || 0}
        onPageChange={(page) => setSearchParams((prev) => ({ ...prev, page: String(page) }))}
      />
    </div>
  );
};

export default InvoicesTable;
