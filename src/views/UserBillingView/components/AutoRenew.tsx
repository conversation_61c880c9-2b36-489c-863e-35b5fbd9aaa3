import { useSelector } from "react-redux";
import { useContext } from "react";
import { ModalContext } from "providers/Modal";
import * as Popover from "@radix-ui/react-popover";
import { RiInformation2Line } from "@remixicon/react";
import { subscriptionStore } from "store/redux/subscription/slice";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import ConfirmCancelSubs from "./ConfirmCancelSubs";

import LABEL from "constants/label";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import { cn } from "utils/utils";

const AutoRenew = () => {
  const { last_sub } = useSelector(subscriptionStore);
  const modal = useContext(ModalContext);

  const handleConfirm = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <ConfirmCancelSubs />,
      className: "max-w-[530px]",
    }));
  };

  return (
    <Box className="py-2 w-full rounded-sm  items-center mb-6 gap-4 justify-between lg:justify-start">
      <Box className="gap-1 w-fit">
        <div className="text-primary text-sm font-medium">
          {USER_PROFILE_LABEL.BILLING.auto_renew}
        </div>
        <Popover.Root>
          <Popover.Trigger>
            <RiInformation2Line size={20} className="text-secondary" />
          </Popover.Trigger>
          <Popover.Portal>
            <Popover.Content
              side="top"
              align="start"
              className="w-[200px] md:w-[450px] bg-black overflow-hidden z-10 rounded-xl "
            >
              <div className="text-sm text-white relative z-10 px-2 py-1">
                {USER_PROFILE_LABEL.BILLING.message_renew}
              </div>
              <Popover.Arrow className="relative left-2 z-0 bg-transparent" />
            </Popover.Content>
          </Popover.Portal>
        </Popover.Root>
      </Box>
      <Button
        variant="secondary"
        className={cn(
          "font-sm px-2 py-1 h-8 border-[#A7AAB1] rounded-sm",
          !last_sub?.is_renewal && "border-none bg-custom-secondary hover:bg-custom-secondary"
        )}
        disabled={!last_sub?.is_renewal}
        onClick={last_sub?.is_renewal ? handleConfirm : () => {}}
      >
        {last_sub?.is_renewal ? LABEL.cancel : "Canceled"}
      </Button>
    </Box>
  );
};

export default AutoRenew;
