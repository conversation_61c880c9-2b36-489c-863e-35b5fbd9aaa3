import { useContext, useState } from "react";
import { useF<PERSON>, SubmitHandler } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { ModalContext } from "providers/Modal";
import { BillingInfoSchema, BillingInfoSchemaType } from "validations/billingInfo";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { Label } from "components/ui/label";
import InputController from "components/InputController";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import { paymentAPI } from "apis/plansAndPayment";
import handleCloseModal from "utils/handleCloseModal";
import { InformationInvoice } from "types/Payment";

interface Props {
  infoInvoice?: InformationInvoice;
  getInfoInvoice: () => Promise<void>;
}
function ChangeBillingInfo({ infoInvoice, getInfoInvoice }: Props) {
  const context = useContext(ModalContext);
  const [loading, setLoading] = useState<boolean>(false);

  const {
    control,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm<BillingInfoSchemaType>({
    resolver: yupResolver(BillingInfoSchema),
    defaultValues: {
      email_invoice: infoInvoice?.email_invoice || "",
      address_line_1: infoInvoice?.address_line_1 || "",
      tax_id: infoInvoice?.tax_id || "",
      name_invoice: infoInvoice?.name_invoice || "",
    },
    mode: "onChange",
  });
  const onSubmit: SubmitHandler<BillingInfoSchemaType> = async (data) => {
    setLoading(true);
    await paymentAPI.updateInforInvoice(data);
    reset();
    getInfoInvoice();
    handleCloseModal(context);
    setLoading(false);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="w-full">
      <Box variant="col-start" className="w-full gap-2 lg:gap-6">
        <div className="text-base lg:text-[20px] text-primary font-semibold text-center w-full">
          Change Billing Information
        </div>

        <Box variant="col-start" className="w-full gap-3 lg:gap-6">
          <div className="grid grid-cols-1 lg:grid-cols-6 w-full gap-2">
            <div className="col-span-4">
              <Label className="text-sm lg:text-xs text-secondary font-semibold leading-5">Company Name</Label>
              <InputController
                name="name_invoice"
                control={control}
                className="h-10 text-sm relative"
              />
            </div>
            <div className="col-span-4 lg:col-span-2">
              <Label className="text-sm lg:text-xs text-secondary font-semibold leading-5">Tax Code</Label>
              <InputController
                name="tax_id"
                control={control}
                className="h-10 text-sm relative"
                error={errors?.tax_id?.message}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-6 w-full gap-2">
            <div className="col-span-4">
              <Label className="text-sm lg:text-xs text-secondary font-semibold leading-5">
                Company Address
              </Label>
              <InputController
                name="address_line_1"
                control={control}
                className="h-10 text-sm relative"
              />
            </div>
            <div className="col-span-4 lg:col-span-2">
              <Label className="text-sm lg:text-xs text-secondary font-semibold leading-5">
                Email Address
              </Label>
              <InputController
                name="email_invoice"
                control={control}
                className="h-10 text-sm relative"
                error={errors?.email_invoice?.message}
              />
            </div>
          </div>
        </Box>

        <Box className="flex flex-col-reverse gap-3 lg:gap-6 lg:flex-nowrap lg:grid grid-cols-2 text-md font-semibold w-full">
          <Button
            className="w-full text-md font-semibold text-custom-brand rounded-xl"
            variant="main"
            type="submit"
            disabled={loading}
            children={loading ? <LoadingButtonIcon /> : "Save change"}
          />

          <Button
            className="w-full text-md font-semibold text-primary border-custom-primary rounded-xl mt-1 lg:mt-auto"
            variant="secondary"
            children="Cancel"
            type="button"
            onClick={() => handleCloseModal(context)}
          />
        </Box>
      </Box>
    </form>
  );
}
export default ChangeBillingInfo;
