import { useContext, useEffect, useState } from "react";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import ChangeBillingInfo from "./ChangeBillingInfo";
import GroupControl from "views/UserProfile/components/GroupControl";
import ButtonEditUserInfor from "components/Button/ButtonEditUserInfor";

import { paymentAPI } from "apis/plansAndPayment";
import { InformationInvoice } from "types/Payment";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import useResponsive from "../../../hooks/useResponsive.tsx";

const BillingInformation = () => {
  const modal = useContext(ModalContext);
  const {isTablet} =useResponsive()
  const [infoInvoice, setInfoInvoice] = useState<InformationInvoice>();

  useEffect(() => {
    getInfoInvoice();
  }, []);

  const handleEditBillingInfor = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      title: "",
      className: "max-w-[calc(100%-32px)] lg:max-w-[825px] rounded-sm p-6",
      content: <ChangeBillingInfo infoInvoice={infoInvoice} getInfoInvoice={getInfoInvoice} />,
      footer: "",
    }));
  };
  const getInfoInvoice = async () => {
    const response = await paymentAPI.getInfoInvoice();
    if (response && response.data) {
      setInfoInvoice(response.data);
    }
  };
  return (
    <div className="p-3 lg:p-6 border border-custom-primary rounded-2xl w-full">
      <Box variant="col-start" className="w-full gap-3 lg:gap-6">
        <Box className="justify-between w-full flex-wrap lg:flex-nowrap gap-2 lg:gap-6">
          <div>
            <h4
              className="text-[18px] lg:text-[20px] text-primary font-semibold"
              children={USER_PROFILE_LABEL.BILLING.billing_profile}
            />
            {!isTablet && <div className="text-sm text-secondary mt-1">
              {USER_PROFILE_LABEL.BILLING.billing_profile_mess}
            </div>}
          </div>
          <ButtonEditUserInfor CallBack={handleEditBillingInfor} />
          {isTablet && <div className="text-base text-secondary mt-1">
            {USER_PROFILE_LABEL.BILLING.billing_profile_mess}
          </div>}
        </Box>

        <Box variant="col-start" className="w-full gap-3 md:gap-6">
          <div className="flex flex-col lg:grid grid-cols-6 w-full gap-3">
            <div className="col-span-4">
              <GroupControl label="Company Name" value={infoInvoice?.name_invoice || ""} />
            </div>
            <div className="col-span-2">
              <GroupControl label="Tax Code" value={infoInvoice?.tax_id || ""} />
            </div>
          </div>
          <div className="flex flex-col lg:grid grid-cols-6 w-full gap-3">
            <div className="col-span-4">
              <GroupControl label="Company Address" value={infoInvoice?.address_line_1 || ""} />
            </div>
            <div className="col-span-2">
              <GroupControl label="Email Address" value={infoInvoice?.email_invoice || ""} />
            </div>
          </div>
        </Box>
      </Box>
    </div>
  );
};

export default BillingInformation;
