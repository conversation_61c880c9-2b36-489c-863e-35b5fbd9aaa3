import LoadingFavicon from 'components/Loading/LoadingFavicon';
import getUserInfor from 'hooks/getUserInfor';
import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { ROOT_PATH, USER_PROFILE_PATH } from 'types/Router';
import { getMe } from 'store/redux/auth/slice';
import { fetchActiveSubscription, fetchLastSubscription } from '../../../store/redux/subscription/slice';

const PaymentStatus = ({ status }: {status: 'SUCCESS' | 'FAIL'}) => {
  const [countdown, setCountdown] = useState(10000);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const path =
    status == 'SUCCESS'
      ? `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.BILLING}`
      : '/plan/upgrade';
  const { isNewNoti } = useAppSelector((state) => state.notification);

  //call all information sau khi countdown
  isNewNoti ? getUserInfor(false) : getUserInfor(countdown > 0);

  const handleRefetch = () => {
    dispatch(getMe()).unwrap();
    dispatch(fetchLastSubscription()).unwrap();
    dispatch(fetchActiveSubscription()).unwrap();
  };

  useEffect(() => {
    if (isNewNoti) {
      navigate(path);
      if (status == 'SUCCESS') {
        handleRefetch();
      }
    } else {
      setTimeout(() => {
        navigate(path);
        handleRefetch();
      }, 10000);
    }
  }, [isNewNoti]);

  useEffect(() => {
    //count down when payment failed
    let timer: any;

    if (status === 'FAIL') {
      timer = setTimeout(() => {
        navigate(path);
      }, 3000);
    }
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [status, path, navigate]);

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  const isSuccess = status == 'SUCCESS';
  return (
    <div className="h-full flex flex-col gap-4 justify-center w-full">
      <div className="flex gap-2 flex-col items-center justify-center">
        <span className="text-primary font-semibold text-dp-xs text-center">
          Payment {isSuccess ? 'success' : 'Failed'}!
        </span>
        <span className="text-md text-center text-secondary">
          {isSuccess
            ?
            ' Thank you for your purchase! Your package has been successfully activated'
            :
            'Unfortunately, your payment could not be processed. Please try again or contact support if the issue persists.'}
        </span>
      </div>

      {isSuccess && (
        <div>
          <div className="text-sm text-secondary text-center">
            Please wait a moment, the system will automatically redirect you to the Billing page.
          </div>
          <LoadingFavicon className="!min-h-fit" />
        </div>
      )}

      {!isSuccess && (
        <>
          <div className="flex text-sm gap-1 justify-center text-center">
            <span className="text-tertiary">
              If you are not redirected automatically, please click
              <Link to={path} className="text-primary underline mx-1">
                here
              </Link>
              to go to
              {isSuccess ? 'your Invoice History.' : 'plan upgrade'}
            </span>
          </div>
          <div className="flex text-sm gap-1 justify-center text-secondary">
            Auto redirect to plan upgrade
          </div>
        </>
      )}
    </div>
  );
};

export default PaymentStatus;
