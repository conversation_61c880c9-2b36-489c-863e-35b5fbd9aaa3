import { <PERSON> } from "react-router-dom";

import { Box } from "components/Box";
import { But<PERSON> } from "components/ui/button";

import { USER_PROFILE_LABEL } from "constants/UserProfile/label";

const InvoicesSkeleton = () => {
  return (
    <Box
      variant="col-start"
      className="h-[300px] p-6 bg-custom-secondary w-full rounded-2xl items-center justify-center"
    >
      <span className="text-secondary text-sm">{USER_PROFILE_LABEL.BILLING.nodata}</span>
      <Link to="/plan/upgrade">
        <Button variant="main" children={USER_PROFILE_LABEL.BILLING.purchase} />
      </Link>
    </Box>
  );
};

export default InvoicesSkeleton;
