import { useAppDispatch } from "store";
import { useContext, useState } from "react";
import { useSelector } from "react-redux";
import { ModalContext } from "providers/Modal";
import { RiCloseLine, RiErrorWarningLine } from "@remixicon/react";
import { fetchLastSubscription, subscriptionStore } from "store/redux/subscription/slice";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import { subscriptionAPI } from "apis/subscription";
import handleCloseModal from "utils/handleCloseModal";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import LABEL from "constants/label";

const ConfirmCancelSubs = () => {
  const dispatch = useAppDispatch();
  const { last_sub } = useSelector(subscriptionStore);
  const modal = useContext(ModalContext);
  const [loading, setLoading] = useState<boolean>(false);

  const handleUpdateRenewal = async () => {
    setLoading(true);
    await subscriptionAPI.updateRenewal({
      payload: { is_renewal: !last_sub?.is_renewal },
    });
    await dispatch(fetchLastSubscription()).unwrap();
    setLoading(false);
    handleCloseModal(modal);
  };
  return (
    <>
      <Box variant="col-start" className="items-center gap-0 relative">
        <Box className="justify-end w-full">
          <RiCloseLine
            className="cursor-pointer"
            size={20}
            onClick={() => handleCloseModal(modal)}
          />
        </Box>
        <RiErrorWarningLine
          size={80}
          className="text-center justify-center items-center text-error-default"
        />
        <div className="text-lg font-medium text-primary">
          {USER_PROFILE_LABEL.BILLING.cancel_sub}
        </div>
        <span className="text-secondary text-sm mt-2 text-center">
          {USER_PROFILE_LABEL.BILLING.confirm_cancel}
        </span>
      </Box>
      <Box className="grid grid-cols-2 w-full gap-4">
        <Button
          variant="secondary"
          className="bg-custom-secondary"
          onClick={() => handleCloseModal(modal)}
        >
          {LABEL.keep_plan}
        </Button>
        <Button
          className="bg-error-default text-white hover:bg-error-default"
          onClick={handleUpdateRenewal}
          disabled={loading}
        >
          {loading ? <LoadingButtonIcon /> : LABEL.cancel}
        </Button>
      </Box>
    </>
  );
};

export default ConfirmCancelSubs;
