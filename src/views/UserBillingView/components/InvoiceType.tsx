import { cn } from 'utils/utils';

const handleConvertType = (type: 'SUBSCRIPTION' | 'CREDIT' | 'CRM_CONTACT') => {
  switch (type) {
    case 'SUBSCRIPTION':
      return 'SUBSCRIPTION';
    case 'CREDIT':
      return 'CREDIT';
    case 'CRM_CONTACT':
      return 'CRM CONTACT';
    default:
      return '';
  }
};

const InvoiceType = ({ type }: {type: 'SUBSCRIPTION' | 'CREDIT' | 'CRM_CONTACT'}) => {
  return (
    <div
      className={cn(
        'text-sm rounded-xl p-2 capitalize',
        type === 'CREDIT' || type === 'CRM_CONTACT'
          ? 'bg-brand-disabled text-brand-strong'
          : 'text-secondary bg-custom-secondary'
      )}
    >
      {handleConvertType(type)}
    </div>
  );
};

export default InvoiceType;
