import Breadcrumb from "components/Breadcrumb";
import HeaderWrapper from "components/Header";
import UserTabs from "components/Tabs/UserTabs";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import Container from "./components/Container";
import { RiAccountCircleLine } from '@remixicon/react';
import { Box } from '../../components/Box';

const BillingView = () => {
  return (
    <>
      <Box className="gap-2 justify-start hidden md:flex">
        <RiAccountCircleLine color={'#515667'} size={20} /><Breadcrumb />
      </Box>
      <HeaderWrapper
        className="items-center md:items-center"
        leftChildren={{
          title: USER_PROFILE_LABEL.BILLING.title,
          subTitle: USER_PROFILE_LABEL.BILLING.subtitle,
        }}
      />
      <UserTabs />
      <Container />
    </>
  );
};

export default BillingView;
