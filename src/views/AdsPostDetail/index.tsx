import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useAppSelector } from "store";

import Breadcrumb from "components/Breadcrumb";
import HeaderDetailModal from "components/HeaderDetailModal";
import TablePreview from "views/SocialDataDetail/components/TablePreview";
import ChartSummarize from "views/SocialDataDetail/components/ChartSummarize";
import UpdateAndBuyAudience from "views/SocialDataDetail/components/UpdateAndBuyAudience";
import QuickViewImageLeft from "views/AdsPostView/components/QuickViewImageLeft";
import QuickViewDetail from "views/AdsPostView/components/QuickViewDetail";

import { adsPostAPI } from "apis/adsPost";
import { pageAPI } from "apis/pagePost";
import useFilters from "hooks/useFilters";
import useAvatar from "hooks/useAvatar";
import useGetAudiencePreview from "hooks/useGetAudiencePreview";
import { IAdsPostItem, IPageDetail } from "types/CrawlService";
import { formatPayloadAvatar } from "utils/utils";

const PostConverterDetail = () => {
  const { id = "" } = useParams();
  const { params } = useFilters();
  const { data: categories } = useAppSelector((state) => state.category);

  const [pageInformation, setPageInformation] = useState<IPageDetail>();
  const [data, setData] = useState<IAdsPostItem>();
  const [loading, setLoading] = useState<boolean>(false);

  const previewData = useGetAudiencePreview({
    id: data?.audience?.fb_uid || "",
    page: params.page || 1,
  });

  const payloadAvatar = formatPayloadAvatar(
    data?.audience?.type || 1,
    data?.audience?.fb_uid || "",
    data?.actor_id || ""
  );
  const { avatar } = useAvatar(payloadAvatar);

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    data?.actor_id && getDataPage(data?.actor_id);
  }, [data]);

  const getData = async () => {
    setLoading(true);
    const response = await adsPostAPI.getPostDetail({ id: id });
    if (response && response.data) {
      setData(response.data);
    }
    setLoading(false);
  };

  const getDataPage = async (id: string) => {
    const response = await pageAPI.getPageById({ id: id });
    if (response && response.data) {
      setPageInformation(response.data);
    }
  };

  const categoryName = categories?.items.find(
    (cate) => cate.code === data?.audience?.category
  )?.name;

  return (
    <>
      <Breadcrumb path={pageInformation?.name} loading={loading} />
      <HeaderDetailModal
        avatarUrl={avatar.url}
        name={pageInformation?.name || ""}
        size={data?.audience?.size}
        packageValue={data?.audience?.package}
        uid={data?.actor_id}
        description={data?.audience?.description}
        category={categoryName}
        typeAudience={data?.audience?.type || 4}
        subTypeAudience={data?.audience?.subtype}
        contentRight={
          <UpdateAndBuyAudience
            audience={data?.audience}
            disableBuyDataset={data?.audience?.is_ds_added}
            disableBuyAudience={data?.audience?.is_aud_added}
            disableUpdate={data?.audience?.system}
          />
        }
      />
      {data && (
        <div className="grid grid-cols-1 rounded-2xl shadow-sm overflow-hidden mt-3 sm:mt-6 lg:grid-cols-2">
          <QuickViewImageLeft
            title={data?.actor_name}
            att_type={data?.att_type || ""}
            attachments={data?.attachments}
          />
          <QuickViewDetail isShowPageInfor={false} isShowCTA={false} post={data} />
        </div>
      )}
      {data?.audience?.summarize && (
        <div className="mt-6">
          <ChartSummarize data={data.audience?.summarize} loading={false} />
        </div>
      )}
      {previewData && <TablePreview data={previewData} />}
    </>
  );
};
export default PostConverterDetail;
