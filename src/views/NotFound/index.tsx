import { Box } from 'components/Box';
import { Button } from 'components/ui/button';
import { Link, useNavigate } from 'react-router-dom';
import { PATH_DASHBOARD } from 'types/path';
import { RiArrowLeftLine } from '@remixicon/react';
import LogoSvg from '../../assets/icons/LogoSvg';
import IconFavicon from '../../assets/icons/IconFavicon';
import useResponsive from '../../hooks/useResponsive';
import { useEffect, useRef, useState } from 'react';

const NotFound = () => {
  const navigate = useNavigate();
  const { isTablet,width } = useResponsive();
  const svgRef = useRef<SVGSVGElement>(null);
  const [resolution, setResolution] = useState({
    width: isTablet ? 343 : 782,
    height: isTablet ? 134 : 302
  });
  useEffect(() => {
    if (svgRef.current) {
      const widthSvg = ( width - 32 ) < 782 ? ( width - 32 ) : 782;
      setResolution({
        width: widthSvg,
        height: +( widthSvg / 2.5 ).toFixed(0)
      });
    }
  }, [width]);
  return (
    <Box variant="col-start" className="relative w-full h-full justify-center items-center gap-12">
      <div className="sm:absolute top-1/2 left-1/2  sm:transform sm:-translate-x-1/2 sm:-translate-y-1/2">
        <svg xmlns="http://www.w3.org/2000/svg" width={resolution.width} height={resolution.height} viewBox="0 0 782 302" fill="none" ref={svgRef}>
          <path
            d="M0.499146 244.111V195.673L121.948 4.33811H163.709V71.3836H138.993L62.431 192.548V194.821H235.016V244.111H0.499146ZM140.13 295.247V229.338L141.266 207.889V4.33811H198.937V295.247H140.13Z"
            fill="#F0F0F0"
          />
          <path
            d="M390.059 301.639C365.627 301.545 344.604 295.531 326.991 283.599C309.472 271.668 295.977 254.385 286.508 231.753C277.133 209.12 272.493 181.895 272.587 150.077C272.587 118.353 277.275 91.3173 286.65 68.9688C296.119 46.6203 309.614 29.6222 327.133 17.9745C344.746 6.23206 365.722 0.36084 390.059 0.36084C414.396 0.36084 435.324 6.23206 452.843 17.9745C470.457 29.7169 483.998 46.7624 493.468 69.1108C502.938 91.3646 507.625 118.353 507.53 150.077C507.53 181.99 502.796 209.262 493.326 231.895C483.951 254.528 470.504 271.81 452.985 283.742C435.466 295.673 414.491 301.639 390.059 301.639ZM390.059 250.645C406.725 250.645 420.03 242.264 429.974 225.503C439.917 208.742 444.841 183.599 444.746 150.077C444.746 128.012 442.474 109.641 437.928 94.9631C433.477 80.2851 427.133 69.2529 418.894 61.8665C410.75 54.4802 401.138 50.787 390.059 50.787C373.487 50.787 360.229 59.073 350.286 75.6449C340.343 92.2169 335.324 117.028 335.229 150.077C335.229 172.425 337.455 191.081 341.905 206.043C346.451 220.91 352.843 232.084 361.082 239.565C369.32 246.952 378.979 250.645 390.059 250.645Z"
            fill="#F0F0F0"
          />
          <path
            d="M546.984 244.111V195.673L668.432 4.33811H710.194V71.3836H685.478L608.915 192.548V194.821H781.501V244.111H546.984ZM686.614 295.247V229.338L687.751 207.889V4.33811H745.421V295.247H686.614Z"
            fill="#F0F0F0"
          />
        </svg>
      </div>
      <Box variant="col-start" className="gap-3 w-full items-center z-10">
        <div className="flex gap-2 p-[1px] mb-3 items-center sm:px-[66px]">
          <IconFavicon />
          <LogoSvg color="hsla(229, 41%, 10%, 0.6)" />
        </div>
        <div className="text-primary text-2xl md:text-4xl font-medium">Page not found!</div>
        <p className="text-secondary text-sm md:text-base font-normal text-center whitespace-pre-line md:whitespace-nowrap">
          Sorry, we couldn’t find the page you’re looking for. Try going back to our homepage.
        </p>
        <Box className="flex-col lg:flex-row w-full lg:w-fit gap-3">
          <Button
            variant="outline"
            className="w-full lg:w-fit"
            onClick={() => navigate(-1)}
          ><RiArrowLeftLine size={21} /> Go back</Button>
          <Link to={`${PATH_DASHBOARD.audience_finder.social}`} className="w-full lg:w-fit">
            <Button variant="main" className="w-full lg:w-fit">Go Home</Button>
          </Link>
        </Box>
      </Box>
    </Box>
  );
};

export default NotFound;
