import { Box } from "components/Box";
import { Link, useNavigate } from "react-router-dom";
import useResponsive from "hooks/useResponsive";
import { useEffect, useRef, useState } from "react";
import IconFavicon from "assets/icons/IconFavicon";
import LogoSvg from "assets/icons/LogoSvg";
import { Button } from "components/ui/button";
import { RiArrowLeftLine } from "@remixicon/react";
import { PATH_DASHBOARD } from "types/path";

const ComingSoon = () => {
  const navigate = useNavigate();
  const { isTablet, width } = useResponsive();
  const svgRef = useRef<SVGSVGElement>(null);
  const [resolution, setResolution] = useState({
    width: isTablet ? 333 : 622,
    height: isTablet ? 152 : 281
  });

  useEffect(() => {
    if (svgRef.current) {
      const widthSvg = ( width - 42 ) < 622 ? ( width - 42 ) : 622;
      setResolution({
        width: widthSvg,
        height: +( widthSvg / 2.19 ).toFixed(0)
      });
    }
  }, [width]);

  return (
    <Box variant="col-start" className="relative w-full h-full justify-center items-center gap-7 lg:gap-8">
      <div className="sm:absolute top-1/2 left-1/2  sm:transform sm:-translate-x-1/2 sm:-translate-y-1/2">
        <svg xmlns="http://www.w3.org/2000/svg" width={resolution.width} height={resolution.height} viewBox="0 0 622 281" fill="none" ref={svgRef}>
          <path
            d="M85.0949 32.4701H73.1508C72.4444 29.0345 71.2082 26.0163 69.4423 23.4156C67.7084 20.8148 65.5893 18.6315 63.0849 16.8655C60.6126 15.0675 57.8673 13.719 54.8492 12.8199C51.831 11.9209 48.6844 11.4714 45.4094 11.4714C39.4373 11.4714 34.0271 12.9805 29.1788 15.9986C24.3626 19.0168 20.5257 23.4637 17.6681 29.3395C14.8425 35.2153 13.4298 42.4235 13.4298 50.9643C13.4298 59.505 14.8425 66.7133 17.6681 72.5891C20.5257 78.4648 24.3626 82.9118 29.1788 85.9299C34.0271 88.9481 39.4373 90.4572 45.4094 90.4572C48.6844 90.4572 51.831 90.0077 54.8492 89.1086C57.8673 88.2096 60.6126 86.8771 63.0849 85.1112C65.5893 83.3131 67.7084 81.1137 69.4423 78.513C71.2082 75.8801 72.4444 72.862 73.1508 69.4585H85.0949C84.1959 74.4995 82.5584 79.0107 80.1824 82.9921C77.8064 86.9735 74.8525 90.3608 71.3206 93.1543C67.7887 95.9155 63.8234 98.0186 59.4246 99.4635C55.0579 100.908 50.3861 101.631 45.4094 101.631C36.9971 101.631 29.5159 99.5759 22.9659 95.466C16.4158 91.3562 11.2625 85.5125 7.50585 77.935C3.74921 70.3575 1.87089 61.3673 1.87089 50.9643C1.87089 40.5613 3.74921 31.571 7.50585 23.9935C11.2625 16.416 16.4158 10.5724 22.9659 6.46254C29.5159 2.35271 36.9971 0.297791 45.4094 0.297791C50.3861 0.297791 55.0579 1.02022 59.4246 2.46509C63.8234 3.90995 67.7887 6.02908 71.3206 8.82248C74.8525 11.5838 77.8064 14.9551 80.1824 18.9365C82.5584 22.8858 84.1959 27.397 85.0949 32.4701Z"
            fill="#F0F0F0"
          />
          <path
            d="M202.539 50.9643C202.539 61.3673 200.66 70.3575 196.904 77.935C193.147 85.5125 187.994 91.3562 181.444 95.466C174.894 99.5759 167.412 101.631 159 101.631C150.588 101.631 143.107 99.5759 136.557 95.466C130.007 91.3562 124.853 85.5125 121.097 77.935C117.34 70.3575 115.462 61.3673 115.462 50.9643C115.462 40.5613 117.34 31.571 121.097 23.9935C124.853 16.416 130.007 10.5724 136.557 6.46254C143.107 2.35271 150.588 0.297791 159 0.297791C167.412 0.297791 174.894 2.35271 181.444 6.46254C187.994 10.5724 193.147 16.416 196.904 23.9935C200.66 31.571 202.539 40.5613 202.539 50.9643ZM190.98 50.9643C190.98 42.4235 189.551 35.2153 186.693 29.3395C183.868 23.4637 180.031 19.0168 175.183 15.9986C170.366 12.9805 164.972 11.4714 159 11.4714C153.028 11.4714 147.618 12.9805 142.769 15.9986C137.953 19.0168 134.116 23.4637 131.259 29.3395C128.433 35.2153 127.02 42.4235 127.02 50.9643C127.02 59.505 128.433 66.7133 131.259 72.5891C134.116 78.4648 137.953 82.9118 142.769 85.9299C147.618 88.9481 153.028 90.4572 159 90.4572C164.972 90.4572 170.366 88.9481 175.183 85.9299C180.031 82.9118 183.868 78.4648 186.693 72.5891C189.551 66.7133 190.98 59.505 190.98 50.9643Z"
            fill="#F0F0F0"
          />
          <path
            d="M237.541 1.64633H251.797L285.318 83.5218H286.474L319.994 1.64633H334.25V100.282H323.077V25.3421H322.113L291.29 100.282H280.501L249.678 25.3421H248.714V100.282H237.541V1.64633Z"
            fill="#F0F0F0"
          />
          <path d="M385.062 1.64633V100.282H373.118V1.64633H385.062Z" fill="#F0F0F0" />
          <path
            d="M502.144 1.64633V100.282H490.585L436.836 22.8376H435.873V100.282H423.929V1.64633H435.488L489.429 79.2836H490.393V1.64633H502.144Z"
            fill="#F0F0F0"
          />
          <path
            d="M608.39 32.4701C607.331 29.2271 605.934 26.3214 604.2 23.7527C602.498 21.152 600.46 18.9365 598.084 17.1064C595.74 15.2762 593.075 13.8795 590.089 12.9162C587.103 11.953 583.828 11.4714 580.264 11.4714C574.42 11.4714 569.106 12.9805 564.322 15.9986C559.538 19.0168 555.733 23.4637 552.908 29.3395C550.082 35.2153 548.669 42.4235 548.669 50.9643C548.669 59.505 550.098 66.7133 552.956 72.5891C555.813 78.4648 559.682 82.9118 564.563 85.9299C569.443 88.9481 574.934 90.4572 581.034 90.4572C586.685 90.4572 591.662 89.2531 595.964 86.845C600.299 84.4048 603.67 80.9693 606.078 76.5383C608.519 72.0753 609.739 66.8257 609.739 60.7893L613.399 61.5599H583.731V50.9643H621.298V61.5599C621.298 69.6833 619.564 76.747 616.096 82.7513C612.661 88.7555 607.909 93.4111 601.84 96.7182C595.804 99.9933 588.869 101.631 581.034 101.631C572.301 101.631 564.627 99.5759 558.013 95.466C551.431 91.3562 546.293 85.5125 542.601 77.935C538.941 70.3575 537.11 61.3673 537.11 50.9643C537.11 43.162 538.154 36.1464 540.241 29.9175C542.36 23.6564 545.346 18.3265 549.199 13.9277C553.052 9.52886 557.611 6.15751 562.877 3.81362C568.143 1.46973 573.938 0.297791 580.264 0.297791C585.465 0.297791 590.313 1.08444 594.809 2.65773C599.336 4.19892 603.365 6.39832 606.897 9.25593C610.461 12.0814 613.431 15.4688 615.807 19.4181C618.183 23.3353 619.821 27.686 620.72 32.4701H608.39Z"
            fill="#F0F0F0"
          />
          <path
            d="M92.0134 163.284C91.2942 156.555 88.2632 151.315 82.9204 147.564C77.629 143.814 70.745 141.939 62.2685 141.939C56.3093 141.939 51.1977 142.838 46.9337 144.636C42.6698 146.434 39.4076 148.874 37.1472 151.957C34.8868 155.039 33.7309 158.558 33.6795 162.514C33.6795 165.802 34.4244 168.653 35.9142 171.067C37.4554 173.482 39.536 175.537 42.156 177.232C44.7761 178.876 47.6786 180.263 50.8637 181.393C54.0489 182.524 57.2597 183.474 60.4961 184.245L75.2915 187.943C81.2508 189.331 86.9789 191.206 92.4758 193.569C98.024 195.932 102.981 198.912 107.348 202.508C111.766 206.104 115.26 210.445 117.828 215.531C120.397 220.617 121.681 226.576 121.681 233.408C121.681 242.656 119.318 250.798 114.592 257.836C109.865 264.823 103.033 270.294 94.094 274.25C85.2065 278.154 74.4439 280.106 61.8062 280.106C49.528 280.106 38.8682 278.206 29.8265 274.404C20.8363 270.602 13.7982 265.054 8.71229 257.759C3.67775 250.464 0.954989 241.577 0.544006 231.097H28.6707C29.0816 236.594 30.7769 241.166 33.7566 244.813C36.7362 248.461 40.6148 251.183 45.3925 252.982C50.2216 254.78 55.6157 255.679 61.575 255.679C67.7911 255.679 73.2366 254.754 77.9116 252.904C82.6379 251.004 86.3367 248.384 89.0081 245.044C91.6795 241.654 93.0409 237.698 93.0922 233.177C93.0409 229.067 91.8336 225.677 89.4704 223.005C87.1073 220.283 83.7937 218.022 79.5298 216.224C75.3172 214.375 70.3854 212.731 64.7344 211.292L46.7796 206.669C33.7823 203.33 23.5077 198.269 15.9559 191.488C8.45543 184.656 4.70521 175.588 4.70521 164.286C4.70521 154.988 7.22248 146.845 12.257 139.858C17.3429 132.872 24.2526 127.452 32.986 123.599C41.7194 119.695 51.6086 117.742 62.6538 117.742C73.8531 117.742 83.6653 119.695 92.0905 123.599C100.567 127.452 107.22 132.82 112.049 139.704C116.878 146.537 119.369 154.397 119.524 163.284H92.0134Z"
            fill="#F0F0F0"
          />
          <path
            d="M288.187 198.809C288.187 215.813 285.002 230.377 278.632 242.501C272.313 254.574 263.682 263.821 252.74 270.243C241.849 276.664 229.494 279.875 215.674 279.875C201.855 279.875 189.474 276.664 178.532 270.243C167.641 263.77 159.01 254.497 152.64 242.424C146.321 230.3 143.161 215.762 143.161 198.809C143.161 181.804 146.321 167.266 152.64 155.193C159.01 143.069 167.641 133.796 178.532 127.375C189.474 120.953 201.855 117.742 215.674 117.742C229.494 117.742 241.849 120.953 252.74 127.375C263.682 133.796 272.313 143.069 278.632 155.193C285.002 167.266 288.187 181.804 288.187 198.809ZM259.444 198.809C259.444 186.839 257.569 176.744 253.819 168.525C250.12 160.253 244.983 154.012 238.407 149.799C231.831 145.535 224.254 143.403 215.674 143.403C207.095 143.403 199.518 145.535 192.942 149.799C186.366 154.012 181.203 160.253 177.453 168.525C173.754 176.744 171.905 186.839 171.905 198.809C171.905 210.779 173.754 220.899 177.453 229.17C181.203 237.39 186.366 243.632 192.942 247.896C199.518 252.108 207.095 254.214 215.674 254.214C224.254 254.214 231.831 252.108 238.407 247.896C244.983 243.632 250.12 237.39 253.819 229.17C257.569 220.899 259.444 210.779 259.444 198.809Z"
            fill="#F0F0F0"
          />
          <path
            d="M456.234 198.809C456.234 215.813 453.049 230.377 446.679 242.501C440.36 254.574 431.729 263.821 420.787 270.243C409.896 276.664 397.541 279.875 383.721 279.875C369.902 279.875 357.521 276.664 346.579 270.243C335.688 263.77 327.057 254.497 320.687 242.424C314.368 230.3 311.209 215.762 311.209 198.809C311.209 181.804 314.368 167.266 320.687 155.193C327.057 143.069 335.688 133.796 346.579 127.375C357.521 120.953 369.902 117.742 383.721 117.742C397.541 117.742 409.896 120.953 420.787 127.375C431.729 133.796 440.36 143.069 446.679 155.193C453.049 167.266 456.234 181.804 456.234 198.809ZM427.491 198.809C427.491 186.839 425.616 176.744 421.866 168.525C418.167 160.253 413.03 154.012 406.454 149.799C399.878 145.535 392.301 143.403 383.721 143.403C375.142 143.403 367.565 145.535 360.989 149.799C354.413 154.012 349.25 160.253 345.5 168.525C341.801 176.744 339.952 186.839 339.952 198.809C339.952 210.779 341.801 220.899 345.5 229.17C349.25 237.39 354.413 243.632 360.989 247.896C367.565 252.108 375.142 254.214 383.721 254.214C392.301 254.214 399.878 252.108 406.454 247.896C413.03 243.632 418.167 237.39 421.866 229.17C425.616 220.899 427.491 210.779 427.491 198.809Z"
            fill="#F0F0F0"
          />
          <path
            d="M612.954 119.9V277.718H587.524L513.162 170.22H511.852V277.718H483.263V119.9H508.846L583.132 227.475H584.519V119.9H612.954Z"
            fill="#F0F0F0"
          />
        </svg>
      </div>
      <Box variant="col-start" className="gap-3 w-full items-center z-10">
        <div className="flex gap-2 p-[1px] mb-3 items-center sm:px-[66px]">
          <IconFavicon />
          <LogoSvg color="hsla(229, 41%, 10%, 0.6)" />
        </div>
        <div className="text-primary text-2xl md:text-4xl font-medium">Under Construction</div>
        <p className="text-secondary text-sm md:text-base font-normal text-center whitespace-pre-line md:whitespace-nowrap">
          We are launching the feature very soon. Please comeback later.
        </p>
        <Box className="flex-col lg:flex-row w-full lg:w-fit gap-3">
          <Button
            variant="outline"
            className="w-full lg:w-fit"
            onClick={() => navigate(-1)}
          >
            <RiArrowLeftLine size={21} />
            Go back
          </Button>
          <Link to={`${PATH_DASHBOARD.audience_finder.social}`} className="w-full lg:w-fit">
            <Button variant="main" className="w-full lg:w-fit">Go Home</Button>
          </Link>
        </Box>
      </Box>
    </Box>
  );
};

export default ComingSoon;
