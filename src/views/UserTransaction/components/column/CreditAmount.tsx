import { CREDIT_TYPE_ENUM, ITransactionResponse } from "types/Transaction";
import { fNumberToString } from "utils/number";
import { cn } from "utils/utils";

interface Props
  extends Partial<Pick<ITransactionResponse, "credit_change_reason" | "credit_amount">> {
  isShowDescription?: boolean;
  isSearchModal?: boolean;
}
const CreditAmount = ({ credit_amount, credit_change_reason }: Props) => {
  const isAdd =
    credit_change_reason == CREDIT_TYPE_ENUM.ADD_CREDIT ||
    credit_change_reason == CREDIT_TYPE_ENUM.REFERRAL ||
    credit_change_reason == CREDIT_TYPE_ENUM.RESTORE ||
    credit_change_reason == CREDIT_TYPE_ENUM.M_SUBSCRIPTION ||
    credit_change_reason == CREDIT_TYPE_ENUM.SUBSCRIPTION;
  return (
    <div
      className={cn(
        "capitalize text-right font-medium",
        isAdd ? "text-success-default" : "text-error-default"
      )}
    >
      {(isAdd ? "+" : "") + credit_amount ? fNumberToString(credit_amount) : 0}
    </div>
  );
};

export default CreditAmount;
