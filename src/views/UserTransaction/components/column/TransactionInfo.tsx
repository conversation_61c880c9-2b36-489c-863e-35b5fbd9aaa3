import { useState } from "react";
// import { socialAPI } from "apis/socialData";
import { CREDIT_TYPE_ENUM, ITransactionResponse } from "types/Transaction";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import useAvatar from "hooks/useAvatar";
import { formatPayloadAvatar } from "utils/utils";

// import { pageAPI } from "apis/pagePost";
import InfoByCreditType from "../RenderInfoByType";

const TransactionInfo = (props: ITransactionResponse) => {
  const { credit_change_reason } = props;

  const [dataResponse] = useState<any>(undefined);
  const [isLoading] = useState(false);

  const payloadAvatar = formatPayloadAvatar(
    dataResponse?.type || 1,
    dataResponse?.fb_uid || "",
    dataResponse?.actor_id || ""
  );
  const { avatar } =
    credit_change_reason === CREDIT_TYPE_ENUM.FOLLOW_PAGE
      ? useAvatar({ type: "page", uid: dataResponse?.page_id || "" })
      : useAvatar(payloadAvatar);

  // useEffect(() => {
  //   fetchData();
  // }, [credit_change_reason, ref_id]);

  // const fetchData = async () => {
  //   if (credit_change_reason === CREDIT_TYPE_ENUM.BUY_SC_AUDIENCE) {
  //     setIsLoading(true);
  //     try {
  //       const response = await socialAPI.get({ endpoint: `audiences/${ref_id}/` });
  //       if (response && response.data) {
  //         setDataResponse(response.data);
  //       }
  //     } catch (error) {
  //       console.error("Error fetching audience data:", error);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   }
  //   if (credit_change_reason === CREDIT_TYPE_ENUM.FOLLOW_PAGE) {
  //     setIsLoading(true);
  //     try {
  //       const response = await pageAPI.getPageById({ id: ref_id });
  //       if (response && response.data) {
  //         setDataResponse(response.data);
  //       }
  //     } catch (error) {
  //       console.error("Error fetching audience data:", error);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   }
  // };

  return (
    <div className="text-left max-w-[200px] md:max-w-[250px] lg:max-w-[400px] w-full">
      {isLoading ? (
        <LoadingButtonIcon />
      ) : (
        InfoByCreditType({ props, dataResponse, avatar: avatar.url })
      )}
    </div>
  );
};

export default TransactionInfo;
