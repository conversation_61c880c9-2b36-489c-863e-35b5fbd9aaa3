import { useState } from "react";
import { RiDeleteBin6Line } from "@remixicon/react";

import { Box } from "components/Box";
import useFilters from "hooks/useFilters";
import { Button } from "components/ui/button";
import DateCalender, { CalenderType } from "./DateCalender";

import { formatDateISOString } from "utils/datePicker";
import { cn } from "utils/utils";
import { CREDIT_LABEL } from "constants/PlanPayment/label";
import LABEL from "constants/label";

const FilterWrapper = () => {
  const { params, setSearchParams } = useFilters();
  const [date, setDate] = useState<{
    from: Date | string | undefined;
    to: Date | string | undefined;
  }>({
    from: params["created_at__gte"] || "",
    to: params["created_at__lte"] || "",
  });

  const handleSetDate = (value?: Date, type?: CalenderType) => {
    type && type == "from"
      ? setDate((prev: any) => ({ ...prev, from: value }))
      : setDate((prev: any) => ({ ...prev, to: value }));
  };

  const handleFilter = () => {
    setSearchParams({
      ...params,
      created_at__gte: formatDateISOString(date?.from as any),
      created_at__lte: formatDateISOString(date?.to as any),
    });
  };

  const handleClearAll = () => {
    setSearchParams({});
    setDate({ from: undefined, to: undefined });
  };
  return (
    <Box className="w-full flex-col lg:flex-row items-start lg:items-center gap-3 lg:gap-6">
      <div className='xl:flex-1'>
        <div className="text-xl font-medium ">{CREDIT_LABEL.history_title}</div>
      </div>
      <Box className="flex-1 text-md gap-4 text-tertiary w-full flex-col lg:flex-row">
        <Box className='w-full gap-4'>
          <DateCalender label="From" date={date?.from} setDate={handleSetDate} type="from" />
          <span className='hidden xl:block'>
            -
          </span>
          <DateCalender label="To" date={date?.to} setDate={handleSetDate} type="to" />
        </Box>
        <Button
          variant="main"
          className="rounded-xl py-1 px-3 font-medium text-sm h-[44px] max-lg:w-full"
          disabled={!(date?.from && date.to) || false}
          onClick={handleFilter}
        >
          {LABEL.apply}
        </Button>
        <button
          className={cn(
            "flex items-center gap-1 text-sm text-red-400 hover:text-red-500",
            Object.keys(params).length == 0 && "hidden"
          )}
          onClick={handleClearAll}
        >
          <span>Clear</span>
          <RiDeleteBin6Line size={16} />
        </button>
      </Box>
    </Box>
  );
};

export default FilterWrapper;
