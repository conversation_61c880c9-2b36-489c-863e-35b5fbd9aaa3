import { RiCopper<PERSON>oinLine, RiLink } from "@remixicon/react";
import FileCSV from "assets/icons/FileCSV";
import AvatarByName from "components/AvatarByName";
import { Box } from "components/Box";
import { Link } from "react-router-dom";
import { PATH_DASHBOARD } from "types/path";
import { CREDIT_TYPE_ENUM, ITransactionResponse } from "types/Transaction";
import { YOUR_AUDIENCES } from '../../../types/Router';
import { PLAN_TYPE } from '../../../constants/permission/permissionPlan';
import { subscriptionStore } from '../../../store/redux/subscription/slice';
import { useAppSelector } from '../../../store';

interface Props {
  props: ITransactionResponse;
  dataResponse?: any;
  avatar?: string;
}

const InfoByCreditType = ({ props, dataResponse, avatar }: Props) => {
  const { credit_change_reason, ref_id, info } = props;
  const { last_sub } = useAppSelector(subscriptionStore);
  const isDisableTrial =
    (last_sub?.plan_code !== PLAN_TYPE.TRIAL ||
      ( last_sub.plan_code == PLAN_TYPE.TRIAL && last_sub?.status !== 'ACTIVE' ) );
  switch (credit_change_reason) {
    case CREDIT_TYPE_ENUM.RESTORE:
      return <Box className="gap-2">
        <div>
          <RiCopperCoinLine size={32} />
        </div>
        <Box variant="col-start" className="flex-1 gap-1">
          <strong>Restore Request Audience</strong>
          <Link
            to={info}
            className="text-xs hover:text-infor-text block line-clamp-1 lowercase max-w-[400px] text-ellipsis"
            target="_blank"
            children={info}
          />
        </Box>
      </Box>
    case CREDIT_TYPE_ENUM.REFERRAL:
      return (
        <Box className="gap-2">
          <div>
            <RiCopperCoinLine size={32} />
          </div>
          <div className="flex-1">
            <strong>{info}</strong>
            <div className="text-xs">
              <span>#{ref_id}</span>
            </div>
          </div>
        </Box>
      );
    case CREDIT_TYPE_ENUM.SUBSCRIPTION:
    case CREDIT_TYPE_ENUM.M_SUBSCRIPTION:
    case CREDIT_TYPE_ENUM.ADD_CREDIT:
      return (
        <Box className="gap-2">
          <div>
            <RiCopperCoinLine size={32} />
          </div>
          <div className="flex-1">
            <strong>{info}</strong>
            <div className="text-xs hover:text-infor-text">
              <Link to={`/${PATH_DASHBOARD.user.billing}`}>#{ref_id}</Link>
            </div>
          </div>
        </Box>
      );

    case CREDIT_TYPE_ENUM.ADD_MORE_RQA:
      return (
        <Box className="gap-2">
          <div>
            <RiLink size={32} />
          </div>
          <Box variant="col-start" className="flex-1 gap-1">
            <strong>Add more Request</strong>
            <Link
              to={info}
              className="text-xs hover:text-infor-text block line-clamp-1 lowercase max-w-[400px] text-ellipsis"
              target="_blank"
              children={info}
            />
          </Box>
        </Box>
      );

    case CREDIT_TYPE_ENUM.FOLLOW_PAGE:
      return (
        <Box className="gap-2">
          <AvatarByName
            urlImage={avatar}
            name={dataResponse?.name || ""}
            type={dataResponse?.type || 1}
          />
          <Box variant="col-start" className="flex-1 gap-1">
            <Link
              className="font-medium text-primary line-clamp-1"
              to={`/${PATH_DASHBOARD.live_analysis.your_following}`}
              children={dataResponse?.name}
            />
            <div className="text-xs">{info}</div>
          </Box>
        </Box>
      );

    case CREDIT_TYPE_ENUM.BUY_PN_PROFILE:
      return (
        <Box className="gap-2">
          <AvatarByName
            urlImage={""}
            name={info || ""}
            type={dataResponse?.type || 1}
            position="first"
          />
          <Box variant="col-start" className="flex-1 gap-1">
            <Link
              className="font-medium text-primary line-clamp-1"
              to={`/${PATH_DASHBOARD.your_data.your_audience.persona}/${YOUR_AUDIENCES.ARCHIVE}`}
              children={info}
            />
          </Box>
        </Box>
      );

    case CREDIT_TYPE_ENUM.BUY_SC_DATASET:
    case CREDIT_TYPE_ENUM.BUY_SP_DATASET:
      return (
        <Box className="gap-2">
          <AvatarByName name={info} urlImage={""} />
          <Box variant="col-start" className="flex-1 gap-1">
            <Link
              className="font-medium text-primary line-clamp-1"
              to={!isDisableTrial ?
                '/plan/upgrade' :
                `/${PATH_DASHBOARD.your_data.your_audience.socialDataset}/${ref_id}/`}
              children={info}
            />
            {/*<div className="text-xs">*/}
            {/*  Audience size: {dataResponse?.size ? fNumberToString(dataResponse?.size) : 0}*/}
            {/*</div>*/}
          </Box>
        </Box>
      );

    case CREDIT_TYPE_ENUM.BUY_SC_AUDIENCE:
    case CREDIT_TYPE_ENUM.BUY_SP_AUDIENCE:
      return (
        <Box className="gap-2">
          <AvatarByName
            urlImage={avatar}
            name={dataResponse?.name || ""}
            type={dataResponse?.type || 1}
          />
          <Box variant="col-start" className="flex-1 gap-1">
            <Link
              className="font-medium text-primary line-clamp-1"
              to={!isDisableTrial ?
                '/plan/upgrade' :
                `/${PATH_DASHBOARD.your_data.your_audience.social}/${ref_id}/audience`}
              children={info}
            />
            {/*<div className="text-xs">*/}
            {/*  Audience size: {dataResponse?.size ? fNumberToString(dataResponse?.size) : 0}*/}
            {/*</div>*/}
          </Box>
        </Box>
      );
    case CREDIT_TYPE_ENUM.BUY_PN_AUDIENCE:
    case CREDIT_TYPE_ENUM.BUY_PN_DATASET:
      return (
        <Box className="gap-2">
          <AvatarByName
            urlImage={""}
            name={info || ""}
            type={dataResponse?.type || 1}
            position="first"
          />
          <Link
            className="font-medium text-primary line-clamp-1 hover:text-infor-primary w-full"
            to={`/${PATH_DASHBOARD.your_data.your_audience.persona}/${ref_id}`}
            children={info}
          />
        </Box>
      );
    case CREDIT_TYPE_ENUM.ENRICHMENT:
      return (
        <Box className="gap-2 justify-start">
          <FileCSV />
          <div
            className="text-sm hover:text-infor-text block line-clamp-1  max-w-[400px] text-ellipsis font-medium text-primary"
            children={info}
          />
        </Box>
      );
    default:
      return <div>{info}</div>;
  }
};

export default InfoByCreditType;
