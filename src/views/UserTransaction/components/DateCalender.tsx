import { useState } from "react";
import { RiArrowDownSLine, RiCalendarLine } from "@remixicon/react";
import { Box } from "components/Box";

import { Button } from "components/ui/button";
import { Calendar } from "components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";

import { formatDate } from "utils/utils";

export type CalenderType = "from" | "to";
interface Props {
  date?: Date | string;
  label: string;
  type: CalenderType;
  setDate: (value?: Date, type?: CalenderType) => void;
}

const DateCalender = ({ date, label, setDate, type }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const handleSelectDate = (value: any) => {
    setDate(value, type);
    setOpen(false);
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-full xl:w-[220px] justify-start text-left font-normal p-2 border-custom-primary text-tertiary gap-1 rounded-xl"
          >
            <Box className="flex-1 w-full gap-1 justify-start">
              <RiCalendarLine size={16} />
              {date ? formatDate(date) : <span>{label}</span>}
            </Box>
            <RiArrowDownSLine size={16} />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={date as Date}
            onSelect={(value) => handleSelectDate(value)}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </>
  );
};

export default DateCalender;
