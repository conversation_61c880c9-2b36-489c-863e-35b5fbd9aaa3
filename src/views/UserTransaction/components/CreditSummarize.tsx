import { useEffect, useState } from "react";
import { Box } from "components/Box";
import CreditCard from "views/UserPlan/components/CreditCard";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import { CREDIT_LABEL } from "constants/PlanPayment/label";
import { creditAPI } from "apis/credit";
import { fNumberToString } from "utils/number";

interface ICreditSummarize {
  credit_remain: number;
  credit_used: number;
  credit_plan: number;
  credit_bought: number;
  loading: boolean;
}
const INIT_DATA: ICreditSummarize = {
  credit_remain: 0,
  credit_used: 0,
  credit_plan: 0,
  credit_bought: 0,
  loading: false,
};
const CreditSummarize = () => {
  const [summarize, setSummarize] = useState<ICreditSummarize>(INIT_DATA);

  useEffect(() => {
    getSummarizeCredit();
  }, []);

  const getSummarizeCredit = async () => {
    setSummarize((prev) => ({ ...prev, loading: true }));
    const response = await creditAPI.getSummarize();
    if (response && response.data) {
      setSummarize((prev) => ({ ...prev, ...response.data }));
    }
    setSummarize((prev) => ({ ...prev, loading: false }));
  };

  return (
    <Box variant="col-start" className="p-4 xl:p-6 border rounded-2xl ">
      <div className="text-xl font-medium text-primary">{CREDIT_LABEL.summarize_title}</div>
      {summarize.loading && (
        <div className="flex justify-center w-full">
          <LoadingButtonIcon />
        </div>
      )}
      {!summarize.loading && (
        <div className="grid grid-cols-3 xl:grid-cols-4 gap-6 w-full max-xl:gap-x-0">
          <CreditCard
            title={CREDIT_LABEL.your_credit}
            count={(fNumberToString(summarize?.credit_remain) as any) || 0}
            isActive
            className={'max-xl:col-span-3'}
            classNameTitle='max-lg:text-sm max-lg:leading-[44px]'
            classNameCredit='max-lg:text-2xl'
          />
          <CreditCard
            title={CREDIT_LABEL.credit_plan}
            count={summarize.credit_plan}
            className="bg-transparent "
          />
          <CreditCard
            title={CREDIT_LABEL.credit_add}
            count={summarize.credit_bought}
            className="bg-transparent border-l border-r rounded-none"
          />
          <CreditCard
            title={CREDIT_LABEL.credit_used}
            count={(fNumberToString(Math.abs(summarize.credit_used)) as any) || 0}
            className="bg-transparent "
          />
        </div>
      )}
      <div className="text-secondary text-sm">{CREDIT_LABEL.notice}</div>
    </Box>
  );
};

export default CreditSummarize;
