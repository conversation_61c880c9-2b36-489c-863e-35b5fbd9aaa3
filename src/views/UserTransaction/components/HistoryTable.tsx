import { useCallback, useEffect, useState } from "react";

import { Box } from "components/Box";
import { DataTable } from "components/DataTable/TableCustom";
import Pagination from "components/Pagination";

import { creditAPI } from "apis/credit";
import { useCancelToken } from "hooks/useCancelToken";
import useFilters from "hooks/useFilters";

import { TRANSACTION_COLUMN } from "constants/Transaction/column";
import { toPaginationParams } from "utils/pagination";
import FilterWrapper from "./FilterWrapper";

interface IData {
  transaction: any;
  loading: boolean;
  count: number;
}
const HistoryTable = () => {
  const { newCancelToken } = useCancelToken();
  const { params, setSearchParams } = useFilters();

  const [histories, setHistories] = useState<IData>({
    transaction: [],
    count: 0,
    loading: false,
  });

  useEffect(() => {
    getListTransactions();
  }, [params]);

  const getListTransactions = useCallback(async () => {
    setHistories((prev) => ({ ...prev, loading: true }));

    const response = await creditAPI.getTransactions({ params: toPaginationParams(params) });
    if (response && response.data) {
      setHistories((prev) => ({
        ...prev,
        count: response.data.count,
        transaction: response.data.items,
      }));
    }

    setHistories((prev) => ({ ...prev, loading: false }));
  }, [newCancelToken, params]);

  return (
    <Box variant="col-start" className="w-full xl:p-6 xl:border xl:rounded-2xl">
      <FilterWrapper />
      <div className="w-full">
        <DataTable
          columns={TRANSACTION_COLUMN}
          data={histories.transaction || []}
          loading={histories.loading}
          cellClass="text-center first:text-left text-secondary text-sm capitalize"
        />
        <Pagination
          className="mt-6"
          currentPage={Number(params["page"]) || 1}
          pageSize={10}
          totalCount={histories.count}
          onPageChange={(page) => setSearchParams((prev) => ({ ...prev, page: String(page) }))}
        />
      </div>
    </Box>
  );
};

export default HistoryTable;
