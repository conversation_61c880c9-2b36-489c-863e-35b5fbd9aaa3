import { useContext, useEffect } from "react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import PageInfoAndFilter from "components/YourPage/PageInfoAndFilter";
import Container from "views/AdsPostView/components/Container";

import useGetListPost from "hooks/useGetListPost";
import useFilters from "../../hooks/useFilters";

const PageBrandDetail = () => {
  const { paramsNoPage } = useFilters();
  const id = useContext(ShowDetailContext)?.showDetail?.id;
  const { data, lastElementRef } = useGetListPost({ page_id: id, type: "ads", paramsNoPage });

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="mt-6">
      <PageInfoAndFilter isStatus isDateTime />
      {/* using container ads post view */}
      <Container
        key="ads_post"
        data={data.posts}
        total={data.total}
        lastElementRef={lastElementRef}
      />
    </div>
  );
};
export default PageBrandDetail;
