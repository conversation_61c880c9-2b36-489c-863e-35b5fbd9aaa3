import { useContext } from "react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import PageInfoAndFilter from "../../components/YourPage/PageInfoAndFilter";
import Container from "views/LivePostView/components/Container";

import useGetListPost from "hooks/useGetListPost";
import useFilters from '../../hooks/useFilters';

const YourPageDetail = () => {
  const id = useContext(ShowDetailContext)?.showDetail?.id;
  const { paramsNoPage} = useFilters()
  const { data, lastElementRef } = useGetListPost({ page_id: id, type: 'your-pages', paramsNoPage });

  return (
    <>
      <PageInfoAndFilter isLive isDateTime />
      {/* using container of LivePostView  */}
      <Container data={data.posts || []} total={data.total} lastElementRef={lastElementRef} />
    </>
  );
};

export default YourPageDetail;
