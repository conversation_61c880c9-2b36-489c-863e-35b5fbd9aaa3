import { createContext, ReactNode, useEffect, useState } from "react";
import { pageAPI } from "apis/pagePost";
import { PageFollow } from "types/LiveService";
import useFeatures from "hooks/useFeatures";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";

export interface IListPageData {
  data: PageFollow[];
  total: number;
}

type YourPageContextProps = {
  listPageFollow: IListPageData;
  archives: IListPageData;
  dateExpired: string;
  setListPageFollow: React.Dispatch<React.SetStateAction<IListPageData>>;
  setArchives: React.Dispatch<React.SetStateAction<IListPageData>>;
  setDateExpired: React.Dispatch<React.SetStateAction<string>>;
  handleGetListFollow: () => void;
  handleGetListArchives: () => void;
} | null;

const YourPageContext = createContext<YourPageContextProps>(null);

const YourFollowProvider = ({ children }: { children: ReactNode }) => {
  const [dateExpired, setDateExpired] = useState<string>("");
  const [listPageFollow, setListPageFollow] = useState<IListPageData>({
    data: [],
    total: 0,
  });
  const [archives, setArchives] = useState<IListPageData>({
    data: [],
    total: 0,
  });
  //permission check
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.SCD_VIEW_YOUR_PAGE)?.is_enabled;

  useEffect(() => {
    if (isEnable) {
      handleGetListFollow();
      handleGetListArchives();
    }
  }, [isEnable]);

  const handleGetListFollow = async () => {
    const response = await pageAPI.getListPageFollow({});
    setListPageFollow({ data: response?.data?.items, total: response?.data?.count });
  };

  const handleGetListArchives = async () => {
    const response: any = await pageAPI.getListPageArchives({});
    setArchives({ data: response?.data?.items, total: response?.data?.count });
  };

  return (
    <YourPageContext.Provider
      value={{
        listPageFollow,
        dateExpired,
        archives,
        setArchives,
        setDateExpired,
        setListPageFollow,
        handleGetListFollow,
        handleGetListArchives,
      }}
    >
      {children}
    </YourPageContext.Provider>
  );
};
export { YourFollowProvider, YourPageContext };
