import { useState } from "react";

import { Box } from "components/Box";
import { Label } from "components/ui/label";
import { RadioGroup, RadioGroupItem } from "components/ui/radio-group";

import { cn } from "utils/utils";
import { LIVE_COMMENT_LABEL } from "constants/LiveAdsPost/label";

interface Props {
  className?: string;
  setDateExpired: (value: number) => void;
}
const DateExpirationOptions = ({ className, setDateExpired }: Props) => {
  const [valueSelected, setValueSelected] = useState<string>("3");

  const handleChange = (value: string) => {
    const newValue = Number(value);
    setValueSelected(value);
    setDateExpired(newValue);
  };

  return (
    <RadioGroup
      value={valueSelected}
      onValueChange={(value) => handleChange(value)}
      defaultValue="3"
      className={cn("pb-4 text-secondary text-md gap-4 ", className)}
    >
      <h4
        className="text-md font-medium text-primary"
        children={LIVE_COMMENT_LABEL.message_pricing}
      />
      <div className="grid grid-cols-2 lg:grid-cols-4">
        <OptionGroup
          value="3"
          label="3 days"
          isChecked={valueSelected === "3"}
          defaultChecked={true}
        />
        <OptionGroup value="7" label="7 days" isChecked={valueSelected === "7"} />
        <OptionGroup value="15" label="15 days" isChecked={valueSelected === "15"} />
        <OptionGroup value="30" label="30 days" isChecked={valueSelected === "30"} />
      </div>
    </RadioGroup>
  );
};

export default DateExpirationOptions;

interface OptionGroupProps {
  value: string;
  label: string;
  isChecked: boolean;
  defaultChecked?: boolean;
}
const OptionGroup = ({ label, value, isChecked, defaultChecked = false }: OptionGroupProps) => {
  return (
    <Box className={cn("gap-2 justify-start ", isChecked ? "text-primary" : "text-secondary")}>
      <div
        className={cn(
          "hover:text-primary hover:bg-brand-disabled hover:rounded-full leading-4 p-1 bg-white"
        )}
      >
        <RadioGroupItem
          value={value}
          id={value}
          checked={isChecked}
          defaultChecked={defaultChecked}
          className="border-2"
        />
      </div>
      <Label className="text-md" htmlFor={value} children={label} />
    </Box>
  );
};
