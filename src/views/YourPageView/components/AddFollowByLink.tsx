import { useContext } from "react";

import AttachmentIcon from "assets/icons/AttachmentIcon";
import { Button } from "components/ui/button";
import { LIVE_COMMENT_LABEL } from "constants/LiveAdsPost/label";
import usePreviewFB from "hooks/usePreviewFB";
import { ModalContext } from "providers/Modal";

import { YourPageContext } from "views/YourPageView/YourPageContext";
import SearchPreviewURL from "./SearchPreviewURL";
import handleCloseModal from "utils/handleCloseModal";
import useFeatures from "hooks/useFeatures";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";

const AddFollowByLink = (props: { path: string; onRefresh?: () => void }) => {
  const contextModal = useContext(ModalContext);
  const contextYourPage = useContext(YourPageContext);
  const { setRequestData } = usePreviewFB({ type: "livePost" });

  const handleOpenModal = async () => {
    contextModal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      title: "Add Facebook’s URL to follow",
      content: (
        <SearchPreviewURL
          {...props}
          dateExpired={contextYourPage?.dateExpired}
          setListPageFollow={contextYourPage?.setListPageFollow}
          getListPage={contextYourPage?.handleGetListFollow}
        />
      ),
      footer: "",
      className: "bg-secondary gap-6 lg:max-w-[1000px]",
      handleCancel: () => {
        setRequestData({ data: undefined, loading: false });
        handleCloseModal(contextModal);
      },
    }));
  };

  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.SCD_VIEW_YOUR_PAGE)?.is_enabled;

  return (
    <Button
      className="rounded-xl w-full md:w-fit"
      size="sm"
      variant="default"
      onClick={handleOpenModal}
      disabled={!isEnable}
    >
      <AttachmentIcon className="mr-2 -rotate-45" color="white" />
      <span children={LIVE_COMMENT_LABEL.follow_page} />
    </Button>
  );
};

export default AddFollowByLink;
