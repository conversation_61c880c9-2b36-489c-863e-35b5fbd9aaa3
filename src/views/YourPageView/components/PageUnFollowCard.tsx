import { Box } from "components/Box";
import { Card } from "components/ui/card";
import AvatarByName from "components/AvatarByName";
import LikeAndFollowMetrics from "components/LikeAndFollow";
import LinkToFacebook from "components/LinkToFacebook";
import ButtonAction from "./ButtonCTA/ButtonAction";

import useAvatar from "hooks/useAvatar";
import { PageFollow } from "types/LiveService";
import ButtonFollow from "./ButtonCTA/ButtonFollow";
import useGetCategoryName from "hooks/useGetCategoryName";

const PageUnFollowCard = (props: PageFollow) => {
  const { name, description, page_id, follow_count, like_count, category } = props;
  const { avatar } = useAvatar({ type: "page", uid: page_id });
  const categoryName = useGetCategoryName(category);

  return (
    <Card className="p-4 rounded-2xl bg-[#FDFDFD] border-none !shadow-medium">
      <Box variant="col-start" className="h-full justify-between gap-4 py-2">
        <Box variant="col-start" className="gap-2 flex-1 text-primary mt-2  relative w-full">
          <Box className="justify-between w-full">
            <AvatarByName
              className="w-20 h-20 rounded-full object-cover text-[40px]"
              urlImage={avatar.url}
              name={name}
              position="first"
            />
            <ButtonFollow id={page_id} />
          </Box>
          <Box variant="col-start" className="gap-2 w-full">
            <LinkToFacebook id={page_id} name={name} />
            {categoryName && <p className="text-secondary font-semibold text-sm">{categoryName}</p>}
            {description && (
              <p
                className="line-clamp-3 text-secondary text-sm font-normal"
                children={description}
              />
            )}
            <LikeAndFollowMetrics
              loading={false}
              followCount={follow_count}
              likeCount={like_count}
              className="gap-6"
            />
          </Box>
        </Box>
        <Box
          className="w-full gap-2"
          children={
            <ButtonAction id={page_id} type="archive" status={props.status} isExp={false} />
          }
        />
      </Box>
    </Card>
  );
};

export default PageUnFollowCard;
