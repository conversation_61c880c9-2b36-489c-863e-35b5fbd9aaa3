import { useContext, useState } from "react";
import { RiUserFollowLine, RiUserUnfollowLine } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import { YourPageContext } from "views/YourPageView/YourPageContext";

import { pageAPI } from "apis/pagePost";
import handleCloseModal from "utils/handleCloseModal";
import { LIVE_COMMENT_LABEL } from "constants/LiveAdsPost/label";
import { cn } from "utils/utils";

type Props = {
  id: string;
  callBack?: (follow: boolean) => void;
  className?: string;
};

const ButtonUnFollow = ({ id, callBack, className }: Props) => {
  const modalContext = useContext(ModalContext);
  const yourPageContext = useContext(YourPageContext);
  const [isHover, setHover] = useState<boolean>(false);
  const [isClicked, setIsClicked] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);

  const handleUnFollow = async () => {
    setLoading(true);
    const result = await pageAPI.unfollow({ page_id: id });

    if (result == 200 || result == 201) {
      toast({
        title: "Unfollow Success",
        status: "success",
        duration: 3000,
      });

      yourPageContext?.handleGetListFollow();
      yourPageContext?.handleGetListArchives();
      setLoading(false);
      handleClose();
      setHover(false);
      setIsClicked(false);
      callBack && callBack(false);
    }
  };

  const handleClose = () => {
    setHover(false);
    setIsClicked(false);
    handleCloseModal(modalContext);
  };

  const handleOpenModal = () => {
    setIsClicked(true);
    setHover(true);

    modalContext?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      className: "max-w-[520px]",
      title: "Unfollow this page?",
      message: `Are you sure you want to unfollow this page? You will no longer receive any data updates.`,
      footer: (
        <Box className="gap-4 justify-start">
          <Button
            variant="secondary"
            className="w-1/2 p-2 bg-secondary text-primary font-semibold text-md rounded-xl "
            children="Cancel"
            onClick={() => handleClose()}
          />
          <Button
            variant="destructive"
            className="w-1/2 bg-error-default text-white font-semibold text-md  rounded-xl "
            children="Unfollow"
            disabled={loading}
            onClick={() => handleUnFollow()}
          />
        </Box>
      ),
      handleCancel: () => {
        setHover(false);
        setIsClicked(false);
      },
    }));
  };
  return (
    <Button
      className={cn(
        "pl-2 pr-2 py-1 rounded-xl bg-custom-secondary font-medium text-sm text-primary hover:bg-custom-secondary hover:text-secondary gap-1 min-w-[105px]",
        className
      )}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => !isClicked && setHover(false)}
      onClick={() => handleOpenModal()}
    >
      {!isHover && <RiUserFollowLine size={16} />}
      {isHover && <RiUserUnfollowLine size={16} />}
      <span
        children={!isHover ? LIVE_COMMENT_LABEL.button_follow : LIVE_COMMENT_LABEL.button_unfollow}
      />
    </Button>
  );
};

export default ButtonUnFollow;
