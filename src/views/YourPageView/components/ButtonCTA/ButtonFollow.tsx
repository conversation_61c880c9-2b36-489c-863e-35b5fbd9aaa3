import { useContext } from "react";
import { ModalContext } from "providers/Modal";
import { YourPageContext } from "views/YourPageView/YourPageContext";

import { Button } from "components/ui/button";
import UpdateContainer from "../UpdateContainer";

const ButtonFollow = (props: { id: string; callBack?: (follow: boolean) => void }) => {
  const context = useContext(ModalContext);
  const getListFollow = useContext(YourPageContext)?.handleGetListFollow;
  const getListArchives = useContext(YourPageContext)?.handleGetListArchives;

  const handleOpenModal = () => {
    context?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      title: "Follow Page",
      message: "",
      className: "max-w-[520px]",
      content: (
        <UpdateContainer
          {...props}
          isFollowModal
          getListArchives={getListFollow}
          getListFollow={getListArchives}
        />
      ),
      footer: "",
    }));
  };

  return (
    <Button
      className="px-3 py-1 rounded-xl bg-custom-secondary font-medium text-sm text-primary hover:bg-tertiary gap-1 w-full sm:w-fit"
      children="Follow"
      onClick={() => handleOpenModal()}
    />
  );
};

export default ButtonFollow;
