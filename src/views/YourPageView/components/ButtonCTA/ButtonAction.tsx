import { useContext } from "react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Button } from "components/ui/button";
import ButtonUpdate from "./ButtonUpdate";
import ButtonDelete from "./ButtonDelete";

import useFilters from "hooks/useFilters";
import { LIVE_COMMENT_LABEL } from "constants/LiveAdsPost/label";
interface Props {
  status: string;
  id: string;
  type: "detail" | "archive" | string;
  isExp: boolean;
}
const ButtonAction = ({ id, type, status, isExp }: Props) => {
  const context = useContext(ShowDetailContext);
  const { setSearchParams } = useFilters();

  const handleClick = () => {
    setSearchParams({});
    context?.setShowDetail({ isShow: true, id: id });
  };
  switch (type) {
    case "detail":
      return (
        <Button
          size="lg"
          className="w-full rounded-xl"
          variant={status ? "main" : "tertiary"}
          children={status ? "Total Lead" : "Processing"}
        />
      );
    case "archive":
      return (
        <>
          <ButtonDelete id={id} />
          <Button
            className="w-full gap-1 text-md font-semibold rounded-xl"
            variant="main"
            size="lg"
            children={LIVE_COMMENT_LABEL.follow_detail}
            onClick={() => handleClick()}
          />
        </>
      );
    default:
      return (
        <>
          <ButtonUpdate id={id} isExp={isExp} className="pl-2 pr-3 w-full xl:w-auto" />
          <Button
            className="w-full gap-1 text-md font-medium rounded-xl"
            variant="main"
            size="lg"
            children={LIVE_COMMENT_LABEL.follow_detail}
            onClick={() => context?.setShowDetail({ isShow: true, id: id })}
          />
        </>
      );
  }
};
export default ButtonAction;
