import { useContext, useState } from 'react';
import { RiDeleteBin6Line } from "@remixicon/react";
import { YourPageContext } from "views/YourPageView/YourPageContext";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Button } from "components/ui/button";

import { pageAPI } from "apis/pagePost";
import handleCloseModal from "utils/handleCloseModal";

const ButtonDelete = ({ id }: { id: string }) => {
  const yourPageContext = useContext(YourPageContext);
  const [loading, setLoading] = useState(false);
  const modalContext = useContext(ModalContext);

  const handleOpenModal = () => {
    modalContext?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      title: "Permanent Deletion",
      message:
        "You are about to permanently delete this page. This action cannot be undone. Are you sure you want to proceed?",
      footer: (
        <Box className="gap-4 justify-start w-full">
          <Button
            variant="secondary"
            className="w-1/2 p-2 bg-secondary text-primary font-semibold text-md rounded-xl"
            children="Cancel"
            onClick={() => handleCloseModal(modalContext)}
          />
          <Button
            className="w-1/2 bg-error-default gap-1 text-md font-semibold text-white hover:bg-error-default hover:text-white hover:opacity-75 rounded-xl"
            variant="secondary"
            children="Delete"
            onClick={handleDelete}
          />
        </Box>
      ),
      className: "max-w-[520px]",
    }));
  };

  const handleDelete = async () => {
    setLoading(true);
    await pageAPI.removePage({ page_id: id });

    //update list data archives
    const filteredData = yourPageContext?.archives?.data.filter((item) => item.page_id !== id);
    yourPageContext?.setArchives((prev) => ({ total: prev.total - 1, data: filteredData || [] }));
    setLoading(false);
    handleCloseModal(modalContext);
  };

  return (
    <Button
      className="border border-error gap-1 hover:bg-error-default text-md text-error-default font-semibold fill-[#F53E3E] hover:text-white hover:fill-white"
      variant="secondary"
      disabled={loading}
      onClick={handleOpenModal}
    >
      <RiDeleteBin6Line size={20} />
      <span children="Delete" />
    </Button>
  );
};

export default ButtonDelete;
