import { useContext } from "react";
import { RiLoopLeftLine } from "@remixicon/react";
import { ModalContext } from "providers/Modal";
import { YourPageContext } from "views/YourPageView/YourPageContext";

import { Button } from "components/ui/button";
import UpdateContainer from "../UpdateContainer";

import { LIVE_COMMENT_LABEL } from "constants/LiveAdsPost/label";
import { cn } from "utils/utils";
import { PageFollow } from "types/LiveService";

interface Props {
  id: string;
  isExp: boolean;
  trigger?: JSX.Element | React.ReactNode;
  className?: string;
  setExpDate?: React.Dispatch<React.SetStateAction<PageFollow | undefined>>;
}
const ButtonUpdate = (props: Props) => {
  const context = useContext(ModalContext);
  const getListFollow = useContext(YourPageContext)?.handleGetListFollow;
  const getListArchives = useContext(YourPageContext)?.handleGetListArchives;

  const handleOnClick = () => {
    context?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      className: "max-w-[520px]",
      title: "Extend Data Update",
      message: "",
      content: (
        <UpdateContainer
          {...props}
          getListFollow={getListFollow}
          getListArchives={getListArchives}
        />
      ),
      footer: "",
    }));
  };

  return (
    <Button
      className={cn(
        "hover:fill-text fill-[#20232C] h-10 gap-1 border-none rounded-xl bg-secondary text-primary text-sm font-medium  hover:bg-tertiary hover:text-tertiary",
        props.className
      )}
      variant="secondary"
      size="lg"
      disabled={!props.isExp}
      onClick={() => handleOnClick()}
    >
      {props.trigger ? (
        props.trigger
      ) : (
        <>
          <RiLoopLeftLine size={20} />
          <span children={LIVE_COMMENT_LABEL.update} />
        </>
      )}
    </Button>
  );
};

export default ButtonUpdate;
