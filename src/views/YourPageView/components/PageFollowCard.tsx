import { Box } from "components/Box";
import { But<PERSON> } from "components/ui/button";
import { Card } from "components/ui/card";
import LikeAndFollowMetrics from "components/LikeAndFollow";
import AvatarByName from "components/AvatarByName";
import LinkToFacebook from "components/LinkToFacebook";
import ButtonAction from "./ButtonCTA/ButtonAction";
import ButtonUnFollow from "./ButtonCTA/ButtonUnFollow";
import CountdownTimeCol from "components/column/CountDownTimeCol";

import useAvatar from "hooks/useAvatar";
import { PageFollow } from "types/LiveService";

import { cn, formatDate } from "utils/utils";
import checkDateExp from "utils/LiveAdsPost/checkDateExp";
import useGetCategoryName from "hooks/useGetCategoryName";

const PageFollowCard = (props: PageFollow) => {
  const {
    name,
    description,
    page_id,
    follow_count,
    like_count,
    expired_date_follow,
    last_update_follow,
    category,
  } = props;

  const { avatar } = useAvatar({ type: "page", uid: page_id });
  const categoryName = useGetCategoryName(category);

  const isExp = checkDateExp(expired_date_follow);
  return (
    <Card className="p-4 rounded-2xl bg-[#FDFDFD] border-none !shadow-medium">
      <Box variant="col-start" className="h-full justify-between pb-2">
        <Box variant="col-start" className="flex-1 text-primary relative w-full gap-0">
          <Box className="justify-between w-full">
            <AvatarByName
              className="w-12 h-12 xl:w-20 xl:h-20 rounded-full object-cover text-[40px] bg-black"
              urlImage={avatar.url}
              name={name}
              position="first"
            />
            <ButtonUnFollow id={page_id} />
          </Box>
          <Box variant="col-start" className="gap-2 w-full mt-6">
            <Box variant="col-start" className="gap-1">
              <LinkToFacebook id={page_id} name={name} />
              {categoryName && (
                <p className="text-secondary font-semibold text-sm">{categoryName}</p>
              )}
            </Box>
            {description && (
              <p
                className="line-clamp-3 text-secondary text-sm font-normal"
                children={description}
              />
            )}
          </Box>
        </Box>
        <Box variant="col-start" className="w-full gap-4">
          <Box variant="col-start" className="gap-2 text-sm font-medium text-secondary w-full">
            <LikeAndFollowMetrics
              loading={false}
              followCount={follow_count}
              likeCount={like_count}
              className="gap-6 text-secondary font-medium"
            />
            <div children={"Last Update: " + formatDate(last_update_follow, true) || "--"} />
            {!isExp && (
              <Box className={cn("gap-1 cursor-pointer", isExp && "text-red-600")}>
                <span children={"Livestream Data Expires In: "} />
                <CountdownTimeCol
                  status={1}
                  eta_time={expired_date_follow}
                  className="text-sm w-fit"
                />
              </Box>
            )}
            {isExp && (
              <Button
                className="w-full rounded-xl bg-error-subtitle text-error-default pointer-events-none justify-start text-sm font-medium p-2"
                children="Your data is expired"
              />
            )}
          </Box>
          <Box
            className="w-full gap-2 flex-col xl:flex-row"
            children={
              <ButtonAction id={page_id} type={props.type} status={props.status} isExp={isExp} />
            }
          />
        </Box>
      </Box>
    </Card>
  );
};

export default PageFollowCard;
