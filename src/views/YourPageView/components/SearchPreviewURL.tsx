import { useAppDispatch } from "store";
import { useContext, useState } from "react";
import { getMe } from "store/redux/auth/slice";
import { useLocation, useNavigate } from "react-router-dom";
import { RiFacebookCircleFill, RiLoaderLine, RiSearchLine } from "@remixicon/react";
import { ModalContext } from "providers/Modal";
import { IListPageData } from "../YourPageContext";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { Card } from "components/ui/card";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import DateExpirationOptions from "./DateExpirationOptions";
import NoticeMessage from "views/RequestAudienceView/components/NoticeMessage";
import RequestPreview from "views/RequestAudienceView/components/RequestPreview";

import { pageAPI } from "apis/pagePost";
import usePreviewFB from "hooks/usePreviewFB";
import useShowEnoughCredit from "hooks/useShowEnoughCredit";
import { REQUEST_AUDIENCE_LABEL } from "constants/requestAudience/label";
import { handleConfirmPageFollow } from "utils/Transaction/handleConfirmPageFollow";
import handleCloseModal from "utils/handleCloseModal";
import { showLimitPageExceededToast } from "utils/Transaction/showLimitPageExceededToast";

interface Props {
  path: string;
  dateExpired: string | undefined;
  getListPage?: () => void;
  setListPageFollow?: React.Dispatch<React.SetStateAction<IListPageData>> | undefined;
}
const SearchPreviewURL = ({ path, getListPage, setListPageFollow }: Props) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const contextModal = useContext(ModalContext);

  const [data, setData] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const [dateExpired, setDateExpired] = useState<number>(3);

  const { requestData, setRequestURL, handleGetPreview } = usePreviewFB({ type: "livePost" });
  const { handleShowEnoughCredit } = useShowEnoughCredit();

  const handleFollow = async () => {
    setLoading(true);
    const createPage: any = await pageAPI.createPageFromPreview({
      payload: { ...data.data },
    });

    const response: any = await pageAPI.followPage({
      page_id: createPage?.data?.page_id,
      days: dateExpired,
    });

    if (response && response.data) {
      await dispatch(getMe());
      if (setListPageFollow) {
        setListPageFollow((prev) => ({ ...prev, data: [response.data, ...prev.data] }));
      }
      getListPage && getListPage();
      handleCloseModal(contextModal);
      pathname != path && navigate(path);
    }
    if (response.error?.code == 6005 || response.error?.code == 5004) {
      handleShowEnoughCredit();
    }
    if (response.error?.code == 2001) {
      showLimitPageExceededToast();
      handleCloseModal(contextModal);
    }
    setLoading(false);
  };

  const handleSearch = async () => {
    const result = await handleGetPreview();
    setData(result);
    return result;
  };

  return (
    <Box variant="col-start" className="justify-start gap-4">
      <Card className="my-6 shadow-sm border border-secondary w-full p-4 m-0">
        <Box className="gap-0 p-0 bg-white rounded-xl w-full h-[60px] shadow-sm mb-2">
          <Box className="px-3 py-[10px] text-secondary font-semibold gap-2 text-sm bg-secondary rounded-tl-xl rounded-bl-xl border-r border-secondary h-full">
            <RiFacebookCircleFill size={20} />
            <span children="Facebook" />
          </Box>
          <Box className="px-3 flex-1 w-full h-full">
            <input
              className="w-full font-medium bg-transparent focus:outline-none text-md text-secondary placeholder:text-tertiary"
              placeholder={REQUEST_AUDIENCE_LABEL.placeholder}
              onChange={(e) => setRequestURL(e.target.value)}
              onKeyUp={(e) => e.key === "Enter" && handleSearch()}
              disabled={requestData.loading ? true : false}
            />
            {requestData.loading ? (
              <RiLoaderLine className="animate-spin" />
            ) : (
              <RiSearchLine
                size={24}
                className="fill-icon-primary cursor-pointer"
                onClick={() => handleSearch()}
              />
            )}
          </Box>
        </Box>
        <NoticeMessage message="Enter Facebook's URL of Fanpage" />
      </Card>
      {requestData.data && (
        <>
          <RequestPreview data={requestData.data} />
          <DateExpirationOptions
            setDateExpired={setDateExpired}
            className="my-6 shadow-sm border border-secondary w-full p-4 m-0 bg-white rounded-xl"
          />
          <Button
            variant="main"
            disabled={loading}
            children={loading ? <LoadingButtonIcon /> : "Follow"}
            className=" w-full cursor-pointer"
            onClick={() => {
              handleConfirmPageFollow({
                context: contextModal,
                dateExpired: dateExpired,
                callBack: handleFollow
              });
            }}
          />
        </>
      )}
    </Box>
  );
};

export default SearchPreviewURL;
