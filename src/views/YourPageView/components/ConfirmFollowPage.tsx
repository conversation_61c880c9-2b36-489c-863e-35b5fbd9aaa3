import getCreditCost from "hooks/getCreditCost";
import { CREDIT_TYPE_ENUM } from "types/Transaction";
import { Box } from 'components/Box';
import { Button } from 'components/ui/button';
import handleCloseModal from 'utils/handleCloseModal';
import LABEL from 'constants/label';
import { RiLoader2Line } from '@remixicon/react';
import { useContext, useState } from 'react';
import { ModalContext } from 'providers/Modal';

const ConfirmFollowPage = ({ dateExpired,callBack }: { dateExpired: number,  callBack: () => Promise<void>;
}) => {
  const modal = useContext(ModalContext);
  const [loading, setLoading] = useState(false);
  const { credit } = getCreditCost({
    creditType: CREDIT_TYPE_ENUM.FOLLOW_PAGE,
    params: dateExpired,
  });
  const handleSubmit = async ()=>{
    setLoading(true);
    await callBack().finally(()=>{
      setLoading(false);
    });
  }
  return (
    <>
      <div className="text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="80"
          height="80"
          viewBox="0 0 80 80"
          fill="none"
          className="w-full"
        >
          <path
            d="M23.3333 71.6667C15.0491 71.6667 8.33334 64.951 8.33334 56.6667C8.33334 48.3824 15.0491 41.6667 23.3333 41.6667C31.6176 41.6667 38.3333 48.3824 38.3333 56.6667C38.3333 64.951 31.6176 71.6667 23.3333 71.6667ZM56.6667 38.3334C48.3823 38.3334 41.6667 31.6176 41.6667 23.3334C41.6667 15.0491 48.3823 8.33337 56.6667 8.33337C64.951 8.33337 71.6667 15.0491 71.6667 23.3334C71.6667 31.6176 64.951 38.3334 56.6667 38.3334ZM23.3333 65C27.9357 65 31.6667 61.269 31.6667 56.6667C31.6667 52.0644 27.9357 48.3334 23.3333 48.3334C18.731 48.3334 15 52.0644 15 56.6667C15 61.269 18.731 65 23.3333 65ZM56.6667 31.6667C61.269 31.6667 65 27.9357 65 23.3334C65 18.731 61.269 15 56.6667 15C52.0643 15 48.3333 18.731 48.3333 23.3334C48.3333 27.9357 52.0643 31.6667 56.6667 31.6667ZM10 26.6667C10 17.462 17.4619 10 26.6667 10H36.6667V16.6667H26.6667C21.1438 16.6667 16.6667 21.1439 16.6667 26.6667V36.6667H10V26.6667ZM70 43.3334H63.3333V53.3334C63.3333 58.8564 58.8563 63.3334 53.3333 63.3334H43.3333V70H53.3333C62.538 70 70 62.538 70 53.3334V43.3334Z"
            fill="#8F5CFF"
          />
        </svg>
        <div className="w-full text-xl font-medium">Confirm Data Update Cost</div>
        <div className="mt-2 text-sm text-secondary">
          You have selected a {dateExpired}-day interval for data updates. This will cost you
          <strong className="text-error-default mx-1">{credit.cost} credits</strong> from your account
          balance. Do you want to proceed?
        </div>
      </div>
      <Box className="w-full gap-4 mt-6">
        <Button
          variant="secondary"
          className="w-full rounded-xl"
          onClick={() => handleCloseModal(modal)}
        >
          {LABEL.cancel}
        </Button>
        <Button variant="default" disabled={loading} className="w-full rounded-xl" onClick={handleSubmit}>
          {loading ? <RiLoader2Line className="animate-spin" size={14} /> : LABEL.confirm}
        </Button>
      </Box>
    </>
  );
};

export default ConfirmFollowPage;
