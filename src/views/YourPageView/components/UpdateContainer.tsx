import { useContext, useState } from "react";
import { useAppDispatch } from "store";
import { getMe } from "store/redux/auth/slice";
import { ModalContext } from "providers/Modal";

import { Button } from "components/ui/button";
import { Box } from "components/Box";
import { toast } from "components/ui/use-toast";
import DateExpirationOptions from "./DateExpirationOptions";

import { pageAPI } from "apis/pagePost";
import { PageFollow } from "types/LiveService";
import useShowEnoughCredit from "hooks/useShowEnoughCredit";
import handleCloseModal from "utils/handleCloseModal";
import { handleConfirmPageFollow } from "utils/Transaction/handleConfirmPageFollow";
import { YourPageContext } from "../YourPageContext";
import handleCheckConcurrent from "utils/Transaction/handleCheckConcurrent";
import { showLimitPageExceededToast } from "utils/Transaction/showLimitPageExceededToast";
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';
interface Props {
  id: string;
  //tracking new page or update tracking page
  isFollowModal?: boolean;
  getListArchives: (() => void) | undefined;
  getListFollow: (() => void) | undefined;
  setExpDate?: React.Dispatch<React.SetStateAction<PageFollow | undefined>>;
  callBack?: (follow: boolean) => void;
}
const UpdateContainer = ({
  id,
  isFollowModal = false,
  setExpDate,
  getListFollow,
  getListArchives,
  callBack
}: Props) => {
  const dispatch = useAppDispatch();
  const context = useContext(ModalContext);
  const pageContext = useContext(YourPageContext);

  const [dateExpired, setDateExpired] = useState<number>(3);
  const [loading, setLoading] = useState<boolean>(false);
  const { handleShowEnoughCredit } = useShowEnoughCredit();

  const handleDateExpiredChange = (newValue: number) => {
    setDateExpired(newValue);
  };

  const handleUpdateFollowPage = async () => {
    setLoading(true);
    const response: any = await pageAPI.updateFollow({
      page_id: id,
      days: dateExpired,
    });
    if (response && response.data) {
      //update data
      await dispatch(getMe());
      getListFollow && getListFollow();
      getListArchives && getListArchives();
      handleCloseModal(context);
      callBack && callBack(true);
      //update date expired ở detail page
      if (setExpDate) {
        setExpDate((prev: any) => ({
          ...prev,
          expired_date_follow: response.data?.expired_date_follow,
        }));

      }

      toast({
        title: response.data?.message,
        status: "success",
        duration: 3000,
      });
    }
    if (response.error?.code == 6005 || response.error?.code == 5004) {
      handleShowEnoughCredit();
    }
    if (response.error?.code == 2001) {
      handleCloseModal(context);
      showLimitPageExceededToast();
    }
    setLoading(false);
  };

  const isExceedingFollowLimit = handleCheckConcurrent(pageContext?.listPageFollow.total);
  isExceedingFollowLimit && showLimitPageExceededToast();
  return (
    <div>
      <DateExpirationOptions setDateExpired={handleDateExpiredChange} />
      <Box className="gap-4 justify-start">
        <Button
          variant="secondary"
          className="w-1/2 p-2 rounded-sm bg-secondary text-primary font-semibold text-md"
          children="Cancel"
          onClick={() => handleCloseModal(context)}
        />
        <Button
          variant="main"
          className="w-1/2 font-semibold text-md rounded-xl"
          disabled={loading}
          onClick={() =>
            handleConfirmPageFollow({
              context: context,
              dateExpired: dateExpired,
              callBack: handleUpdateFollowPage,
            })
          }
        >
          {loading ? <LoadingButtonIcon /> : isFollowModal ? "Follow" : "Update"}
        </Button>
      </Box>
    </div>
  );
};

export default UpdateContainer;
