import { Box } from 'components/Box';
import NoPermissionSvgLivePage from '../../../assets/icons/NoPermissionSvgLivePage';
import { LIVE_COMMENT_LABEL } from '../../../constants/LiveAdsPost/label';

const NoLivestreamMatch = ({ isDisableSVG = false }: { isDisableSVG?: boolean }) => {

  return (
    <Box variant="col-start" className="flex items-center justify-center gap-0 h-full w-full my-28">
      {!isDisableSVG && <NoPermissionSvgLivePage />}
      <div className="text-center max-w-[560px]">
        <p className="font-bold text-base text-primary-crm mb-2" children={'No Livestream that match your filters...'} />
        <div className="text-secondary text-md mb-5">{LIVE_COMMENT_LABEL.message_no_per}</div>
      </div>
    </Box>
  );
};
export default NoLivestreamMatch;
