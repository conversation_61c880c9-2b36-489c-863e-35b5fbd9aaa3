import PageFollowCard from "views/YourPageView/components/PageFollowCard";
import PageUnFollowCard from "views/YourPageView/components/PageUnFollowCard";

import { PageFollow } from "types/LiveService";

const ListPageWrapper = (props: { data?: PageFollow[]; typeCard: "follow" | "archive" }) => {
  return (
    <div>
      {" "}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {props.data?.map((page) => {
          return props.typeCard === "archive" ? (
            <PageUnFollowCard {...page} key={page.page_id} />
          ) : (
            <PageFollowCard {...page} key={page.page_id} />
          );
        })}
      </div>
    </div>
  );
};
export default ListPageWrapper;
