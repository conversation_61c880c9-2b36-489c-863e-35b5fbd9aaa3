import { useContext, useState } from "react";
import { RiCloseLine } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

import handleCloseModal from "utils/handleCloseModal";
import { fNumberToString } from "utils/number";
import { cn } from "utils/utils";
import { DATAPROCESSING_LABEL } from "constants/yourData/label";
import StepProcessing from "./StepProcessing";

interface Props {
  analyzeCount?: number;
  handleCreateSegment: (segmentName: string) => Promise<void>;
}
const CreateSegmentModal = ({ analyzeCount = 0, handleCreateSegment }: Props) => {
  const modal = useContext(ModalContext);
  const [segmentName, setSegmentName] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);

  return (
    <Box variant="col-start" className="w-full">
      <div className="w-full">
        <Box>
          <div className="text-lg font-semibold text-primary text-center flex-1">
            {DATAPROCESSING_LABEL.your_segment}
          </div>
          <RiCloseLine size={20} onClick={() => handleCloseModal(modal)} />
        </Box>
        <div className="text-secondary text-sm mt-2 text-center">
          We found
          <span className="text-brand-default font-medium text-lg  mx-1 align-baseline">
            {fNumberToString(analyzeCount)}
          </span>
          Persona profile that match your data. Do you want to create a Segment?
        </div>
      </div>
      <Box variant="col-start" className="w-full gap-3">
        <StepProcessing text="Step 4: Create your segment with new data processed." />
        <Box className="gap-3 w-full">
          <input
            placeholder="Enter the Your Segment name"
            onChange={(e) => setSegmentName(e.target.value)}
            className={cn(
              "text-primary text-sm w-full gap-1 flex items-baseline p-3 border  border-[#A7AAB1] rounded-xl h-10  focus:outline-none"
            )}
          />
          <Button
            variant="main"
            onClick={() => {
              handleCreateSegment(segmentName);
              setLoading(true);
            }}
            disabled={loading || ( analyzeCount == 0 || segmentName == '' )}
            children={!loading ? "Create Segment" : <LoadingButtonIcon />}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default CreateSegmentModal;
