import { useContext } from "react";
import { ModalContext } from "providers/Modal";
import { ISegment, ProcessingTabContext } from "../context/ProcessingTabsContext";

import { Box } from "components/Box";
import ListSegment from "./ListSegment";
import SegmentSelectedItem from "./SegmentSelectedItem";

import handleCloseModal from "utils/handleCloseModal";
import { DATAPROCESSING_LABEL } from "constants/yourData/label";
import { useAppDispatch, useAppSelector } from "../../../store";
import { setDatatype } from "../../../store/redux/DataProcessing/slice";

interface Props {
  title: string;
  segmentType: "left" | "right";
}
const SegmentBlock = ({ title, segmentType }: Props) => {
  const modal = useContext(ModalContext);
  const context = useContext(ProcessingTabContext);
  const { datatype } = useAppSelector((state) => state.dataProcessing);
  const dispatch = useAppDispatch();

  const handleOpenAdd = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      className: "max-w-[730px] p-2 md:p-4",
      content: (
        <ListSegment
          allSelected={[
            ...(context?.selectedSegment.left_segments || []),
            ...(context?.selectedSegment.right_segments || []),
          ]}
          listSelected={listSelected || []}
          type={context?.type}
          handleAddSegment={handleAddSegment}
        />
      ),
    }));
  };
  const handleAddSegment = (data: ISegment[]) => {
    if (!datatype) {
      const dataType = data[0].datatype;
      dispatch(setDatatype(dataType));
    }
    context?.setSelectedSegment((prev) => ({
      ...prev,
      [segmentType === "left" ? "left_segments" : "right_segments"]: data,
    }));
    handleCloseModal(modal);
  };

  const listSelected =
    segmentType === "left"
      ? context?.selectedSegment.left_segments
      : context?.selectedSegment.right_segments;

  return (
    <Box variant="col-start" className="col-span-2 w-full gap-2 md:gap-6">
      <div className="text-md text-primary font-medium text-center w-full">{title}</div>
      <SegmentSelectedItem segments={listSelected} segmentType={segmentType} />
      <Box
        className="p-4 w-full justify-center items-center bg-white h-[108px] border border-dashed text-xs text-secondary cursor-pointer"
        onClick={handleOpenAdd}
      >
        {DATAPROCESSING_LABEL.add_segment}
      </Box>
    </Box>
  );
};

export default SegmentBlock;
