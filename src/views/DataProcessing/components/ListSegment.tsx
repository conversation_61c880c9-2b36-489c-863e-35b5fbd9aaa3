import { useContext, useEffect, useState, useRef } from 'react';
import { ISegment, ITypeTab } from '../context/ProcessingTabsContext';
import { ModalContext } from 'providers/Modal';
import { Box } from 'components/Box';
import { Button } from 'components/ui/button';
import EmptyData from './EmptyData';
import SegmentOverview from './SegmentOverview';
import SearchSegment from './SearchSegment';
import LoadingSkeleton from 'components/LiveAdsPost/LoadingSkeleton';

import useDebounce from 'hooks/useDebounce';
import { socialAPI } from 'apis/socialData';
import { personaAPI } from 'apis/persona';
import handleCloseModal from 'utils/handleCloseModal';
import LABEL from 'constants/label';
import useInfiniteScroll from 'hooks/useLoadInfinity';
import { cn } from 'utils/utils';
import { RiCloseLine } from '@remixicon/react';
import { useAppSelector } from '../../../store';
import { useAbortController } from '../../../hooks/useAbortController';

interface Props {
  type?: ITypeTab;
  allSelected: ISegment[];
  listSelected: ISegment[];
  handleAddSegment: (data: ISegment[]) => void;
}

const ListSegment = ({ type, listSelected, allSelected, handleAddSegment }: Props) => {
  const modal = useContext(ModalContext);
  const { datatype } = useAppSelector((state) => state.dataProcessing);
  const [searchValue, setSearchValue] = useState<string>('');
  const [selectedSegment, setSelectedSegments] = useState<ISegment[]>([]);
  const [data, setData] = useState<{data: ISegment[]; total: number}>({
    data: [],
    total: 0
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const { newAbortController } = useAbortController();
  const debounce = useDebounce(searchValue, 700);
  const rootApi = type === 'social' ? socialAPI : personaAPI;
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Reset on page or search value change
    setPage(1);
    setData({ data: [], total: 0 });
    getListSegment(1);
  }, [debounce, type]);

  const { lastElementRef } = useInfiniteScroll({
    fetchNextPage: async () => getListSegment(page + 1),
    hasNextPage: data.data.length < data.total,
    isFetching: loading,
    scrollContainer: scrollContainerRef.current,
    autoScrollAfterFetch: true
  });
  const getListSegment = async (pageToLoad: number) => {
    const controller = newAbortController();
    setLoading(true);
    const response = await rootApi.get({
      endpoint: 'segments/',
      params: { page: pageToLoad, limit: 10, q: debounce },
      signal: controller.signal
    });

    if (response && response.data) {
      setData((prev) => ( {
        ...prev,
        total: response.data.count,
        data: [...prev.data, ...response.data.items]
      } ));
      setPage(pageToLoad);
    }
    setLoading(false);
  };

  const listSegment = !datatype
    ? data.data
    : data.data.filter((item) => item.datatype === datatype);

  return (
    <Box variant="col-start" className="gap-4">
      <Box className="justify-between w-full">
        <div className="text-primary text-md font-medium">Choose your segments</div>
        <RiCloseLine size={20} className="cursor-pointer" onClick={() => handleCloseModal(modal)} />
      </Box>

      <SearchSegment setSearchValue={setSearchValue} />

      <div
        ref={scrollContainerRef}
        className="min-h-[350px] w-full max-h-[120px] overflow-y-auto"
        style={{
          WebkitOverflowScrolling: 'touch'
        }}
      >
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {listSegment.filter((segment) => !allSelected.some((selected) => selected.id === segment.id)).map((segment: ISegment) => (
              <SegmentOverview
                key={segment.id}
                segment={{ ...segment }}
                allSelected={allSelected}
                setSelectedSegments={setSelectedSegments}
              />
            ))}
          </div>
          <div
            ref={lastElementRef}
            className={cn(
              'w-full grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 items-start justify-center gap-6 py-6',
              data.data.length == data.total && 'hidden'
            )}
          >
            {loading && [1, 2, 3].map((item) => (
              <LoadingSkeleton key={item} />
            ))}
          </div>
        </>
        {data.data.length == 0 && !loading && <EmptyData />}
      </div>

      {data.data.length > 0 && (
        <div className="grid grid-cols-2 gap-4 w-full">
          <Button
            type="button"
            variant="secondary"
            className="border shadow-xs rounded-xl text-md font-medium"
            onClick={() => handleCloseModal(modal)}
          >
            {LABEL.cancel}
          </Button>
          <Button
            variant="main"
            className="rounded-xl text-md font-medium"
            onClick={() => {
              handleAddSegment([...listSelected, ...selectedSegment]);
              setSelectedSegments([]);
            }}
          >
            {LABEL.add}
          </Button>
        </div>
      )}
    </Box>
  );
};

export default ListSegment;
