import { useContext } from "react";
import { ProcessingTabContext } from "../context/ProcessingTabsContext";

import { Box } from "components/Box";

import { cn } from "utils/utils";
import { IOperationTabs } from "types/YourData";
import { DATA_OPERATION_TABS } from "constants/yourData";
import StepProcessing from "./StepProcessing";

const OperationTabs = () => {
  const context = useContext(ProcessingTabContext);

  return (
    <Box variant="col-start" className="gap-3 w-full">
      <StepProcessing text="Step 1: Select your processing method to create your new segment" />
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 w-full">
        {DATA_OPERATION_TABS.map((operation: IOperationTabs) => {
          const isActive = operation.value === context?.operator;
          return (
            <Box
              key={operation.value}
              variant="col-start"
              className={cn(
                "p-4 rounded-2xl border gap-4 justify-between cursor-pointer",
                isActive && "bg-brand-subtitle border-brand-default"
              )}
              onClick={() => context?.setOperator(operation.value)}
            >
              <Box className="gap-3 justify-start">
                <div className="pointer-events-none rounded-xl bg-brand-subtitle p-2 h-10">
                  <operation.iconComponent />
                </div>
                <span className="text-primary font-medium text-md">{operation.title}</span>
              </Box>
              <div className="text-secondary text-sm font-normal">{operation.content}</div>
            </Box>
          );
        })}
      </div>
    </Box>
  );
};

export default OperationTabs;
