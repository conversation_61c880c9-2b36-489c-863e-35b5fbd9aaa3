import { useContext } from "react";
import { <PERSON> } from "react-router-dom";
import { ModalContext } from "providers/Modal";
import { ITypeTab } from "../context/ProcessingTabsContext";

import { Box } from "components/Box";
import { Button } from "components/ui/button";

import handleCloseModal from "utils/handleCloseModal";
import { PATH_DASHBOARD } from "types/path";

const EmptyData = ({ type }: { type?: ITypeTab }) => {
  const modal = useContext(ModalContext);
  const path =
    type == "social"
      ? PATH_DASHBOARD.your_data.your_audience.social
      : PATH_DASHBOARD.your_data.your_audience.persona;
  return (
    <Box variant="col-start" className="p-6 w-full gap-4 items-center">
      <svg
        width="81"
        height="80"
        viewBox="0 0 81 80"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.271729 40C0.271729 17.9086 18.1803 0 40.2717 0C62.3631 0 80.2717 17.9086 80.2717 40C80.2717 62.0914 62.3631 80 40.2717 80C18.1803 80 0.271729 62.0914 0.271729 40Z"
          fill="#F0F0F0"
        />
        <path
          d="M26.9781 26.3051C27.1476 25.5426 27.8239 25 28.6051 25H51.9384C52.7196 25 53.3959 25.5426 53.5654 26.3051L56.8988 41.3052C56.9251 41.4238 56.9384 41.545 56.9384 41.6667V53.3333C56.9384 54.2538 56.1923 55 55.2718 55H25.2718C24.3513 55 23.6051 54.2538 23.6051 53.3333V41.6667C23.6051 41.545 23.6184 41.4238 23.6448 41.3052L26.9781 26.3051ZM29.9421 28.3333L27.3495 40H35.2718C35.2718 42.7615 37.5103 45 40.2718 45C43.0333 45 45.2718 42.7615 45.2718 40H53.1941L50.6014 28.3333H29.9421ZM47.9118 43.3333C46.6258 46.2765 43.6889 48.3333 40.2718 48.3333C36.8546 48.3333 33.9178 46.2765 32.6318 43.3333H26.9384V51.6667H53.6051V43.3333H47.9118Z"
          fill="#515667"
        />
      </svg>
      <div className="text-center">
        <div className="text-primary text-md font-medium leading-6">No data to display.</div>
        <span className="text-secondary text-sm mt-2 leading-5">
          It looks like there’s no data available yet. Try adding some new items.
        </span>
      </div>
      <Link to={path} onClick={() => handleCloseModal(modal)}>
        <Button variant="default" className="mt-1">
          Create segment
        </Button>
      </Link>
    </Box>
  );
};

export default EmptyData;
