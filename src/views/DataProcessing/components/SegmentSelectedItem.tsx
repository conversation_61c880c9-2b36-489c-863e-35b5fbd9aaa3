import { useContext, useEffect } from 'react';
import { RiSubtractLine } from "@remixicon/react";
import { ISegment, ProcessingTabContext } from "../context/ProcessingTabsContext";

import { Box } from "components/Box";
import { fNumberToString } from "utils/number";
import { useAppDispatch, useAppSelector } from '../../../store';
import { setDatatype } from '../../../store/redux/DataProcessing/slice';
import { Badge } from '../../../components/ui/badge';

interface Props {
  segments: ISegment[] | undefined;
  segmentType: "left" | "right";
}
const SegmentSelectedItem = ({ segments, segmentType }: Props) => {
  const setSelectedSegment = useContext(ProcessingTabContext)?.setSelectedSegment;
  const selectedSegment = useContext(ProcessingTabContext)?.selectedSegment;
  const { datatype } = useAppSelector((state) => state.dataProcessing);
  const dispatch = useAppDispatch();
  const handleRemoveItem = (id: number) => {
    setSelectedSegment &&
      setSelectedSegment((prev) => {
        const updatedData = prev[
          segmentType === "left" ? "left_segments" : "right_segments"
        ].filter((item) => item.id !== id);
        return {
          ...prev,
          [segmentType === "left" ? "left_segments" : "right_segments"]: updatedData,
        };
      });
  };
  useEffect(() => {
    if (selectedSegment?.left_segments.length === 0 && selectedSegment?.right_segments.length === 0 && !!datatype) {
      dispatch(setDatatype(''));
    }
  }, [selectedSegment]);

  return (
    segments &&
    segments.length > 0 && (
      <Box variant="col-start" className="gap-4">
        {segments.map((selected) => {
          return (
            <Box
              className="rounded-2xl p-5 border bg-white w-full items-start h-[108px]"
              key={selected.id}
            >
              <div className="flex-1">
                <Badge className="capitalize text-[8px] mb-1">{datatype.toLocaleLowerCase()}</Badge>
                <div className="text-primary text-sm font-medium line-clamp-1">{selected.name}</div>
                <div className="text-tertiary text-xs">
                  segment size: {fNumberToString(selected.segment_size)}
                </div>
              </div>
              <RiSubtractLine
                size={20}
                className="text-error-default cursor-pointer"
                onClick={() => handleRemoveItem(selected.id)}
              />
            </Box>
          );
        })}
      </Box>
    )
  );
};

export default SegmentSelectedItem;
