import map from "lodash/map";
import { useContext, useState } from "react";
import { ModalContext } from "providers/Modal";
import { ProcessingTabContext } from "../context/ProcessingTabsContext";

import { Box } from "components/Box";
import { But<PERSON> } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import NoticeMessage from "./NoticeMessage";
import CreateSegmentModal from "./CreateSegmentModal";

import { personaAPI } from "apis/persona";
import { socialAPI } from "apis/socialData";
import useFeatures from "hooks/useFeatures";

import { cn } from "utils/utils";
import { fNumberToString } from "utils/number";
import handleCloseModal from "utils/handleCloseModal";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import StepProcessing from "./StepProcessing";

interface Props {
  total?: number;
}
const ActionComponent = ({ total = 0 }: Props) => {
  const [loading, setLoading] = useState<boolean>(false);

  const context = useContext(ProcessingTabContext);
  const modal = useContext(ModalContext);
  const rootApi = context?.type === "social" ? socialAPI : personaAPI;

  const max_rows = useFeatures(FEATURE_PERMISSION_KEY.SEGMENT_PROCESSING)?.extra_limit.max_rows;
  const left = map(context?.selectedSegment.left_segments, (segment) => segment.id);
  const right = map(context?.selectedSegment.right_segments, (segment) => segment.id);

  const handleProcessing = async () => {
    setLoading(true);
    const res = await rootApi.AnalyzeProcessing({
      payload: {
        left_segments: left,
        right_segments: right,
        action: context?.operator,
      },
    });
    if (res.data) {
      handleCreateSegmentModal(res.data.count);
    }
    setLoading(false);
  };

  const addSegment = async (segmentName: string) => {
    const params = {
      segment: { name: segmentName, description: "" },
      process: {
        left_segments: left,
        right_segments: right,
        action: context?.operator,
      },
    };
    if (segmentName === "") {
      toast({
        title: "Please enter segment name !!",
        status: "error",
        duration: 3000,
      });
    } else {
      await rootApi.createSegmentByProcessing({
        payload: params,
      });
    }
    handleCloseModal(modal);
  };

  const handleCreateSegmentModal = (count: number) => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "max-w-[680px] p-4",
      isOpen: true,
      isShowTitle: false,
      content: (
        <CreateSegmentModal
          analyzeCount={count}
          handleCreateSegment={(value) => addSegment(value)}
        />
      ),
    }));
  };

  const isLimit = max_rows && total > max_rows;
  const disableAnalyze = left.length == 0 || right.length == 0 || isLimit;

  return (
    <div className="text-left">
      <StepProcessing text="Step 3: Analyze your data." />
      {isLimit && <NoticeMessage />}
      <Box className="justify-between h-10 items-center w-full">
        <div className="text-secondary font-normal text-sm">
          <span>Process</span>
          <span
            className={cn(" text-lg mx-1", isLimit ? "text-error-default" : "text-brand-default")}
          >
            {fNumberToString(total)}
          </span>
          <span>data</span>
        </div>
        <Button
          variant="main"
          className="text-sm h-10 rounded-xl p-3 w-[150px]"
          disabled={loading || ( disableAnalyze ? disableAnalyze : false )}
          onClick={() => handleProcessing()}
          children={!loading ? "Analyze" : <LoadingButtonIcon />}
        />
      </Box>
    </div>
  );
};

export default ActionComponent;
