import { RiInformation2Line, RiRepeat2Line } from "@remixicon/react";

import { Box } from "components/Box";
import SegmentBlock from "./SegmentBlock";

import { cn } from "utils/utils";
import { DATAPROCESSING_LABEL } from "constants/yourData/label";
import StepProcessing from "./StepProcessing";

const SegmentChooseWrapper = ({ stepActive = 0 }: { stepActive?: number }) => (
  <div className={cn("w-full", stepActive >= 1 && "mt-6")}>
    <StepProcessing text="Step 2: Add your segment to start analyze the process data." />
    <Box
      variant="col-start"
      className={cn(
        "gap-4 p-3 md:p-6 bg-custom-secondary rounded-2xl w-full mt-3",
        stepActive == 1 && "border border-brand-default",
        stepActive > 1 && "border-none"
      )}
    >
      <Box className="w-full md:gap-8 flex flex-col md:flex-row gap-2">
        <SegmentBlock title={DATAPROCESSING_LABEL.segment_A} segmentType="left" />
        <div className="flex flex-col items-center justify-center h-full">
          <RiRepeat2Line className="text-secondary flex-shrink-0 mt-4 md:mt-10" size={24} />
        </div>
        <SegmentBlock title={DATAPROCESSING_LABEL.segment_B} segmentType="right" />
      </Box>
      <div className="text-secondary text-sm flex gap-2">
        <RiInformation2Line size={20} />
        <span>{DATAPROCESSING_LABEL.add_segment_notice}</span>
      </div>
    </Box>
  </div>
);

export default SegmentChooseWrapper;
