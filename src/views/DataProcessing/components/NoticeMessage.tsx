import { RiAlertLine } from "@remixicon/react";
import { Box } from "components/Box";
import useFeatures from "hooks/useFeatures";
import { fNumberToString } from "utils/number";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";

const NoticeMessage = () => {
  const max_rows = useFeatures(FEATURE_PERMISSION_KEY.SEGMENT_PROCESSING)?.extra_limit.max_rows;
  return (
    <Box className="bg-warning-subtitle-default text-warning-text-strong px-4 py-3 rounded-xl mt-3 gap-1 justify-start text-xs mb-1">
      <RiAlertLine size={20} />
      <span>
        Data analysis feature process under <strong>{fNumberToString(max_rows)} profiles.</strong>
      </span>
    </Box>
  );
};

export default NoticeMessage;
