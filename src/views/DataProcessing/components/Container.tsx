import { useContext, useEffect, useState } from "react";
import { ModalContext } from "providers/Modal";
import {
  ITypeTab,
  ProcessingTabContext,
  ProcessingTabsProvider,
} from "../context/ProcessingTabsContext";

import { Box } from "components/Box";
import Overlay from "./Overlay";
import OperationTabs from "./OperationTabs";
import ActionComponent from "./ActionComponent";
import SegmentSelectedWrapper from "./SegmentChooseWrapper";

import { cn } from "utils/utils";

export interface ContainerTabProps {
  type: ITypeTab;
  tabName?: string;
}

const Container = ({ type }: ContainerTabProps) => {
  const [total, setTotal] = useState<number>(0);
  const context = useContext(ProcessingTabContext);
  const modal = useContext(ModalContext);

  // Set the tab type "social" |"persona"
  useEffect(() => {
    context?.setType(type);
  }, [context, type]);

  useEffect(() => {
    const totalSegments = [
      ...(context?.selectedSegment.left_segments || []),
      ...(context?.selectedSegment.right_segments || []),
    ].reduce((acc, segment: any) => acc + segment.segment_size, 0);

    setTotal(totalSegments);
  }, [context?.selectedSegment]);

  const getStepActive = () => {
    if (
      context?.operator !== null &&
      context?.selectedSegment &&
      context?.selectedSegment?.left_segments.length > 0 &&
      context?.selectedSegment?.right_segments.length > 0
    ) {
      return 3; // Analyze step active
    }
    if (context?.operator !== null) {
      return 1; // Operator step active
    }
    if (
      context?.selectedSegment &&
      context?.selectedSegment?.left_segments.length > 0 &&
      context?.selectedSegment?.right_segments.length > 0
    ) {
      return 2; // Segment selection step active
    }
    return 0; // No step active
  };

  const stepActive = getStepActive();
  const isOpenModal = modal?.dataDialog?.isOpen;

  const renderContent = () => {
    return (
      <div
        className={cn(
          "p-3 bg-white z-[2] rounded-2xl overflow-hidden transition-all duration-500 ease-in-out min-h-[200px] w-full max-h-full",
          stepActive == 3 && "p-3"
        )}
      >
        <OperationTabs />
        {stepActive >= 1 && <SegmentSelectedWrapper stepActive={stepActive} />}
        {stepActive >= 2 && (
          <div className="mt-6">
            <ActionComponent total={total} />
          </div>
        )}
      </div>
    );
  };

  return (
    <Box
      variant="col-start"
      className={cn(
        "gap-6 rounded-2xl shadow-sm relative p-3 flex-1 h-full",
        (stepActive > 1 || isOpenModal) && "p-3 h-fit"
      )}
    >
      {renderContent()}
      {stepActive < 1 && <SegmentSelectedWrapper />}
      {stepActive < 2 && (
        <div
          className={cn("pb-6 w-full", stepActive == 1 && "px-2")}
          children={<ActionComponent />}
        />
      )}
      <Overlay isOpenModal={isOpenModal} stepActive={stepActive} />
    </Box>
  );
};

const ContainerWrapper = (props: ContainerTabProps) => {
  return (
    <ProcessingTabsProvider>
      <Container {...props} />
    </ProcessingTabsProvider>
  );
};

export default ContainerWrapper;
