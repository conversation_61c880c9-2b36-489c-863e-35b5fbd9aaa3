import { useState } from 'react';
import { ISegment } from '../context/ProcessingTabsContext';

import { Box } from "components/Box";
import { Checkbox } from "components/ui/checkbox";
import { fNumberToString } from "utils/number";
import { cn } from "utils/utils";
import { useAppDispatch } from '../../../store';
import { setDatatype } from '../../../store/redux/DataProcessing/slice';
import { Badge } from '../../../components/ui/badge';

interface Props {
  segment: ISegment;
  setSelectedSegments: React.Dispatch<React.SetStateAction<ISegment[]>>;
  allSelected: ISegment[]
}

const SegmentOverview = ({ segment, setSelectedSegments, allSelected }: Props) => {
  const { name, segment_size, id, checked, datatype } = segment;
  const [isChecked, setIsChecked] = useState<boolean>(checked || false);

  const dispatch = useAppDispatch();
  const handleChange = (check: boolean) => {
    if (checked === undefined || checked === false) {
      setIsChecked(check);
      if (check) {
        setSelectedSegments((prev) => {
          dispatch(setDatatype(segment.datatype));
          if (!prev.some((s) => s.id === id)) {
            return [...prev, segment];
          }
          return prev;
        });
      } else {
        setSelectedSegments((prev) => {
          const value = prev.filter((s) => s.id !== id);
          if (allSelected.length === 0) {
            dispatch(setDatatype(''));
          }
          return value
        });
      }
    }
  };
  return (
    <Box
      className={cn(
        'p-5 border rounded-2xl gap-2 items-start cursor-pointer',
        checked
          ? 'bg-custom-disable pointer-events-none'
          : 'bg-custom-secondary pointer-events-auto'
      )}
      onClick={() => handleChange(!isChecked)}
    >
      <div className="mt-1">
        <Checkbox id={String(id)} checked={isChecked} onCheckedChange={handleChange} />
      </div>
      <div className="w-full flex-1">
        <Badge className="capitalize text-[8px]">{datatype.toLocaleLowerCase()}</Badge>
        <div
          className={cn(
            'font-medium text-sm cursor-pointer line-clamp-2',
            checked ? 'text-custom-disable' : 'text-primary'
          )}
          children={name}
        />
        <div className={cn('text-xs mt-1', checked ? 'text-custom-disable' : 'text-tertiary')}>
          segment size: {fNumberToString(segment_size)}
        </div>
      </div>
    </Box>
  );
};

export default SegmentOverview;
