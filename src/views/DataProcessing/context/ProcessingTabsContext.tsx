import React, { createContext, useState } from "react";
import { TParams } from "types/Param";
import { Operator } from "types/YourData";

export type ITypeTab = "social" | "persona";
export interface ISegment {
  id: number;
  name: string;
  description: string;
  segment_size: number;
  checked: boolean;
  datatype: 'AUDIENCE' | 'DATASET';
}
interface ISelectedSegment {
  left_segments: ISegment[];
  right_segments: ISegment[];
}
export type IProcessingContext = {
  params: TParams;
  listSegment: {
    data: ISegment[];
    loading: boolean;
  };
  selectedSegment: ISelectedSegment;
  type: ITypeTab;
  operator: Operator | null;
  setParams: React.Dispatch<React.SetStateAction<TParams>>;
  setOperator: React.Dispatch<React.SetStateAction<Operator | null>>;
  setType: React.Dispatch<React.SetStateAction<ITypeTab>>;
  setSelectedSegment: React.Dispatch<React.SetStateAction<ISelectedSegment>>;
  setListSegment: React.Dispatch<
    React.SetStateAction<{
      data: ISegment[];
      loading: boolean;
    }>
  >;
} | null;

const initParams: TParams = { page: 1, limit: 10 };
export const ProcessingTabContext = createContext<IProcessingContext>(null);
export const ProcessingTabsProvider = ({
  children,
}: {
  children: React.ReactNode | JSX.Element;
}) => {
  const [type, setType] = useState<ITypeTab>("social");
  const [params, setParams] = useState<TParams>(initParams);
  const [listSegment, setListSegment] = useState<{ data: ISegment[]; loading: boolean }>({
    data: [],
    loading: false,
  });
  const [selectedSegment, setSelectedSegment] = useState<ISelectedSegment>({
    left_segments: [],
    right_segments: [],
  });
  const [operator, setOperator] = useState<Operator | null>(null);
  return (
    <ProcessingTabContext.Provider
      value={{
        params,
        listSegment,
        selectedSegment: selectedSegment,
        operator,
        type,
        setType,
        setOperator,
        setParams,
        setSelectedSegment: setSelectedSegment,
        setListSegment,
      }}
    >
      {children}
    </ProcessingTabContext.Provider>
  );
};
