import { RiContactsLine, RiG<PERSON>Line } from "@remixicon/react";

import { Box } from "components/Box";
import TabRouteWrap, { ITab } from "components/Tabs/TabRouteWrap";
import HeaderWrapper from "components/Header";

import { DATAPROCESSING_LABEL } from "constants/yourData/label";
import { PATH_DASHBOARD } from "types/path";
import { Button } from "../../components/ui/button";
import handleCloseModal from "../../utils/handleCloseModal";
import useOutsideClick from "../../hooks/useClickOutSide";
import { ModalContext } from "../../providers/Modal";
import { RiCloseLine } from "@remixicon/react";
import { useContext, useRef } from "react";
import { TutorialIcon } from "../../assets/icons/TutorialIcon";

const LIST_TAB: ITab[] = [
  {
    title: DATAPROCESSING_LABEL.social_title_tab,
    path: `/${PATH_DASHBOARD.your_data.data_processing.social}`,
    tab_icon: RiGroupLine,
  },
  {
    title: DATAPROCESSING_LABEL.persona_title_tab,
    path: `/${PATH_DASHBOARD.your_data.data_processing.persona}`,
    tab_icon: RiContactsLine,
  },
];

const DataProcessingPage = () => {
  return (
    <TabRouteWrap
      tabs={LIST_TAB}
      className="w-full flex-1 h-full"
      tabContentClass="mt-0 w-full h-fit relative mt-4"
    />
  );
};

const DataProcessingView = () => {
  const modal = useContext(ModalContext);
  const ref = useRef(null);
  const refSecond = useRef(null);
  useOutsideClick(ref, refSecond, () => {
    if (!!modal?.dataDialog?.isPopupVideo) {
      handleCloseModal(modal);
    }
  });
  const handleOpenTutorial = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      isPopupVideo: true,
      content: (
        <div ref={refSecond}>
          <div className="text-right">
            <Button
              onClick={() => handleCloseModal(modal)}
              className="p-[10px] text-sm font-semibold mb-1 bg-transparent hover:bg-transparent text-primary"
            >
              <RiCloseLine size={20} />
              Close
            </Button>
          </div>
          <iframe
            src="https://www.youtube.com/embed/suguAyv8GDs?autoplay=1"
            title="Big360 - Data Processing"
            className="rounded-2xl w-full max-w-[920px] aspect-video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
        </div>
      ),
      className: "max-w-[936px] !p-2 max-lg:max-w-[calc(100%-12px)]",
    }));
  };

  return (
    <Box variant="col-start" className="flex w-full h-full gap-0">
      <HeaderWrapper
        isShowBreadcrumb
        leftChildren={{
          title: (
            <div className="flex gap-2" ref={ref}>
              <h1>{DATAPROCESSING_LABEL.title}</h1>
              <Button
                onClick={() => handleOpenTutorial()}
                className="flex items-center justify-center bg-[#F0F0F0] h-[36px] gap-1 p-2 rounded-xl	hover:bg-[#F0F0F0]"
              >
                <TutorialIcon />
                <p className="text-[#8F5CFF] text-xs mt-[1px]">Guide</p>
              </Button>
            </div>
          ),
          subTitle: DATAPROCESSING_LABEL.subtitle,
        }}
      />

      <DataProcessingPage />
    </Box>
  );
};

export default DataProcessingView;
