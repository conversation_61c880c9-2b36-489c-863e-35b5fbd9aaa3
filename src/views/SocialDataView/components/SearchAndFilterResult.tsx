import { useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import isEqual from 'lodash/isEqual';

import { Tabs, TabsContent, TabsList, TabsTrigger } from 'components/ui/tabs';
import DataTable from 'components/DataTable';
import { Badge } from 'components/ui/badge';
import Pagination from 'components/Pagination';
import { ParamsProps } from '../index';

import { socialAPI } from 'apis/socialData';
import usePrevious from 'hooks/usePrevious';
import { IRow, TableType } from 'types/Table';
import { SortType } from 'types';

import { toPaginationParams } from 'utils/pagination';
import { fNumberToString } from 'utils/number';
import { cn } from 'utils/utils';
import LABEL from 'constants/label';
import { SOCIAL_AUDIENCES_COLUMN } from 'constants/socialData/column';
import { useAbortController } from '../../../hooks/useAbortController';

const SearchAndFilterResult = ({ params }: {params: ParamsProps}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const prevParams = usePrevious(params);
  const { newAbortController } = useAbortController();
  const [data, setData] = useState<TableType<IRow>>({
    data: [],
    count: 0,
    loading: false
  });

  const getData = useCallback(async () => {
    setData({ ...data, loading: true });
    const controller = newAbortController();
    const res = await socialAPI.get({
      endpoint: 'audiences/',
      params: { ...toPaginationParams(params), order_by: toPaginationParams(params).order_by ?? '-size' },
      signal: controller.signal
    });

    if (res.data) {
      const { data = {} } = res;
      setData({ data: data.items, count: data.count, loading: false });
    } else {
      setData({ ...data, loading: false });
    }
  }, [params]);

  const handleSortChange = (sortType: SortType, field: string) => {
    const sortBy = {
      asc: `${field}`,
      desc: `-${field}`
    };
    setSearchParams({ ...params, order_by: sortBy[sortType] });
  };

  useEffect(() => {
    if (!isEqual(params, prevParams)) {
      getData();
    }
  }, [params, prevParams]);

  return (
    <div className={cn('bg-white transition-transform duration-700 z-50')}>
      <Tabs defaultValue={LABEL.result}>
        <div className="flex items-end justify-between mt-2">
          <TabsList>
            <TabsTrigger className="relative" value={LABEL.result}>
              {LABEL.result}
              <Badge className="bg-[#0A0F290A] text-[#924FE8] rounded-sm hover:bg-[#0A0F290A] shadow-sm ">
                {fNumberToString(data.count)}
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>
        <TabsContent className="mt-2" value={LABEL.result}>
          <DataTable
            rowData={data.data}
            column={SOCIAL_AUDIENCES_COLUMN({ handleSortChange, onRefresh: getData })}
            pagination
            className="h-[700px] mt-2 mb-0"
            loading={data.loading}
          />
          {/* Limit 50 page */}
          <Pagination
            className="mt-4"
            currentPage={Number(searchParams.get('page')) || 1}
            pageSize={10}
            totalCount={data.count < 500 ? data.count : 500}
            onPageChange={(page) => setSearchParams({ ...params, page: String(page) })}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SearchAndFilterResult;
