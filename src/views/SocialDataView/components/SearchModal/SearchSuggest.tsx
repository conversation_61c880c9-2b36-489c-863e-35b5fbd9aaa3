import { Link } from "react-router-dom";
import { LegacyRef } from "react";

import { useDispatch } from "react-redux";
import { onCloseSearchSuggest } from "store/redux/social/slice";

import { ENDPOINT_TYPE } from "constants/label";
import { RiSearchLine } from "@remixicon/react";

export interface ISuggestSearchItem {
  fb_uid: string;
  keywords: string;
  name: string;
}
const SearchSuggest = ({
  suggestData,
  refSecond,
}: {
  suggestData: ISuggestSearchItem[];
  refSecond: LegacyRef<HTMLDivElement> | undefined;
}) => {
  const dispatch = useDispatch();
  return (
    <div
      ref={refSecond}
      className="absolute w-full z-50 min-h-[250px] pl-[6px] border border-[#924FE8] rounded-xl shadow-sm p-2 bg-white -bottom-1 translate-y-full"
    >
      {!!suggestData.length && (
        <span className="text-tertiary text-sm pl-[6px]">Try searching for</span>
      )}
      <div>
        {suggestData.length ? (
          suggestData.map((suggest: ISuggestSearchItem) => {
            const { fb_uid, name } = suggest;

            return (
              <div className="flex items-center gap-2 px-[6px] py-2 text-primary text-sm hover:bg-[#0A0F290A] hover:rounded-md">
                <RiSearchLine opacity={0.4} color="#0F1324" size={16} />
                <Link
                  to={`/social-data/${ENDPOINT_TYPE.DETAIL}/${fb_uid}`}
                  className="capitalize"
                  onClick={() => dispatch(onCloseSearchSuggest())}
                >
                  {name.toLocaleLowerCase()}
                </Link>
              </div>
            );
          })
        ) : (
          <div className="text-center">There are no matches</div>
        )}
      </div>
    </div>
  );
};

export default SearchSuggest;
