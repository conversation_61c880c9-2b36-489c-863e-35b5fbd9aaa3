import { useState, useEffect } from "react";
import { TRENDING_COLUMN } from "constants/socialData/column";
import { socialAPI } from "apis/socialData";

import DataTable from "components/DataTable";
import { cn } from "utils/utils";
import HeaderWrapper from "./Header";
import { ColDef, ICellRendererParams } from "ag-grid-community";
import AddAudienceColumn from "../columns/AddAudienceColumn";
import { useAbortController } from '../../../hooks/useAbortController';

const TableTrending = ({ isShow }: { isShow: boolean }) => {
  const [data, setData] = useState({
    data: [],
    loading: false,
  });
  const { newAbortController } = useAbortController();

  const getData = async () => {
    setData({ ...data, loading: true });
    const controller = newAbortController();
    const res = await socialAPI.get({ endpoint: "audiences-trending/",signal: controller.signal });
    if (res.data) {
      setData({ ...data, data: res.data.items });
    } else {
      setData({ ...data, loading: false });
    }
  };

  useEffect(() => {
    getData();
  }, []);

  const columnDef: ColDef[] = [
    ...TRENDING_COLUMN,
    {
      field: "actions",
      headerName: "",
      cellClass: '!px-4',
      minWidth: 300,
      width: 300,
      maxWidth: 300,
      cellRenderer: (params: ICellRendererParams) => (
        <AddAudienceColumn params={params} onRefresh={getData} />
      ),
    },
  ];

  return (
    <>
      <div className={cn(isShow && "hidden", "mt-10")}>
        <HeaderWrapper leftChildren={{ title: "Audiences Trending", titleSize: "lg" }} />
        <DataTable
          column={columnDef}
          rowData={data.data}
          pagination={false}
          className=" border-none"
          loading={data.loading}
        />
      </div>
    </>
  );
};

export default TableTrending;
