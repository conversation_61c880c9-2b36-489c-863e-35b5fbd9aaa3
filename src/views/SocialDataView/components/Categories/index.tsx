import { useState } from "react";
import map from "lodash/map";
import isArray from "lodash/isArray";
import range from "lodash/range";
import HeaderWrapper from "../Header";
import CategoryItem from "./CategoryItem";
import ViewMore from "./ViewMore";
import { ParamsProps } from "views/SocialDataView";
import { Skeleton } from "components/ui/skeleton";
import { ICategoryItem } from "types/SocialData";
import { cn } from "utils/utils";
import { useAppSelector } from "store";

interface Props {
  loading: boolean;
  isShow: boolean;
  params: ParamsProps;
  setSearchParams: any;
}
const CategoriesWrapper = (props: Props) => {
  const { loading } = props;
  const [isShowMore, setIsShowMore] = useState<boolean>(false);
  const { data } = useAppSelector((state) => state.category);

  const renderCategory = () => {
    const dataToRender = isShowMore ? data.items : data.items.slice(0, 8);

    if (loading) {
      return range(0, 4).map((index: number) => (
        <div key={index}>
          <Skeleton className="h-[125px] w-full rounded-xl" />
          <Skeleton className="h-4 w-1/2 my-4" />
          <Skeleton className="h-4 w-1/3" />
        </div>
      ));
    }

    return map(dataToRender, (category: ICategoryItem) => (
      <CategoryItem key={category.name} {...category} {...props} />
    ));
  };

  const isShowCategory = isArray(data.items) && data.items.length > 0;

  return (
    <div className={cn((props.isShow || (!loading && !isShowCategory)) && "hidden")}>
      <HeaderWrapper
        params={props.params}
        leftChildren={{ title: "Categories", titleSize: "lg" }}
        rightChildren={
          <ViewMore
            count={data.items.length || 0}
            isShowMore={isShowMore}
            setIsShowMore={setIsShowMore}
            // className="hidden md:flex"
          />
        }
        className="md:items-center"
      />
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-10 lg:gap-12 mt-4">
        {renderCategory()}
      </div>
    </div>
  );
};

export default CategoriesWrapper;
