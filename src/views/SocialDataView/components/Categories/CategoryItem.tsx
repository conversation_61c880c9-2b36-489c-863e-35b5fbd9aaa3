import styled from "styled-components";
import { ICategoryItem } from "types/SocialData";
import { fNumberToString } from "utils/number";
import { ParamsProps } from "views/SocialDataView";
import { tracking } from '../../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../../store';

const CategoryItem = ({
  code,
  img_cover,
  audience_count,
  name,
  params,
  setSearchParams,
}: ICategoryItem & {
  params: ParamsProps;
  setSearchParams: any;
}) => {
  const { user } = useAppSelector((state) => state.auth);
  const handleGetCategoryDetail = async () => {
    setSearchParams({ ...params, category__in: code });
    tracking({
      eventName: 'view_category',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          category: code
        })
      }
    });
  };

  return (
    <div key={code}>
      <div className="relative mb-3" onClick={handleGetCategoryDetail}>
        {img_cover ?
          <img src={img_cover} className="w-full h-[177px] object-cover rounded-2xl cursor-pointer" alt={img_cover} />
          :
          <div className="w-full h-[177px] object-cover rounded-2xl cursor-pointer"></div>}
        <OverLayWrapper />
        <h3 className="absolute bottom-[11px] left-[23px] z-[1] text-white text-lg font-semibold mt-4 cursor-pointer">
          {name}
        </h3>
      </div>
      <span className="text-md font-semibold">
        <span className="text-primary mr-1">+ {fNumberToString(audience_count)}</span>
        <span className="text-secondary">Audiences</span>
      </span>
    </div>
  );
};

export default CategoryItem;

const OverLayWrapper = styled("div")({
  position: "absolute",
  borderRadius: "16px",
  height: "100%",
  width: "100%",
  zIndex: "1",
  top: "0",
  left: "0",
  background:
    "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.05449929971988796) 40%, rgba(0, 0, 0, 0.107777) 50%, rgba(0, 0, 0, 0.6970763305322129) 100%)",
});
