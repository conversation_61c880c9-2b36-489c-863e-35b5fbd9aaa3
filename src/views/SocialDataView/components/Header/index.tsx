import LeftHeader from "./LeftHeader";
import { RightHeader } from "./RightHeader";
import { ParamsProps } from "views/SocialDataView";

import FilterOptions from "./FilterOptions";
import { TSelectOption } from "types/Select";
import { cn } from "utils/utils";

interface Props {
  leftChildren?: {
    title?: React.ReactNode;
    highlight?: string | undefined;
    titleSize?: "sm" | "md" | "lg" | "xl" | "xxl";
    subTitle?: string;
  };

  rightChildren?: React.ReactNode | JSX.Element;
  className?: string;
  filterOptions?: TSelectOption[] | undefined;
  isFilterType?: boolean;
  isFilterCategory?: boolean;
  isFilterPackage?: boolean;
  params?: ParamsProps;
  setOpenDrawer?: React.Dispatch<React.SetStateAction<boolean>> | undefined;
  handleFilter?: (params: ParamsProps) => void;
}
const HeaderWrapper = ({ rightChildren, leftChildren, setOpenDrawer, ...props }: Props) => {
  const { title, titleSize, subTitle, highlight } = leftChildren || {};

  return (
    <>
      <div className={cn("flex items-center md:items-start flex-wrap mt-6", props.className)}>
        <LeftHeader title={title} highlight={highlight} titleSize={titleSize} subTitle={subTitle} />
        <RightHeader rightChildren={rightChildren} />
      </div>
      <FilterOptions {...props} />
    </>
  );
};

export default HeaderWrapper;
