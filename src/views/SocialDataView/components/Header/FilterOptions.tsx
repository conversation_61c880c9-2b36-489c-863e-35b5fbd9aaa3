import { useEffect, useState } from "react";
import { RiDeleteBin6Line, RiFilter2Line } from "@remixicon/react";
import omit from "lodash/omit";

import MultiSelect from "components/MSelect";

import { TSocialFilterProps } from "types/SocialData";
import { LABEL } from "constants/index";
import { cn } from "utils/utils";
import filterOptions from "utils/socialData/filterOption";
import { Button } from "components/ui/button";
import { Box } from '../../../../components/Box';
import { tracking } from '../../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../../store';

const FilterOptions = ({
  isFilterType,
  isFilterCategory,
  isFilterPackage,
  handleFilter,
  params,
  className,
}: TSocialFilterProps & { handleFilter?: (paramsFilter: any) => void; params?: any }) => {
  const [selectedOption, setSelectedOption] = useState<{ [key: string]: string }>({});
  const { user } = useAppSelector((state) => state.auth);
  useEffect(() => {
    setSelectedOption(params);
  }, [params]);

  const handleChange = (key: string, value: { label: string; value: string }[]) => {
    const newValue = value.map((item: { label: string; value: string }) => item?.value).join(",");
    setSelectedOption((prev: any) => ({ ...prev, [key]: newValue ? newValue : undefined }));
  };

  const filters = filterOptions({ isFilterType, isFilterCategory, isFilterPackage, params });

  const renderFilterOptions = () => {
    return filters.map((filter) => {
      let paramsDefault;
      if (filter?.key) {
        const paramsFilter = (selectedOption?.[filter?.key] || "").split(",");
        paramsDefault = (filter.options || []).filter((x) => {
          return paramsFilter.indexOf(x?.value as any) !== -1;
        });
      }
      switch (filter?.type) {
        case "select":
          return (
            <MultiSelect
              key={filter.key}
              defaultValue={paramsDefault}
              options={filter.options}
              placeholder={filter.placeholder}
              onChange={(value: any) => {
                handleChange(filter.key, value);
              }}
              isMulti
            />
          );
        default:
          break;
      }
    });
  };
  const handleTracking = () => {
    handleFilter&& handleFilter({ ...params, ...(selectedOption || {}) })
    tracking({
      eventName: 'social_data_filter',
      params:{
        user_id: user?.uuid,
        valueTracking: JSON.stringify({ ...params, ...(selectedOption || {}) })
      }
    })
  }
  return (
    !!handleFilter && (
      <Box className={cn("flex items-center gap-3 mt-6", className)}>
        {renderFilterOptions()}

        <Box className="gap-3 items-center h-10 md:justify-start max-md:flex-row-reverse max-md:w-full">
          <Button
            variant={"main"}
            size={"default"}
            className="max-lg:flex-1 p-3 rounded-xl"
            onClick={handleTracking}
          >
            <RiFilter2Line size={17} className="mr-1 hidden min-[768px]:block" />
            <span>{LABEL.filter}</span>
          </Button>
          {Object.keys(omit(selectedOption, ["page", "q"])).length > 0 && (
            <Button variant={"ghost"} onClick={() => handleFilter({})} className="max-lg:flex-1 text-destructive">
              <RiDeleteBin6Line size={16} />
              {LABEL.clear}
            </Button>
          )}
        </Box>
      </Box>
    )
  );
};

export default FilterOptions;
