import { Link } from "react-router-dom";
import { ICellRendererParams } from "ag-grid-community";

import AvatarByName from "components/AvatarByName";
import useAvatar from "hooks/useAvatar";

import { ENDPOINT_TYPE } from "constants/label";
import { formatPayloadAvatar } from "utils/utils";

const InformationColumn = (params: ICellRendererParams | any) => {
  const { fb_uid, name, type, actor_id } = params.data || params;

  const payload = formatPayloadAvatar(type || 1, fb_uid, actor_id);
  const { avatar } = useAvatar(payload);

  return (
    <div className="flex items-center gap-2">
      <AvatarByName
        urlImage={avatar.url}
        className={"w-[40px] h-[40px] rounded-md text-xl object-cover"}
        name={name || ""}
        position="first"
      />
      <div className="flex flex-col flex-1">
        <Link
          to={`/social-data/${ENDPOINT_TYPE.DETAIL}/${fb_uid}`}
          className="text-primary text-sm mb-1 overflow-hidden line-clamp-1 hover:text-primary-hover"
        >
          <p className="overflow-hidden text-ellipsis w-fit whitespace-nowrap max-md:max-w-[130px] md:max-w-[150px] lg:max-w-[200px] xl:max-w-[250px] inline-block">
            {name}
          </p>
        </Link>
        <Link
          target="_blank"
          to={`https://www.facebook.com/groups/${fb_uid}`}
          className="text-xs flex items-center text-secondary text-blue-400"
        >
          Facebook
        </Link>
      </div>
    </div>
  );
};

export default InformationColumn;
