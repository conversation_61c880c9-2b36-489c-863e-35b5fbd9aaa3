import AvatarByName from "components/AvatarByName";
import useAvatar from "hooks/useAvatar";

const InfoDetailCol = (params: any) => {
  const { fullname, fb_uid } = params;
  const { avatar } = useAvatar({ type: "profile", uid: fb_uid });

  return (
    <div className="flex items-center py-2 gap-2 flex-shrink-0 max-w-[300px] overflow-hidden">
      <AvatarByName urlImage={avatar.url} name={fullname} />
      <div className="text-primary flex flex-col max-w-full overflow-hidden">
        <a
          className="hover:text-blue-400 text-ellipsis overflow-hidden whitespace-nowrap"
          href={`https://www.facebook.com/${fb_uid}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {fullname}
        </a>
        <a
          className="text-xs text-secondary hover:text-blue-400 text-ellipsis overflow-hidden whitespace-nowrap"
          href={`https://www.facebook.com/${fb_uid}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          Facebook
        </a>
      </div>
    </div>
  );
};

export default InfoDetailCol;
