import { TooltipPortal } from "@radix-ui/react-tooltip";
import { ICellRendererParams } from "ag-grid-community";
import StarRating from "assets/icons/StartIcon";
import { Box } from "components/Box";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";

const RatingColumn = (params: ICellRendererParams | any) => {
  const { rating } = params.data || params;

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger>
          <Box className="gap-1">
            <StarRating rating={rating} />
          </Box>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent children={"Rating: " + rating.toFixed(1)} />
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};

export default RatingColumn;
