import { useContext } from "react";
import { ICellRendererParams } from "ag-grid-community";
import { ModalContext } from "providers/Modal";

import { Button } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import BuySocialAudience from "components/Transaction/BuySocialAudience";

import LABEL from "constants/label";
import { SOCIAL_DATA_LABEL } from "constants/socialData/label";
import { Box } from '../../../components/Box';

interface Props {
  params: ICellRendererParams;
  onRefresh: () => void;
}
const AddAudienceColumn = ({ params, onRefresh }: Props) => {
  const modal = useContext(ModalContext);
  const { is_aud_added, is_ds_added, size } = params.data;

  const handleAddAudience = (isDataset: boolean) => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      title: "",
      content: <BuySocialAudience audience={params.data} onRefresh={onRefresh} isDataset={isDataset}/>,
      footer: "",
      className: "max-w-[680px]",
      isShowTitle: false,
    }));
  };

  const handleClick = (isDataset:boolean) => {
    !size
      ? toast({
          title: SOCIAL_DATA_LABEL.message_audience_empty,
          status: "warning",
          duration: 3000,
        })
      : handleAddAudience(isDataset);
  };

  return (
    <Box className="gap-4 justify-start">
      <Button
        className="bg-gray-100 text-primary font-semibold rounded-lg hover:bg-primary-hover hover:text-white"
        variant="default"
        disabled={is_ds_added}
        onClick={() => handleClick(true)}
      >
        {LABEL.buy_dataset}
      </Button>
      <Button
        className="bg-gray-100 text-primary font-semibold rounded-lg hover:bg-primary-hover hover:text-white"
        variant="default"
        disabled={is_aud_added}
        onClick={()=>handleClick(false)}
      >
        {LABEL.buy_audience}
      </Button>
    </Box>
  );
};

export default AddAudienceColumn;
