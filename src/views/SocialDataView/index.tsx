import { useSearchParams } from "react-router-dom";
import omitBy from "lodash/omitBy";
import { useAppSelector } from "store";
import { RiFilter2Line } from "@remixicon/react";

import HeaderWrapper from "./components/Header";
import TableTrending from "./components/TableTrending";
import CategoriesWrapper from "./components/Categories";
import SearchModal from "./components/SearchModal";
import BreadCrumb from "components/Breadcrumb";
import ButtonRequest from "components/Button/ButtonRequest";
import SearchAndFilterResult from "./components/SearchAndFilterResult";

import { SOCIAL_DATA_LABEL } from "constants/socialData/label";
import FilterOptions from "./components/Header/FilterOptions";
import ScrollButton from "components/ScrollButton";
import { Box } from '../../components/Box';
import { cn } from '../../utils/utils';
import { But<PERSON> } from '../../components/ui/button';
import { TutorialIcon } from '../../assets/icons/TutorialIcon';
import { ModalContext } from '../../providers/Modal';
import useOutsideClick from '../../hooks/useClickOutSide';
import handleCloseModal from '../../utils/handleCloseModal';
import { useContext, useRef } from 'react';
import { RiCloseLine } from '@remixicon/react';

export interface ParamsProps {
  limit?: string;
  page?: string;
  category__in?: string;
  type__in?: string;
  package__in?: string;
  q?: string;
  order_by?: string;
  [key: string]: any;
}

const SocialDataPage = () => {
  const { loading } = useAppSelector((state) => state.category);
  const [searchParams, setSearchParams] = useSearchParams();
  const params: ParamsProps = Object.fromEntries(searchParams.entries());

  const modal = useContext(ModalContext);
  const ref = useRef(null);
  const refSecond = useRef(null);

  const handleFilter = (paramsFilter: any) => {
    const _paramsFilter = omitBy(paramsFilter, (x) => x === undefined);
    setSearchParams({ q: params?.q || "", ..._paramsFilter, page: "1" });
  };

  const openSearchAndFilterResult = !!searchParams.size;

  const propsFilter = {
    isFilterType: true,
    isFilterCategory: true,
    isFilterPackage: true,
    params,
    handleFilter,
  };


  useOutsideClick(ref, refSecond, () => {
    if (!!modal?.dataDialog?.isPopupVideo) {
      handleCloseModal(modal);
    }
  });
  const handleOpenTutorial =()=>{
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      isPopupVideo: true,
      content: (
        <div ref={refSecond}>
          <div className="text-right">
            <Button
              onClick={() => handleCloseModal(modal)}
              className="p-[10px] text-sm font-semibold mb-1 bg-transparent hover:bg-transparent text-primary"
            >
              <RiCloseLine size={20} />
              Close
            </Button>
          </div>
          <iframe
            src="https://www.youtube.com/embed/VAocWxv0fwI?autoplay=1"
            title="Big360 - Social Data"
            className="rounded-2xl w-full max-w-[920px] aspect-video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
        </div>
      ),
      className: 'max-w-[936px] !p-2 max-lg:max-w-[calc(100%-12px)]'
    } ))
  }

  return (
    <>
      <BreadCrumb />
      <HeaderWrapper
        className="flex-col gap-3 sm:flex-col-reverse max-[768px]:mt-8 items-start md:flex-row"
        leftChildren={{
          title: openSearchAndFilterResult ? 'Search for' :
            <div className="flex gap-2" ref={ref}>
              <h1>{SOCIAL_DATA_LABEL.title}</h1>
              <Button
                onClick={() => handleOpenTutorial()}
                className="flex items-center justify-center bg-[#F0F0F0] h-[36px] gap-1 p-2 rounded-xl	hover:bg-[#F0F0F0]"
              >
                <TutorialIcon /><p className="text-[#8F5CFF] text-xs mt-[1px]">Guide</p>
              </Button>
            </div>,
          highlight: openSearchAndFilterResult ? 'anything you want.' : '',
          subTitle: SOCIAL_DATA_LABEL.subTitle
        }}
        rightChildren={<ButtonRequest />}
      />
      <SearchModal params={params} setSearchParams={setSearchParams} />
      <div
        className={cn(
          'rounded-xl max-lg:mt-6 max-[768px]:shadow-sm transition-all duration-300 max-[768px]:h-[256px]'
        )}
      >
        <Box className="flex min-[768px]:hidden items-center py-[10px] px-2 ">
          <div className="flex gap-1 font-semibold text-sm text-[#515667]">
            <RiFilter2Line size={20} color={'#515667'} className="mr-1 block xl:hidden" />
            <span>Filter</span>
          </div>
        </Box>
        <FilterOptions
          className="flex gap-4 max-md:p-2 min-lg:mt-6 flex-col items-start max-[768px]:mt-0 md:flex-row [&>div]:w-full"
          {...propsFilter}
        />
      </div>
      {openSearchAndFilterResult && <SearchAndFilterResult params={params} />}
      <CategoriesWrapper
        params={params}
        setSearchParams={setSearchParams}
        loading={loading}
        isShow={openSearchAndFilterResult}
      />
      <TableTrending isShow={openSearchAndFilterResult} />
      <ScrollButton />
    </>
  );
};

const SocialDataView = () => {
  return <SocialDataPage />;
};
export default SocialDataView;
