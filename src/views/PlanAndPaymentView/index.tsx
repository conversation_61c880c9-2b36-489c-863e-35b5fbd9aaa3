import HeaderWrapper from "components/Header";
import PLAN_LABEL from "constants/PlanPayment/label";
import Container from "./components/Container";
import { Box } from "components/Box";

const PlanAndPaymentView = () => {
  return (
    <Box variant="col-start">
      <HeaderWrapper
        className="mt-0"
        leftChildren={{
          title: PLAN_LABEL.title,
          subTitle: PLAN_LABEL.subtitle,
        }}
      />
      <Container />
    </Box>
  );
};

export default PlanAndPaymentView;
