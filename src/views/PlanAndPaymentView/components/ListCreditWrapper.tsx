import { Box } from "components/Box";
import BuyCredit from "components/Transaction/CreditModal";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";

const ListCreditWrapper = () => {
  return (
    <Box variant="col-start" className="p-6 w-full rounded-2xl bg-custom-secondary">
      <div className="text-primary font-medium text-lg">{USER_PROFILE_LABEL.CREDIT.buy_credit}</div>
      <BuyCredit />
    </Box>
  );
};

export default ListCreditWrapper;
