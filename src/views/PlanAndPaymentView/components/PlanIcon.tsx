const PlanIcon = ({ type }: { type: "TRIAL" | "BASIC" | "PRO" }) => {
  return type == "TRIAL" ? TRIAL_ICON : type == "BASIC" ? BASIC_ICON : PRO_ICON;
};

export default PlanIcon;

const TRIAL_ICON = (
  <svg xmlns="http://www.w3.org/2000/svg" width="57" height="56" viewBox="0 0 57 56" fill="none">
    <circle cx="28.6665" cy="27.9999" r="23.3345" fill="#E2DAFF" />
    <g filter="url(#filter0_dd_325_1429)">
      <rect
        x="28.6665"
        y="19.7516"
        width="11.6647"
        height="11.6647"
        transform="rotate(45 28.6665 19.7516)"
        fill="url(#paint0_radial_325_1429)"
      />
    </g>
    <defs>
      <filter
        id="filter0_dd_325_1429"
        x="-19.5815"
        y="-4.24835"
        width="96.4961"
        height="96.4964"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="16" />
        <feGaussianBlur stdDeviation="20" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.886275 0 0 0 0 0.854902 0 0 0 0 1 0 0 0 1 0"
        />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_325_1429" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="8" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.647059 0 0 0 0 0.521569 0 0 0 0 1 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="effect1_dropShadow_325_1429"
          result="effect2_dropShadow_325_1429"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_325_1429"
          result="shape"
        />
      </filter>
      <radialGradient
        id="paint0_radial_325_1429"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(46.5319 33.9709) rotate(-139.716) scale(21.9915)"
      >
        <stop offset="0.24" stopColor="#E2DAFF" />
        <stop offset="1" stopColor="white" />
      </radialGradient>
    </defs>
  </svg>
);
const BASIC_ICON = (
  <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none">
    <circle cx="28" cy="28" r="23.3345" fill="#E3DAFF" />
    <g filter="url(#filter0_dd_325_1768)">
      <path
        d="M29.0374 33.1228V35.2617H34.2243V37.3364H21.7757V35.2617H26.9626V33.1228C22.8688 32.6123 19.701 29.1201 19.701 24.8879V18.6637H36.299V24.8879C36.299 29.1201 33.1312 32.6123 29.0374 33.1228ZM16.5889 20.7384H18.6636V24.8879H16.5889V20.7384ZM37.3364 20.7384H39.4111V24.8879H37.3364V20.7384Z"
        fill="url(#paint0_radial_325_1768)"
      />
    </g>
    <defs>
      <filter
        id="filter0_dd_325_1768"
        x="-23.4111"
        y="-5.3363"
        width="102.822"
        height="98.6727"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="16" />
        <feGaussianBlur stdDeviation="20" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.886275 0 0 0 0 0.854902 0 0 0 0 1 0 0 0 1 0"
        />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_325_1768" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="8" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.647059 0 0 0 0 0.521569 0 0 0 0 1 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="effect1_dropShadow_325_1768"
          result="effect2_dropShadow_325_1768"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_325_1768"
          result="shape"
        />
      </filter>
      <radialGradient
        id="paint0_radial_325_1768"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(51.5429 41.4255) rotate(-145.26) scale(39.9429 37.9214)"
      >
        <stop offset="0.24" stopColor="#E2DAFF" />
        <stop offset="1" stopColor="white" />
      </radialGradient>
    </defs>
  </svg>
);
const PRO_ICON = (
  <svg xmlns="http://www.w3.org/2000/svg" width="57" height="56" viewBox="0 0 57 56" fill="none">
    <circle cx="28.333" cy="27.9998" r="23.3345" fill="#E2DAFF" />
    <g filter="url(#filter0_dd_325_1599)">
      <path
        d="M21.6854 19.25H34.9803C35.2787 19.25 35.5591 19.3928 35.7346 19.6341L39.3001 24.5367C39.431 24.7166 39.4165 24.9639 39.2657 25.1273L28.6755 36.5999C28.5008 36.7892 28.2058 36.801 28.0165 36.6263C28.0074 36.6179 27.9986 36.6091 27.9902 36.5999L17.4 25.1273C17.2492 24.9639 17.2347 24.7166 17.3656 24.5367L20.9311 19.6341C21.1066 19.3928 21.387 19.25 21.6854 19.25Z"
        fill="url(#paint0_radial_325_1599)"
      />
    </g>
    <defs>
      <filter
        id="filter0_dd_325_1599"
        x="-22.7236"
        y="-4.75"
        width="102.113"
        height="97.5"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="16" />
        <feGaussianBlur stdDeviation="20" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.886275 0 0 0 0 0.854902 0 0 0 0 1 0 0 0 1 0"
        />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_325_1599" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="8" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.647059 0 0 0 0 0.521569 0 0 0 0 1 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="effect1_dropShadow_325_1599"
          result="effect2_dropShadow_325_1599"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect2_dropShadow_325_1599"
          result="shape"
        />
      </filter>
      <radialGradient
        id="paint0_radial_325_1599"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(51.144 40.5823) rotate(-146.147) scale(38.2946 35.9174)"
      >
        <stop offset="0.24" stopColor="#E2DAFF" />
        <stop offset="1" stopColor="white" />
      </radialGradient>
    </defs>
  </svg>
);
