import { Button } from "components/ui/button";
import { Box } from "components/Box";
import DiscountTag from "components/DiscountTag";

import LABEL from "constants/label";
import { cn } from "utils/utils";
import { CREDIT_PLANS } from "constants/UserProfile";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import { paymentAPI } from "apis/plansAndPayment";
import { useState, useContext } from "react";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import { ModalContext } from '../../../providers/Modal';
import handleCloseModal from '../../../utils/handleCloseModal';
import { tracking } from '../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../store';

const CreditItem = () => {
  const [loadingCode, setLoadingCode] = useState<string | null>(null);
  const modal = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);

  const handleBuyCredit = async (credit_code: string, price: number) => {
    setLoadingCode(credit_code);
    const response = await paymentAPI.createPaymentLink({
      credit_code: credit_code,
      type: "credit",
    });
    if (response && response.data) {
      handleCloseModal(modal);
      window.open(response.data.url);
    }
    tracking({
      eventName: 'buy_credits_button',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          credits_amount: price,
        })
      }
    });
    setLoadingCode(null);
  };

  return CREDIT_PLANS.map((item) => {
    const { count, price, discount, code } = item;
    const discountPrice = price - (price * discount) / 100;

    return (
      <Box
        variant="col-start"
        className="w-full px-3 py-2 rounded-2xl bg-custom-primary shadow-sm gap-3 items-center relative"
        key={item.code}
      >
        <Box
          variant="col-start"
          className="rounded-sm p-3 gap-1 text-brand-strong items-center bg-custom-secondary"
        >
          <span className="font-semibold items-center text-lg leading-4">{count}</span>
          <span className="text-xs">{USER_PROFILE_LABEL.CREDIT.credits}</span>
        </Box>

        <div className="text-center">
          <div className="text-secondary text-sm line-through h-5">
            {discount > 0 ? `$${price}` : ""}
          </div>
          <div className="text-primary text-lg leading-4 font-semibold mt-1">${discountPrice}</div>
        </div>

        <Button
          variant="default"
          className={cn("p-1 text-sm w-full h-8 hover:bg-hover")}
          onClick={() => handleBuyCredit(code, price)}
          disabled={loadingCode === code}
        >
          {loadingCode === code ? <LoadingButtonIcon /> : LABEL.buy}
        </Button>
        {discount > 0 && <DiscountTag percent={discount} className="absolute top-2 -right-2" />}
      </Box>
    );
  });
};

export default CreditItem;
