import { useState } from 'react';
import { useSelector } from 'react-redux';
import { RiCheckboxCircleLine } from '@remixicon/react';
import { subscriptionStore } from 'store/redux/subscription/slice';

import { Card } from 'components/ui/card';
import { Box } from 'components/Box';
import { Button } from 'components/ui/button';
import DiscountTag from 'components/DiscountTag';
import HeaderPlanDetail from './HeaderPlanDetail';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';

import { paymentAPI } from 'apis/plansAndPayment';
import { PLAN_CODE } from 'types/Payment';

import { optionIconFilter } from 'constants/persona';
import { USER_PROFILE_LABEL } from 'constants/UserProfile/label';
import { PLAN_TYPE } from 'constants/permission/permissionPlan';
import PLAN_LABEL, { CONTENT_BASIC, CONTENT_VIP, CONTENT_TRIAL } from 'constants/PlanPayment/label';
import { cn } from 'utils/utils';
import { tracking } from '../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../store';

export interface IHeaderPlan {
  planCode: PLAN_CODE;
  planName: string;
  planPrice: number;
}

interface Props extends IHeaderPlan {
  features: IFeature[];
  isSale?: boolean;
}

interface IFeature {
  title: string;
  isNewFeature: boolean;
}


const ListPlanTrial: IFeature[] = [
  {
    title: CONTENT_TRIAL.trial,
    isNewFeature: false
  },
  {
    title: CONTENT_TRIAL.request,
    isNewFeature: false
  },
  {
    title: CONTENT_TRIAL.segment,
    isNewFeature: false
  },
  {
    title: CONTENT_TRIAL.data_processing,
    isNewFeature: false
  },
  {
    title: CONTENT_TRIAL.crm_contact,
    isNewFeature: false
  },
  {
    title: CONTENT_TRIAL.custom_audience,
    isNewFeature: false
  }
];

const ListPlanBasic: IFeature[] = [
  {
    title: CONTENT_BASIC.credit,
    isNewFeature: false
  },
  {
    title: CONTENT_BASIC.request,
    isNewFeature: false
  },
  {
    title: CONTENT_BASIC.follow_page,
    isNewFeature: false
  },
  {
    title: CONTENT_BASIC.data_processing,
    isNewFeature: false
  },
  {
    title: CONTENT_BASIC.crm_contact,
    isNewFeature: false
  },
  {
    title: CONTENT_BASIC.custom_audience,
    isNewFeature: false
  }
];

const ListPlanPro: IFeature[] = [
  {
    title: CONTENT_VIP.credit,
    isNewFeature: false
  },
  {
    title: CONTENT_VIP.request,
    isNewFeature: false
  },
  {
    title: CONTENT_VIP.follow_page,
    isNewFeature: false
  },
  {
    title: CONTENT_VIP.data_processing,
    isNewFeature: false
  },
  {
    title: CONTENT_VIP.crm_contact,
    isNewFeature: false
  },
  {
    title: CONTENT_VIP.custom_audience,
    isNewFeature: false
  }
];

const PlanInformation = (props: Props) => {
  const { last_sub, is_active } = useSelector(subscriptionStore);
  const [loading, setLoading] = useState<boolean>(false);
  const { user } = useAppSelector((state) => state.auth);

  const handleUpgradePlan = async () => {
    setLoading(true);
    const response = await paymentAPI.createPaymentLink({
      plan_code: props.planCode,
      type: 'subscription'
    });
    tracking({
      eventName: 'buy_plan_button',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({ plan_code: props.planCode })
      }
    });
    if (response && response.data) {
      window.open(response.data.url);
    }
    setLoading(false);
  };

  const isCurrentPlan = last_sub?.plan_code == props.planCode && is_active;

  /**
   *  ** User current plan difference TRIAL --> DISABLE CARD TRIAL
   *  ** User current plan in TRIAL but exp --> DISABLE CARD TRIAL
   */

  let percentage = 0;

  if (last_sub?.end_date && last_sub?.start_date) {
    const endDate = new Date(last_sub.end_date);
    const startDate = new Date(last_sub.start_date);
    const now = new Date();
    const usedTime = now.getTime() - startDate.getTime();
    const totalTime = endDate.getTime() - startDate.getTime();
    percentage = Math.min(Math.max(( usedTime / totalTime ) * 100, 0), 100);
  }

  const isDisableTrial =
    props.planCode == PLAN_TYPE.TRIAL &&
    ( last_sub?.plan_code !== PLAN_TYPE.TRIAL ||
      ( last_sub.plan_code == PLAN_TYPE.TRIAL && last_sub?.status !== 'ACTIVE' || percentage === 100 ) );

  return (
    <Card
      className={cn(
        'p-6 rounded-2xl shadow-md border border-custom-primary w-full gap-6 flex flex-col relative',
        isCurrentPlan && 'shadow-[0px_0px_20px_0px_#A585FF]',
        isDisableTrial && 'shadow-none'
      )}
    >
      {props.isSale && (
        <DiscountTag isShowDiscount percent={20} className="absolute top-6 right-6" />
      )}
      <Box variant="col-start" className="flex-1">
        <HeaderPlanDetail {...props} />
        <Box variant="col-start" className={cn('gap-3 text-secondary')}>
          {props.features.map((item: IFeature, index: number) => {
            const { title, isNewFeature } = item;
            return <Box className="text-sm font-normal gap-2" key={index}>
              <RiCheckboxCircleLine size={20} color={optionIconFilter.color_active} />
              <span className="flex-1">{title}{isNewFeature && <span className="text-[#f53e3e] ml-1">*</span>}</span>
            </Box>;
          })}
        </Box>
      </Box>
      <Button
        variant="default"
        disabled={loading}
        className={cn(
          'rounded-xl font-medium text-sm hover:bg-hover',
          isCurrentPlan && 'bg-brand-light text-brand-default opacity-1 pointer-events-none',
          isDisableTrial && 'bg-custom-disable border text-custom-disable pointer-events-none '
        )}
        onClick={() => handleUpgradePlan()}
      >
        {loading && <LoadingButtonIcon />}
        {!loading &&
          ( isCurrentPlan
            ? 'Current Plan'
            : isDisableTrial
              ? 'Your trial plan has ended'
              : USER_PROFILE_LABEL.BILLING.purchase )}
      </Button>
    </Card>
  );
};

const ListPlanWrapper = () => {
  return (
    <div className="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-6 w-full">
      <PlanInformation
        planCode="TRIAL"
        planName={PLAN_LABEL.plan.trial}
        planPrice={0}
        features={ListPlanTrial}
      />
      <PlanInformation
        planCode="BASIC"
        planName={PLAN_LABEL.plan.basic}
        planPrice={9.99}
        features={ListPlanBasic}
      />
      <PlanInformation
        planCode="PRO"
        planName={PLAN_LABEL.plan.pro}
        planPrice={99.99}
        features={ListPlanPro}
        isSale
      />
    </div>
  );
};

export default ListPlanWrapper;
