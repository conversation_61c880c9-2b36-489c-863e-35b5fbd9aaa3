import { Box } from "components/Box";
import { IHeaderPlan } from "./PlanDetail";
import PlanIcon from "./PlanIcon";

const HeaderPlanDetail = (props: IHeaderPlan & { isSale?: boolean }) => {
  const { planCode, planName, planPrice, isSale } = props;
  return (
    <Box
      variant="col-start"
      className="gap-2 items-start justify-center  pb-3 border-b border-custom-primary"
    >
      <PlanIcon type={planCode} />
      <h4 className="text-sm font-semibold uppercase text-brand-default " children={planName} />
      <div className="text-[36px] text-primary  font-semibold">
        ${planPrice}
        {isSale && (
          <span className="text-sm text-tertiary line-through font-normal ml-2">
            ${Math.round(planPrice + (planPrice * 20) / 100)}
          </span>
        )}
      </div>
    </Box>
  );
};

export default HeaderPlanDetail;
