import { useContext, useEffect } from "react";
import { YourPageContext } from "../../YourPageView/YourPageContext";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Box } from "components/Box";
import BackToYourPage from "components/YourPage/BackToYourPage";
import FollowDetail from "views/YourPageView";
import EmptyYourFollow from "../../YourPageView/components/EmptyYourFollow";
import ListPageWrapper from "../../YourPageView/components/ListPageWrapper";
import NoHavePermission from "../components/NoHavePermission";

import useFilters from "hooks/useFilters";
import useFeatures from "hooks/useFeatures";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import NoticeLimitRequest from "components/Transaction/NoticeLimitRequest";

const YourFollowTab = () => {
  const context = useContext(YourPageContext);
  const showDetailContext = useContext(ShowDetailContext);
  const { setSearchParams } = useFilters();

  //permission check
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.SCD_VIEW_YOUR_PAGE)?.is_enabled;
  const usage_limit = useFeatures(FEATURE_PERMISSION_KEY.SCD_LIVEPOST_FOLLOW_PAGE)?.extra_limit;

  useEffect(() => {
    setSearchParams({});
    showDetailContext?.setShowDetail((prev) => ({ ...prev, isShow: false }));
    isEnable && context?.handleGetListFollow();
  }, []);

  const length = context?.listPageFollow.total || 0;
  const isShowDetail = showDetailContext?.showDetail.isShow;

  return isEnable ? (
    <>
      {isShowDetail && <BackToYourPage />}
      <Box variant="col-center" className="bg-custom-primary mt-6">
        <NoticeLimitRequest
          denominator={usage_limit?.concurrent || 0}
          used_count={length}
          message={
            <div>
              You used
              <strong className="px-1">
                {length}/{usage_limit?.concurrent} free
              </strong>
              follows for pages
            </div>
          }
        />
        {length > 0 && (
          <>
            {isShowDetail && <FollowDetail />}
            {!isShowDetail && (
              <Box variant="col-start" className="gap-6 w-full">
                <ListPageWrapper data={context?.listPageFollow?.data} typeCard="follow" />
              </Box>
            )}
          </>
        )}
        {length == 0 && <EmptyYourFollow />}
      </Box>
    </>
  ) : (
    <NoHavePermission />
  );
};
export default YourFollowTab;
