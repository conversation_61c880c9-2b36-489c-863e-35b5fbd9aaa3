import { useContext, useEffect } from "react";
import { YourPageContext } from "views/YourPageView/YourPageContext";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Box } from "components/Box";
import BackToYourPage from "components/YourPage/BackToYourPage";
import FollowDetail from "views/YourPageView";
import EmptyYourFollow from "views/YourPageView/components/EmptyYourFollow";
import ListPageWrapper from "../../YourPageView/components/ListPageWrapper";
import NoHavePermission from "../components/NoHavePermission";

import useFilters from "hooks/useFilters";
import useFeatures from "hooks/useFeatures";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";

const ArchivesTab = () => {
  const context = useContext(YourPageContext);
  const showDetailContext = useContext(ShowDetailContext);
  const { setSearchParams } = useFilters();

  //permission check
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.SCD_VIEW_YOUR_PAGE)?.is_enabled;

  useEffect(() => {
    setSearchParams({});
    showDetailContext?.setShowDetail((prev) => ({ ...prev, isShow: false }));
    isEnable && context?.handleGetListArchives();
  }, []);

  const length = context?.archives.total || 0;
  const isShowDetail = showDetailContext?.showDetail.isShow;

  return isEnable ? (
    <>
      {isShowDetail && <BackToYourPage />}
      <Box variant="col-center" className="bg-[#FDFDFD] mt-6">
        {length > 0 && (
          <>
            {isShowDetail && <FollowDetail />}
            {!isShowDetail && (
              <Box variant="col-start" className="gap-6 w-full">
                <ListPageWrapper data={context?.archives.data} typeCard="archive" />
              </Box>
            )}
          </>
        )}
        {length == 0 && <EmptyYourFollow />}
      </Box>
    </>
  ) : (
    <NoHavePermission />
  );
};

export default ArchivesTab;
