import { useContext, useEffect } from "react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Box } from "components/Box";
import Container from "../components/Container";
import LivePostBrandDetail from "views/LivePageDetail";
import BackToYourPage from "components/YourPage/BackToYourPage";
import NoHavePermission from "../components/NoHavePermission";
import SearchAndFilter from "../components/SearchAndFilter/SearchAndFilter";

import useGetListPost from "hooks/useGetListPost";
import useFeatures from "hooks/useFeatures";
import useCheckPermissionFeature from "hooks/useCheckPermissionFeature";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import ShowResult from '../../SocialDataDetail/components/ShowResult';
import useFilters from '../../../hooks/useFilters';
import NoLivestreamMatch from '../../YourPageView/components/NoLivestreamMatch';

const CommunityLive = () => {
  const {paramsNoPage} = useFilters();
  const context = useContext(ShowDetailContext);
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.SCD_VIEW_LIVEPOST_COMMUNITY)?.is_enabled;
  useCheckPermissionFeature(isEnable);

  // Check permission
  const { data, lastElementRef } = useGetListPost({ type: 'live', paramsNoPage });

  useEffect(() => {
    context?.setShowDetail((prev) => ({ ...prev, isShow: false }));
  }, []);

  const isShowDetail = context?.showDetail.isShow;
  return isEnable ? (
    <>
      {isShowDetail && isEnable && (
        <>
          <BackToYourPage />
          <LivePostBrandDetail />
        </>
      )}
      {!isShowDetail && data && isEnable && (
        <Box variant="col-start" className="w-full gap-0">
          <SearchAndFilter
            isCategory
            isLive
            isDateTime
            isEmpty={data?.posts.length === 0}
            className="border-none shadow-none p-0 mt-6"
          />
          <div className='mt-[18px] mb-[24px]'>
            <ShowResult total={data.total}/>
          </div>
          <Container
            data={data?.posts}
            total={data?.total}
            lastElementRef={lastElementRef}
            loading={data.loading}
          />
        </Box>
      )}
    </>
  ) : (
    paramsNoPage && Object.keys(paramsNoPage).length > 0 && data.posts.length == 0 ? <NoLivestreamMatch /> : <NoHavePermission isDisableSVG />
  );
};

export default CommunityLive;
