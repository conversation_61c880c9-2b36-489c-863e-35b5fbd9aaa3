import { Box } from "components/Box";
import AvatarByName from "components/AvatarByName";
import { CommentDetail } from "types/CrawlService";
import useAvatar from "hooks/useAvatar";
import timeDifference from "utils/LiveAdsPost/timeDifference";

const CommentItem = ({ comment }: { comment: CommentDetail }) => {
  const { text_content = "", phone_number, actor_id } = comment;
  const { avatar } = useAvatar({ type: "profile", uid: comment.actor_id });

  const time_comment = timeDifference(comment.comment_time);

  const handleCheckPhone = () => {
    const phoneIndex = phone_number !== null ? text_content.indexOf(phone_number) : -1;

    if (phoneIndex !== -1) {
      const beforePhone = comment.text_content.slice(0, phoneIndex);
      const afterPhone = comment.text_content.slice(phoneIndex + phone_number.length);

      return { beforePhone, afterPhone };
    }
    return { beforePhone: comment.text_content, afterPhone: undefined };
  };
  const { beforePhone, afterPhone } = handleCheckPhone();

  return (
    <Box variant="default" className="items-start justify-start gap-2 w-full p-2">
      <AvatarByName urlImage={avatar.url} name={comment.actor_name} className="w-8 h-8" />
      <Box variant="col-start" className="flex-1 gap-1 bg-[#F0F2F5] rounded-sm px-3 py-[10px]">
        <Box className="gap-2">
          <a
            href={`https://www.facebook.com/${actor_id}`}
            target='_blank'
            className="text-primary font-semibold text-xs"
            children={comment.actor_name ? comment.actor_name : "--"}
          />
          <span className="text-secondary text-[10px]">{time_comment}</span>
        </Box>
        {afterPhone !== undefined ? (
          <span className="text-secondary text-sm">
            {beforePhone}
            <b
              style={{
                fontFamily: 'Mulish'
              }}
            >
              {comment.phone_number}
            </b>
            {afterPhone}
          </span>
        ) : (
          <span
            className="text-secondary text-sm"
            style={{
              fontFamily: 'Mulish'
            }}
          >
            {comment.text_content}
          </span>
        )}
      </Box>
    </Box>
  );
};

export default CommentItem;
