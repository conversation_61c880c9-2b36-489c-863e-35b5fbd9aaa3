import { Box } from "components/Box";
import PostInformation from "./PostInformation";

import { ILivePostItem } from "types/CrawlService";
import { getPublishDate } from "utils/CrawlService";
import { cn } from "utils/utils";

interface Props {
  post: ILivePostItem;
  className?: string;
  avatar?: string;
  isShowContent?: boolean;
}

const QuickViewImageLeft = ({ post, className, isShowContent = true }: Props) => {
  const { actor_name, thumbnail, publish_time, playable_duration } = post;

  const { publishDate, timeStart, timeEnd } = getPublishDate({
    date: publish_time,
    duration: playable_duration,
  });

  return (
    <Box className={cn("bg-black relative pt-[45px] md:pt-0", className)}>
      <img
        className={cn("w-full object-cover h-[343px] md:h-[650px]", isShowContent && "")}
        src={thumbnail}
        alt={actor_name}
        width="100%"
      />
      <PostInformation
        post={{ ...post }}
        publishDate={publishDate}
        timeStart={timeStart}
        timeEnd={timeEnd}
      />
    </Box>
  );
};

export default QuickViewImageLeft;
