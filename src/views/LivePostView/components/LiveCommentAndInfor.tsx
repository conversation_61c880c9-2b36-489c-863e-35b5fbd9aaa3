import { Link } from "react-router-dom";

import { But<PERSON> } from "components/ui/button";
import { Box } from "components/Box";
import PageInformation from "components/YourPage/PageInformation";
import LiveChatWrapper from "./LiveChatWrapper";

import { FB_SERVICE_PATH } from "types/Router";
import { ILivePostItem, IQuickViewItem } from "types/CrawlService";
import { cn } from "utils/utils";

const LiveCommentAndInfor = ({ id, post, avatar }: IQuickViewItem<ILivePostItem>) => {
  const { actor_id, audience_id, total_lead } = post;
  return (
    <Box
      variant="col-start"
      className="p-3 gap-3 flex-1 mt-0 overflow-y-auto max-h-[650px] w-full md:p-6 md:gap-4"
    >
      <div className="text-secondary text-md font-medium" children="About the advertiser" />
      <PageInformation id={actor_id} avatar={avatar || ""} />
      <Button
        variant="main"
        className={cn(
          "w-full rounded-xl font-medium text-md px-3 py-1",
          total_lead
            ? "bg-primary text-white pointer-events-auto"
            : "bg-custom-disable text-custom-disable hover:bg-custom-disable pointer-events-none"
        )}
        children={
          <Link
            to={`/${FB_SERVICE_PATH.LIVE}/${FB_SERVICE_PATH.LIVE_COMMUNITY}/${audience_id}`}
            children={total_lead ? "See Total Lead" : "Updating"}
            className="w-full leading-8"
          />
        }
      />
      <LiveChatWrapper id={id} />
    </Box>
  );
};

export default LiveCommentAndInfor;
