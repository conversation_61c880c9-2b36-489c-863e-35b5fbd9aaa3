import { Box } from "components/Box";

const StatusBadge = ({ isLive }: { isLive: boolean }) => {
  return (
    <>
      {isLive && (
        <Box className="bg-error-default text-white  rounded-full gap-1 justify-start px-2 py-1">
          <div className="w-2 h-2 rounded-full bg-green-500" />
          <span className="text-xs font-semibold" children="LIVE" />
        </Box>
      )}
      {!isLive && (
        <Box className="bg-tertiary justify-start py-1 px-2 gap-1 rounded-full">
          <div className="w-2 h-2 rounded-full bg-gray-500" />
          <span className="text-xs text-tertiary font-semibold" children="Lived" />
        </Box>
      )}
    </>
  );
};

export default StatusBadge;
