import NoPermissionSvgLivePage from "assets/icons/NoPermissionSvgLivePage";
import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { LIVE_COMMENT_LABEL } from "constants/LiveAdsPost/label";
import <PERSON>BEL from "constants/label";
import { Link } from "react-router-dom";
import { PATH_DASHBOARD } from "types/path";

const NoHavePermission = ({ isDisableSVG = false }: { isDisableSVG?: boolean }) => {
  return (
    <Box variant="col-start" className="flex items-center justify-center gap-0 h-full w-full my-28">
      {!isDisableSVG && <NoPermissionSvgLivePage />}
      <div className="text-center max-w-[560px]">
        <p className="font-bold text-base text-primary-crm mb-2" children={'No Pages in Your Following List'} />
        <div className="text-secondary text-md mb-5">{LIVE_COMMENT_LABEL.message_no_per}</div>
        <Link to={`${PATH_DASHBOARD.plan[""]}`}>
          <Button variant="default" className="mt-3 text-md rounded-xl w-[220px]">
            {LABEL.upgrade}
          </Button>
        </Link>
      </div>
    </Box>
  );
};

export default NoHavePermission;
