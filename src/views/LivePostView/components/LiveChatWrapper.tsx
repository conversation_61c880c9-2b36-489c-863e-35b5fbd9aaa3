import { useEffect, useState } from "react";
import { RiMessage2Line } from "@remixicon/react";

import { Box } from "components/Box";
import { Skeleton } from "components/ui/skeleton";
import CommentItem from "./CommentItem";

import { CommentDetail } from "types/CrawlService";
import { liveCommentAPI } from "apis/livePost";
import { cn } from "utils/utils";

interface Props {
  id: string;
  showTitle?: boolean;
  isDetail?: boolean;
}
const LiveChatWrapper = ({ id, showTitle = true, isDetail = false }: Props) => {
  const [data, setData] = useState({
    data: [],
    count: 0,
    loading: false,
  });

  const [comments, setComment] = useState<CommentDetail[]>([]);
  const [params, setParams] = useState({
    limit: isDetail ? 6 : 5,
    page: 1,
  });

  const getListComment = async () => {
    setData((prev) => ({ ...prev, loading: true }));
    const res = await liveCommentAPI.getListComment({ id: id, params: params });
    if (res.data) {
      setData({ loading: false, data: res.data.items, count: res.data.count });
    }
    setData((prev) => ({ ...prev, loading: false }));
  };

  useEffect(() => {
    if (id != "") getListComment();
  }, [id, params]);

  useEffect(() => {
    setComment([...comments, ...data.data]);
  }, [data.data]);

  const handleViewMore = () => {
    params.page < totalPageCount && setParams((prev) => ({ ...prev, page: prev.page + 1 }));
  };
  const totalPageCount = Math.ceil(data.count / params.limit);

  return (
    <Box variant="col-start" className="w-full gap-3">
      {showTitle && (
        <Box className="gap-1 text-secondary">
          <RiMessage2Line color="#515667" size={16} />
          <span className="font-semibold text-sm uppercase">Live Chat</span>
        </Box>
      )}
      <Box
        variant="col-start"
        className={cn(
          "p-0 w-full gap-2 overflow-y-auto md:p-4",
          isDetail ? "max-h-[580px]" : "max-h-[510px] shadow-sm rounded-sm"
        )}
      >
        {comments.length > 0 &&
          comments.map((comment: CommentDetail) => (
            <CommentItem comment={comment} key={comment.comment_id} />
          ))}
        {totalPageCount > 1 && !data.loading && (
          <Box className="text-sm w-full">
            <span
              className={cn(
                "text-primary cursor-pointer font-semibold text-sm",
                params.page == totalPageCount && "cursor-default opacity-20"
              )}
              onClick={() => handleViewMore()}
              children="View more comments"
            />
            <span className="text-tertiary font-semibold">
              {comments.length} of {data.count}
            </span>
          </Box>
        )}
        {comments.length == 0 && <span className="text-primary">No comments</span>}
        {data.loading && <SkeletonComment />}
      </Box>
    </Box>
  );
};

export default LiveChatWrapper;

const SkeletonComment = () => (
  <Box variant="default" className="items-start justify-start gap-2 my-3">
    <Skeleton className="w-8 h-8 rounded-full " />
    <Box variant="col-start" className="flex-1 gap-1 bg-[#F0F2F5] rounded-[20px] px-3 py-[10px]">
      <Skeleton className="w-4 h-4" />
      <Skeleton className="w-full h-5" />
    </Box>
  </Box>
);
