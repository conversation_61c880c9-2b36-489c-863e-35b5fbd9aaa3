import { useEffect, useRef, useState } from "react";
import { RiCalendarLine, RiTimeLine } from "@remixicon/react";

import { Box } from "components/Box";
import PageMetrics from "../../../components/YourPage/PageMetrics";
import StatusBadge from "./StatusBadge";

import { cn } from "utils/utils";
interface Props<T> {
  post: T;
  publishDate: string;
  timeStart: string;
  timeEnd?: string;
}
const PostInformation = (props: Props<any>) => {
  const { is_live, title, post_id, comment_count, reaction_count, share_count } = props.post;
  const contentRef = useRef<HTMLDivElement>(null);

  const [showMore, setShowMore] = useState<boolean>(false);
  const [isClamped, setClamped] = useState(false);

  useEffect(() => {
    if (contentRef && contentRef.current) {
      setClamped(contentRef.current.scrollHeight > contentRef.current.clientHeight);
    }
  }, []);

  return (
    <div className="absolute left-0 right-0 w-full bottom-0 p-2 md:py-6 md:px-4 bg-[#090A0DCC] text-white">
      <Box
        variant="col-start"
        className="gap-4 px-1 py-2 md:px-3 md:py-4 w-full border-b border-custom-primary"
      >
        <Box className="text-xs w-full flex flex-col md:flex-row items-start md:items-center justify-between gap-2">
          <StatusBadge isLive={is_live} />
          <Box className="gap-4">
            <Box className="gap-1">
              <RiCalendarLine size={16} />
              <span>{props.publishDate}</span>
            </Box>
            <Box className="gap-1">
              <RiTimeLine size={16} />
              {props.timeStart} - {props.timeEnd}
            </Box>
          </Box>
        </Box>
        <div className="max-h-[500px] overflow-y-auto">
          <span
            ref={contentRef}
            className={cn("text-sm font-normal", showMore ? "line-clamp-none" : "line-clamp-4")}
            children={title}
          />
        </div>
        {isClamped && (
          <span
            className="underline text-white font-semibold text-sm uppercase cursor-pointer block"
            children={showMore ? "SHOW LESS" : "SHOW MORE"}
            onClick={() => setShowMore(!showMore)}
          />
        )}
      </Box>
      <PageMetrics
        post_id={post_id}
        comment_count={comment_count}
        reaction_count={reaction_count}
        share_count={share_count}
        className="md:py-3 text-sm px-1 py-2"
      />
    </div>
  );
};
export default PostInformation;
