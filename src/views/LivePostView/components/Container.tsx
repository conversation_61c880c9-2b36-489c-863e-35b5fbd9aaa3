import { useState } from "react";
import { RiCloseLine } from "@remixicon/react";

import { Box } from "components/Box";
import { Dialog, DialogContent } from "components/ui/dialog";
import LoadingSkeleton from "components/LiveAdsPost/LoadingSkeleton";
import ListPostWrapper from "./ListPostWrapper";
import LiveCommentAndInfor from "./LiveCommentAndInfor";
import QuickViewImageLeft from "views/LivePostView/components/QuickViewImageLeft";
import EmptyYourFollow from "views/YourPageView/components/EmptyYourFollow";

import { ILivePostItem, IQuickViewItem, IQuickViewModal } from "types/CrawlService";
import distributeIntoColumns from "utils/LiveAdsPost/distributeIntoColumns";
import { cn } from "utils/utils";
import useFilters from "../../../hooks/useFilters";
import NoLivestreamMatch from "../../YourPageView/components/NoLivestreamMatch";

interface Props {
  data: ILivePostItem[];
  total: number;
  loading?: boolean;
  lastElementRef: any;
}

const Container = ({ data, total, lastElementRef, loading }: Props) => {
  const { paramsNoPage } = useFilters();
  const { columnA, columnB, columnC } = distributeIntoColumns(data);
  const [quickView, setQuickView] = useState<IQuickViewModal<ILivePostItem>>({
    isOpen: false,
    id: "",
    data: undefined,
  });

  const handleClick = ({ id, post, avatar }: IQuickViewItem<ILivePostItem>) => {
    setQuickView((prev) => ({ ...prev, isOpen: true, id: id, data: post, avatar: avatar }));
  };

  return (
    <Box variant="col-start" className="w-full">
      <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 items-start justify-center gap-6">
        <ListPostWrapper data={columnA} handleClick={handleClick} />
        <ListPostWrapper data={columnB} handleClick={handleClick} />
        <ListPostWrapper data={columnC} handleClick={handleClick} />
      </div>

      {/* show skeleton */}
      {(data.length > 0 || loading) && (
        <div
          ref={lastElementRef}
          className={cn(
            "w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 items-start justify-center gap-6 py-6",
            data.length == total && "hidden"
          )}
        >
          {[1, 2, 3].map((item) => (
            <LoadingSkeleton key={item} />
          ))}
        </div>
      )}

      {/* show data empty */}

      {paramsNoPage && Object.keys(paramsNoPage).length > 0 && data.length == 0 ? (
        <NoLivestreamMatch />
      ) : (
        data.length == 0 &&
        !loading && (
          <div className="w-full text-center">
            <EmptyYourFollow />
          </div>
        )
      )}

      {/* show modal quick view */}
      {quickView.data && (
        <Dialog
          open={quickView.isOpen}
          onOpenChange={(open) => setQuickView((prev) => ({ ...prev, isOpen: open }))}
        >
          <DialogContent
            className="h-auto max-w-screen-xl p-0 md:rounded-sm shadow-sm m-0 border-none"
            closeIcon={<RiCloseLine className="text-white md:text-inherit" />}
          >
            <div className="grid grid-cols-1 md:grid-cols-2">
              <QuickViewImageLeft
                avatar={quickView.avatar}
                post={quickView.data}
                className="max-h-[650px]"
              />
              <LiveCommentAndInfor
                avatar={quickView.avatar}
                id={quickView.id}
                post={quickView?.data}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </Box>
  );
};

export default Container;
