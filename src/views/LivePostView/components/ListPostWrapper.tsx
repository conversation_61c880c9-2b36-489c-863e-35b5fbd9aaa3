import { ILivePostItem, IQuickViewItem } from "types/CrawlService";
import CardOverviewWrapper from "./Card/CardOverview";
import { Box } from "components/Box";

interface ListPostWrapper {
  data: ILivePostItem[];
  handleClick?: ({ id, post, avatar }: IQuickViewItem<ILivePostItem>) => void;
}

const ListPostWrapper = ({ data, handleClick }: ListPostWrapper) => (
  <Box variant="col-start" className="w-full">
    {data.map((post, index) => (
      <CardOverviewWrapper handleClick={handleClick} key={post.actor_id + index} post={post} />
    ))}
  </Box>
);

export default ListPostWrapper;
