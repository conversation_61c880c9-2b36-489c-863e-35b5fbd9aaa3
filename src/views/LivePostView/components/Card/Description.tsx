import { RiCalendarLine, RiPlayLine, RiTimer2Line } from "@remixicon/react";
import { Box } from "components/Box";
import { ILivePostItem } from "types/CrawlService";
import { getPublishDate } from "utils/CrawlService";
import { fNumberToString } from "utils/number";
import StatusBadge from "../StatusBadge";

interface DescriptionCard
  extends Partial<
    Pick<ILivePostItem, "publish_time" | "playable_duration" | "play_count" | "is_live">
  > {}

const Description = ({ publish_time, playable_duration, play_count, is_live }: DescriptionCard) => {
  const { publishDate, timeStart, timeEnd } = getPublishDate({
    date: publish_time,
    duration: playable_duration,
  });

  return (
    <div className="w-full">
      <Box className="justify-between">
        <StatusBadge isLive={is_live || false} />
        {play_count && play_count > 0 ? (
          <Box className="justify-end gap-0 text-secondary text-xs">
            <RiPlayLine size={18} />
            <span children={fNumberToString(play_count) + " plays"} />
          </Box>
        ) : (
          ""
        )}
      </Box>
      <Box className="gap-1 text-secondary  text-xs mt-2">
        {publish_time && (
          <Box className="justify-between w-full">
            <Box className="justify-start gap-1">
              <RiCalendarLine size={16} color="#0F132499" />
              <span>{publishDate}</span>
            </Box>

            {playable_duration && (
              <Box className="flex-1 justify-end gap-1">
                <RiTimer2Line size={16} color="#0F132499" />
                <div children={`${timeStart}  ${is_live ? "- Now" : "-" + timeEnd}`} />
              </Box>
            )}
          </Box>
        )}
      </Box>
    </div>
  );
};

export default Description;
