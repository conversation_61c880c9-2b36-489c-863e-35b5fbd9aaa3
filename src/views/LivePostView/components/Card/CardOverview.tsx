import { <PERSON> } from "react-router-dom";
import { RiEyeLine, RiFocus3Line } from "@remixicon/react";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import AvatarByName from "components/AvatarByName";
import PagePreview from "../../../../components/YourPage/PagePreview";
import Description from "./Description";

import useAvatar from "hooks/useAvatar";
import { ILivePostItem, IQuickViewItem } from "types/CrawlService";
import { FB_SERVICE_PATH } from "types/Router";
import { cn } from "utils/utils";
import { fNumberToString } from "utils/number";
import { tracking } from '../../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../../store';

interface Props {
  post: ILivePostItem;
  className?: string;
  avatar?: string;
  handleClick?: ({ id, post, avatar }: IQuickViewItem<ILivePostItem>) => void;
}

const CardOverview = ({ post, className, handleClick }: Props) => {
  const { post_id, actor_name, title, publish_time, thumbnail, actor_id, total_lead, audience_id } =
    post;
  const { avatar } = useAvatar({ type: "page", uid: actor_id });
  const { user } = useAppSelector((state) => state.auth);


  const handleTracking = () => {
    handleClick && handleClick({ id: post_id, post, avatar: avatar.url });
    tracking({
      eventName: 'preview_data',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          data_id: post_id
        })
      }
    });
  };
  return (
    <Box
      variant="col-start"
      className={cn(
        "p-4 rounded-2xl bg-custom-primary border-none !shadow-medium w-full gap-3",
        className
      )}
    >
      <Box className="gap-2 w-full">
        <AvatarByName urlImage={avatar.url} name={actor_name} className="w-12 h-12" />
        <PagePreview id={actor_id} post_id={post_id} avatar={avatar.url} page_name={actor_name} />
        {handleClick && (
          <div
            className="cursor-pointer hover:opacity-70"
            onClick={handleTracking}
            children={
              <div className="p-1 bg-custom-secondary rounded-sm">
                <RiEyeLine className="text-secondary" size={18} />
              </div>
            }
          />
        )}
      </Box>
      <Description
        publish_time={publish_time}
        play_count={post.play_count}
        playable_duration={post.playable_duration}
        is_live={post.is_live}
      />
      <img
        className={cn("rounded-xl w-full h-full object-cover mt-2")}
        src={thumbnail ? thumbnail : "https://cdn.adslibrary.ai/images/6e888ad1ac401ea5.png"}
        alt={actor_name}
        width="100%"
      />
      <div
        className="h-fit text-sm text-secondary line-clamp-3 overflow-hidden text-ellipsis"
        children={title}
      />

      <Link
        className={cn("w-full", !total_lead && "pointer-events-none")}
        to={`/${FB_SERVICE_PATH.LIVE}/${FB_SERVICE_PATH.LIVE_COMMUNITY}/${audience_id}`}
        children={
          <Button
            variant="main"
            className={cn(
              "w-full font-medium rounded-xl",
              total_lead
                ? "bg-primary cursor-pointer text-custom-brand"
                : "bg-custom-disable cursor-default text-custom-disable"
            )}
          >
            <RiFocus3Line size={18} className="mr-1" />
            <span
              children={`Total Leads: ${total_lead ? fNumberToString(total_lead) : "Updating"}`}
            />
          </Button>
        }
      />
    </Box>
  );
};

const CardOverviewWrapper = (props: Props) => {
  const { avatar } = useAvatar({ type: "page", uid: props.post.actor_id });
  return <CardOverview avatar={avatar.url} {...props} />;
};

export default CardOverviewWrapper;
