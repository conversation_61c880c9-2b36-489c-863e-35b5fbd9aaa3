import { LegacyRef, useEffect, useState } from "react";
import { RiSearchLine } from "@remixicon/react";

import { Box } from "components/Box";
import { Card, CardHeader } from "components/ui/card";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import ListSuggestWrapper from "./ListSuggestWrapper";

import useDebounce from "hooks/useDebounce";
import { liveCommentAPI } from "apis/livePost";
import { PageSuggest } from "types/LiveService";
import { ILivePostItem } from "types/CrawlService";
import { cn } from "utils/utils";

interface Props {
  value: string;
  refSecond: LegacyRef<HTMLDivElement> | undefined;
  handleSearch: () => void;
}
interface IData {
  pageSuggest: PageSuggest[];
  livepostSuggest: ILivePostItem[];
  loading: boolean;
}

const SearchSuggest = ({ value, refSecond, handleSearch }: Props) => {
  const debounceValue = useDebounce(value, 500);

  const [data, setData] = useState<IData>({
    pageSuggest: [],
    livepostSuggest: [],
    loading: false,
  });

  useEffect(() => {
    if (debounceValue !== "") {
      getData();
    } else {
      setData((prev) => ({
        ...prev,
        pageSuggest: [],
        livepostSuggest: [],
      }));
    }
  }, [debounceValue]);

  const getData = async () => {
    setData((prev) => ({ ...prev, loading: true }));
    const response = await liveCommentAPI.getSuggest({ q: debounceValue });
    if (response && response.data) {
      setData((prev) => ({
        ...prev,
        pageSuggest: response.data.pages,
        livepostSuggest: response.data.live_posts,
      }));
    }
    setData((prev) => ({ ...prev, loading: false }));
  };

  const isEmpty = debounceValue == "";

  return (
    <Card
      ref={refSecond}
      className="mt-2 shadow-sm text-primary p-4 absolute w-full top-3 z-[99] translate-y-[24px] gap-2 border-custom-primary"
    >
      <CardHeader className="p-0 m-0">
        <Box
          variant="row-start"
          className="justify-start gap-1 cursor-pointer text-custom-tertiary"
          onClick={isEmpty ? () => {} : handleSearch}
        >
          <RiSearchLine size={20} />
          {data.loading && <LoadingButtonIcon />}
          {!data.loading && (
            <p
              className={cn("text-md font-normal", !isEmpty && "text-brand-strong")}
              children={isEmpty ? '"keyword"' : value}
            />
          )}
        </Box>
      </CardHeader>

      <ListSuggestWrapper<ILivePostItem>
        listSuggest={data.livepostSuggest}
        isShow={data.livepostSuggest.length > 0}
        type="live-post"
      />
      <ListSuggestWrapper<PageSuggest>
        listSuggest={data.pageSuggest}
        isShow={data.pageSuggest.length > 0}
        type="page"
      />
    </Card>
  );
};

export default SearchSuggest;
