import { <PERSON> } from "react-router-dom";
import { Ri<PERSON>hat3Line, RiShareForwardLine, RiThumbUpLine } from "@remixicon/react";

import { Box } from "components/Box";
import AvatarByName from "components/AvatarByName";
import MetricsCompact from "components/LiveAdsPost/MetricsCompact";

import { ILivePostItem } from "types/CrawlService";
import { PATH_DASHBOARD } from "types/path";

interface Props
  extends Partial<
    Pick<
      ILivePostItem,
      | "post_id"
      | "actor_name"
      | "title"
      | "description"
      | "actor_name"
      | "is_live"
      | "comment_count"
      | "reaction_count"
      | "share_count"
      | "thumbnail"
    >
  > {}

const LivePostSuggest = (props: Props) => {
  const {
    post_id = "",
    actor_name,
    title = "",
    thumbnail,
    comment_count,
    reaction_count,
    share_count,
    is_live,
  } = props;

  return (
    <Box className="gap-3 flex-1 w-full items-start p-2">
      <AvatarByName urlImage={thumbnail} name={title} className="w-12 h-12" type={1} />
      <Box variant="col-start" className="gap-1 w-full flex-1">
        {post_id &&
          (is_live ? (
            renderBox(actor_name, title)
          ) : (
            <Link to={`/${PATH_DASHBOARD.live_analysis.community_live}/${post_id}`}>
              {renderBox(actor_name, title)}
            </Link>
          ))}

        <Box className="text-secondary text-sm gap-2">
          <MetricsCompact
            num={reaction_count || 0}
            icon={<RiThumbUpLine size={16} />}
            content="likes"
            className="text-sm text-secondary"
            sizeNumber="sm"
          />
          <MetricsCompact
            num={comment_count || 0}
            icon={<RiChat3Line size={16} />}
            content="comments"
            className="text-sm text-secondary"
            sizeNumber="sm"
          />
          <MetricsCompact
            num={share_count || 0}
            icon={<RiShareForwardLine size={16} />}
            content="shares"
            className="text-sm text-secondary"
            sizeNumber="sm"
          />
        </Box>
      </Box>
    </Box>
  );
};

export default LivePostSuggest;

const renderBox = (actor_name?: string, title?: string) => (
  <Box
    variant="col-start"
    className="gap-1 text-custom-tertiary hover:text-tertiary cursor-pointer text-sm"
  >
    <h3 className="text-secondary">{actor_name}</h3>
    {title && <span className="line-clamp-1">{title}</span>}
  </Box>
);
