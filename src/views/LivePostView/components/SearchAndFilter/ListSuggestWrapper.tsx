import { Box } from "components/Box";
import { CardContent, CardTitle } from "components/ui/card";
import LivePostSuggest from "./LivePostSuggest";
import PageSuggestPreview from "components/LiveAdsPost/PageSuggestPreview";
import AdsPostSuggest from "views/AdsPostView/components/SearchAndFilter/AdsPostSuggest";

interface Props<T> {
  listSuggest: T[];
  isShow: boolean;
  type: "page" | "ads-post" | "live-post";
}

const ListSuggestWrapper = <T,>({ listSuggest, isShow, type }: Props<T>) => {
  return (
    isShow && (
      <CardContent className="p-0">
        <CardTitle
          className="text-sm font-semibold uppercase text-tertiary mt-3"
          children={type === "page" ? "Suggest Page" : "Suggest Post"}
        />
        <Box variant="col-start" className="mt-2 gap-2">
          {listSuggest.map((post, index) => {
            return type === "page" ? (
              <PageSuggestPreview {...post} isSearchModal={true} key={index} />
            ) : type === "live-post" ? (
              <LivePostSuggest key={index} {...post} />
            ) : (
              <AdsPostSuggest {...post} key={index} />
            );
          })}
        </Box>
      </CardContent>
    )
  );
};

export default ListSuggestWrapper;
