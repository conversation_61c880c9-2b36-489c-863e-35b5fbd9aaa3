import { useEffect, useRef, useState } from "react";

import { Box } from "components/Box";
import { Card, CardContent, CardHeader, CardTitle } from "components/ui/card";

import ButtonFollow from "views/YourPageView/components/ButtonCTA/ButtonFollow";
import ButtonUnFollow from "views/YourPageView/components/ButtonCTA/ButtonUnFollow";
import PageSuggestPreview from "components/LiveAdsPost/PageSuggestPreview";

import useInfiniteScroll from "hooks/useLoadInfinity";
import useFilters from "hooks/useFilters";
import { pageAPI } from "apis/pagePost";
import { PageSuggest } from "types/LiveService";
import { cn } from "utils/utils";
import PageSuggestSkeleton from "components/LiveAdsPost/PageSuggestSkeleton";

const ListPageSuggest = () => {
  const [data, setData] = useState<{ pages: PageSuggest[]; loading: boolean; total: number }>({
    total: 0,
    pages: [],
    loading: false,
  });
  const [viewMore, setViewMore] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);

  const ref: any = useRef(null);
  const { paramsNoPage } = useFilters();
  const { lastElementRef } = useInfiniteScroll({
    fetchNextPage: async () => {
      await getListPage(page + 1).then(() => setPage((prev) => prev + 1));
    },
    hasNextPage: data.pages.length < data.total,
    isFetching: data.loading,
    scrollContainer: ref.current,
    autoScrollAfterFetch: true,
  });

  useEffect(() => {
    if (paramsNoPage.q) {
      setPage(1);
      setData((prev) => ({ ...prev, pages: [] }));
      getListPage(1);
    } else {
      setData((prev) => ({ ...prev, pages: [] }));
    }
  }, [paramsNoPage]);

  const getListPage = async (pageToFetch: number) => {
    setData((prev) => ({ ...prev, loading: true }));
    try {
      const response = await pageAPI.getListPage({
        params: { page: pageToFetch, limit: 6, q: paramsNoPage.q },
      });
      if (response && response.data) {
        setData((prev) => ({
          ...prev,
          pages: [...prev.pages, ...response.data.items],
          total: response.data.count,
          loading: false,
        }));
      }
    } catch (_error) {}

    setData((prev) => ({ ...prev, loading: false }));
  };
  const dataRenderFirst = viewMore ? data.pages : data.pages.slice(0, 3);
  const isLoadInfinity = viewMore && data.total > 6;

  return (
    data.pages.length > 0 && (
      <Card className="text-primary m-0 p-2 md:p-6 shadow-sm rounded-sm bg-custom-primary w-full border-none ">
        <CardHeader className="p-0">
          <CardTitle className="text-sm uppercase font-semibold text-tertiary">Brands</CardTitle>
        </CardHeader>
        <CardContent className="p-0 m-0">
          <Box
            ref={ref}
            variant="col-start"
            className={cn(
              "mt-2 gap-1 pr-0 md:pr-6 w-full",
              isLoadInfinity && `overflow-y-scroll max-h-[400px]`
            )}
            style={isLoadInfinity ? {
              maxHeight: `400px`,
              WebkitOverflowScrolling: 'touch'
            } : {}}
          >
            {dataRenderFirst.map((page) => (
              <Box
                key={page.page_id}
                className={cn(
                  "justify-between w-full border-b border-custom-primary p-0 py-3 md:p-4"
                )}
              >
                <PageSuggestPreview {...page} isShowDescription={true} />
                <div>
                  {page?.is_following ? (
                    <ButtonUnFollow id={page.page_id || ""} />
                  ) : (
                    <ButtonFollow id={page.page_id || ""} />
                  )}
                </div>
              </Box>
            ))}
            {data.pages.length > 0 && viewMore && isLoadInfinity && (
              <PageSuggestSkeleton
                lastElementRef={lastElementRef}
                isHidden={data.pages.length == data.total}
              />
            )}
          </Box>

          {!viewMore && data.total > 3 && (
            <div className="w-full text-center mt-6">
              <span
                className="text-primary hover:text-tertiary cursor-pointer font-semibold font-sm"
                onClick={() => setViewMore(true)}
                children="View more"
              />
            </div>
          )}
        </CardContent>
      </Card>
    )
  );
};

export default ListPageSuggest;
