import { useEffect, useState } from "react";

import { Box } from "components/Box";
import { Card } from "components/ui/card";
import ClearAll from "components/FilterChips/ClearAll";
import FilterOptions from "components/FilterOptions";
import FilterChipGroup from "components/FilterChips/FilterChipGroup";
import SearchModal from "components/LiveAdsPost/SearchModal";
import ListPageSuggest from "./ListPageSuggest";

import useFilters from "hooks/useFilters";
import { TSelectedOption } from "types/Select";
import { IFilterOptions } from "types/PostService";

import { cn } from "utils/utils";
import filterOptions from "utils/LiveAdsPost/filterOptions";
import { handleFilter } from "utils/LiveAdsPost";
import { FILTER_CHIP_LABEL } from "constants/postComment";
import SearchSuggest from "./SearchSuggest";
import { RiFilter2Line } from '@remixicon/react';

const SearchAndFilter = (props: IFilterOptions) => {
  const { typeShow, isShowListPageSuggest = true } = props;
  const { params, paramsNoPage, setSearchParams } = useFilters();

  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedOption, setSelectedOption] = useState<TSelectedOption>(params);

  useEffect(() => {
    setSelectedOption(params);
  }, [params]);

  useEffect(() => {
    handleFilter({ params, searchTerm, selectedOption, setSearchParams });
  }, [selectedOption]);

  const filterGroup = filterOptions({
    ...props,
    params,
    setSelectedOption,
  });

  const isShowClearAll = Object.keys(paramsNoPage).length > 0;
  return (
    <>
      <Card className={cn("mt-0 w-full border-none shadow-none p-0", props.className)}>
        {/* using in ads post detail & live post detail */}
        {typeShow == "detail" && (
          <>
            <Box variant="default" className="flex flex-col mt-3 gap-3 w-full sm:flex-row md:mt-4">
              <SearchModal
                value={searchTerm}
                placeholder="Search by keyword"
                className="w-full"
                setSearchTerm={setSearchTerm}
              />
              <Box
                variant="default"
                className={cn(
                  "flex flex-col gap-3 w-full sm:w-fit min-w-[200px] sm:flex-row sm:[&>div]:lg:w-[400px]"
                )}
              >
                <FilterOptions filterGroup={filterGroup} selectedOption={selectedOption} />
              </Box>
            </Box>
            <div className="flex gap-2 flex-wrap items-baseline mt-2">
              <FilterChipGroup chipLabel={FILTER_CHIP_LABEL} limit_badge={2} />
              {isShowClearAll && <ClearAll />}
            </div>
          </>
        )}
        {/* using in live post community */}
        {typeShow !== "detail" && (
          <>
            <SearchModal
              value={searchTerm}
              placeholder="Search by keyword or page"
              SearchSuggest={SearchSuggest}
              setSearchTerm={setSearchTerm}
            />
            <div className="block md:hidden rounded-xl max-lg:mt-6 max-[768px]:shadow-sm transition-all duration-300">
              <div>
                <div className="gap-6 justify-between flex min-[768px]:hidden items-center py-[10px] px-2">
                  <div className="flex gap-1 font-semibold text-sm text-[#515667]">
                    <RiFilter2Line size={20} /> Filter
                  </div>
                </div>
                <div className="flex gap-4 max-md:p-2 min-lg:mt-6 flex-col items-start max-[768px]:mt-0 md:flex-row [&>div]:w-full">
                  <FilterOptions filterGroup={filterGroup} selectedOption={selectedOption} />
                </div>
              </div>
              <div className="md:hidden flex gap-2 flex-wrap items-baseline mb-1 p-2 pt-0">
                <FilterChipGroup chipLabel={FILTER_CHIP_LABEL} limit_badge={2} />
                {isShowClearAll && <ClearAll />}
              </div>
            </div>
            <Box
              variant="default"
              className="flex-wrap hidden lg:flex-nowrap justify-start mt-3 gap-4 box-border ![&_div]:md:w-full ![&_div]:sm:w-full md:mt-4 md:flex"
            >
              <FilterOptions filterGroup={filterGroup} selectedOption={selectedOption} />
            </Box>

            <div className="hidden md:flex gap-2 flex-wrap items-baseline mt-2">
              <FilterChipGroup chipLabel={FILTER_CHIP_LABEL} limit_badge={2} />
              {isShowClearAll && <ClearAll />}
            </div>
          </>
        )}
      </Card>
      {params.q !== "" && typeShow !== "detail" && isShowListPageSuggest && <ListPageSuggest />}
    </>
  );
};

export default SearchAndFilter;
