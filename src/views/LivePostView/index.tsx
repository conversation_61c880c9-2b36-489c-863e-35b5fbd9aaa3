import { useContext, useRef, useEffect } from "react";
import { YourFollowProvider, YourPageContext } from "views/YourPageView/YourPageContext";
import { ShowDetailProvider } from "providers/ShowDetailProvider";
import { Ri<PERSON>rchiveLine, RiUserCommunityLine, RiUserFollowLine } from "@remixicon/react";

import HeaderWrapper from "components/Header";
import TabRouteWrap from "components/Tabs/TabRouteWrap";
import AddFollowByLink from "views/YourPageView/components/AddFollowByLink";
import CommunityLive from "./tabs/CommunityLive";
import YourFollowTab from "./tabs/YourFollowTab";
import ArchivesTab from "./tabs/Archives";

import { PATH_DASHBOARD } from "types/path";
import { LIVE_COMMENT_LABEL } from "constants/LiveAdsPost/label";
import { Button } from '../../components/ui/button';
import { TutorialIcon } from '../../assets/icons/TutorialIcon';
import { useAppDispatch, useAppSelector } from '../../store';
import { ModalContext } from '../../providers/Modal';
import { getAudienceProfilesSummarize } from '../../store/redux/AudienceProfile/slice';
import useOutsideClick from '../../hooks/useClickOutSide';
import handleCloseModal from '../../utils/handleCloseModal';
import { RiCloseLine } from '@remixicon/react';

const LivePageView = () => {
  const context = useContext(YourPageContext);

  return (
    <>
      <TabRouteWrap
        tabContentClass="mt-0"
        tabs={[
          {
            title: "Community Live",
            path: `/${PATH_DASHBOARD.live_analysis.community_live}`,
            content: <CommunityLive />,
            tab_icon: RiUserCommunityLine,
          },
          {
            title: "Your Following",
            path: `/${PATH_DASHBOARD.live_analysis.your_following}`,
            content: <YourFollowTab />,
            totalItems: context?.listPageFollow?.total,
            tab_icon: RiUserFollowLine,
          },
          {
            title: "Archive",
            path: `/${PATH_DASHBOARD.live_analysis.your_archives}`,
            content: <ArchivesTab />,
            totalItems: context?.archives.total,
            tab_icon: RiArchiveLine,
          },
        ]}
      />
    </>
  );
};

const LivePostView = () => {
  const { data } = useAppSelector((state) => state.audienceProfiles);
  const modal = useContext(ModalContext);
  const ref = useRef(null);
  const refSecond = useRef(null);

  const dispatch = useAppDispatch();
  useEffect(() => {
    if (!data) {
      dispatch(getAudienceProfilesSummarize()).unwrap();
    }
  }, []);

  useOutsideClick(ref, refSecond, () => {
    if (!!modal?.dataDialog?.isPopupVideo) {
      handleCloseModal(modal);
    }
  });
  const handleOpenTutorial =()=>{
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      isPopupVideo: true,
      content: (
        <div ref={refSecond}>
          <div className="text-right">
            <Button
              onClick={() => handleCloseModal(modal)}
              className="p-[10px] text-sm font-semibold mb-1 bg-transparent hover:bg-transparent text-primary"
            >
              <RiCloseLine size={20} />
              Close
            </Button>
          </div>
          <iframe
            src="https://www.youtube.com/embed/lYahVCNzUYo?autoplay=1"
            title="Big360 - Live Post Analysis"
            className="rounded-2xl w-full max-w-[920px] aspect-video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
        </div>
      ),
      className: 'max-w-[936px] !p-2 max-lg:max-w-[calc(100%-12px)]'
    } ))
  }

  return (
    <YourFollowProvider>
      <ShowDetailProvider>
        <>
          <HeaderWrapper
            className="items-start lg:items-center w-full max-lg:flex-col max-lg:item"
            isShowBreadcrumb
            leftChildren={{
              title: <div className="flex gap-2" ref={ref}>
                <h1>{LIVE_COMMENT_LABEL.title}</h1>
                <Button onClick={()=>handleOpenTutorial()} className='flex items-center justify-center bg-[#F0F0F0] h-[36px] gap-1 p-2 rounded-xl	hover:bg-[#F0F0F0]'>
                  <TutorialIcon /><p className="text-[#8F5CFF] text-xs mt-[1px]">Guide</p>
                </Button>
              </div>,
              subTitle: LIVE_COMMENT_LABEL.sub_title,
            }}
            rightChildren={
              <AddFollowByLink path={`/${PATH_DASHBOARD.live_analysis.your_following}`} />
            }
          />
          <LivePageView />
        </>
      </ShowDetailProvider>
    </YourFollowProvider>
  );
};

export default LivePostView;
