import { useAppSelector } from "store";
import { RiMapPin2Line } from "@remixicon/react";
import FilterHoverCard from "components/FilterOptions/FilterHoverCard";

import useFilters from "hooks/useFilters";
import useFeatures from "hooks/useFeatures";
import { HandleSelectChangeProps, TSelectOption } from 'types/Select';

import { onChangeDimensions } from "utils/persona/changeDimensions";
import { PERSONA_LABEL } from "constants/persona/label";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { THandleShowMap } from "../../Persona/context/MapProvider.tsx";
import { FILTER_KEY } from "../../../constants/socialPersona";
import { optionIconFilter } from "../../../constants/persona";
import { filterIsShowOptions } from '../../../utils/options';
import { removeVietnameseTones } from '../../../utils/utils';
import { regionNames } from '../../../constants/yourData/label';
import { useEffect, useState } from 'react';
import { TAttribute } from '../../../types/Attribute';

interface IFilterOption {
  handleShowMap?: THandleShowMap;
}

const Location = ({ handleShowMap }: IFilterOption) => {
  const { city } = useAppSelector((state) => state.social);
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const [cityOptions,setCityOptions] = useState<TAttribute[]>([]);
  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) =>
    onChangeDimensions({
      selectedNodes,
      key,
      params: paramsNoPage,
      setSearchParams: setSearchParams,
    });

  useEffect(() => {
    if (city.items.length > 0) {
      sortVNCity();
    }
  }, [city.items]);

  //PERMISSION
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.PSN_FT_LOCATION)?.is_enabled;

  const sortVNCity = () => {
    const newTemp = new Set(regionNames.map((item) => {
      return removeVietnameseTones(item.toLocaleLowerCase());
    }));
    const [VNCity, otherCity] = city.items.reduce<[TAttribute[], TAttribute[]]>((acc, item) => {
      const [vnCity, otherCity] = acc;
      const cityName = removeVietnameseTones(item.name?.toLowerCase() ?? '');
      newTemp.has(cityName) ? vnCity.push(item) : otherCity.push(item);
      return acc;
    }, [[], []]);

    const sortByName = (a: TAttribute, b: TAttribute) => a.name?.localeCompare(b.name ?? '') ?? 0;
    setCityOptions([...VNCity.sort(sortByName), ...otherCity.sort(sortByName)]);
  };

  return (
    <FilterHoverCard
      label={PERSONA_LABEL.location}
      icon={
        <RiMapPin2Line
          size={optionIconFilter.size}
          color={isEnable ? optionIconFilter.color : optionIconFilter.disable}
        />
      }
      isEnable={isEnable}
      activeCount={
        (searchSchema[FILTER_KEY.city]?.length || 0) +
        (searchSchema[FILTER_KEY.region]?.length || 0)
      }
      onClickChangeTab={handleShowMap}
      groupSelect={[
        {
          options: filterIsShowOptions(cityOptions, searchSchema[FILTER_KEY.city]),
          placeholderOptions: "City",
          isSearchable: true,
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.city }),
        },
      ]}
    />
  );
};

export default Location;
