import { RiGroup3Line } from '@remixicon/react';

import FilterHoverCard from 'components/FilterOptions/FilterHoverCard';

import useFilters from 'hooks/useFilters';
import { HandleSelectChangeProps, TSelectOption } from 'types/Select';

import { onChangeDimensions } from 'utils/persona/changeDimensions';
import { formatDataToOptionSelect } from 'utils/options';
import { optionIconFilter } from 'constants/persona';
import { FILTER_KEY } from 'constants/socialPersona';

const FriendSegmentOption = () => {
  const Friends = [
    '<100',
    '100-499',
    '500-999',
    '1000-4999',
    '5000-9999',
    '10000+',
    null
  ];
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) =>
    onChangeDimensions({
      selectedNodes,
      key,
      params: paramsNoPage,
      setSearchParams: setSearchParams
    });

  return (
    <FilterHoverCard
      isEnable
      label="Friend"
      icon={<RiGroup3Line size={20} color={optionIconFilter.color} />}
      activeCount={searchSchema[FILTER_KEY.friend]?.length || 0}
      groupSelect={[
        {
          options: formatDataToOptionSelect(Friends, searchSchema[FILTER_KEY.friend]),
          placeholderOptions: 'Friend',
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.friend })
        }
      ]}
    />
  );
};

export default FriendSegmentOption;
