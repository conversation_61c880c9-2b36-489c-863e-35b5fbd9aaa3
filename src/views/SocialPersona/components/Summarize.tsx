import React, { useEffect, useState } from "react";

import BarChartHorizontal from "components/chart/BarChartHorizontal";
import ChartWrapper from "components/chart/ChartWrapper";
import Polar<PERSON>hart from "components/chart/PolarChart";

import { getCityData } from "utils/persona";
import useFilters from "hooks/useFilters";
import { socialPersonaApi } from "apis/socialPersona";
import { SocialPersonaSummarizeResponse } from "types/SocialPersona.ts";
import isEqual from "lodash/isEqual";
import omit from "lodash/omit";
import usePrevious from "hooks/usePrevious.tsx";
import { formatRelationShipsData } from '../../../utils/socialData/formatRelationShips';
import { fNumberToString } from '../../../utils/number';
import { useAppSelector } from '../../../store';
import { cn } from '../../../utils/utils';
import AgeGroupChartSP from '../../Persona/Summarize/AgeGroupChartSP';
import { useAbortController } from '../../../hooks/useAbortController';

interface IDataSummarize {
  data: {
    age_gender: {};
    gender: {};
    person_province: {};
    person_region: {
      [key: string]: number;
    };
    city: {},
    relationships: {
      [key: string]: number;
    }
  };
  loading: boolean;
}

const SocialPersonaSummary: React.FC = () => {
  const { collapse } = useAppSelector((state) => state.sidebar);
  const { paramsNoPage, searchParams } = useFilters();
  const prevParams = omit(usePrevious(paramsNoPage), ["page", "order_by"]);
  const { newAbortController } = useAbortController();

  const [dataSum, setDataSum] = useState<IDataSummarize>({
    data: {
      age_gender: {},
      gender: {},
      person_province: {},
      person_region: {},
      city: {},
      relationships: {}
    },
    loading: false
  });

  const cityData = getCityData(dataSum.data.city, 'social');

  useEffect(() => {
  }, [paramsNoPage]);

  useEffect(() => {
    if (!isEqual(paramsNoPage, prevParams) && !!searchParams.size) {
      getDataSummarize();
    }
  }, [paramsNoPage, prevParams]);

  const getDataSummarize = async () => {
    const controller = newAbortController();
    setDataSum((prev) => ({ ...prev, loading: true }));

    try {
      const res = await socialPersonaApi.get<SocialPersonaSummarizeResponse>({
        endpoint: "summarize/",
        params: paramsNoPage,
        signal: controller.signal
      });

      if (res.data) {
        setDataSum((prev) => ({
          data: { ...prev.data, ...res.data },
          loading: false
        }));
      } else {
        setDataSum((prev) => ({ ...prev, loading: false }));
      }

      return res.data;
    } catch (error: any) {
      if (error.name === "AbortError" || error.name === "CanceledError") {
        return;
      }

      console.error("Error fetching summarize data:", error);
      setDataSum((prev) => ({ ...prev, loading: false }));
    }
  };

  const relationships = formatRelationShipsData(dataSum.data.relationships);

  return (
    <div
      className={cn(
        'grid grid-cols-1 my-6 gap-x-7 gap-y-6 xl:grid-cols-3',
        collapse ? 'md:grid-cols-3' : 'md:grid-cols-2'
      )}
    >
      <AgeGroupChartSP
        dataAge={dataSum.data.age_gender}
        dataGender={dataSum.data.gender}
        loading={dataSum.loading}
      />
      <>
        <ChartWrapper
          isEmptyData={cityData.length === 0}
          loading={dataSum.loading}
          isBarHorizonChart
          className={cn("col-span-2 lg:col-span-1 xl:col-span-2", collapse ? "md:col-span-1" : "md:col-span-2")}
          title="City"
        >
          <BarChartHorizontal
            values={cityData?.map((item) => item.count) || []}
            labels={cityData?.map((item) => item.city) || []}
          />
        </ChartWrapper>
        <ChartWrapper
          isEmptyData={relationships.total === 0} loading={dataSum.loading}
          isPieChart
          className={cn('col-span-2 xl:col-span-1', collapse ? 'col-span-3' : 'md:col-span-2')}
          title="Relationships"
        >
          <PolarChart
            labels={relationships.labels}
            values={[...relationships.value]}
            showTotal={true}
            titleTooltip="Relationships"
            isCustomTooltip={true}
            customTooltip={CustomOtherTooltip(relationships.otherItems, relationships.total)}
          />
        </ChartWrapper>
      </>
    </div>
  );
};

export default SocialPersonaSummary;


interface IOtherItems {
  label: string;
  value: number;
}

const CustomOtherTooltip = (items: IOtherItems[], total: number) => `
 ${items
.map(
  (item) =>
    `<div class="flex gap-1 justify-start w-[250px]">
            <div class= "font-medium text-xs text-secondary 3xl:text-md ml-1 capitalize">${
      item.label
    }:</div>
            <div class = "font-medium text-xs text-primary 3xl:text-md">${
      fNumberToString(item.value) + " - " + ((item.value / total) * 100).toFixed(2)
    }%</div>
        </div>`
)
.join("")}
`;
