import FilterHoverCard from "components/FilterOptions/FilterHoverCard";

import useFilters from "hooks/useFilters";
import { HandleSelectChangeProps, TSelectOption } from "types/Select";

import { onChangeDimensions } from "utils/persona/changeDimensions";
import { filterIsShowOptions } from "utils/options";
import { genderOptionsAtt } from "constants/persona";
import { FILTER_KEY } from "constants/socialPersona";

const GenderOptions = () => {
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) =>
    onChangeDimensions({
      selectedNodes,
      key,
      params: paramsNoPage,
      setSearchParams: setSearchParams,
    });

  return (
    <FilterHoverCard
      isEnable
      label="Gender"
      icon={genderIcon}
      activeCount={searchSchema[FILTER_KEY.gender]?.length || 0}
      groupSelect={[
        {
          options: filterIsShowOptions(genderOptionsAtt, searchSchema[FILTER_KEY.gender]),
          placeholderOptions: "Gender",
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.gender }),
        },
      ]}
    />
  );
};

export default GenderOptions;

const genderIcon = (
  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
    <path
      d="M17.3912 10.983L17.3912 7.97683L15.4423 9.92577L14.5923 9.07578L17.9923 5.67582L21.3922 9.07578L20.5422 9.92577L18.5933 7.97683L18.5933 10.983C19.5393 11.1095 20.4528 11.5363 21.1797 12.2632C22.9401 14.0236 22.9401 16.8778 21.1797 18.6382C19.4193 20.3986 16.5652 20.3986 14.8048 18.6382C13.0444 16.8778 13.0444 14.0236 14.8048 12.2632C15.5317 11.5363 16.4452 11.1095 17.3912 10.983ZM20.3297 17.7882C21.6207 16.4972 21.6207 14.4042 20.3297 13.1132C19.0388 11.8223 16.9457 11.8223 15.6548 13.1132C14.3638 14.4042 14.3638 16.4972 15.6548 17.7882C16.9457 19.0792 19.0388 19.0791 20.3297 17.7882Z"
      fill="#0F132499"
    />
    <path
      d="M6.53636 14.9496C4.25777 14.646 2.5 12.6949 2.5 10.3333C2.5 7.76111 4.58516 5.67595 7.15734 5.67595C9.7295 5.67595 11.8147 7.76111 11.8147 10.3333C11.8147 12.6949 10.0569 14.646 7.77832 14.9496V16.2326H10.8832V17.4745H7.77832V19.9585H6.53636V17.4745H3.43147V16.2326H6.53636V14.9496ZM7.15734 13.7487C9.04363 13.7487 10.5727 12.2196 10.5727 10.3333C10.5727 8.44703 9.04363 6.91791 7.15734 6.91791C5.27107 6.91791 3.74196 8.44703 3.74196 10.3333C3.74196 12.2196 5.27107 13.7487 7.15734 13.7487Z"
      fill="#0F132499"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.9923 5.32227L21.7458 9.07578L20.5422 10.2793L18.8433 8.58038L18.8433 10.7693C19.7639 10.9358 20.6451 11.3751 21.3565 12.0865C23.2145 13.9445 23.2145 16.957 21.3565 18.815C19.4985 20.673 16.486 20.673 14.628 18.815C12.77 16.957 12.77 13.9445 14.628 12.0865C15.3394 11.3751 16.2206 10.9359 17.1412 10.7693L17.1412 8.58039L15.4423 10.2793L14.2387 9.07578L17.9923 5.32227ZM7.15734 5.92595C4.72323 5.92595 2.75 7.89918 2.75 10.3333C2.75 12.568 4.41334 14.4145 6.56938 14.7018L6.78636 14.7307V16.4826H3.68147V17.2245H6.78636V19.7085H7.52832V17.2245H10.6332V16.4826H7.52832V14.7307L7.7453 14.7018C9.90136 14.4145 11.5647 12.568 11.5647 10.3333C11.5647 7.89918 9.59143 5.92595 7.15734 5.92595ZM2.25 10.3333C2.25 7.62304 4.44709 5.42595 7.15734 5.42595C9.86757 5.42595 12.0647 7.62304 12.0647 10.3333C12.0647 12.7464 10.3232 14.7524 8.02832 15.1635V15.9826H11.1332V17.7245H8.02832V20.2085H6.28636V17.7245H3.18147V15.9826H6.28636V15.1635C3.99152 14.7524 2.25 12.7464 2.25 10.3333ZM14.9458 9.07578L15.4423 9.57222L17.6412 7.37327L17.6412 11.2018L17.4244 11.2308C16.5309 11.3503 15.6684 11.7532 14.9816 12.44C13.3188 14.1028 13.3188 16.7987 14.9816 18.4614C16.6443 20.1241 19.3402 20.1242 21.0029 18.4614C22.6657 16.7987 22.6657 14.1028 21.0029 12.44C20.3161 11.7532 19.4537 11.3503 18.5601 11.2308L18.3433 11.2018L18.3433 7.37328L20.5422 9.57222L21.0387 9.07578L17.9923 6.02937L14.9458 9.07578ZM3.49196 10.3333C3.49196 8.30896 5.133 6.66791 7.15734 6.66791C9.1817 6.66791 10.8227 8.30896 10.8227 10.3333C10.8227 12.3576 9.1817 13.9987 7.15734 13.9987C5.133 13.9987 3.49196 12.3577 3.49196 10.3333ZM7.15734 7.16791C5.40915 7.16791 3.99196 8.5851 3.99196 10.3333C3.99196 12.0815 5.40915 13.4987 7.15734 13.4987C8.90556 13.4987 10.3227 12.0815 10.3227 10.3333C10.3227 8.5851 8.90555 7.16791 7.15734 7.16791Z"
      fill="#0F132499"
    />
  </svg>
);
