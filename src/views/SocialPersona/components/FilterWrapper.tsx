import InterestOptions from "./InterestOptions";

import AgeOptions from "./AgeOptions";
import GenderOptions from "./GenderOptions";
import RelationshipOptions from "./RelationshipOptions";
import Location from "./Location.tsx";
import FriendSegmentOption from './FriendSegmentOption';
import FollowerSegmentOption from './FollowerSegmentOption';
import HorizontalScrollNav from '../../../components/ListFilterResponsive';
import useFilters from '../../../hooks/useFilters';
import { useEffect } from 'react';
import { tracking } from '../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../store';

const FilterWrapper = () => {
  const { params, searchParams } = useFilters();
  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (searchParams.size > 0) {
      tracking({
        eventName: 'social_persona_filter',
        params: {
          user_id: user?.uuid,
          valueTracking: JSON.stringify({
            filter_params: params
          })
        }
      });
    }
  }, [params]);

  return (
    <HorizontalScrollNav className="mt-0">
      <InterestOptions />
      <AgeOptions />
      <GenderOptions />
      <Location />
      <RelationshipOptions />
      <FriendSegmentOption />
      <FollowerSegmentOption />
    </HorizontalScrollNav>
  );
};

export default FilterWrapper;
