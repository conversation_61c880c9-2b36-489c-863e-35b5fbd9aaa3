import SocialPersonaSummary from "./Summarize";
import FilterChipsWrapper from "./FilterChipWrapper";
import FilterWrapper from "./FilterWrapper";
import NodataOverlay from "views/Persona/components/NodataOverlay";
import SocialScoreTable from "./SocialScoreTable";

import useFilters from "hooks/useFilters";

const Container = () => {
  const { searchParams } = useFilters();
  return (
    <div className="flex flex-col w-full h-full">
      <FilterWrapper />
      <FilterChipsWrapper />

      {!searchParams.size ? (
        <NodataOverlay />
      ) : (
        <div className="mt-6">
          <SocialPersonaSummary />
          <SocialScoreTable />
        </div>
      )}
    </div>
  );
};

export default Container;
