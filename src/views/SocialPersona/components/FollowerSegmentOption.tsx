import { RiUserFollowLine } from '@remixicon/react';

import FilterHoverCard from 'components/FilterOptions/FilterHoverCard';

import useFilters from 'hooks/useFilters';
import { HandleSelectChangeProps, TSelectOption } from 'types/Select';

import { onChangeDimensions } from 'utils/persona/changeDimensions';
import { formatDataToOptionSelect } from 'utils/options';
import { optionIconFilter } from 'constants/persona';
import { FILTER_KEY } from 'constants/socialPersona';

const FollowerSegmentOption = () => {
  const Followers = [
    '<100',
    '100-499',
    '500-999',
    '1000-4999',
    '5000-9999',
    '10000+',
    null
  ];
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) =>
    onChangeDimensions({
      selectedNodes,
      key,
      params: paramsNoPage,
      setSearchParams: setSearchParams
    });

  return (
    <FilterHoverCard
      isEnable
      label="Followers"
      icon={<RiUserFollowLine size={20} color={optionIconFilter.color} />}
      activeCount={searchSchema[FILTER_KEY.follower]?.length || 0}
      groupSelect={[
        {
          options: formatDataToOptionSelect(Followers, searchSchema[FILTER_KEY.follower]),
          placeholderOptions: 'Followers',
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.follower })
        }
      ]}
    />
  );
};

export default FollowerSegmentOption;
