import React, { useEffect, useMemo, useState } from 'react';

import { DataTable } from "components/DataTable/TableCustom";
import Pagination from "components/Pagination";

import { getSocialScoreColumn } from "constants/socialPersona/column";
import { socialPersonaApi } from "apis/socialPersona";
import useFilters from "hooks/useFilters";
import { SocialPersonaPreview, SocialPersonaPreviewResponse } from "../../../types/SocialPersona.ts";
import { ColumnDef } from "@tanstack/react-table";
import { socialPersonaContext } from "../Context/SocialPersonaContext.tsx";
import { useAbortController } from '../../../hooks/useAbortController';

type TSocialPersonaPreview = {
  data: SocialPersonaPreview[];
  count: number;
  loading: boolean;
};
const SocialScoreTable: React.FC = () => {
  const { searchParams, params, setSearchParams } = useFilters();
  const resultCount = socialPersonaContext().resultCount.number;
  const { newAbortController } = useAbortController();

  const [dataPreview, setDataPreview] = useState<TSocialPersonaPreview>({
    data: [],
    loading: false,
    count: 0
  });

  useEffect(() => {
    if (searchParams.size) {
      getDataPreview();
    }
  }, [searchParams]);

  const getDataPreview = async () => {
    const controller = newAbortController();

    setDataPreview((prev) => ({ ...prev, loading: true }));

    try {
      const res = await socialPersonaApi.get<SocialPersonaPreviewResponse>({
        endpoint: "preview/",
        params: { ...params, limit: 10 },
        signal: controller.signal
      });

      const { data } = res;

      setDataPreview({
        data: data?.items ?? [],
        count: data?.count ?? 0,
        loading: false
      });
    } catch (error: any) {
      if (error.name === "AbortError" || error.name === "CanceledError") {
        return;
      }
      console.error("Error fetching data preview:", error);
      setDataPreview((prev) => ({ ...prev, loading: false }));
    }
  };

  const memoizedColumns: ColumnDef<SocialPersonaPreview>[] = useMemo(() => {
    return getSocialScoreColumn();
  }, []);

  return (
    <div className="my-6">
      <DataTable columns={memoizedColumns} data={dataPreview?.data || []} loading={dataPreview.loading}/>
      <Pagination
        className="mt-4"
        currentPage={Number(searchParams.get("page")) || 1}
        pageSize={10}
        totalCount={resultCount}
        onPageChange={(page) => setSearchParams({ ...params, page: String(page) })}
      />
    </div>
  );
};

export default SocialScoreTable;
