import { RiParentLine } from "@remixicon/react";

import FilterHoverCard from "components/FilterOptions/FilterHoverCard";

import useFilters from "hooks/useFilters";
import { HandleSelectChangeProps, TSelectOption } from "types/Select";

import { onChangeDimensions } from "utils/persona/changeDimensions";
import { formatDataToOptionSelect } from "utils/options";
import { optionIconFilter } from "constants/persona";
import { FILTER_KEY } from "constants/socialPersona";
import { AGE_RANGE } from '../../../constants';

const AgeOptions = () => {
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) =>
    onChangeDimensions({
      selectedNodes,
      key,
      params: paramsNoPage,
      setSearchParams: setSearchParams,
    });

  return (
    <FilterHoverCard
      isEnable
      label="Age"
      icon={<RiParentLine size={20} color={optionIconFilter.color} />}
      activeCount={searchSchema[FILTER_KEY.age]?.length || 0}
      groupSelect={[
        {
          options: formatDataToOptionSelect(AGE_RANGE, searchSchema[FILTER_KEY.age]),
          placeholderOptions: "Age",
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.age }),
        },
      ]}
    />
  );
};

export default AgeOptions;
