import { RiServiceLine } from "@remixicon/react";

import FilterHoverCard from "components/FilterOptions/FilterHoverCard";

import useFilters from "hooks/useFilters";
import { HandleSelectChangeProps, TSelectOption } from "types/Select";

import { onChangeDimensions } from "utils/persona/changeDimensions";
import { filterIsShowOptions } from "utils/options";
import { optionIconFilter } from "constants/persona";
import { FILTER_KEY } from "constants/socialPersona";
import { RELATIONSHIP_GENDER_OPTIONS } from "constants/yourData";

const RelationshipOptions = () => {
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) =>
    onChangeDimensions({
      selectedNodes,
      key,
      params: paramsNoPage,
      setSearchParams: setSearchParams,
    });

  const newOptions = RELATIONSHIP_GENDER_OPTIONS.map((option) => ({
    name: option.label,
    id: option.value,
  }));

  return (
    <FilterHoverCard
      isEnable
      label="Relationships"
      icon={<RiServiceLine size={20} color={optionIconFilter.color} />}
      activeCount={searchSchema[FILTER_KEY.relationships]?.length || 0}
      groupSelect={[
        {
          options: filterIsShowOptions(newOptions, searchSchema[FILTER_KEY.relationships]),
          placeholderOptions: "Relationships",
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.relationships }),
        },
      ]}
    />
  );
};

export default RelationshipOptions;
