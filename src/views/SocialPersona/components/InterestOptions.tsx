import { RiChat<PERSON><PERSON>tLine, RiStarFill } from '@remixicon/react';
import { useAppSelector } from "store";

import FilterHoverCard from "components/FilterOptions/FilterHoverCard";

import { HandleSelectChangeProps, TSelectOption } from "types/Select";
import useFilters from "hooks/useFilters";

import { filterIsShowOptions } from "utils/options";

import { cn, toDataOptions } from 'utils/utils';
import { optionIconFilter } from "constants/persona";
import { FILTER_KEY, ScoreOptionsAtt } from "constants/socialPersona";
import SOCIAL_PERSONA_LABEL from "constants/socialPersona/label";
import React, { useEffect, useState } from "react";
import { TRange } from 'components/AgeRangeFilter';
import useDebounce from '../../../hooks/useDebounce';
import { convertRangeToValue } from '../../../utils/socialPersona';
import { Box } from '../../../components/Box';
import { styled } from 'styled-components';
import useResponsive from '../../../hooks/useResponsive';

const InterestOptions = () => {
  const keyOfCategory = FILTER_KEY.categoryDefault;
  const keyOfScore = FILTER_KEY.score;

  const {isMobile} = useResponsive()

  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const { data: categories } = useAppSelector((state) => state.category);
  const filterCategory = filterIsShowOptions(
    toDataOptions(categories.items, "code", "name"),
    searchSchema[FILTER_KEY.categoryDefault]
  );
  const filterScore = filterIsShowOptions(ScoreOptionsAtt, searchSchema[FILTER_KEY.score]);
  const paramsScore = convertRangeToValue(searchSchema[FILTER_KEY.score]?.[0] ?? '');

  const [rangeSelected, setRangeSelected] = useState<TRange>({
    min: paramsScore.min / 10 ?? 0,
    max: paramsScore.max / 10 ?? 5
  });
  const [enable, setEnable] = useState<{rank: boolean, range: boolean}>({
    rank: false,
    range: false
  });

  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) => {
    if (!enable.rank && !enable.range) {
      setEnable({
        rank: true,
        range: true
      });
    }
    if (![keyOfCategory, keyOfScore].includes(key)) return;

    const paramCategory = paramsNoPage[keyOfCategory] || filterCategory[0]?.value;
    const paramScore = paramsNoPage[keyOfScore] || filterScore[0]?.value;
    if (key === keyOfCategory) {
      handleUpdateSlider(paramScore.toString());
    } else {
      const [min, max] = selectedNodes[0].value.toString().split('-').map(value => parseInt(value) / 10);
      setRangeSelected({ min, max });
    }

    const updatedParams = new URLSearchParams({
      ...paramsNoPage,
      [keyOfCategory]: key === keyOfCategory ? selectedNodes[0].value.toString() : paramCategory.toString(),
      [keyOfScore]: key === keyOfScore ? selectedNodes[0].value.toString() : paramScore.toString()
    });
    setSearchParams(updatedParams);
  };

  const handleChangeRange = (range: TRange) => {
    if (range.max === 0) {
      return;
    }
    setRangeSelected({
      min: range.min === range.max ? range.min - 1 : range.min,
      max: range.max
    });
  };

  const handleUpdateSlider = (str: string) => {
    const [start, end] = str.split(',').reduce(
      ([min, max], range) => {
        const [rangeStart, rangeEnd] = range.split('-').map(Number);
        return [Math.min(min, rangeStart), Math.max(max, rangeEnd)];
      },
      [Infinity, -Infinity]
    );
    setRangeSelected({
      min: start / 10,
      max: end / 10
    });
  };

  const renderStar = () =>
    ScoreOptionsAtt.map((item, index) => {
      const { min, max } = convertRangeToValue(item.name ?? '');
      const checked = rangeSelected.min === min && rangeSelected.max === max;
      const stars = Array.from({ length: max }, (_, i) => (
        <RiStarFill key={i} size={20} color={(i === max - 1 || min === i + 1) ? '#FBCA24' : '#FCEAAA'} />
      ));
      return (
        <Box
          key={index}
          className={cn('justify-start gap-2 mb-4 mt-2 mx-2 cursor-pointer w-fit', { 'opacity-50 cursor-not-allowed': !enable.rank })}
          onClick={() => {
            if (enable.rank) {
              handleChange({
                selectedNodes: [{
                  checked: true,
                  dept: 0,
                  label: item.name ?? '',
                  value: item.id ?? ''
                }],
                key: FILTER_KEY.score
              });
            }
          }}
        >
          <ItemRadio type="radio" className={cn(enable.rank?'cursor-pointer':'cursor-not-allowed')} value={item.value} checked={checked} readOnly disabled={!enable.rank}/>
          <Box className="gap-2">{stars as React.ReactNode}</Box>
        </Box>
      );
    });

  const debouncedSearchQuery: TRange = useDebounce(rangeSelected, 300) ?? '';

  useEffect(() => {
    if (debouncedSearchQuery.min !== undefined && debouncedSearchQuery.max !== undefined && searchSchema[FILTER_KEY.score]) {
      const rangeValue = debouncedSearchQuery.min * 10 + '-' + debouncedSearchQuery.max * 10;
      const updatedParams = new URLSearchParams({
        ...paramsNoPage,
        [keyOfScore]: rangeValue
      });
      setSearchParams(updatedParams);
    }
  }, [debouncedSearchQuery.min, debouncedSearchQuery.max]);

  useEffect(() => {
    const isCategoryOrScoreEmpty = !searchSchema[FILTER_KEY.categoryDefault] && !searchSchema[FILTER_KEY.score];
    setEnable({
      rank: !isCategoryOrScoreEmpty,
      range: !isCategoryOrScoreEmpty
    });
    if (isCategoryOrScoreEmpty) {
      setRangeSelected({ min: 0, max: 5 });
    }
  }, [searchSchema]);

  return (
    <FilterHoverCard
      isEnable
      label={SOCIAL_PERSONA_LABEL.category_filter}
      icon={<RiChatHeartLine size={20} color={optionIconFilter.color} />}
      activeCount={searchSchema[FILTER_KEY.categoryDefault]?.length || 0}
      className={'max-[768px]:h-[320px]'}
      groupSelect={[
        {
          placeholderOptions: "Categories",
          placeholderOptionsChildren: "Score Range",
          isSearchable: true,
          isSingleSelect: true,
          options: filterCategory,
          className: isMobile ? '!h-[140px]' : 'h-full',
          isHaveChildren: isMobile,
          rangeOption: isMobile ? {
            min: 0,
            max: 5,
            hideInput: true,
            className: 'w-full',
            isRangeEnable: enable.range,
            rangeSelected: rangeSelected,
            onRangeChange: (value) => {
              handleChangeRange(value);
            }
          } : undefined,
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.categoryDefault })
        },
        {
          isSingleSelect: true,
          placeholderOptions: "Score Range",
          options: filterScore,
          isEnable: enable.rank,
          isCustomOptions: true,
          isMobile: isMobile,
          customOptions: renderStar(),
          rangeOption: {
            min: 0,
            max: 5,
            hideInput: true,
            className:'!w-[140px]',
            isRangeEnable: enable.range,
            rangeSelected: rangeSelected,
            onRangeChange: (value) => {
              handleChangeRange(value);
            }
          },
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.score })
        }
      ]}
    />
  );
};

export default InterestOptions;

const ItemRadio = styled.input`
  position: relative;
  width: 16px;
  height: 16px;
  appearance: none;
  &:before {
    content: "";
    display: block;
    position: absolute;
    min-width: 16px;
    min-height: 16px;
    width: 16px;
    height: 16px;
    border: 1px solid #dee0e3;
    border-radius: 30px;
    background-color: white;
    z-index: 10;
    box-shadow: 0 1px 2px 0 #14151a0d;
  }
  &:checked:after {
    content: "";
    display: block;
    min-width: 6px;
    min-height: 6px;
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
  &:checked:before {
    background-color: #924fe8;
    border: 1px solid #924fe8;
  }
`;
