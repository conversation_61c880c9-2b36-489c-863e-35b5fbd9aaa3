import FilterChipGroup from 'components/FilterChips/FilterChipGroup';
import ClearAll from 'components/FilterChips/ClearAll';
import { LoadingButtonIcon } from 'assets/icons/LoadingButtonIcon';
import { useState } from 'react';
import useFilters from 'hooks/useFilters';
import { PERSONA_LABEL } from 'constants/persona/label';
import { FILTER_TITLE } from 'constants/socialPersona';
import { socialPersonaContext } from '../Context/SocialPersonaContext.tsx';
import { fNumberToString } from '../../../utils/number.ts';
import { RiArrowUpSLine, RiArrowDownSLine } from '@remixicon/react';

const FilterChipsWrapper = () => {
  const { searchParams } = useFilters();
  const [isExpanded, setIsExpanded] = useState(false);

  const resultCount = socialPersonaContext()?.resultCount;
  const activeFiltersCount = searchParams.size;

  return (
    searchParams.size !== 0 && (
      <div className="mt-2 w-full">
        <div className="flex items-center justify-between max-lg:mb-2">
          <div className="text-sm text-secondary mb-0 lg:mb-2 flex items-center">
            {resultCount?.loading ? (
              <LoadingButtonIcon width="20" height="20" />
            ) : (
              <span className="text-primary font-semibold mr-1">{fNumberToString(resultCount.number)}</span>
            )}
            <span className="font-medium">{PERSONA_LABEL.results_found}</span>
          </div>
          <div className="block lg:hidden"><ClearAll className="mt-0" /></div>
        </div>
        <button
          className="flex items-center justify-between w-full py-2 px-3 bg-gray-50 rounded-lg border border-gray-200 lg:hidden mb-2"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <span className="font-medium text-sm">Filters ({activeFiltersCount})</span>
          {isExpanded ? (
            <RiArrowUpSLine size={20} color="#000" />
          ) : (
            <RiArrowDownSLine size={20} color="#000" />
          )}
        </button>
        <div className={`flex lg:gap-2 flex-wrap items-baseline ${isExpanded ? 'block' : 'hidden'} lg:flex`}>
          <FilterChipGroup chipLabel={FILTER_TITLE} />
          <div className="hidden lg:block">
            <ClearAll />
          </div>
        </div>
      </div>
    )
  );
};

export default FilterChipsWrapper;
