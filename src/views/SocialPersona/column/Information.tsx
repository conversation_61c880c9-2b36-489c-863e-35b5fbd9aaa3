import { Link } from "react-router-dom";
import { TooltipPortal } from "@radix-ui/react-tooltip";

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import AvatarByName from "components/AvatarByName";

import useAvatar from "hooks/useAvatar";
import { SocialPersonaPreview } from "types/SocialPersona";

const Information = (params: SocialPersonaPreview) => {
  const { fullname, fb_uid } = params;
  const { avatar } = useAvatar({ type: "profile", uid: fb_uid || "" });
  const name = fullname || "-";
  return (
    <div className="flex items-center gap-2 w-[200px]">
      <AvatarByName urlImage={avatar.url} name={name} />
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            {fb_uid ? (
              <Link
                target="_blank"
                to={`https://www.facebook.com/profile.php?id=${fb_uid}`}
                className="overflow-hidden text-ellipsis  whitespace-nowrap md:max-w-[100px] lg:max-w-[150px] inline-block"
              >
                {name}
              </Link>
            ) : (
              <span className="overflow-hidden text-ellipsis w-fit whitespace-nowrap md:max-w-[100px] lg:max-w-[150px] xl:max-w-[220px] inline-block">
                {name}
              </span>
            )}
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent
              align="center"
              side="bottom"
              sideOffset={5}
              className="flex items-baseline gap-1"
            >
              {name}
            </TooltipContent>
          </TooltipPortal>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default Information;
