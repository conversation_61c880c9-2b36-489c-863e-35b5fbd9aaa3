// import { useContext } from "react";
import { RiShoppingCartLine } from "@remixicon/react";
// import { ModalContext } from "providers/Modal";

import useGetPhone from "hooks/useGetPhone";

type Props = {
  id?: number;
  endpoint: string;
  phoneNumber: string;
  buyProfile?: boolean;
  name: string;
};

const PhoneColumn = ({ ...props }: Props) => {
  const {  endpoint, phoneNumber, buyProfile } = props;
  // const modal = useContext(ModalContext);
  const { phone } = useGetPhone({
    defaultValue: phoneNumber,
    endpoint: endpoint,
    type: "persona"
  });

  const handleOpenModalBuy = () => {
    // modal?.setDataDialog((prev) => ({
    //   ...prev,
    //   isOpen: true,
    //   isShowTitle: false,
    //   content: <BuyAudienceCustom uid={id} fullName={name} phone={phone.phone} />,
    //   className: "max-w-[680px]",
    // }));
  };

  return buyProfile ? (
    <button
      id="phone_persona"
      className="text-center text-sm p-1 rounded-sm bg-brand-subtitle text-brand-default !font-sans flex gap-1 items-center"
      onClick={handleOpenModalBuy}
    >
      <span className="w-[80px]">{phone.phone}</span>
      <RiShoppingCartLine size={16} />
    </button>
  ) : (
    <div className="text-center text-sm p-1 rounded-sm bg-brand-subtitle text-brand-default !font-sans flex gap-1 items-center">
      <span className="w-[80px]">{phone.phone}</span>
    </div>
  );
};
export default PhoneColumn;
