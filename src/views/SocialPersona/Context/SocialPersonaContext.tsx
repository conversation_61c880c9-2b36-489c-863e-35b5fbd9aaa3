import useFilters from "hooks/useFilters";
import usePrevious from "hooks/usePrevious";
import isEqual from "lodash/isEqual";
import omit from "lodash/omit";
import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useContext,
  useEffect,
  useState,
} from "react";
import { socialPersonaApi } from "../../../apis/socialPersona.ts";
import { useAbortController } from '../../../hooks/useAbortController';

export interface ResultCountProps {
  number: number;
  loading: boolean;
}
export interface SocialPersonaContextProps {
  resultCount: ResultCountProps;
  setResultCount: Dispatch<SetStateAction<ResultCountProps>>;
}

const SocialPersonaContext = createContext<SocialPersonaContextProps>({
  resultCount: {
    number: 0,
    loading: false,
  },
  setResultCount: () => {},
});

const SocialPersonaProvider = ({ children }: { children: ReactNode }) => {
  const { paramsNoPage, searchParams } = useFilters();
  const prevParams = usePrevious(paramsNoPage);
  const { newAbortController } = useAbortController();
  const [resultCount, setResultCount] = useState<ResultCountProps>({
    number: 0,
    loading: false,
  });

  useEffect(() => {
    const prevParamsOmitted = omit(prevParams, ["page", "order_by"]);
    if (!isEqual(paramsNoPage, prevParamsOmitted) && !!searchParams.size) {
      getCountResult();
    }

    if (searchParams.size === 0) {
      setResultCount((prev) => ({ ...prev, number: 0 }));
    }
  }, [paramsNoPage, searchParams.size]);

  const getCountResult = async () => {
    const controller = newAbortController();
    setResultCount((prev) => ({ ...prev, loading: true }));
    const res = await socialPersonaApi.get({
      endpoint: 'count/',
      params: { ...paramsNoPage },
      signal: controller.signal
    });
    if (res.data) {
      setResultCount((prev) => ({ ...prev, number: res.data.count }));
    }
    setResultCount((prev) => ({ ...prev, loading: false }));
  };

  return (
    <SocialPersonaContext.Provider
      value={{
        resultCount,
        setResultCount,
      }}
    >
      {children}
    </SocialPersonaContext.Provider>
  );
};

const socialPersonaContext = (): SocialPersonaContextProps => useContext(SocialPersonaContext);
export { SocialPersonaProvider, socialPersonaContext };
