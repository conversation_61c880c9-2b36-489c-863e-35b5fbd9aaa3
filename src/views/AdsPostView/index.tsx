import { useContext, useRef } from "react";
import { ShowDetailContext, ShowDetailProvider } from "providers/ShowDetailProvider";

import Container from "./components/Container";
import ButtonRequest from "components/Button/ButtonRequest";
import HeaderWrapper from "components/Header";
import BackToYourPage from "components/YourPage/BackToYourPage";
import PageBrandDetail from "views/AdsPageDetail";
import SearchAndFilterWrapper from "./components/SearchAndFilter/SearchAndFilterWrapper";

import useGetListPost from "hooks/useGetListPost";
import useFeatures from "hooks/useFeatures";
import useCheckPermissionFeature from "hooks/useCheckPermissionFeature";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { POST_COMMENT_LABEL } from "constants/LiveAdsPost/label";
import ShowResult from "../SocialDataDetail/components/ShowResult";
import useFilters from "../../hooks/useFilters";
import { But<PERSON> } from '../../components/ui/button';
import { TutorialIcon } from '../../assets/icons/TutorialIcon';
import { ModalContext } from '../../providers/Modal';
import useOutsideClick from '../../hooks/useClickOutSide';
import handleCloseModal from '../../utils/handleCloseModal';
import { RiCloseLine } from '@remixicon/react';

const ListAdsWrapper = () => {
  const { paramsNoPage } = useFilters();
  const context = useContext(ShowDetailContext);
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.SCD_VIEW_ADPOST_COMMUNITY)?.is_enabled;
  useCheckPermissionFeature(isEnable);

  // Check permission
  const { data, lastElementRef } = useGetListPost({ type: "ads", paramsNoPage });

  const isShowDetail = context?.showDetail.isShow;
  return (
    <>
      {isShowDetail && (
        <>
          <BackToYourPage />
          <PageBrandDetail />
        </>
      )}
      {!isShowDetail && data && isEnable && (
        <>
          <SearchAndFilterWrapper
            className="mt-3 sm:mt-6"
            isCategory
            isFormat
            isPLatform
            isStatus
            isDateTime
            isEmpty={data.posts.length == 0}
          />
          <ShowResult total={data.total} />
          <Container
            key="ads_post"
            data={data.posts}
            total={data.total}
            loading={data.loading}
            lastElementRef={lastElementRef}
          />
        </>
      )}
    </>
  );
};

const AdsPostView = () => {
  const modal = useContext(ModalContext);
  const ref = useRef(null);
  const refSecond = useRef(null);
  useOutsideClick(ref, refSecond, () => {
    if (!!modal?.dataDialog?.isPopupVideo) {
      handleCloseModal(modal);
    }
  });

  const handleOpenTutorial =()=>{
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      isPopupVideo: true,
      content: (
        <div ref={refSecond}>
          <div className="text-right">
            <Button
              onClick={() => handleCloseModal(modal)}
              className="p-[10px] text-sm font-semibold mb-1 bg-transparent hover:bg-transparent text-primary"
            >
              <RiCloseLine size={20} />
              Close
            </Button>
          </div>
          <iframe
            src="https://www.youtube.com/embed/xr8egNoFEpE?autoplay=1"
            title="Big360 - Ads Post Analysis"
            className="rounded-2xl w-full max-w-[920px] aspect-video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
        </div>
      ),
      className: 'max-w-[936px] !p-2 max-lg:max-w-[calc(100%-12px)]'
    } ))
  }
  return (
    <ShowDetailProvider>
      <>
        <HeaderWrapper
          isShowBreadcrumb
          leftChildren={{
            title: <div className="flex gap-2" ref={ref}>
              <h1>{POST_COMMENT_LABEL.title}</h1>
              <Button
                onClick={() => handleOpenTutorial()}
                className="flex items-center justify-center bg-[#F0F0F0] h-[36px] gap-1 p-2 rounded-xl	hover:bg-[#F0F0F0]"
              >
                <TutorialIcon /><p className="text-[#8F5CFF] text-xs mt-[1px]">Guide</p>
              </Button>
            </div>,
            subTitle: POST_COMMENT_LABEL.sub_title,
          }}
          rightChildren={
            <ButtonRequest
              title="Add Facebook Post URL"
              colorButton="white"
              className="[&_svg]:-rotate-45 w-full h-10 sm:h-9 sm:w-fit"
            />
          }
          className="items-center md:items-center"
        />
        <ListAdsWrapper />
      </>
    </ShowDetailProvider>
  );
};

export default AdsPostView;
