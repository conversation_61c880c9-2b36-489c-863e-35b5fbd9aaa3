import { <PERSON> } from "react-router-dom";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { BadgeStatus } from "./Card/CardDescription";
import PostInformation from "./PostInformation";
import PageInformation from "components/YourPage/PageInformation";
import PageMetrics from "components/YourPage/PageMetrics";

import { IAdsPostItem } from "types/CrawlService";
import { getPublishDate } from "utils/CrawlService";
import { FB_SERVICE_PATH } from "types/Router";

interface Props {
  post: IAdsPostItem;
  isShowPageInfor?: boolean;
  isShowCTA?: boolean;
}
const QuickViewDetail = ({ isShowPageInfor = true, isShowCTA = true, post }: Props) => {
  const {
    actor_id,
    avatar,
    is_active,
    publish_time,
    content,
    post_id,
    count_comment,
    count_reaction,
    count_share,
  } = post;

  return (
    <Box
      variant="col-start"
      className="p-3 gap-4 flex-1 mt-0 overflow-y-auto max-h-[650px] w-full bg-custom-primary sm:p-6"
    >
      <div className="text-secondary text-md font-medium" children="About the advertiser" />
      {isShowPageInfor && (
        <PageInformation id={actor_id} avatar={avatar || ""} className="bg-white" />
      )}

      <Box variant="col-start" className="w-full my-3 gap-4">
        <Box className="w-full">
          <BadgeStatus active={is_active} />
          <div className="text-xs text-secondary">
            <span>Started running: </span>
            <span>{getPublishDate({ date: publish_time, monthShort: true }).publishDate}</span>
          </div>
        </Box>
      </Box>
      <div className="h-fit text-sm text-secondary overflow-y-auto max-h-[400px] flex-1">
        {content}
      </div>
      <Box variant="col-start" className="w-full bg-white gap-0">
        <PostInformation {...post} />
        <PageMetrics
          post_id={post_id}
          comment_count={count_comment}
          reaction_count={count_reaction}
          share_count={count_share}
          className="pt-3 text-secondary font-bold text-sm"
        />
      </Box>
      {isShowCTA && (
        <Button
          variant="main"
          className="w-full rounded-xl h-10 border border-[#0A0F2914] shadow-[0px_1px_2px_0px_#14151A0D] bg-primary"
          children={
            <Link
              to={`/${FB_SERVICE_PATH.POST}/${post_id}`}
              children={"View Ads Detail"}
              className="w-full"
            />
          }
        />
      )}
    </Box>
  );
};

export default QuickViewDetail;
