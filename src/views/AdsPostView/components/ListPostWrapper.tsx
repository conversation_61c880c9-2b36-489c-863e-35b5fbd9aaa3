import { IAdsPostItem, IQuickViewItem } from "types/CrawlService";
import CardOverview from "./Card/CardOverviews";

const ListPostWrapper = ({
  ColumnData,
  handleQuickView,
}: {
  ColumnData: IAdsPostItem[];
  handleQuickView: ({ id, post, avatar }: IQuickViewItem<IAdsPostItem>) => void;
}) => (
  <div className="flex flex-col w-full gap-6">
    {ColumnData.map((post, index) => {
      return <CardOverview post={post} handleClick={handleQuickView} key={post.actor_id + index} />;
    })}
  </div>
);

export default ListPostWrapper;
