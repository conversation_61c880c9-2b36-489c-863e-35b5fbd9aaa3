import { useState } from "react";
import { RiCloseLine } from "@remixicon/react";

import { Dialog, DialogContent } from "components/ui/dialog";
import LoadingSkeleton from "components/LiveAdsPost/LoadingSkeleton";
import ListPostWrapper from "./ListPostWrapper";
import QuickViewDetail from "./QuickViewDetail";
import QuickViewImageLeft from "./QuickViewImageLeft";
import EmptyYourFollow from "views/YourPageView/components/EmptyYourFollow";

import { IAdsPostItem, IQuickViewItem, IQuickViewModal } from "types/CrawlService";
import distributeIntoColumns from "utils/LiveAdsPost/distributeIntoColumns";
import { cn } from "utils/utils";
import NoLivestreamMatch from '../../YourPageView/components/NoLivestreamMatch';
import useFilters from '../../../hooks/useFilters';

interface Props {
  data: IAdsPostItem[];
  total: number;
  loading?: boolean;
  lastElementRef: any;
}
const Container = ({ data, total, lastElementRef, loading }: Props) => {
  const {paramsNoPage} = useFilters();
  const [quickView, setQuickView] = useState<IQuickViewModal<IAdsPostItem>>({
    isOpen: false,
    id: "",
    data: undefined,
  });

  const handleQuickView = ({ id, post, avatar }: IQuickViewItem<IAdsPostItem>) => {
    setQuickView((prev) => ({
      ...prev,
      isOpen: true,
      id: id,
      data: { ...post, avatar: avatar },
    }));
  };
  const { columnA, columnB, columnC } = distributeIntoColumns(data);

  return (
    <>
      <>
        <div className="w-full grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 items-start justify-center gap-6 py-6">
          <ListPostWrapper handleQuickView={handleQuickView} ColumnData={columnA} />
          <ListPostWrapper handleQuickView={handleQuickView} ColumnData={columnB} />
          <ListPostWrapper handleQuickView={handleQuickView} ColumnData={columnC} />
        </div>

        {/* show skeleton */}
        {(data.length > 0 || loading) && (
          <div
            ref={lastElementRef}
            className={cn(
              "w-full grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 items-start justify-center gap-6 py-6",
              data.length == total && "hidden"
            )}
          >
            {[1, 2, 3].map((item) => (
              <LoadingSkeleton key={item} />
            ))}
          </div>
        )}
      </>

      {/* show empty data */}
      {paramsNoPage && Object.keys(paramsNoPage).length > 0 && data.length == 0 ? <NoLivestreamMatch /> : data.length == 0 && !loading && (
        <div className="w-full text-center">
          <EmptyYourFollow />
        </div>
      )}

      {/* Show dialog quick view */}
      {quickView?.data && (
        <Dialog
          open={quickView.isOpen}
          onOpenChange={(open) => setQuickView((prev) => ({ ...prev, isOpen: open }))}
        >
          <DialogContent
            className="h-auto max-w-screen-xl p-0 rounded-sm shadow-sm m-0 border-none bg-custom-secondary overflow-auto [&>button]:text-white [&>button]:z-[3] sm:[&>button]:text-black"
            closeIcon={<RiCloseLine />}
          >
            <div className={cn("grid grid-cols-1 sm:grid-cols-2")}>
              <QuickViewImageLeft
                title={quickView?.data?.actor_name}
                att_type={quickView?.data?.att_type || ""}
                attachments={quickView?.data?.attachments}
              />

              <QuickViewDetail post={quickView?.data} />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default Container;
