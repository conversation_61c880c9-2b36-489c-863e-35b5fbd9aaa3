import { Box } from "components/Box";
import PlatformFormat from "components/LiveAdsPost/Platforms";
import { getPublishDate } from "utils/CrawlService";
import { cn } from "utils/utils";

interface Props {
  publish_time: string;
  is_active: boolean | undefined;
  publisher_platform: string[];
}
const CardDescription = ({ publish_time, is_active, publisher_platform }: Props) => (
  <Box variant="col-start" className="gap-0 w-full justify-end text-xs text-secondary">
    <Box className="w-full">
      <BadgeStatus active={is_active} />
      <span>Started running: {getPublishDate({ date: publish_time }).publishDate}</span>
    </Box>
    {publisher_platform && publisher_platform.length > 0 && (
      <Box className="flex-1 gap-1 justify-end w-full">
        <span>Platforms:</span>
        <PlatformFormat platforms={publisher_platform} />
      </Box>
    )}
  </Box>
);

export default CardDescription;

export const BadgeStatus = ({ active }: { active?: boolean }) => (
  <Box
    className={cn(
      "px-2 py-1 rounded-full gap-1 text-xs",
      active ? "bg-success-primary text-success-subtitle" : "bg-custom-disable text-custom-tertiary"
    )}
  >
    <div className={cn("w-2 h-2 rounded-full", active ? "bg-green-500" : "bg-custom-tertiary")} />
    <span>{active ? "Active" : "Inactive"}</span>
  </Box>
);
