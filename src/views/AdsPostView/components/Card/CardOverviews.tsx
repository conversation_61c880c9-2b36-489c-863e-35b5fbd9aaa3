import { RiFocus3Line } from "@remixicon/react";
import { Link } from "react-router-dom";

import { Card } from "components/ui/card";
import { Button } from "components/ui/button";
import PostThumbnail from "components/LiveAdsPost/PostThumbnail";
import CardDescription from "./CardDescription";
import CardTitleOverview from "./CardTitleOverview";

import { FB_SERVICE_PATH } from "types/Router";
import { IAdsPostItem, IQuickViewItem } from "types/CrawlService";
import { cn } from "utils/utils";
import { fNumberToString } from "utils/number";

export interface CardOverViewProps {
  post: IAdsPostItem;
  className?: string;
  disablePreview?: boolean;
  handleClick: ({ id, post, avatar }: IQuickViewItem<IAdsPostItem>) => void;
}

const CardOverview = ({ post, className, handleClick }: CardOverViewProps) => {
  const {
    post_id,
    actor_name,
    content,
    publish_time = "",
    is_active,
    publisher_platform,
    attachments,
    att_type,
  } = post;
  return (
    <Card
      className={cn(
        "p-4 rounded-2xl bg-custom-primary border-none !shadow-medium w-full gap-4 h-fit flex flex-col",
        className
      )}
    >
      <CardTitleOverview post={post as IAdsPostItem} handleClick={handleClick} />

      <CardDescription
        is_active={is_active}
        publish_time={publish_time}
        publisher_platform={publisher_platform || []}
      />

      <PostThumbnail
        title={actor_name}
        attachments={attachments}
        att_type={att_type || ""}
        classSlider="post-carousel"
      />
      <div className="h-fit text-sm text-secondary line-clamp-3 overflow-hidden text-ellipsis">
        {content}
      </div>

      <Link className="w-full" to={`/${FB_SERVICE_PATH.POST}/${post_id}`}>
        <Button className="w-full text-md font-medium rounded-xl" variant="main">
          <RiFocus3Line size={20} className="mr-1" />
          Total Lead : {post.audience?.size ? fNumberToString(post.audience?.size) : 0}
        </Button>
      </Link>
    </Card>
  );
};
export default CardOverview;
