import { RiEyeLine } from "@remixicon/react";

import { Box } from "components/Box";
import { CardOverViewProps } from "./CardOverviews";
import AvatarByName from "components/AvatarByName";
import PagePreview from "components/YourPage/PagePreview";

import useAvatar from "hooks/useAvatar";
import { tracking } from '../../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../../store';

const CardTitleOverview = ({ post, disablePreview, handleClick }: CardOverViewProps) => {
  const { actor_name, post_id, actor_id } = post;
  const { user } = useAppSelector((state) => state.auth);
  const { avatar } = useAvatar({ type: "page", uid: post.actor_id });

  const handleTracking = () => {
    handleClick({ id: post_id, post: { ...post }, avatar: avatar.url });
    tracking({
      eventName: 'preview_data',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          data_id: post_id
        })
      }
    });
  };
  return (
    <Box className="flex items-center justify-between gap-2 w-full">
      <AvatarByName urlImage={avatar.url} name={actor_name} className="w-12 h-12" />
      <PagePreview
        id={actor_id}
        post_id={post_id}
        avatar={avatar.url}
        page_name={actor_name}
        disablePreview={disablePreview}
      />
      <div
        className="cursor-pointer hover:opacity-70"
        onClick={handleTracking}
        children={
          <div className="p-1 bg-custom-secondary rounded-sm">
            <RiEyeLine className="text-secondary" size={18} />
          </div>
        }
      />
    </Box>
  );
};

export default CardTitleOverview;
