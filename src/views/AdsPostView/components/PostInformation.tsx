import { Box } from "components/Box";
import MetricItem from "components/LiveAdsPost/MetricItem";

import { IAdsPostItem } from "types/CrawlService";

import { FormatOptions } from "constants/postComment";
import useGetCategoryName from "hooks/useGetCategoryName";

interface IPageMetricsProps
  extends Partial<Pick<IAdsPostItem, "audience" | "post_id" | "att_type" | "publisher_platform">> {}

const PostInformation = (props: IPageMetricsProps) => {
  const { att_type, publisher_platform } = props;
  const categoryName = useGetCategoryName(props.audience?.category);
  const className =
    "w-fit gap-0 justify-center items-center flex-col text-secondary font-semibold text-md";
  return (
    <Box variant="default" className="w-full justify-between border-b border-t py-3 max-[768px]:flex-col">
      <MetricItem
        title="Display Format:"
        content={FormatOptions.find((option) => option.value === att_type)?.label || "--"}
        className={className}
      />
      <MetricItem title="Categories:" content={categoryName || ""} className={className} />
      <MetricItem
        title="Platforms:"
        content={publisher_platform || ["FACEBOOK"]}
        isPlatform
        className={className}
      />
    </Box>
  );
};

export default PostInformation;
