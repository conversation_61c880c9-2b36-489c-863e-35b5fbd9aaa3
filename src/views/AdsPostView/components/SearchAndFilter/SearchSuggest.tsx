import { LegacyRef, useEffect, useState } from "react";
import { RiSearchLine } from "@remixicon/react";

import { Box } from "components/Box";
import { Card, CardHeader } from "components/ui/card";

import { adsPostAPI } from "apis/adsPost";
import useDebounce from "hooks/useDebounce";
import { PageSuggest } from "types/LiveService";
import { IAdsPostItem } from "types/CrawlService";
import ListSuggestWrapper from "views/LivePostView/components/SearchAndFilter/ListSuggestWrapper";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import { cn } from "utils/utils";

interface Props {
  value: string;
  refSecond: LegacyRef<HTMLDivElement> | undefined;
  handleSearch: () => void;
}

interface IData {
  pageSuggest: PageSuggest[];
  adsSuggest: IAdsPostItem[];
  loading: boolean;
}

const SearchSuggest = ({ value, refSecond, handleSearch }: Props) => {
  const debounceValue = useDebounce(value, 500);

  const [data, setData] = useState<IData>({
    pageSuggest: [],
    adsSuggest: [],
    loading: false,
  });

  useEffect(() => {
    if (debounceValue !== "") {
      getData();
    } else {
      setData((prev) => ({
        ...prev,
        pageSuggest: [],
        adsSuggest: [],
      }));
    }
  }, [debounceValue]);

  const getData = async () => {
    setData((prev) => ({ ...prev, loading: true }));

    const response = await adsPostAPI.getSuggest({ q: debounceValue });
    if (response && response.data) {
      setData((prev) => ({
        ...prev,
        pageSuggest: response.data.pages,
        adsSuggest: response.data.ad_posts,
      }));
    }
    setData((prev) => ({ ...prev, loading: false }));
  };
  const isEmpty = debounceValue == "";
  return (
    <Card
      ref={refSecond}
      className="mt-2 shadow-sm text-primary p-4 absolute w-full top-3 z-[99] translate-y-[24px] gap-2 border-custom-primary"
    >
      <CardHeader className="p-0 m-0">
        <Box
          variant="row-start"
          className="justify-start gap-1 cursor-pointer text-custom-tertiary"
          onClick={isEmpty ? () => {} : () => handleSearch()}
        >
          <RiSearchLine size={20} />
          {data.loading && <LoadingButtonIcon />}
          {!data.loading && (
            <p
              className={cn("text-md font-normal", !isEmpty && "text-brand-strong")}
              children={isEmpty ? '"keyword"' : value}
            />
          )}
        </Box>
      </CardHeader>

      <ListSuggestWrapper<IAdsPostItem>
        listSuggest={data.adsSuggest}
        isShow={data.adsSuggest.length > 0}
        type="ads-post"
      />

      <ListSuggestWrapper<PageSuggest>
        listSuggest={data.pageSuggest}
        isShow={data.pageSuggest.length > 0}
        type="page"
      />
    </Card>
  );
};

export default SearchSuggest;
