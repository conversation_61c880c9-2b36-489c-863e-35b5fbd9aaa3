import { useEffect, useState } from 'react';

import { Box } from 'components/Box';
import { Card } from 'components/ui/card';
import filterOptions from 'utils/LiveAdsPost/filterOptions';
import FilterChipGroup from 'components/FilterChips/FilterChipGroup';
import FilterOptions from 'components/FilterOptions';
import SearchModal from 'components/LiveAdsPost/SearchModal';
import SearchSuggest from './SearchSuggest';
import ClearAll from 'components/FilterChips/ClearAll';

import useFilters from 'hooks/useFilters';
import { TSelectedOption } from 'types/Select';
import { IFilterOptions } from 'types/PostService';

import { cn } from 'utils/utils';
import { handleFilter } from 'utils/LiveAdsPost';
import { FILTER_CHIP_LABEL } from 'constants/postComment';
import { RiFilter2Line } from '@remixicon/react';

const SearchAndFilterWrapper = (props: IFilterOptions) => {
  const { params, paramsNoPage, setSearchParams } = useFilters();

  const [selectedOption, setSelectedOption] = useState<TSelectedOption>(params);
  const [searchTerm, setSearchTerm] = useState<string>('');

  useEffect(() => {
    setSelectedOption(params);
  }, [params]);

  useEffect(() => {
    handleFilter({ params, searchTerm, selectedOption, setSearchParams });
  }, [selectedOption]);

  const filterGroup = filterOptions({
    ...props,
    params,
    setSelectedOption
  });
  const isShowClearAll = Object.keys(paramsNoPage).length > 0;

  return (
    // using in ads post community
    <Card className={cn('mt-0 w-full border-none shadow-none p-0', props.className)}>
      <>
        <SearchModal
          value={searchTerm}
          placeholder="Search by keyword or page"
          SearchSuggest={SearchSuggest}
          setSearchTerm={setSearchTerm}
        />
        <Box variant="default" className="my-4 gap-4 box-border hidden md:flex">
          <FilterOptions filterGroup={filterGroup} selectedOption={selectedOption} />
        </Box>
        <div className="block md:hidden rounded-xl max-lg:mt-6 max-[768px]:shadow-sm transition-all duration-300">
          <div>
            <div className="gap-6 justify-between flex min-[768px]:hidden items-center py-[10px] px-2">
              <div className="flex gap-1 font-semibold text-sm text-[#515667]">
                <RiFilter2Line size={20} /> Filter
              </div>
            </div>
            <div className="flex gap-4 max-md:p-2 min-lg:mt-6 flex-col items-start max-[768px]:mt-0 md:flex-row [&>div]:w-full">
              <FilterOptions filterGroup={filterGroup} selectedOption={selectedOption} />
            </div>
          </div>
          <div className="md:hidden flex gap-2 flex-wrap items-baseline mb-5 p-2 pt-0">
            <FilterChipGroup chipLabel={FILTER_CHIP_LABEL} limit_badge={2} />
            {isShowClearAll && <ClearAll />}
          </div>
        </div>


        <div className="gap-2 flex-wrap items-baseline hidden md:flex">
          <FilterChipGroup chipLabel={FILTER_CHIP_LABEL} limit_badge={2} />
          {isShowClearAll && <ClearAll />}
        </div>
      </>
    </Card>
  );
};

export default SearchAndFilterWrapper;
