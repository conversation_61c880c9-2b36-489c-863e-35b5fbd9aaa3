import { <PERSON> } from "react-router-dom";
import { Ri<PERSON>hat3Line, RiShareForwardLine, RiThumbUpLine } from "@remixicon/react";

import { Box } from "components/Box";
import AvatarByName from "components/AvatarByName";
import MetricsCompact from "components/LiveAdsPost/MetricsCompact";

import { IAdsPostItem } from "types/CrawlService";
import { PATH_DASHBOARD } from "types/path";
import useAvatar from "hooks/useAvatar";

interface Props
  extends Partial<
    Pick<
      IAdsPostItem,
      | "post_id"
      | "actor_name"
      | "content"
      | "is_active"
      | "publish_time"
      | "count_comment"
      | "count_reaction"
      | "count_share"
      | "actor_id"
    >
  > {}

const AdsPostSuggest = (props: Props) => {
  const {
    post_id = "",
    actor_id = "",
    actor_name = "",
    content = "",
    count_comment,
    count_share,
    count_reaction,
  } = props;

  const { avatar } = useAvatar({ type: "page", uid: actor_id });

  return (
    <Box className="gap-3 flex-1 w-full items-start p-2">
      <AvatarByName urlImage={avatar.url} name={actor_name} className="w-12 h-12" type={1} />
      <Box variant="col-start" className="gap-1 w-full flex-1">
        {post_id && (
          <Link to={`${PATH_DASHBOARD.ads_analytics[""]}/${post_id}`}>
            <Box
              variant="col-start"
              className="gap-1 text-custom-tertiary hover:text-tertiary cursor-pointer text-sm"
            >
              <h3 className="text-secondary">{actor_name}</h3>
              {content && <span className="line-clamp-1">{content}</span>}
            </Box>
          </Link>
        )}
        <Box className="text-secondary text-sm gap-2">
          <MetricsCompact
            num={count_reaction || 0}
            icon={<RiThumbUpLine size={16} />}
            content="likes"
            className="text-sm text-secondary"
            sizeNumber="sm"
          />
          <MetricsCompact
            num={count_comment || 0}
            icon={<RiChat3Line size={16} />}
            content="comments"
            className="text-sm text-secondary"
            sizeNumber="sm"
          />
          <MetricsCompact
            num={count_share || 0}
            icon={<RiShareForwardLine size={16} />}
            content="shares"
            className="text-sm text-secondary"
            sizeNumber="sm"
          />
        </Box>
      </Box>
    </Box>
  );
};

export default AdsPostSuggest;
