import { Box } from "components/Box";
import PostThumbnail from "components/LiveAdsPost/PostThumbnail";
import { FORMAT_TYPE_LABEL } from "constants/postComment";
import { IAttachment } from "types/CrawlService";
import { cn } from "utils/utils";

interface Props {
  title: string;
  att_type: string;
  attachments: IAttachment[];
}
const QuickViewImageLeft = ({ att_type, title, attachments }: Props) => {
  return (
    <>
      {att_type == FORMAT_TYPE_LABEL.VIDEO && (
        <Box className="w-full bg-black items-center">
          <PostThumbnail
            title={title}
            att_type={att_type || "img"}
            attachments={attachments}
            className="max-h-[650px] rounded-none"
          />
        </Box>
      )}
      {att_type == FORMAT_TYPE_LABEL.IMAGE && (
        <Box className="items-center bg-custom-secondary sm:bg-black">
          <PostThumbnail
            title={title}
            att_type={att_type || "img"}
            attachments={attachments}
            className="!h-full max-h-[343px] rounded-md px-6 w-auto object-cover flex-1 sm:max-h-[650px] sm:object-top sm:rounded-none"
          />
        </Box>
      )}
      {(att_type == FORMAT_TYPE_LABEL.MULTI_IMAGES || att_type == FORMAT_TYPE_LABEL.CAROUSEL) && (
        <div className="overflow-hidden bg-custom-secondary sm:pl-6 sm:pt-6 sm:pb-4">
          <PostThumbnail
            title={title}
            att_type={att_type || "img"}
            attachments={attachments}
            slideToShow={1.5}
            className={cn(
              "h-[490px] max-h-full object-cover  rounded-none border-none",
              att_type == FORMAT_TYPE_LABEL.MULTI_IMAGES ? " h-[590px] rounded-sm" : "h-[490px]"
            )}
            classSlider="quickview-carousel"
          />
        </div>
      )}
    </>
  );
};

export default QuickViewImageLeft;
