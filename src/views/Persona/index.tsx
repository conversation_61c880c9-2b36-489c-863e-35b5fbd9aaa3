import { useContext, useRef } from "react";
import { ModalContext } from "providers/Modal";
import { personaContext } from "./context/PersonaContext";

import { toast } from "components/ui/use-toast";
import { Box } from "components/Box";
import Container from "./components/Container";
import BreadCrumb from "components/Breadcrumb";
import HeaderWrapper from "views/SocialDataView/components/Header";
// import BuyAudienceIcon from "assets/icons/BuyAudienceIcon";
import BuyPersonaAudience from "../../components/Transaction/BuyPersonaAudience";

import useFilters from "hooks/useFilters";
import LABEL from "constants/label";
import { PERSONA_LABEL } from "constants/persona/label";
import { maxExceeded, minExceeded } from "constants/persona";
import {
  convertAudienceName,
  convertDescriptionAudience,
} from "utils/persona/formatAudienceAutoFill";
import { cn } from "utils/utils";
import FilterPersonaHistory from '../../components/PersonaHistorySearch';
import { Button } from '../../components/ui/button';
import { TutorialIcon } from '../../assets/icons/TutorialIcon';
import useOutsideClick from '../../hooks/useClickOutSide';
import handleCloseModal from '../../utils/handleCloseModal';
import { RiCloseLine } from '@remixicon/react';
import { tracking } from '../../utils/Tracking/tracking';
import { useAppSelector } from '../../store';

const PersonaView = () => {
  const ref = useRef(null);
  const refSecond = useRef(null);
  const { params ,paramsNoPage } = useFilters();
  const modal = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);
  const resultCount = personaContext().resultCount;
  const handleBuyAudience = (isDataSet?: boolean) => {
    switch (true) {
      case isMinExceeded:
        return toast({
          title: PERSONA_LABEL.message_limit_min,
          status: "warning",
        });
      case isMaxExceeded:
        return toast({
          title: PERSONA_LABEL.message_limit_max,
          status: "warning",
        });
      default:
        tracking({
          eventName: 'purchase_preview',
          params: {
            user_id: user?.uuid,
            valueTracking: JSON.stringify({
              audience_id: params,
              audience_type: isDataSet ? 'DATASET' : 'AUDIENCE'
            })
          }
        });
        modal?.setDataDialog((prev) => ({
          ...prev,
          isOpen: true,
          isShowTitle: false,
          content: (
            <BuyPersonaAudience
              audienceName={convertAudienceName(paramsNoPage)}
              description={convertDescriptionAudience(paramsNoPage)}
              resultCount={resultCount}
              isDataset={isDataSet}
            />
          ),
          className: "max-w-[680px]",
        }));
        break;
    }
  };

  const isMinExceeded = resultCount.number < minExceeded;
  const isMaxExceeded = resultCount.number !== 0 && resultCount.number > maxExceeded;
  const isParamsValid = Object.keys(paramsNoPage).length === 0;



  useOutsideClick(ref, refSecond, () => {
    if (!!modal?.dataDialog?.isPopupVideo) {
      handleCloseModal(modal);
    }
  });
  const handleOpenTutorial =()=>{
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      isPopupVideo: true,
      content: (
        <div ref={refSecond}>
          <div className="text-right">
            <Button
              onClick={() => handleCloseModal(modal)}
              className="p-[10px] text-sm font-semibold mb-1 bg-transparent hover:bg-transparent text-primary"
            >
              <RiCloseLine size={20} />
              Close
            </Button>
          </div>
          <iframe
            src="https://www.youtube.com/embed/kcTNOwMBb4U?autoplay=1"
            title="Big360 - Work Persona"
            className="rounded-2xl w-full max-w-[920px] aspect-video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
        </div>
      ),
      className: 'max-w-[936px] !p-2 max-lg:max-w-[calc(100%-12px)]'
    } ))
  }
  return (
    <>
      <BreadCrumb />
      <HeaderWrapper
        className="md:items-end w-full max-lg:gap-4"
        leftChildren={{
          title: <div className="flex gap-2" ref={ref}>
            <h1>{PERSONA_LABEL.title}</h1>
            <Button
              onClick={() => handleOpenTutorial()}
              className="flex items-center justify-center bg-[#F0F0F0] h-[36px] gap-1 p-2 rounded-xl	hover:bg-[#F0F0F0]"
            >
              <TutorialIcon /><p className="text-[#8F5CFF] text-xs mt-[1px]">Guide</p>
            </Button>
          </div>, subTitle: PERSONA_LABEL.subTitle }}
        rightChildren={
          <Box variant="row-start" className="gap-4 max-lg:w-full">
            <FilterPersonaHistory type={'work'}/>
            <Button
              variant="secondary"
              onClick={() => handleBuyAudience(true)}
              disabled={isParamsValid || isMaxExceeded || resultCount.number == 0}
              className={cn(
                'flex items-center gap-0.5 px-[10px] shadow-xs rounded-lg py-1.5 font-medium text-sm text-primary bg-[#F0F0F0] w-[130px] max-lg:w-full',
                ( isParamsValid || resultCount.number == 0 ) && 'text-secondary bg-white'
              )}
            >
              {LABEL.buy_dataset}
            </Button>


            {/*<Button*/}
            {/*  variant="secondary"*/}
            {/*  onClick={()=>handleBuyAudience(false)}*/}
            {/*  disabled={isParamsValid || isMaxExceeded || resultCount.number == 0}*/}
            {/*  className={cn(*/}
            {/*    "flex items-center gap-0.5 px-[10px] shadow-xs rounded-lg py-1.5 font-medium text-sm text-white bg-custom-gradient w-[130px]",*/}
            {/*    (isParamsValid || resultCount.number == 0) && "text-secondary bg-white"*/}
            {/*  )}*/}
            {/*>*/}
            {/*  <BuyAudienceIcon isLimitFilter={isParamsValid || resultCount.number == 0} />*/}
            {/*  {isMaxExceeded ? LABEL.talk_to_sale : LABEL.buy_audience}*/}
            {/*</Button>*/}
          </Box>
        }
      />
      <Container />
    </>
  );
};

export default PersonaView;
