import { persona<PERSON><PERSON> } from "apis/persona";
import { toast } from "components/ui/use-toast";
import { FILTER_KEY } from "constants/persona";
import useFilters from "hooks/useFilters";
import React, { createContext, useState, ReactNode, useEffect } from "react";
import { IUserMap, LocationData } from "types/map";
import { Map as LeafletMap } from "leaflet";

export type THandleShowMap = (input: "SHOW" | "HIDE") => void;
export interface IMapContext {
  parentMap: LeafletMap;
  usersRadius: IUserMap;
  mapCenter: [number, number];
  setMapCenter: React.Dispatch<React.SetStateAction<[number, number]>>;
  handleResearchRadius: (radius: number) => void;
  showMap: "SHOW" | "HIDE";
  handleShowMap: THandleShowMap;
  handleSearchLocation: (q: string) => Promise<LocationData[]>;
  mapRadius: {
    radius: string;
    unit: string;
  };
  setParentMap: React.Dispatch<React.SetStateAction<LeafletMap | undefined>>;
  setMapRadius: React.Dispatch<React.SetStateAction<{ radius: string; unit: string }>>;
}
const mapDefault = {
  usersRadius: {
    items: [],
    count: 0,
  },
  mapCenter: [10.762622, 106.660172],
};

const MapContext = createContext<IMapContext | undefined>(undefined);

export const MapProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { setSearchParams, searchParams, params } = useFilters();
  const [parentMap, setParentMap] = useState<LeafletMap>();
  const geosearch = params[FILTER_KEY.map_radius]?.split(",") ?? [];
  const [showMap, setShowMap] = useState<"SHOW" | "HIDE">("HIDE");
  const [lat, lng] = geosearch.map(Number);
  const handleShowMap = (input: "SHOW" | "HIDE") => {
    setShowMap(input);
  };
  const [mapRadius, setMapRadius] = useState<{ radius: string; unit: string }>({
    radius: "",
    unit: "km",
  });

  const [mapCenter, setMapCenter] = useState<[number, number]>([
    lat ?? mapDefault.mapCenter[0],
    lng ?? mapDefault.mapCenter[1],
  ]);
  const [usersRadius, setUsersRadius] = useState<IUserMap>(mapDefault.usersRadius);

  const handleResearchRadius = (radius: number) => {
    setSearchParams({
      ...params,
      [FILTER_KEY.map_radius]: [`${mapCenter[0]},${mapCenter[1]},${radius * 1000}`],
    });
  };
  useEffect(() => {
    const [lat, lng] = geosearch.map(Number);
    if (mapCenter[0] !== lat || mapCenter[1] !== lng || geosearch.length === 0) {
      setUsersRadius(mapDefault.usersRadius);
    }
  }, [mapCenter, searchParams]);

  const handleSearchLocation = async (q: string): Promise<LocationData[]> => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(q)}&format=json`,
        {
          method: "GET",
        }
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data: LocationData[] = await response.json();
      if (!data.length) {
        throw new Error("No data found");
      }
      return data;
    } catch (error) {
      throw error;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (geosearch.length === 0) return;
      try {
        const res = await personaAPI.previewMap({
          params: searchParams,
        });
        setUsersRadius(res?.data);
      } catch (error: any) {
        toast({
          title: "Error",
          description: String(error),
          status: "error",
        });
        setUsersRadius(mapDefault.usersRadius);
      }
    };
    fetchData();
  }, [searchParams]);

  const value: IMapContext = {
    showMap,
    usersRadius,
    mapCenter,
    setMapCenter,
    handleResearchRadius,
    handleShowMap,
    handleSearchLocation,
    mapRadius,
    setMapRadius,
    setParentMap,
    parentMap: parentMap!,
  };

  return <MapContext.Provider value={value}>{children}</MapContext.Provider>;
};

export const useMapContext = () => {
  const context = React.useContext(MapContext);
  if (context === undefined) {
    throw new Error("useMapContext must be used within a MapProvider");
  }
  return context;
};
