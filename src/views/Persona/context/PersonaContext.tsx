import { personaAPI } from "apis/persona";
import useFilters from "hooks/useFilters";
import usePrevious from "hooks/usePrevious";
import isEqual from "lodash/isEqual";
import omit from "lodash/omit";
import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useContext,
  useEffect,
  useState,
} from "react";
import { useAbortController } from '../../../hooks/useAbortController';

export interface ResultCountProps {
  number: number;
  loading: boolean;
}
export interface PersonaContextProps {
  resultCount: ResultCountProps;
  setResultCount: Dispatch<SetStateAction<ResultCountProps>>;
}

const PersonaContext = createContext<PersonaContextProps>({
  resultCount: {
    number: 0,
    loading: false,
  },
  setResultCount: () => {},
});

const PersonaProvider = ({ children }: { children: ReactNode }) => {
  const { paramsNoPage, searchParams } = useFilters();
  const { newAbortController } = useAbortController();
  const prevParams = usePrevious(paramsNoPage);

  const [resultCount, setResultCount] = useState<ResultCountProps>({
    number: 0,
    loading: false,
  });

  useEffect(() => {
    const prevParamsOmitted = omit(prevParams, ["page", "order_by"]);
    if (!isEqual(paramsNoPage, prevParamsOmitted) && !!searchParams.size) {
      getCountResult();
    }

    if (searchParams.size === 0) {
      setResultCount((prev) => ({ ...prev, number: 0 }));
    }
  }, [paramsNoPage, searchParams.size]);

  const getCountResult = async () => {
    const controller = newAbortController();
    setResultCount((prev) => ({ ...prev, loading: true }));
    const res = await personaAPI.get({ endpoint: "count/", params: { ...paramsNoPage }, signal: controller.signal });
    if (res.data) {
      setResultCount((prev) => ({ ...prev, number: res.data.count }));
    }
    setResultCount((prev) => ({ ...prev, loading: false }));
  };

  return (
    <PersonaContext.Provider
      value={{
        resultCount,
        setResultCount,
      }}
    >
      {children}
    </PersonaContext.Provider>
  );
};

const personaContext = (): PersonaContextProps => useContext(PersonaContext);
export { PersonaProvider, personaContext };
