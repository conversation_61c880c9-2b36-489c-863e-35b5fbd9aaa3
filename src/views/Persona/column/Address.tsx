import { PersonaAudiencePreview } from "types/Persona";

const Address = ({ person_district, person_province }: PersonaAudiencePreview) => {
  return person_district || person_province ? (
    <div className="w-[250px]">
      {person_district && <span className="mr-2">{person_district},</span>}
      {person_province && <span>{person_province}</span>}
    </div>
  ) : (
    <span>--</span>
  );
};

export default Address;
