import { TooltipPortal } from "@radix-ui/react-tooltip";
import { Box } from "components/Box";
import { Badge } from "components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import { PersonaAudiencePreview } from "types/Persona";

const Position = ({ position_department, position_level, position }: PersonaAudiencePreview) => {
  return position ? (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="py-2 overflow-hidden text-ellipsis w-fit whitespace-nowrap md:max-w-[100px] lg:max-w-[150px] xl:max-w-[250px] inline-block">
            {position}
          </span>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent
            align="center"
            side="bottom"
            sideOffset={-5}
            className="flex items-baseline gap-1"
          >
            <Box variant="col-start" className="gap-1 justify-start">
              <span
                className="capitalize pt-2 font-medium mr-2 max-w-[250px]"
                children={position}
              />
              <Box variant="row-start" className="gap-2 my-2">
                <Badge variant="default" children={position_level} />
                <Badge variant="outline" children={position_department} />
              </Box>
            </Box>
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  ) : (
    <span>--</span>
  );
};

export default Position;
