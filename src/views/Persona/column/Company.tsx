import { TooltipPortal } from "@radix-ui/react-tooltip";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import { PersonaAudiencePreview } from "types/Persona";

const Company = ({ company_name, company_province, company_size }: PersonaAudiencePreview) => {
  return company_name ? (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="py-2 overflow-hidden text-ellipsis w-fit whitespace-nowrap md:max-w-[100px]  lg:max-w-[150px] xl:max-w-[350px] inline-block">
            {String(company_name) === "null" || "" ? "-" : String(company_name)}
          </span>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="bottom" sideOffset={-5} className="">
            <span className="capitalize py-2 font-medium">{String(company_name)}</span>
            <div className="mt-2">
              {company_province} - {company_size} employees
            </div>
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  ) : (
    <span>--</span>
  );
};

export default Company;
