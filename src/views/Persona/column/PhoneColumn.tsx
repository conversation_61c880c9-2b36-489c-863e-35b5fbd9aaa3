import { useContext } from "react";
import { RiShoppingCartLine } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import useGetPhone from "hooks/useGetPhone";
import BuyAudienceCustom from "components/Transaction/BuyAudienceCustom";
import { tracking } from '../../../utils/Tracking/tracking';
import { useAppSelector } from '../../../store';

type Props = {
  id: number;
  endpoint: string;
  phoneNumber: string;
  buyProfile?: boolean;
  name: string;
};

const PhoneColumn = ({ id, endpoint, phoneNumber, buyProfile, name }: Props) => {
  const modal = useContext(ModalContext);
  const { user } = useAppSelector((state) => state.auth);
  const { phone } = useGetPhone({
    defaultValue: phoneNumber,
    endpoint: endpoint,
    type: "persona",
  });

  const handleOpenModalBuy = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <BuyAudienceCustom uid={id} fullName={name} phone={phone.phone} isBuyProfile={true} />,
      className: "max-w-[680px]",
    }));
    tracking({
      eventName: 'purchase_preview',
      params:{
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          audience_id: id,
          audience_type: 'AUDIENCE'
        })
      }
    })
  };

  return buyProfile ? (
    <button
      id="phone_persona"
      className="text-center text-sm p-1 rounded-sm bg-brand-subtitle text-brand-default !font-sans flex gap-1 items-center"
      onClick={handleOpenModalBuy}
    >
      <span>{phone.phone}</span>
      <RiShoppingCartLine size={16} />
    </button>
  ) : (
    <div className="text-center text-sm p-1 rounded-sm bg-brand-subtitle text-brand-default !font-sans flex gap-1 items-center">
      <span>{phone.phone}</span>
    </div>
  );
};
export default PhoneColumn;
