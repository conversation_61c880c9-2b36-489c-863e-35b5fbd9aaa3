import { Link } from "react-router-dom";
import { TooltipPortal } from "@radix-ui/react-tooltip";

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import AvatarByName from "components/AvatarByName";

import useAvatar from "hooks/useAvatar";
import { PersonaAudiencePreview } from "types/Persona";

const Information = (params: PersonaAudiencePreview) => {
  const { full_name, uid_fb } = params;
  const { avatar } = useAvatar({ type: "profile", uid: uid_fb || "" });

  return (
    <div className="flex items-center gap-2 w-[200px]">
      <AvatarByName urlImage={avatar.url} name={full_name} />
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            {uid_fb ? (
              <Link
                target="_blank"
                to={`https://www.facebook.com/profile.php?id=${uid_fb}`}
                className="overflow-hidden text-ellipsis  whitespace-nowrap md:max-w-[100px] lg:max-w-[150px] inline-block"
              >
                {full_name}
              </Link>
            ) : (
              <span className="overflow-hidden text-ellipsis w-fit whitespace-nowrap md:max-w-[100px] lg:max-w-[150px] xl:max-w-[220px] inline-block">
                {full_name}
              </span>
            )}
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent
              align="center"
              side="bottom"
              sideOffset={5}
              className="flex items-baseline gap-1"
            >
              {full_name}
            </TooltipContent>
          </TooltipPortal>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default Information;
