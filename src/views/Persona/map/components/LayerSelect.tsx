import { cn } from "utils/utils";
import MinimapControl from "./MiniMap";
import { mapLayers } from "../utils";

type Props = {
  isShow: boolean;
  setShowLayerSelect: () => void;
  layer: string;
  handeChangeLayer: (input: string) => void;
};

const LayerSelect: React.FC<Props> = ({ isShow, handeChangeLayer, layer }) => {
  return (
    <div
      className={cn(
        "absolute bottom-2 right-20 w-[200px] bg-white rounded-md",
        "transition-transform duration-300 transform z-[9999] p-2",
        isShow ? "translate-y-0" : "translate-y-[200px]"
      )}
    >
      <div className="flex flex-col gap-2">
        {mapLayers.map((item) => (
          <button
            className={cn(
              "border-2 rounded-md overflow-hidden",
              layer === item.value && "border-primary"
            )}
            onClick={() => handeChangeLayer(item.value)}
          >
            <MinimapControl label={item.key} mapLayerUrl={item.value} />
          </button>
        ))}
      </div>
    </div>
  );
};
export default LayerSelect;
