import { Skeleton } from "components/ui/skeleton";
import useGetPhone from "hooks/useGetPhone";
type Props = {
  loading: boolean;
  label: string;
  value: string;
  type: string;
};

const PhoneLabel: React.FC<Props> = ({ loading, label, value, type }) => {
  const { phone } = useGetPhone({ defaultValue: value, type: type });
  return (
    <div className="border-b w-full pb-1.5">
      <div className="text-[10px]">{label}</div>
      {loading ? (
        <Skeleton className="h-[13px] w-full" />
      ) : (
        <div className="text-[10px] flex items-center justify-between">
          {phone.loading ? (
            <Skeleton className="h-[13px] w-1/2" />
          ) : (
            <div className="py-0.5 font-medium px-[3px] rounded-sm bg-[#E3EAFD] text-[#133A9A]">
              {phone.phone}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PhoneLabel;
