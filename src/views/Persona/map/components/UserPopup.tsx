import { persona<PERSON><PERSON> } from "apis/persona";
import { Skeleton } from "components/ui/skeleton";
import { toast } from "components/ui/use-toast";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import InforLabel from "./InforLabel";
import PhoneLabel from "./PhoneLabel";
import { convertDateFormat } from "utils/utils";
import { IPersonMap } from "types/map";
import AvatarByName from "components/AvatarByName";
import { Link } from "react-router-dom";
import useAvatar from "hooks/useAvatar";
import { useAbortController } from '../../../../hooks/useAbortController';

const UserPopup: React.FC<{ uid: string }> = ({ uid }) => {
  const popupRef = useRef<HTMLDivElement>(null);
  const [data, setData] = useState<{ person: IPersonMap | null; loading: boolean }>({
    person: null,
    loading: false,
  });
  const { newAbortController } = useAbortController();
  const { avatar } = useAvatar({ type: "profile", uid: data.person?.uid_fb! });

  useEffect(() => {
    if (data.person !== null && popupRef.current && popupRef.current.clientHeight > 0) {
      const leafletPopup = document.getElementsByClassName(
        "leaflet-popup"
      ) as HTMLCollectionOf<HTMLElement>;
      if (leafletPopup) {
        leafletPopup[0].style.bottom = `-${popupRef.current.clientHeight / 2 + 5}px`;
      }
    }
  }, [data, popupRef.current]);

  useEffect(() => {
    const fetchData = async () => {
      setData({ person: data.person, loading: true });
      try {
        const controller = newAbortController();
        const res = await personaAPI.detailUserMap({ params: uid, signal: controller.signal });
        setData({ person: res.data, loading: false });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to fetch user data",
          status: "error",
        });
        setData({ person: null, loading: false });
      }
    };

    fetchData();
  }, [uid]);
  const avatarComponent = useCallback(() => {
    if (data.loading || !data.person?.uid_fb) {
      return null;
    }
    return (
      <div className="text-[#4778F5] flex items-center gap-0.5">
        <AvatarByName
          className="h-[20px] w-[20px]"
          name={data.person.full_name}
          urlImage={avatar.url}
        />
        <Link
          to={`https://facebook.com/${data.person?.uid_fb}`}
          target="_blank"
          rel="noreferrer"
          className="text-[#1877F2] text-[10px] font-bold"
        >
          {data.person?.full_name ?? "__"}
        </Link>
      </div>
    );
  }, [data, avatar]);

  return (
    <div ref={popupRef} className="flex flex-col gap-2 items-center bg-white w-full min-w-[140px]">
      <div className="flex bg-[#924FE8] flex-col text-white w-full px-3 h-[41px] justify-center gap-1">
        {data.loading && !data.person ? (
          <>
            <Skeleton className="h-[18px] w-full" />
            <Skeleton className="h-[10px] w-full" />
          </>
        ) : (
          <div className="flex items-start justify-center flex-col">
            <div className="text-xs font-semibold whitespace-nowrap">
              {data.person?.full_name ?? "__"}
            </div>
            <span className="text-[10px] font-medium">{GenderMap(data.person?.gender || "")}</span>
          </div>
        )}
      </div>
      <div className="flex flex-col gap-1.5 py-2 px-[13px] w-full">
        <PhoneLabel
          loading={data.loading}
          label={"Phone"}
          value={data.person?.phone ?? "__"}
          type="persona"
        />
        <InforLabel
          loading={data.loading}
          label={"Date of Birth"}
          value={convertDateFormat(data.person?.dob ?? "") || "__"}
        />
        <InforLabel
          loading={data.loading}
          label={"Address"}
          value={data.person?.person_address || "__"}
        />
        <InforLabel
          loading={data.loading}
          label={"Company"}
          value={data.person?.company_name || "__"}
        />
        <InforLabel
          loading={data.loading}
          label={"Position"}
          value={data.person?.position_level || "__"}
        />
        {avatarComponent()}
      </div>
    </div>
  );
};

export default memo(UserPopup);
const GenderMap = (type: string) => {
  const map: { [key: string]: string } = {
    F: "Female",
    M: "Male",
  };

  return map[type] || "__";
};
