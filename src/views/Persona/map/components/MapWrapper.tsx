import React, { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-leaflet";
import L, { Icon } from "leaflet";
import { SetViewOnClick } from "./ViewClick";
import { GeoJSON } from "react-leaflet";
import island from "../island.json";
import "leaflet/dist/leaflet.css";
import { useMapContext } from "views/Persona/context/MapProvider";
import locatioIcon from "assets/images/Icon.png";
import { MAP_LAYER } from "../utils";
import ControlPanel from "./ControlPanelLeft";
import ControlPanelRight from "./ControlPanelRight";
import LayerSelect from "./LayerSelect";
import GetParentmap from "./GetParentMap";
import ViewClickButton from "./ControlPanelRight/ViewClickButton";
import cursor from "assets/images/location.png";
import ZoomController from "./ZoomController";

type Props = {
  children?: React.ReactNode;
  circle?: boolean;
  radius?: number;
};

const customIcon = new Icon({
  iconUrl: locatioIcon,
  iconSize: [26.83, 32.8],
});

const MapWrapper = React.forwardRef<L.Map, Props>(({ children, circle, radius }, ref) => {
  const { mapCenter } = useMapContext();
  const [layer, setLayer] = React.useState<string>(MAP_LAYER.STANDARD);
  const [showLayerSelect, setShowLayerSelect] = React.useState<boolean>(false);
  const [isViewClick, setIsViewClick] = React.useState<boolean>(false);
  useEffect(() => {
    const mapRef = document.querySelector(".leaflet-container") as HTMLElement;
    mapRef.style.cursor = !isViewClick ? "grab" : `url(${cursor}), auto`;

    const handleMouseDown = (event: MouseEvent) => {
      if (event.button === 0) {
        mapRef.style.cursor = "grabbing";
      } else {
        mapRef.style.cursor = "grab";
      }
    };
    const handleMouseMove = (event: MouseEvent) => {
      if (event.buttons === 1) {
        mapRef.style.cursor = "grabbing";
      }
    };
    const handleMouseUp = () => {
      isViewClick ? (mapRef.style.cursor = `url(${cursor}), auto`) : (mapRef.style.cursor = "grab");
    };
    mapRef.addEventListener("mousemove", handleMouseMove);
    mapRef.addEventListener("mousedown", handleMouseDown);
    mapRef.addEventListener("mouseup", handleMouseUp);
    return () => {
      mapRef.removeEventListener("mousedown", handleMouseDown);
      mapRef.removeEventListener("mousemove", handleMouseMove);
      mapRef.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isViewClick]);
  return (
    <>
      <MapContainer
        className="w-full h-full"
        ref={ref}
        center={mapCenter}
        zoom={13}
        scrollWheelZoom={false}
        zoomControl={true}
        doubleClickZoom={false}
        touchZoom={true}
      >
        <GetParentmap />
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url={layer}
        />
        {children}
        {isViewClick && <SetViewOnClick />}
        <GeoJSON
          onEachFeature={(feature, layer) => {
            layer.bindTooltip(feature.properties.name, {
              permanent: true,
              direction: "center",
              className: "my-labels",
            });
          }}
          data={island as any}
        />
        <Marker position={mapCenter} icon={customIcon} />
        {circle && (
          <Circle
            pathOptions={{
              color: "transparent",
              fillColor: "#8D8D8D7A",
              fillOpacity: 1,
            }}
            center={mapCenter}
            radius={((radius && radius) ?? 1) * 1000}
          />
        )}
        <ZoomController />
      </MapContainer>
      <ControlPanel />
      <ControlPanelRight
        isShowLayerSelect={showLayerSelect}
        showLayerSelect={() => setShowLayerSelect(!showLayerSelect)}
      />
      <LayerSelect
        isShow={showLayerSelect}
        setShowLayerSelect={() => setShowLayerSelect(!showLayerSelect)}
        layer={layer}
        handeChangeLayer={(input) => setLayer(input)}
      />
      <ViewClickButton isViewClick={isViewClick} onClick={() => setIsViewClick(!isViewClick)} />
    </>
  );
});
export default MapWrapper;
