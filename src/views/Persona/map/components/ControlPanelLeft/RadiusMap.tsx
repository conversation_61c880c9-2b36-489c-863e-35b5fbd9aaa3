import { RiMap2Line } from "@remixicon/react";
import { distanceObjects } from "../../utils";
import MultiSelect from "components/MSelect";
import { cn } from "utils/utils";

type Props = {
  options: {
    [key: string]: string;
  };
  onChange: ({ key, value }: { key: string; value: string }) => void;
};

const RadiusMap: React.FC<Props> = ({ options, onChange }) => {
  return (
    <div className="flex flex-col gap-[14px]">
      <div className="flex items-center justify-between">
        <div className="flex items-center px-4 pt-2 gap-2">
          <RiMap2Line size={20} />
          <span className="text-sm text-primary font-medium">Create a Radius</span>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-3 relative">
        <MultiSelect
          className={cn("!w-full !min-w-0", options.error && "!border !border-red-500")}
          options={
            options.unit === "km"
              ? distanceObjects
              : distanceObjects.slice(0, distanceObjects.length - 4)
          }
          defaultValue={
            (options.radius && [
              {
                label: options.radius,
                value: options.radius,
              },
            ]) ||
            null
          }
          isMulti={false}
          placeholder="Radius"
          onChange={(value: any) =>
            onChange({
              key: "radius",
              value: value[0].value,
            })
          }
        />
        <MultiSelect
          className="!w-full !min-w-0"
          options={[
            {
              label: "Km",
              value: "km",
            },
            {
              label: "Miles",
              value: "miles",
            },
          ]}
          defaultValue={[
            {
              label: options.unit,
              value: options.unit,
            },
          ]}
          isMulti={false}
          placeholder="Unit"
          onChange={(value: any) =>
            onChange({
              key: "unit",
              value: value[0].value,
            })
          }
        />
        {options.error && (
          <span className="absolute text-[10px] top-full text-red-500 left-1 font-medium">
            {options.error}
          </span>
        )}
      </div>
    </div>
  );
};
export default RadiusMap;
