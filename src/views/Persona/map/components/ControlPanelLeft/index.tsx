import { useEffect, useState, useCallback, useMemo } from "react";
import RadiusMap from "./RadiusMap";
import SearchMap from "./SearchMap";
import { useMapContext } from "views/Persona/context/MapProvider";
import useFilters from "hooks/useFilters";
import { FILTER_KEY } from "constants/persona";
import { MILES_TO_KM } from "../../utils";
import { LocationData } from "types/map";

import { cn } from "utils/utils";

const ControlPanel: React.FC = () => {
  const { params, setSearchParams } = useFilters();
  const [searchLocation, setSearchLocation] = useState<string>("");
  const { handleSearchLocation, setMapRadius, mapCenter } = useMapContext();

  const [resultLocation, setResultLocation] = useState<{
    loading: boolean;
    data: LocationData[];
    error?: string | null;
  }>({
    loading: false,
    data: [],
  });

  const initialRadius = useMemo(() => {
    const radiusValue = params[FILTER_KEY.map_radius]?.split(",")[2];
    if (!radiusValue) return "";
    const currentRadius = params.unit === "km" ? radiusValue : String(+radiusValue / MILES_TO_KM);
    return (+currentRadius / 1000).toString();
  }, [params]);

  const [filterMap, setFilterMap] = useState<{ [key: string]: string }>({
    radius: initialRadius,
    unit: params.unit || "km",
    error: "",
  });

  const handleSearch = useCallback(() => {
    if (filterMap.radius === "") {
      setFilterMap((prev) => ({ ...prev, error: "Please enter radius" }));
      return;
    }
    const currentRadius = filterMap.unit === "km" ? filterMap.radius : +filterMap.radius * 1.60934;
    setFilterMap((prev) => ({ ...prev, error: "" }));
    setSearchParams({
      ...params,
      [FILTER_KEY.map_radius]: mapCenter.join(",") + "," + Number(currentRadius) * 1000,
      unit: filterMap.unit,
    });
  }, [filterMap, mapCenter, setSearchParams, params]);

  useEffect(() => {
    const [, , radius] = params[FILTER_KEY.map_radius]?.split(",") || [];
    const calculatedRadius = params.unit === "km" ? +radius / 1000 : +radius / MILES_TO_KM / 1000;
    const currentRadius = isNaN(calculatedRadius) ? "" : calculatedRadius.toString();

    setFilterMap({
      radius: currentRadius,
      unit: params.unit ?? "km",
      error: "",
    });
    setMapRadius({
      unit: params.unit ?? "km",
      radius: currentRadius,
    });
  }, [params.unit, params.radius]);

  useEffect(() => {
    if (searchLocation.trim() === "") {
      setResultLocation((prev) => ({ ...prev, data: [] }));
      return;
    }

    const handleSubmit = async () => {
      setResultLocation((prev) => ({ ...prev, loading: true }));
      try {
        const res = await handleSearchLocation(searchLocation);
        setResultLocation({ loading: false, data: res });
      } catch (error: any) {
        setResultLocation({ data: [], loading: false, error: String(error) });
      }
    };

    handleSubmit();
  }, [searchLocation]);

  useEffect(() => {
    if (filterMap.radius === "") return;
    const currentRadius = filterMap.unit === "km" ? filterMap.radius : +filterMap.radius * 1.60934;
    setMapRadius({
      radius: currentRadius.toString(),
      unit: filterMap.unit,
    });
  }, [filterMap, setMapRadius]);

  return (
    <>
      <div
        className={cn(
          "absolute top-2 left-0 md:top-4 sm:left-4 z-[9999] bg-white w-full max-w-[339px] rounded-2xl shadow-sm p-0 sm:p-4"
        )}
      >
        <SearchMap
          loading={resultLocation.loading}
          searchResult={resultLocation.data}
          onChange={useCallback((value) => setSearchLocation(value), [])}
          error={resultLocation.error}
          onRemoveText={useCallback(() => {
            setResultLocation({ loading: false, data: [], error: null });
            setSearchLocation("");
          }, [])}
        />
      </div>
      <div
        className={cn(
          "absolute max-w-[339px] rounded-2xl shadow-sm p-1 sm:p-4 bg-white top-[50px] sm:top-[100px] left-0 sm:left-4 z-[999] w-full"
        )}
      >
        <div className={cn("w-full h-full flex-col gap-2 md:gap-[18px] flex relative")}>
          <RadiusMap
            onChange={useCallback(({ key, value }) => {
              setFilterMap((prev) => ({ ...prev, [key]: value }));
            }, [])}
            options={filterMap}
          />
          <button
            onClick={handleSearch}
            className="bg-[#924FE8] py-2 rounded-lg shadow-xs text-white text-sm font-medium"
          >
            Search
          </button>
        </div>
      </div>
    </>
  );
};

export default ControlPanel;
