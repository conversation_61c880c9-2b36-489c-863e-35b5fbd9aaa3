import { useMapEvent } from "react-leaflet";
import { useMapContext } from "views/Persona/context/MapProvider";

export function SetViewOnClick() {
  const { setMapCenter } = useMapContext();

  useMapEvent("click", (e) => {
    const { lat, lng } = e.latlng;
    setMapCenter([lat, lng]);

    e.target.setView(e.latlng, e.target.getZoom(), {
      animate: true,
      duration: 1,
    });
  });

  return null;
}
