import { toast } from "components/ui/use-toast";
import { getMyLocation } from "../../utils";
import { useMapContext } from "views/Persona/context/MapProvider";
import { useState } from "react";
import { RiLoader2Line, RiMap2Fill } from "@remixicon/react";
import { cn } from "utils/utils";

type Props = {
  showLayerSelect: () => void;
  isShowLayerSelect: boolean;
};

const ControlPanelRight: React.FC<Props> = ({ showLayerSelect, isShowLayerSelect }) => {
  const { setMapCenter } = useMapContext();
  const [loading, setLoading] = useState<boolean>(false);
  const handleShowMyLocation = async () => {
    setLoading(true);
    try {
      const res = await getMyLocation();
      setMapCenter([res.lat, res.long]);
      setLoading(false);
    } catch (error) {
      toast({
        title: "Error",
        description: String(error),
        status: "error",
      });
      setLoading(false);
    }
  };
  return (
    <div className="absolute z-[9999] rounded-md overflow-hidden flex flex-col bg-white bottom-4 right-4">
      <button
        onClick={showLayerSelect}
        className={cn("p-3 hover:bg-gray-200", isShowLayerSelect && "bg-primary text-white")}
      >
        <RiMap2Fill size={20} />
      </button>
      <button onClick={handleShowMyLocation} className="p-3 border-t hover:bg-gray-200">
        {!loading ? <MyLocationIcon /> : <RiLoader2Line size={20} className="animate-spin" />}
      </button>
    </div>
  );
};
export default ControlPanelRight;

const MyLocationIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M17.3492 2.5L2.5 10.2782H9.57107V17.3492L17.3492 2.5Z"
      stroke="#6C6C6C"
      strokeWidth="1.5"
      strokeLinejoin="round"
    />
  </svg>
);
