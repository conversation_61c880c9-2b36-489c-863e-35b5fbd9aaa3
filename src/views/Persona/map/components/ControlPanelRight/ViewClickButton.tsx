import { RiMapPinUserFill } from "@remixicon/react";
import { cn } from "utils/utils";
type Props = {
  onClick: () => void;
  isViewClick: boolean;
};

const ViewClickButton: React.FC<Props> = ({ onClick, isViewClick }) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "absolute right-4 p-3 bg-white z-[999] rounded-md bottom-[215px]",
        isViewClick ? "bg-primary text-white" : "bg-white"
      )}
    >
      <RiMapPinUserFill size={20} />
    </button>
  );
};
export default ViewClickButton;
