import { Skeleton } from "components/ui/skeleton";

type Props = {
  loading: boolean;
  label: string;
  value: string;
};

const InforLabel: React.FC<Props> = ({ loading, label, value }) => {
  return (
    <div className="border-b w-full pb-1.5">
      <div className="text-[10px]">{label}</div>
      {loading ? (
        <Skeleton className="h-2 w-full" />
      ) : (
        <div className="text-[10px] font-bold">{value}</div>
      )}
    </div>
  );
};

export default InforLabel;
