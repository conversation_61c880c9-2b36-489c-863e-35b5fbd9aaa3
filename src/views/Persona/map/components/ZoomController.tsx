import { useMap } from "react-leaflet";
import { memo, useEffect, useState } from "react";

const ZoomController = () => {
  const map = useMap();
  const [isCtrlPressed, setIsCtrlPressed] = useState<boolean>(false);

  useEffect(() => {
    const mapRef = document.querySelector(".leaflet-container") as HTMLElement;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Control") {
        setIsCtrlPressed(true);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === "Control") {
        setIsCtrlPressed(false);
      }
    };

    mapRef.addEventListener("keydown", handleKeyDown);
    mapRef.addEventListener("keyup", handleKeyUp);

    return () => {
      mapRef.removeEventListener("keydown", handleKeyDown);
      mapRef.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  useEffect(() => {
    if (!isCtrlPressed) {
      document.body.style.overflow = "";
      return;
    }

    const handleWheel = (e: WheelEvent) => {
      if (!isCtrlPressed) {
        e.preventDefault();
      } else {
        document.body.style.overflow = "hidden";
        if (e.deltaY > 0) {
          map.setZoom(map.getZoom() + 1);
        } else {
          map.setZoom(map.getZoom() - 1);
        }
      }
    };
    const container = map.getContainer();
    container.addEventListener("wheel", handleWheel);

    return () => {
      container.removeEventListener("wheel", handleWheel);
    };
  }, [isCtrlPressed, map]);

  return null;
};

export default memo(ZoomController);
