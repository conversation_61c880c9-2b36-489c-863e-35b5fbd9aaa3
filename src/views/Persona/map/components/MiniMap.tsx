import React, { useCallback, useMemo, useEffect, memo } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON>ayer, useMap } from "react-leaflet";
import { useMapContext } from "views/Persona/context/MapProvider";

const MinimapBounds: React.FC = () => {
  const minimap = useMap();
  const { parentMap } = useMapContext();

  const updateView = useCallback(() => {
    try {
      if (parentMap && minimap) {
        minimap.setView(parentMap.getCenter(), parentMap.getZoom());
      }
    } catch (error) {
      console.warn("Updating minimap view...");
    }
  }, [parentMap, minimap]);

  useEffect(() => {
    if (parentMap) {
      updateView();

      parentMap.on("move", updateView);
      parentMap.on("zoom", updateView);

      return () => {
        parentMap.off("move", updateView);
        parentMap.off("zoom", updateView);
      };
    }
  }, [parentMap, minimap, updateView]);

  return null;
};

interface MinimapControlProps {
  mapLayerUrl: string;
  label: string;
}

const MinimapControl: React.FC<MinimapControlProps> = ({ label, mapLayerUrl }) => {
  const { parentMap, mapCenter } = useMapContext();

  if (!parentMap) {
    return null;
  }

  const minimap = useMemo(
    () => (
      <MapContainer
        style={{ height: "100%", width: "100%" }}
        center={mapCenter}
        dragging={false}
        doubleClickZoom={false}
        scrollWheelZoom={false}
        attributionControl={false}
        zoomControl={false}
        zoom={parentMap.getZoom()}
      >
        <TileLayer url={mapLayerUrl} />
        <MinimapBounds />
      </MapContainer>
    ),
    [parentMap, mapCenter]
  );

  return (
    <div className="w-full h-[60px] relative">
      {parentMap && <div className="leaflet-control leaflet-bar w-full h-full">{minimap}</div>}
      <div className="absolute z-[9999] pointer-events-none bg-black/30 text-white text-xs font-medium px-2 py-1 rounded-br-lg">
        {label}
      </div>
    </div>
  );
};

export default memo(MinimapControl);
