import { useMap } from "react-leaflet";
import L, { FitBoundsOptions } from "leaflet";
import { memo, useEffect } from "react";
import useResponsive from "hooks/useResponsive";

type Props = {
  bounds: L.LatLngBoundsLiteral;
};

const GetBound: React.FC<Props> = ({ bounds }) => {
  const { isMobile } = useResponsive();
  const map = useMap();

  const fitBound: FitBoundsOptions = isMobile
    ? { paddingTopLeft: [0, 250], maxZoom: 16 }
    : { paddingTopLeft: [300, 0], maxZoom: 14 };

  useEffect(() => {
    const latLngBounds = L.latLngBounds(bounds);
    map.fitBounds(latLngBounds, fitBound);
  }, [bounds, map]);

  return null;
};

export default memo(GetBound);
