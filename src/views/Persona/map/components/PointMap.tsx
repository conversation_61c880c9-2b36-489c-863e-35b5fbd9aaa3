import { Fragment, useEffect, useState } from "react";
import { Icon } from "leaflet";
import { Marker, Popup, useMapEvents } from "react-leaflet";
import UserPopup from "./UserPopup";
import userIcon from "assets/images/user.png";
import markerClickedIcon from "assets/images/marker-clicked.png";
import { useMapContext } from "views/Persona/context/MapProvider";

const customIcon = new Icon({
  iconUrl: userIcon,
  iconSize: [16.1, 16.1],
});

const customIconClicked = new Icon({
  iconUrl: markerClickedIcon,
  iconSize: [35.77, 44.71],
});

type MarkerData = {
  uid: string;
  person_address_latitude: number;
  person_address_longitude: number;
};

type Props = {
  data: MarkerData[];
};

const PointMap: React.FC<Props> = ({ data }) => {
  const [activeMarkerUid, setActiveMarkerUid] = useState<string | null>(null);
  const { usersRadius } = useMapContext();
  const handleMarkerClick = (uid: string) => {
    if (activeMarkerUid === uid) {
      setActiveMarkerUid(null);
    } else {
      setActiveMarkerUid(uid);
    }
  };
  useEffect(() => {
    setActiveMarkerUid(null);
  }, [usersRadius]);

  useMapEvents({
    click() {
      setActiveMarkerUid(null); // Reset active marker when clicking elsewhere on the map
    },
  });

  return (
    <Fragment>
      {data.map((item) => {
        const isClicked = activeMarkerUid === item.uid;
        return (
          <Marker
            eventHandlers={{
              click: () => handleMarkerClick(item.uid),
            }}
            icon={isClicked ? customIconClicked : customIcon}
            key={item.uid}
            position={[item.person_address_latitude, item.person_address_longitude]}
          >
            <Popup offset={[90, 130]} minWidth={139} closeButton={false}>
              <UserPopup uid={item.uid} />
            </Popup>
          </Marker>
        );
      })}
    </Fragment>
  );
};

export default PointMap;
