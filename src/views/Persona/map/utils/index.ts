export const getMyLocation = (): Promise<{ lat: number; long: number }> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error("Geolocation is not supported by this browser."));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          lat: position.coords.latitude,
          long: position.coords.longitude,
        });
      },
      (error) => {
        reject(new Error(`Error occurred while retrieving location: ${error.message}`));
      }
    );
  });
};
const distances = [
  "0.5",
  "1",
  "2",
  "3",
  "4",
  "5",
  "6",
  "7",
  "8",
  "9",
  "10",
  "15",
  "20",
  "25",
  "30",
  "35",
  "40",
  "45",
  "50",
];

export const distanceObjects = distances.map((distance) => ({
  label: distance,
  value: distance,
}));
export const MILES_TO_KM = 1.60934;

export enum MAP_LAYER {
  SATELLITE = "https://api.maptiler.com/maps/satellite/{z}/{x}/{y}.jpg?key=1iWzhnSZt0udbU1MN6ES",
  STANDARD = "https://api.maptiler.com/maps/basic-v2/256/{z}/{x}/{y}.png?key=1iWzhnSZt0udbU1MN6ES",
}

export const mapLayers = Object.keys(MAP_LAYER).map((key) => ({
  key,
  value: MAP_LAYER[key as keyof typeof MAP_LAYER],
}));
