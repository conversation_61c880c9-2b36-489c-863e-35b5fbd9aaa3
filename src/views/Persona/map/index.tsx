import MapWrapper from "./components/MapWrapper";
import PointMap from "./components/PointMap";
import { useEffect, useMemo, useRef } from "react";
import { Map } from "leaflet";
import GetBound from "./components/GetBound";
import { useMapContext } from "../context/MapProvider";

const MapResearch: React.FC = () => {
  const { usersRadius, mapRadius, mapCenter } = useMapContext();
  const mapRef = useRef<Map>(null);

  useEffect(() => {
    mapRef.current?.setView(mapCenter, mapRef.current.getZoom());
  }, [mapCenter]);

  const radius = useMemo(() => {
    return isNaN(Number(mapRadius?.radius)) ? 0 : +mapRadius.radius;
  }, [mapRadius]);

  return (
    <div className="h-[620px] z-10 w-full shadow-sm relative border rounded-2xl overflow-hidden">
      <MapWrapper circle={true} radius={radius} ref={mapRef}>
        <PointMap
          data={usersRadius.items.map((item) => ({
            uid: item.uid,
            person_address_latitude: item.person_address_latitude,
            person_address_longitude: item.person_address_longitude,
          }))}
        />
        {usersRadius.count > 0 && (
          <GetBound
            bounds={usersRadius.items.map((item) => [
              item.person_address_latitude,
              item.person_address_longitude,
            ])}
          />
        )}
      </MapWrapper>
    </div>
  );
};

export default MapResearch;
