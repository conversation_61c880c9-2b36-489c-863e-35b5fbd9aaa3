import AvatarSkeleton from "assets/icons/AvatarSkeleton";
import PersonaBackground from "assets/icons/PersonaBackground";
import useFilters from "hooks/useFilters";
import { PERSONA_LABEL } from "constants/persona/label";

const NodataOverlay = () => {
  const { searchParams } = useFilters();
  return (
    !searchParams.size && (
      <div className="flex-1 w-full h-full relative z-0 min-h-[500px]">
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-20">
          <AvatarSkeleton />
          <span className="max-w-[315px] inline-block text-center text-secondary mt-5">
            {PERSONA_LABEL.no_data}
          </span>
        </div>
        <div className="absolute right-0 bottom-0">
          <PersonaBackground />
        </div>
      </div>
    )
  );
};

export default NodataOverlay;
