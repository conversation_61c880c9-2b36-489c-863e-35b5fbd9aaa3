import { Box } from "components/Box";
import { useMapContext } from "../context/MapProvider";

import MapResearch from "../map";
import FilterOptions from "./FilterOptions";
import FilterChipsWrapper from "./FilterOptions/FilterChipWrapper";
import useFilters from '../../../hooks/useFilters';
import { useAppSelector } from '../../../store';
import { useEffect } from 'react';
import { tracking } from '../../../utils/Tracking/tracking';

const FilterWrapper = () => {
  const { showMap } = useMapContext();
  const { params, searchParams } = useFilters();
  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (searchParams.size > 0) {
      tracking({
        eventName: 'work_persona_filter',
        params: {
          user_id: user?.uuid,
          valueTracking: JSON.stringify({
            filter_params: params
          })
        }
      });
    }
  }, [params]);
  return (
    <Box variant="col-start" className="gap-0 w-full">
      <FilterOptions />
      <FilterChipsWrapper />
      {showMap === "SHOW" && <MapResearch />}
    </Box>
  );
};

export default FilterWrapper;
