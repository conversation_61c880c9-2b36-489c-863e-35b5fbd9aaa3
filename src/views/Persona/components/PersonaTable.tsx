import { useEffect, useMemo, useState } from "react";
import { personaContext } from "../context/PersonaContext";

import Pagination from "components/Pagination";
import { DataTable } from "components/DataTable/TableCustom";

import { personaAPI } from "apis/persona";
import useFilters from "hooks/useFilters";

import { SortType } from "types";
import { IRow, TableType } from "types/Table";
import { getPersonaColumns } from "constants/persona/column";
import { useAbortController } from '../../../hooks/useAbortController';

const PersonaTable = () => {
  const { searchParams, params, setSearchParams } = useFilters();
  const resultCount = personaContext().resultCount.number;

  const [data, setData] = useState<TableType<IRow>>({
    data: [],
    count: 0,
    loading: false,
  });

  const { newAbortController } = useAbortController();

  useEffect(() => {
    if (searchParams.size) {
      getDataPersonaPreview();
    }
  }, [searchParams]);

  const getDataPersonaPreview = async () => {
    const controller = newAbortController();

    setData((prev) => ({ ...prev, loading: true }));

    try {
      const res = await personaAPI.get({
        endpoint: "preview/",
        params: { ...params, limit: 10 },
        signal: controller.signal
      });

      if (res.data) {
        setData({
          data: res.data.items,
          count: res.data.count,
          loading: false
        });
      } else {
        setData((prev) => ({ ...prev, loading: false }));
      }
    } catch (error: any) {
      if (error.name === "AbortError" || error.name === "CanceledError") {
        return;
      }
      console.error("Error loading persona preview:", error);
      setData((prev) => ({ ...prev, loading: false }));
    }
  };

  const handleSortChange = (sortType: SortType, field: string) => {
    const sortBy = {
      asc: `${field}`,
      desc: `-${field}`,
    };
    setSearchParams({ ...params, order_by: sortBy[sortType] });
  };

  const memoizedColumns = useMemo(
    () =>
      getPersonaColumns({
        buyProfile: true,
        handleSortChange: handleSortChange,
      }),
    []
  );

  return (
    !!searchParams.size && (
      <div className="mt-6">
        <DataTable loading={data.loading} columns={memoizedColumns} data={data.data as any} classTable='[&_tbody>tr]:h-[75px]' />
        <Pagination
          className="m-4 w-full"
          totalCount={resultCount}
          pageSize={10}
          currentPage={Number(searchParams.get("page")) || 1}
          onPageChange={(page) => setSearchParams({ ...params, page: String(page) })}
        />
      </div>
    )
  );
};

export default PersonaTable;
