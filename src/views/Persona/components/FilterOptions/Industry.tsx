import { RiBookmarkLine } from "@remixicon/react";
import { useAppSelector } from "store";

import FilterHoverCard from "components/FilterOptions/FilterHoverCard";
import useFeatures from "hooks/useFeatures";
import useFilters from "hooks/useFilters";
import { TSelectOption } from "types/Select";
import { IFilterOption } from ".";

import { onChangeDimensions } from "utils/persona/changeDimensions";
import { removeVietnameseTones } from "utils/utils";
import { formatDataToOptionSelect } from "utils/options";
import { FILTER_KEY, optionIconFilter } from "constants/persona";
import { PERSONA_LABEL } from "constants/persona/label";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import useResponsive from '../../../../hooks/useResponsive';

const Industry = ({ handleShowMap }: IFilterOption) => {
  const { industry } = useAppSelector((state) => state.dimensions);
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const { isMobile } = useResponsive();
  const handleSetSearchKeyword = (searchTerm: string) => {
    setSearchParams({ ...paramsNoPage, [FILTER_KEY.keywords]: searchTerm });
  };

  const searchCallback = (nodes: TSelectOption[], searchTerm: string): TSelectOption[] => {
    const lowerCaseSearchTerm = removeVietnameseTones(searchTerm).toLowerCase();
    const newFilteredNodes = nodes.filter((node) =>
      removeVietnameseTones(node.label).toLowerCase().includes(lowerCaseSearchTerm)
    );
    return newFilteredNodes;
  };

  const activeCount =
    (searchSchema[FILTER_KEY.industry]?.length || 0) +
    (searchSchema[FILTER_KEY.keywords] && paramsNoPage[FILTER_KEY.keywords] !== " " ? 1 : 0);

  //check permission
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.PSN_FT_INDUSTRY)?.is_enabled;
  return (
    <FilterHoverCard
      isEnable={isEnable}
      label={PERSONA_LABEL.industry_and_keywords}
      activeCount={activeCount}
      onClickChangeTab={handleShowMap}
      icon={
        <RiBookmarkLine
          size={optionIconFilter.size}
          color={isEnable ? optionIconFilter.color : optionIconFilter.disable}
        />
      }
      groupSelect={[
        {
          options: formatDataToOptionSelect(industry, searchSchema[FILTER_KEY.industry]),
          placeholderOptions: PERSONA_LABEL.industry_and_keywords,
          isSearchable: true,
          className: isMobile ? '!h-[200px]' : 'h-full',
          searchCallback: searchCallback,
          handleSetSearchKeyword: handleSetSearchKeyword,
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            onChangeDimensions({
              selectedNodes: selectedNodes,
              key: FILTER_KEY.industry,
              params: paramsNoPage,
              setSearchParams: setSearchParams,
            }),
        },
      ]}
    />
  );
};

export default Industry;
