import { useSearchParams } from "react-router-dom";
import { RiLockLine, RiMapLine } from "@remixicon/react";

import { cn } from "utils/utils";
import { FILTER_KEY, optionIconFilter } from "constants/persona";
import { PERSONA_LABEL } from "constants/persona/label";
import { useMapContext } from "views/Persona/context/MapProvider";
import useFeatures from "hooks/useFeatures";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { fMapRadius } from "utils/number";

const MapOption: React.FC = () => {
  const [searchParams] = useSearchParams();
  const params = Object.fromEntries(searchParams.entries());
  const geosearch = params[FILTER_KEY.map_radius]?.split(",") ?? [];
  const { handleShowMap, showMap } = useMapContext();

  //check permission
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.PSN_FT_MAP_RADIUS)?.is_enabled;

  return (
    <button
      className={cn(
        "flex items-center gap-2 pb-4 border-b-[2px] border-b-[transparent]",
        showMap === "SHOW" && "border-b-[#924FE8]",
        !isEnable && "text-[#a7aab1] d"
      )}
      onClick={() => {
        if (isEnable) {
          handleShowMap(showMap === "SHOW" ? "HIDE" : "SHOW");
        }
      }}
    >
      <RiMapLine
        size={optionIconFilter.size}
        color={isEnable ? optionIconFilter.color : optionIconFilter.disable}
      />
      <p className='m-0 p-0 w-max'>{PERSONA_LABEL.map_radius}</p>
      {geosearch[2] && (
        <span className="text-[#924FE8] px-2 py-0.5 bg-[#ECDFFB] text-xs rounded-sm font-semibold">
          {`${fMapRadius(Number(geosearch[2]))}km`}
        </span>
      )}
      {!isEnable && <RiLockLine size={17} />}
    </button>
  );
};

export default MapOption;
