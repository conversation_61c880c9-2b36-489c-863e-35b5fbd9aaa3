import { RiBriefcaseLine } from "@remixicon/react";
import { useAppSelector } from "store";

import { TSelectOption } from "types/Select";
import useFilters from "hooks/useFilters";
import FilterHoverCard from "components/FilterOptions/FilterHoverCard";

import { FILTER_KEY, optionIconFilter } from "constants/persona";
import { PERSONA_LABEL } from "constants/persona/label";
import { formatDataToOptionSelect } from "utils/options";
import { onChangeDimensions } from "utils/persona/changeDimensions";
import { IFilterOption } from ".";
import useFeatures from "hooks/useFeatures";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { LEVEL } from '../../../../constants';
import useResponsive from '../../../../hooks/useResponsive';

const JobTitle = ({ handleShowMap }: IFilterOption) => {
  const { department } = useAppSelector((state) => state.dimensions);
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const { isMobile } = useResponsive();
  //Check permission
  const isEnableDepartment = useFeatures(FEATURE_PERMISSION_KEY.PSN_FT_DEPARTMENT)?.is_enabled;
  const isEnableLevel = useFeatures(FEATURE_PERMISSION_KEY.PSN_FT_MANAGEMENT_LEVEL)?.is_enabled;
  const isEnable = isEnableLevel || isEnableDepartment;

  return (
    <FilterHoverCard
      isEnable={isEnable}
      label={PERSONA_LABEL.job_title_filter}
      className={'max-[768px]:max-w-[350px]'}
      icon={
        <RiBriefcaseLine
          size={optionIconFilter.size}
          color={isEnable ? optionIconFilter.color : optionIconFilter.disable}
        />
      }
      activeCount={
        (searchSchema[FILTER_KEY.department]?.length || 0) +
        (searchSchema[FILTER_KEY.level]?.length || 0)
      }
      onClickChangeTab={handleShowMap}
      groupSelect={[
        {
          isEnable: isEnableDepartment,
          className: isMobile ? '!h-[200px]' : 'h-full',
          options: formatDataToOptionSelect(department, searchSchema[FILTER_KEY.department]),
          placeholderOptions: "Department",
          isSearchable: true,
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            onChangeDimensions({
              selectedNodes,
              key: FILTER_KEY.department,
              params: paramsNoPage,
              setSearchParams: setSearchParams,
            }),
        },
        {
          isEnable: isEnableLevel,
          options: formatDataToOptionSelect(LEVEL, searchSchema[FILTER_KEY.level]),
          placeholderOptions: "Management Level",
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            onChangeDimensions({
              selectedNodes,
              key: FILTER_KEY.level,
              params: paramsNoPage,
              setSearchParams: setSearchParams,
            }),
        },
      ]}
    />
  );
};

export default JobTitle;
