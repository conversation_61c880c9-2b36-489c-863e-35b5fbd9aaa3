import { RiUserLocationLine } from "@remixicon/react";

import FilterHoverCard from "components/FilterOptions/FilterHoverCard";
import SearchInput from "components/SearchInput";
import { IFilterOption } from ".";

import useFilters from "hooks/useFilters";
import useFeatures from "hooks/useFeatures";
import { TSelectOption } from "types/Select";

import { formatDataToOptionSelect } from "utils/options";
import { onChangeDimensions } from "utils/persona/changeDimensions";
import { PERSONA_LABEL } from "constants/persona/label";
import { FILTER_KEY, optionIconFilter } from "constants/persona";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { COMPANY_SIZE } from '../../../../constants';

const CompanyInformation = ({ handleShowMap }: IFilterOption) => {
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();

  const handleSearchName = (value: string) => {
    setSearchParams({ ...paramsNoPage, [FILTER_KEY.company_name]: value != "" ? value : [] });
  };

  const activeCount =
    (searchSchema[FILTER_KEY.company_size]?.length || 0) +
    (searchSchema[FILTER_KEY.company_name] && paramsNoPage[FILTER_KEY.company_name] !== " "
      ? 1
      : 0);

  //check permission
  const isEnableCompany = useFeatures(FEATURE_PERMISSION_KEY.PSN_SEARCH_COMPANY)?.is_enabled;
  const isEnableCompanySize = useFeatures(FEATURE_PERMISSION_KEY.PSN_FT_COMPANY_SIZE)?.is_enabled;
  const isEnable = isEnableCompany || isEnableCompanySize;
  return (
    <FilterHoverCard
      isEnable={isEnable}
      label={PERSONA_LABEL.company_infor}
      icon={
        <RiUserLocationLine
          size={optionIconFilter.size}
          color={isEnable ? optionIconFilter.color : optionIconFilter.disable}
        />
      }
      className={'max-[768px]:max-w-[350px]'}
      activeCount={activeCount}
      onClickChangeTab={handleShowMap}
      groupSelect={[
        {
          isEnable: isEnableCompany,
          options: [],
          placeholderOptions: PERSONA_LABEL.company_name,
          isCustomOptions: true,
          customOptions: (
            <SearchInput
              placeholder="Search name..."
              className={'max-[768px]:w-[150px]'}
              onChange={handleSearchName}
              searchTerm={paramsNoPage[FILTER_KEY.company_name]}
              disabled={!isEnableCompany}
            />
          ),
        },
        {
          isEnable: isEnableCompanySize,
          options: formatDataToOptionSelect(COMPANY_SIZE, searchSchema[FILTER_KEY.company_size]),
          placeholderOptions: PERSONA_LABEL.company_size,
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            onChangeDimensions({
              selectedNodes,
              key: FILTER_KEY.company_size,
              params: paramsNoPage,
              setSearchParams: setSearchParams,
            }),
        },
      ]}
    />
  );
};

export default CompanyInformation;
