import { useAppSelector } from "store";
import isArray from "lodash/isArray";
import { RiMapPin2Line } from "@remixicon/react";
import { IFilterOption } from ".";

import FilterHoverCard from "components/FilterOptions/FilterHoverCard";

import useFilters from "hooks/useFilters";
import useFeatures from "hooks/useFeatures";
import { TSelectOption } from "types/Select";

import { onChangeDimensions } from "utils/persona/changeDimensions";
import { PERSONA_LABEL } from "constants/persona/label";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { FILTER_KEY, optionIconFilter } from "constants/persona";
import useResponsive from '../../../../hooks/useResponsive';

const Location = ({ handleShowMap }: IFilterOption) => {
  const { location } = useAppSelector((state) => state.dimensions);
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const { isMobile } = useResponsive();

  const formatData = (options: TSelectOption[]) => {
    const newData = Object.keys(options).map((key) => {
      const regionChecked =
        isArray(searchSchema[FILTER_KEY.region]) &&
        !!searchSchema[FILTER_KEY.region]?.includes(key);

      const provinces = options[key as any].map((province: string) => {
        const provinceChecked =
          isArray(searchSchema[FILTER_KEY.province]) &&
          !!searchSchema[FILTER_KEY.province]?.includes(province);

        return {
          label: province,
          value: province,
          checked: regionChecked || provinceChecked,
        };
      });

      const expanded = regionChecked || provinces.some((province: any) => province.checked);

      return {
        label: key === "null" ? "Unknown" : key,
        value: key,
        checked: regionChecked,
        children: key !== "null" && provinces,
        expand: expanded,
      };
    });

    return newData;
  };

  const data = formatData(location);

  //PERMISSION
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.PSN_FT_LOCATION)?.is_enabled;

  return (
    <FilterHoverCard
      label={PERSONA_LABEL.location}
      icon={
        <RiMapPin2Line
          size={optionIconFilter.size}
          color={isEnable ? optionIconFilter.color : optionIconFilter.disable}
        />
      }
      isEnable={isEnable}
      activeCount={
        (searchSchema[FILTER_KEY.province]?.length || 0) +
        (searchSchema[FILTER_KEY.region]?.length || 0)
      }
      onClickChangeTab={handleShowMap}
      groupSelect={[
        {
          options: data,
          placeholderOptions: "Region",
          className: isMobile ? '!h-[200px]' : 'h-full',
          isSearchable: true,
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) => {
            let parentParams = {};
            const parentSelected = selectedNodes.filter((x) => x.dept == 0);
            const childSelected = selectedNodes.filter((x) => x.dept == 1);

            const newParentValue = parentSelected.map((item) => item.value);
            parentParams = { [FILTER_KEY.region]: newParentValue.join(",") };

            return onChangeDimensions({
              selectedNodes: childSelected,
              key: FILTER_KEY.province,
              params: paramsNoPage,
              parentParams,
              setSearchParams: setSearchParams,
            });
          },
        },
      ]}
    />
  );
};

export default Location;
