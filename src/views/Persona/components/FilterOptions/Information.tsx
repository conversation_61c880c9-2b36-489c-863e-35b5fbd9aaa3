import { RiInformationLine } from "@remixicon/react";

import FilterHoverCard from "components/FilterOptions/FilterHoverCard";

import { HandleSelectChangeProps, TSelectOption } from "types/Select";
import useFilters from "hooks/useFilters";
import useFeatures from "hooks/useFeatures";
import { IFilterOption } from ".";

import { filterIsShowOptions, formatDataToOptionSelect } from "utils/options";
import { onChangeDimensions } from "utils/persona/changeDimensions";
import { PERSONA_LABEL } from "constants/persona/label";
import { FILTER_KEY, genderOptionsAtt, optionIconFilter } from "constants/persona";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { AGE_RANGE } from '../../../../constants';
import useResponsive from '../../../../hooks/useResponsive';

const Information = ({ handleShowMap }: IFilterOption) => {
  const { paramsNoPage, searchSchema, setSearchParams } = useFilters();
  const { isMobile } = useResponsive();

  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) =>
    onChangeDimensions({
      selectedNodes,
      key,
      params: paramsNoPage,
      setSearchParams: setSearchParams,
    });

  //Check permission
  const isEnable = useFeatures(FEATURE_PERMISSION_KEY.PSN_FT_DEMOGRAPHIC)?.is_enabled;

  return (
    <FilterHoverCard
      isEnable={isEnable}
      label={PERSONA_LABEL.information_filter}
      icon={
        <RiInformationLine
          size={optionIconFilter.size}
          color={isEnable ? optionIconFilter.color : optionIconFilter.disable}
        />
      }
      activeCount={
        (searchSchema[FILTER_KEY.age]?.length || 0) + (searchSchema[FILTER_KEY.gender]?.length || 0)
      }
      onClickChangeTab={handleShowMap}
      groupSelect={[
        {
          options: formatDataToOptionSelect(AGE_RANGE, searchSchema[FILTER_KEY.age]),
          placeholderOptions: "Age",
          className: isMobile ? '!h-[150px]' : 'h-full',
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.age }),
        },
        {
          options: filterIsShowOptions(genderOptionsAtt, searchSchema[FILTER_KEY.gender]),
          placeholderOptions: "Gender",
          className: isMobile ? '!h-[150px]' : 'h-full',
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.gender }),
        },
      ]}
    />
  );
};

export default Information;
