import { Box } from "components/Box";
import FilterOptions from "components/FilterOptions";
import { TSelectedOption } from "types/Select";
import { cn } from "utils/utils";
import { TPersonaAudienceFilter } from "utils/yourAudience/personaFilterOption";

type FilterDemographicItem = {
  [key: string]: any;
};

interface Props extends TPersonaAudienceFilter {
  label: string;
  selectedOption: TSelectedOption;
  filterGroup: (FilterDemographicItem | null)[];
  itemToShow: number;
}
const AudienceFilterGroup = (props: Props) => {
  return (
    <Box variant="col-start" className="gap-3 w-full">
      <span className="text-secondary text-sm font-medium">{props.label}</span>
      <Box
        className={cn(
          "gap-2 gap-y-4 w-full flex-wrap xl:flex-nowrap items-start xl:items-center justify-start xl:justify-between",
          props.itemToShow == 5 && "justify-start flex",
          props.itemToShow == 4 && "max-lg:w-full w-fit"
        )}
        children={
          <FilterOptions
            isDisable={props.isDisable}
            filterGroup={props.filterGroup}
            selectedOption={props.selectedOption}
          />
        }
      />
    </Box>
  );
};

export default AudienceFilterGroup;
