import { useState } from "react";

import { Box } from "components/Box";
import AudienceFilterGroup from "./AudienceFilterGroup";
import FilterHeader from "views/YourAudience/components/FilterHeader";
import FilterInformation from "views/YourAudience/components/FilterInformation";

import useFilters from "hooks/useFilters";
import { TSelectedOption } from "types/Select";
import filterOptions, { TPersonaAudienceFilter } from "utils/yourAudience/personaFilterOption";
import { PERSONA_LABEL } from "constants/persona/label";
import { AUDIENCE_PERSONA_FILTER } from "constants/yourData/label";

const AudienceFilterWrapper = (props: TPersonaAudienceFilter) => {
  const {
    isFilterCompanySize,
    isFilterCompanyName,
    isFilterDOB,
    isFilterPersonaAge,
    isFilterGender,
    isFilterIndustry,
    isFilterPosition,
    isFilterProvince,
    isFilterDepartment,
  } = props;

  const { paramsNoPage } = useFilters();

  const [selectedOption, setSelectedOption] = useState<TSelectedOption>(paramsNoPage);

  const handleChange = (key: string, value: any) => {
    switch (true) {
      case key == AUDIENCE_PERSONA_FILTER.AGE_GTE:
        setSelectedOption((prev: any) => ({
          ...prev,
          [AUDIENCE_PERSONA_FILTER.AGE_GTE]: value?.min,
          [AUDIENCE_PERSONA_FILTER.AGE_LTE]: value?.max,
        }));
        break;
      default:
        const newValue = value.map((item: any) => item?.value).join(",");
        setSelectedOption((prev: any) => ({ ...prev, [key]: newValue ? newValue : undefined }));
    }
  };

  const filterDemographic = filterOptions({
    isFilterProvince,
    isFilterGender,
    isFilterPersonaAge,
    isFilterDOB,
    params: paramsNoPage,
    selectedOption: selectedOption,
    onSetParams: handleChange,
    setSelectedOption,
  });
  const filterWorkGroup = filterOptions({
    isFilterCompanyName,
    isFilterCompanySize,
    isFilterIndustry,
    isFilterPosition,
    isFilterDepartment,
    params: paramsNoPage,
    selectedOption: selectedOption,
    onSetParams: handleChange,
    setSelectedOption,
  });

  return (
    <Box variant="col-start" className="gap-2">
      <Box variant="col-start" className="p-4 border rounded-2xl gap-3 mt-6 relative">
        {!props.isDisable && (
          <FilterHeader
            isDisableFilter={props.isDisable}
            selectedOption={selectedOption}
            setSelectedOption={setSelectedOption}
          />
        )}

        <Box variant="col-start" className="gap-4">
          <AudienceFilterGroup
            itemToShow={4}
            label={PERSONA_LABEL.information_filter}
            filterGroup={filterDemographic}
            selectedOption={selectedOption}
          />
          <AudienceFilterGroup
            itemToShow={5}
            label={PERSONA_LABEL.work_info}
            filterGroup={filterWorkGroup}
            selectedOption={selectedOption}
          />
        </Box>
        {props.isDisable && (
          <div className="absolute top-1 right-2">
            <FilterInformation />
          </div>
        )}
      </Box>
    </Box>
  );
};
export default AudienceFilterWrapper;
