import { THandleShowMap, useMapContext } from 'views/Persona/context/MapProvider';

import Information from './Information';
import JobTitle from './JobTitle';
import CompanyInformation from './Company';
import Location from './Location';
import MapOption from './MapOption';

import Industry from './Industry';
import HorizontalScrollNav from '../../../../components/ListFilterResponsive';

export interface IFilterOption {
  handleShowMap?: THandleShowMap;
}

const FilterOptions = () => {
  const { handleShowMap } = useMapContext();

  return (
    <HorizontalScrollNav>
      <Information handleShowMap={handleShowMap} />
      <Location handleShowMap={handleShowMap} />
      <MapOption />
      <Industry handleShowMap={handleShowMap} />
      <JobTitle handleShowMap={handleShowMap} />
      <CompanyInformation handleShowMap={handleShowMap} />
    </HorizontalScrollNav>
  );
};

export default FilterOptions;
