import GroupAvatar from "components/GroupAvatar";
import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import { TypeTreeNode } from "types/YourData";
import ProcessedTree from "views/YourSegment/components/ProcessedTree";

type Props = {
  id: string;
  urls: string[];
  tree: TypeTreeNode;
  name: string;
  rootId: string;
};

const ShowSource: React.FC<Props> = ({ urls, id, tree, name, rootId }) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="w-fit flex cursor-pointer items-center mt-6">
          <span className="font-medium text-sm">Source: </span>
          <GroupAvatar imgUrls={urls.filter((item) => Boolean(item))} type={"horizontal"} />
        </div>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-full z-[9999]">
        <ProcessedTree tree={tree} id={id} name={name} rootId={rootId} />
      </PopoverContent>
    </Popover>
  );
};

export default ShowSource;
