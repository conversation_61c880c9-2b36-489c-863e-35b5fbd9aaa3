import FilterWrapper from "./FilterWrapper";
import PersonaSummary from "../Summarize";
import PersonaTable from "./PersonaTable";
import NodataOverlay from "./NodataOverlay";
import useFilters from "hooks/useFilters";

const Container = () => {
  const { searchParams } = useFilters();
  return (
    <>
      <FilterWrapper />
      {!searchParams.size ? (
        <NodataOverlay />
      ) : (
        <div className="mt-6">
          <PersonaSummary />
          <PersonaTable />
        </div>
      )}
    </>
  );
};

export default Container;
