import { useEffect, useState } from "react";
import isEqual from "lodash/isEqual";
import omit from "lodash/omit";

import AgeGroupChart from "./AgeGroupChart";
import BarChartHorizontal from "components/chart/BarChartHorizontal";
import ChartWrapper from "components/chart/ChartWrapper";
import Polar<PERSON>hart from "components/chart/PolarChart";

import usePrevious from "hooks/usePrevious";
import { personaAPI } from "apis/persona";

import { cn } from "utils/utils";
import useFilters from "hooks/useFilters";
import { getCityData } from "utils/persona";
import { useAbortController } from '../../../hooks/useAbortController';

interface IDataSummarize {
  data: {
    age_gender: {};
    gender: {};
    person_province: {};
    person_region: {
      [key: string]: number;
    };
  };
  loading: boolean;
}

const PersonaSummary = () => {
  const { searchParams, paramsNoPage } = useFilters();
  const prevParams = omit(usePrevious(paramsNoPage), ["page", "order_by"]);
  const { newAbortController } = useAbortController();

  const [data, setData] = useState<IDataSummarize>({
    data: {
      age_gender: {},
      gender: {},
      person_province: {},
      person_region: {},
    },
    loading: false,
  });

  const getData = async () => {
    const controller = newAbortController();

    setData((prev) => ({ ...prev, loading: true }));

    try {
      const res = await personaAPI.get({
        endpoint: "summarize/",
        params: { ...paramsNoPage },
        signal: controller.signal
      });

      if (res.data) {
        setData({ data: res.data, loading: false });
      } else {
        setData((prev) => ({ ...prev, loading: false }));
      }
    } catch (error: any) {
      if (error.name === "AbortError" || error.name === "CanceledError") return;
      console.error("Error fetching summarize:", error);
      setData((prev) => ({ ...prev, loading: false }));
    }
  };


  useEffect(() => {
    if (!isEqual(paramsNoPage, prevParams) && !!searchParams.size) {
      getData();
    }
  }, [paramsNoPage, prevParams]);

  const cityData = getCityData(data.data.person_province);

  const arr = Object.keys(data.data.person_region)
    .map((key: string) => ({
      city: key,
      count: data.data.person_region[key],
    }))
    .sort((a, b) => b.count - a.count);

  const totalOthers = arr
    .map((item) => item.count)
    .slice(3, arr.length)
    .reduce((a, b) => a + b, 0);
  const labels =
    arr.length > 3
      ? arr
          .map((item) => item.city)
          .slice(0, 3)
          .concat("Others")
      : arr.map((item) => item.city);

  const values =
    arr.length > 3
      ? arr
          .map((item) => item.count)
          .slice(0, 3)
          .concat(totalOthers)
      : arr.map((item) => item.count);

  return (
    !!searchParams.size && (
      <div className={cn("block lg:grid grid-cols-1  gap-x-7 gap-y-6 lg:grid-cols-3")}>
        <AgeGroupChart
          dataAge={data.data.age_gender}
          dataGender={data.data.gender}
          loading={data.loading}
        />
        <>
          <ChartWrapper
            isEmptyData={cityData.length === 0}
            isBarHorizonChart
            loading={data.loading}
            className="md:col-span-2"
            title="City"
          >
            <BarChartHorizontal
              values={cityData?.map((item) => item.count) || []}
              labels={cityData?.map((item) => item.city) || []}
            />
          </ChartWrapper>
          <ChartWrapper isEmptyData={values.length === 0} isPieChart loading={data.loading} title="Region">
            <PolarChart labels={labels} values={values} titleTooltip="Region" />
          </ChartWrapper>
        </>
      </div>
    )
  );
};

export default PersonaSummary;
