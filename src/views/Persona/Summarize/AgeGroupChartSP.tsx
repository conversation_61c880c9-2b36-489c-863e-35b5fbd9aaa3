import AgeChart from "components/AgeChart";
import ChartWrapper from "components/chart/ChartWrapper";
import PolarChart from "components/chart/PolarChart";
import ManIcon from "assets/icons/ManIcon";
import WomanIcon from "assets/icons/WomanIcon";

import { cn, getAgeValue } from 'utils/utils';
import { GENDER } from "constants/label";
import { GENDER_COLOR } from "constants/persona";
import { useAppSelector } from '../../../store';

interface AgeGenderData {
  F?: number;
  M?: number;
}
export interface Data {
  [key: string]: AgeGenderData;
}
interface Result {
  label: string[];
  f: number[];
  m: number[];
}

const AgeGroupChartSP = ({
  dataAge,
  dataGender,
  loading,
}: {
  dataAge: Data;
  dataGender: Data;
  loading: boolean;
}) => {
  const { collapse } = useAppSelector((state) => state.sidebar);
  const resultAge: Result = {
    label: [],
    f: [],
    m: [],
  };

  Object.keys(dataAge)
  .filter(key => key !== 'NULL')
  .sort((a, b) => getAgeValue(a) - getAgeValue(b))
  .forEach((key) => {
    resultAge.label.push(key);
    resultAge.f.push(dataAge[key].F || 0);
    resultAge.m.push(dataAge[key].M || 0);
  });


  const LABEL_GENDER = Object.values({female: GENDER.female, male: GENDER.male});

  const valueGender = [dataGender.F || 0, dataGender.M || 0];
  return (
    <>
      <AgeChart
        loading={loading}
        data={[
          {
            label: GENDER.female,
            values: resultAge.f,
            backgroundColor: GENDER_COLOR.female,
            icon: WomanIcon,
          },
          {
            label: GENDER.male,
            values: resultAge.m,
            backgroundColor: GENDER_COLOR.male,
            icon: ManIcon,
          },
        ]}
        labels={[
          "18-24",
          "25-34",
          "35-44",
          "45-54",
          ">54",
        ]}
        targetLabel={resultAge.label.sort((a, b) => getAgeValue(a) - getAgeValue(b))}
      />
      <ChartWrapper
        isEmptyData={valueGender.reduce((acc: number, cur: any) => acc + cur, 0) === 0}
        loading={loading}
        title="Gender"
        isPieChart
        className={cn("col-span-2 lg:col-span-1", collapse ? "md:col-span-1" : "md:col-span-2")}
      >
        <PolarChart
          labels={LABEL_GENDER}
          values={valueGender as number[]}
          backgroundColors={Object.values({female: GENDER_COLOR.female,male:GENDER_COLOR.male})}
          titleTooltip="Gender"
        />
      </ChartWrapper>
    </>
  );
};

export default AgeGroupChartSP;
