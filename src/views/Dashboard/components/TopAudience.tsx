import { useEffect, useState } from "react";
import range from "lodash/range";

import { Box } from "components/Box";
import { Skeleton } from "components/ui/skeleton";
import { Table, TableBody, TableCell, TableRow } from "components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "components/ui/card";
import AudiencesSizeColumn from "components/column/AudiencesSizeColumn";
import BadgePackage from "components/BadgePackage";
import InformationColumn from "views/SocialDataView/columns/InformationColumn";
import RatingColumn from "views/SocialDataView/columns/RatingColumn";
import Champions from "assets/icons/Champions";

import { socialAPI } from "apis/socialData";

import { IYourAudienceItem } from "types/yourAudience";
import { TYPE_SOCIAL_LABEL } from "constants/requestAudience";
import { cn } from "utils/utils";
import { getColorBadge } from "utils/palette";
import { useAbortController } from '../../../hooks/useAbortController';

const TopAudience = () => {
  const [data, setData] = useState<{ data: IYourAudienceItem[]; loading: boolean }>({
    data: [],
    loading: false,
  });
  const { newAbortController } = useAbortController();

  const getData = async () => {
    setData({ ...data, loading: true });
    const controller = newAbortController();
    const res = await socialAPI.get({ endpoint: "audiences-trending/", signal: controller.signal });
    if (res && res.data) {
      setData({ ...data, data: res.data.items.slice(0, 5) });
    } else {
      setData({ ...data, loading: false });
    }
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <div className="my-6 grid grid-cols-1">
      <Card className="text-primary border-none p-6">
        <CardHeader className="p-0">
          <CardTitle className="text-xl">Top Audience</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col justify-between mt-6 p-0">
          <Table>
            <TableBody>
              {data.loading &&
                range(0, 5).map((index: number) => (
                  <div key={index} className="flex items-center justify-between w-full gap-6">
                    <div className="w-[600px]">
                      <Skeleton className="w-[350px] h-4 rounded-xl my-6" />
                    </div>
                    <Skeleton className="w-14 h-5" />
                    <Skeleton className="w-8 h-4" />
                    <Box variant="col-start" className="gap-1">
                      <Skeleton className="w-20 h-4" />
                      <Skeleton className="w-20 h-4" />
                    </Box>
                    <Skeleton className="w-20 h-4" />
                    <Skeleton className="w-10 h-10 rounded-full" />
                  </div>
                ))}

              {data.data.map((item, index) => {
                const color = getColorBadge(index);
                return (
                  <TableRow key={item.fb_uid} className="border-b-0">
                    <TableCell className="font-medium">
                      <InformationColumn {...item} />
                    </TableCell>
                    <TableCell>
                      <BadgePackage packageValue={item.package} />
                    </TableCell>
                    <TableCell className="w-[90px]">
                      {TYPE_SOCIAL_LABEL.find((type) => type.id == Number(item.type))?.name}
                    </TableCell>
                    <TableCell className="text-right flex flex-col text-secondary w-[90px]">
                      <AudiencesSizeColumn size={item.size} />
                    </TableCell>
                    <TableCell className="text-right w-[120px]">
                      <RatingColumn {...item} />
                    </TableCell>
                    <TableCell>
                      <div
                        className={cn(
                          "w-10 h-10 rounded-full bg-black flex items-center justify-center",
                          color
                        )}
                        children={<Champions />}
                      />
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default TopAudience;
