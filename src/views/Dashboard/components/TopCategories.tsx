import { <PERSON> } from "react-router-dom";
import { useAppSelector } from "store";
import range from "lodash/range";

import { Card, CardContent, CardHeader, CardTitle } from "components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "components/ui/table";
import { Badge } from "components/ui/badge";
import { Skeleton } from "components/ui/skeleton";

import { PATH_DASHBOARD } from "types/path";
import { getColorBadge } from "utils/palette";
import { fNumberToString } from "utils/number";

const TopCategories = () => {
  const { data, loading = true } = useAppSelector((state) => state.category);
  const topCategories = data.items.slice(0, 5);

  return (
    <Card className="w-full text-primary border-none">
      <CardHeader>
        <CardTitle className="text-xl">Top Categories</CardTitle>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-[#F7F7F8] text-sm text-primary border-none">
                <TableHead className="rounded-tl-[10px]">Category</TableHead>
                <TableHead className="text-right">Total Audience</TableHead>
                <TableHead className="rounded-tr-[10px]">Rank</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading &&
                range(0, 5).map((index: number) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Skeleton className="w-[250px] h-4 rounded-xl my-6" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="w-16 h-4" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="w-20 h-4" />
                    </TableCell>
                  </TableRow>
                ))}
              {topCategories.map((category, index) => {
                let styleBadge = getColorBadge(index);
                return (
                  <TableRow key={category.code}>
                    <TableCell className="font-medium hover:text-primary-hover">
                      <Link
                        to={`${
                          PATH_DASHBOARD.audience_finder.social
                        }?category__in=${encodeURIComponent(category.code)}`}
                        children={category.name}
                      />
                    </TableCell>
                    <TableCell className="text-right text-secondary font-semibold">
                      {fNumberToString(category.audience_count)}
                    </TableCell>
                    <TableCell>
                      <Badge className={styleBadge}>Top {index + 1}</Badge>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </CardHeader>
    </Card>
  );
};

export default TopCategories;
