import AnimatedProgress from "components/ui/animateProgess";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "components/ui/card";

interface Props {
  color: string;
  value: number;
  title: string;
}
const ProgressComponent = ({ color, value, title }: Props) => (
  <div className="relative w-full">
    <div className="absolute top-0 left-0 -translate-y-[120%] text-primary text-md">{title}</div>
    <AnimatedProgress targetValue={value} color={color} />
    <span className="absolute top-0 right-0 -translate-y-[120%] text-secondary text-sm">
      {value}
    </span>
  </div>
);

const RequestAudience = () => {
  return (
    <Card className="text-primary flex flex-col pb-6 border-none ">
      <CardHeader>
        <CardTitle className="text-xl">Request Audience</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col justify-between mt-6">
        <ProgressComponent value={50} color="#004B50" title="Group" />
        <ProgressComponent value={70} color="#27097E" title="Fanpage" />
        <ProgressComponent value={20} color="#7A4100" title="Profile" />
        <ProgressComponent value={10} color="#7A0916" title="Post" />
      </CardContent>
    </Card>
  );
};

export default RequestAudience;
