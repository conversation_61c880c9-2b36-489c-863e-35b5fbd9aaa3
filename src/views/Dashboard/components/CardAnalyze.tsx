import CountUp from "react-countup";
import styled from "styled-components";
import { RiDatabase2<PERSON>ill, RiGroupFill, RiIdCardFill, RiProfileLine } from "@remixicon/react";

import { Box } from "components/Box";
import { cn } from "utils/utils";

interface Props {
  title: string;
  count: number;
  className?: string;
  colorShape?: string;
  icon?: JSX.Element | React.ReactNode;
}

const CardAnalyze = ({ title, count, className, colorShape, icon }: Props) => (
  <CardWrapper
    className={cn(
      "p-4 w-full border-none relative overflow-hidden bg-white text-primary",
      className
    )}
  >
    <Box className="justify-center h-full">
      <Box variant="col-start" className="flex-1 h-full items-start justify-between gap-2">
        <div className="text-md">{title}</div>
        <CountUp
          className="whitespace-nowrap text-xl font-semibold"
          start={0}
          end={count}
          duration={2.75}
          separator=","
        />
      </Box>
      {icon && icon}
    </Box>
    <Shape style={{ color: colorShape }} />
  </CardWrapper>
);

const CardAnalyzeWrapper = () => {
  return (
    <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-6">
      <CardAnalyze
        title="Your Audience"
        count={50}
        className="bg-[#924FE8] text-white"
        icon={<RiGroupFill size={25} color="white" />}
      />
      <CardAnalyze title="Your Segment" count={120} icon={<RiIdCardFill size={30} />} />
      <CardAnalyze title="Total Profile" count={1000} icon={<RiProfileLine size={30} />} />
      <CardAnalyze title="Record Enrichment" count={50000} icon={<RiDatabase2Fill size={30} />} />
    </div>
  );
};

const CardWrapper = styled("div")(() => ({
  padding: "16px 16px 16px 24px",
  boxShadow: "rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px",
  borderRadius: "12px",
}));

const Shape = styled("span")(() => ({
  width: "240px",
  flexShrink: 0,
  height: "240px",
  display: "inline-flex",
  backgroundColor: "currentcolor",
  mask: "url(./src/assets/square.svg) center center / contain no-repeat",
  top: "0px",
  right: "-20%",
  zIndex: "2",
  opacity: "0.24",
  position: "absolute",
}));

export default CardAnalyzeWrapper;
