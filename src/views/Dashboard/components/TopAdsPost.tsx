import { useEffect, useRef, useState } from "react";
import Slider from "react-slick";
import { <PERSON> } from "react-router-dom";
import { RiArrowLeftSLine, RiArrowRightSLine, RiFilter2Line } from "@remixicon/react";

import { Box } from "components/Box";
import { But<PERSON> } from "components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "components/ui/card";
import AvatarByName from "components/AvatarByName";
import PostThumbnail from "components/LiveAdsPost/PostThumbnail";
import HeaderWrapper from "components/Header";
import CardDescription from "views/AdsPostView/components/Card/CardDescription";

import useAvatar from "hooks/useAvatar";
import { FB_SERVICE_PATH } from "types/Router";
import { IAdsPostItem } from "types/CrawlService";
import { adsPostAPI } from "apis/adsPost";
import { cn } from "utils/utils";

import "../style/customSlide.css";

const FanpageInfo = ({ title, id, page_id }: { title: string; id: string; page_id: string }) => {
  const { avatar } = useAvatar({ type: "page", uid: id });
  return (
    <>
      <AvatarByName urlImage={avatar.url} name={title} className="w-12 h-12" />
      <Link
        className="text-md font-semibold text-primary flex-1 hover:text-primary-hover"
        to={`/${FB_SERVICE_PATH.POST}/${FB_SERVICE_PATH.DETAIL}/${page_id}`}
        children={title}
      />
    </>
  );
};

const TopAdsPost = () => {
  const slider = useRef<any>(null);
  const [data, setData] = useState<IAdsPostItem[]>([]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    const response = await adsPostAPI.getListPost({
      params: { page: 1, limit: 8, att_type__in: "IMAGE" },
    });
    if (response && response.data) {
      setData(response.data.items);
    }
  };

  const classArrow = "cursor-pointer w-8 h-8 p-[6px] bg-white border rounded-full shadow-sm";
  const setting = {
    slidesToShow: 3.5,
    slidesToScroll: 1,
    infinite: false,
    arrows: true,
    dots: false,
    className: "relative slider-dashboard",
  };
  return (
    data.length > 0 && (
      <>
        <HeaderWrapper
          leftChildren={{ title: "Live & Ads post", titleSize: "xl" }}
          rightChildren={
            <Box className="gap-1">
              <RiArrowLeftSLine
                onClick={() => slider?.current?.slickPrev()}
                className={classArrow}
                color="gray"
                size={16}
              />
              <RiArrowRightSLine
                onClick={() => slider?.current?.slickNext()}
                className={cn(classArrow, slider?.current?.slickNext() && "hidden")}
                color="gray"
                size={16}
              />
            </Box>
          }
        />
        <Slider
          ref={slider}
          {...setting}
          children={data.map((item) => (
            <Card className="h-fit flex flex-col justify-start gap-[14px] w-full mt-0 p-2" key={item.post_id}>
              <CardHeader className="p-0">
                <CardTitle className="flex items-center justify-between gap-2 ">
                  <FanpageInfo title={item.actor_name} id={item.actor_id} page_id={item.actor_id} />
                </CardTitle>
                <CardDescription
                  is_active={item.is_active || false}
                  publish_time={item.publish_time || ""}
                  publisher_platform={item.publisher_platform || []}
                />
              </CardHeader>

              <CardContent className="h-fit p-0">
                <PostThumbnail
                  title={item.actor_name}
                  attachments={item.attachments}
                  att_type={item.att_type || ""}
                  className="object-cover h-[300px] w-full mt-2"
                />
                <div className="mt-4 h-fit text-sm text-primary line-clamp-3 overflow-hidden text-ellipsis min-h-[60px] mb-2">
                  {item.content}
                </div>
              </CardContent>

              <CardFooter className="h-fit flex justify-between p-0">
                <Link className="w-full" to={`/${FB_SERVICE_PATH.POST}/${item.post_id}`}>
                  <Button className="w-full" variant="main">
                    <RiFilter2Line size={17} className="mr-1" />
                    Total Lead : {item.audience?.size || 0}
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        />
      </>
    )
  );
};

export default TopAdsPost;
