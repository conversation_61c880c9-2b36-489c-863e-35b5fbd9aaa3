import CardAnalyzeWrapper from "./components/CardAnalyze";
import RequestAudience from "./components/RequestAudience";
import TopAdsPost from "./components/TopAdsPost";
import TopAudience from "./components/TopAudience";
import TopCategories from "./components/TopCategories";

const Dashboard = () => {
  return (
    <div>
      <CardAnalyzeWrapper />
      <div className="my-6 grid lg:grid-cols-2 grid-cols-1 gap-4">
        <RequestAudience />
        <TopCategories />
      </div>
      <TopAudience />
      <TopAdsPost />
    </div>
  );
};

export default Dashboard;
