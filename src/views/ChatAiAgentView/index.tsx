import HeaderWrapper from '../SocialDataView/components/Header';
import ChatBoxContainer from '../../components/ChatAIAgent/ChatBoxContainer';
import { AI_AGENT } from '../../constants/AiAgent';

export const ChatAiAgentView = () => {
  return (
    <>
      <HeaderWrapper
        className="flex-col gap-3 sm:flex-col-reverse max-[768px]:mt-8 items-start md:flex-row"
        leftChildren={{
          title: AI_AGENT.title,
          subTitle: AI_AGENT.subtitle
        }}
      />
      <div className="mt-3 h-full flex flex-col p-2 px-[130px] flex-1">
        <ChatBoxContainer isPopup={false} />
      </div>
    </>
  );
};
