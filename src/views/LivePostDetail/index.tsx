import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import { Box } from "components/Box";
import PostDetail from "../LivePostView/components/QuickViewImageLeft";
import Breadcrumb from "components/Breadcrumb";
import HeaderDetailModal from "components/HeaderDetailModal";
import TablePreview from "views/SocialDataDetail/components/TablePreview";
import ChartSummarize from "views/SocialDataDetail/components/ChartSummarize";
import LiveChatWrapper from "views/LivePostView/components/LiveChatWrapper";
import UpdateAndBuyAudience from "views/SocialDataDetail/components/UpdateAndBuyAudience";

import useGetSocialPreview from "hooks/useGetAudiencePreview";
import useGetAudienceSummarize from "hooks/useGetAudienceSummarize";
import useFilters from "hooks/useFilters";
import useAvatar from "hooks/useAvatar";
import { liveCommentAPI } from "apis/livePost";
import { ILivePostItem } from "types/CrawlService";
import { formatPayloadAvatar } from "utils/utils";
import ScrollButton from "components/ScrollButton";
import UpdateButton from "views/SocialDataDetail/components/UpdateButton";

const LivePostConverterDetail = () => {
  const { id = "" } = useParams<{ id: string }>();
  const { params } = useFilters();

  const [dataPost, setDataPost] = useState<ILivePostItem>();
  const { summarize, loading } = useGetAudienceSummarize(id || "");

  const previewData = useGetSocialPreview({ id: id || "", page: params.page ?? 1 });
  const payload = formatPayloadAvatar(summarize.type || 1, summarize.fb_uid, summarize.actor_id);
  const { avatar } = useAvatar(payload);

  useEffect(() => {
    getDataPost(id);
  }, []);

  const getDataPost = async (id: string) => {
    const response = await liveCommentAPI.getPostDetail({ id: id });
    if (response) {
      setDataPost(response.data);
    }
  };

  return (
    <>
      <Breadcrumb path={summarize.name} loading={loading} />
      <HeaderDetailModal
        name={summarize.name || ""}
        loading={loading}
        avatarUrl={avatar.url}
        size={summarize.size}
        description={summarize.description}
        uid={summarize.fb_uid}
        packageValue={summarize.package || ""}
        contentRight={
          <UpdateAndBuyAudience
            audience={summarize}
            disableBuyAudience={summarize.is_aud_added}
            disableBuyDataset={summarize.is_ds_added}
            disableUpdate={summarize.system}
          />
        }
        typeAudience={summarize.type || 4}
        subTypeAudience={summarize.subtype}
        className={summarize.description ? "items-start" : "items-center"}
        rightAvatarChild={<UpdateButton className="md:hidden" />}
      />
      {dataPost && (
        <Box className="shadow-sm mt-3 rounded-md items-start grid grid-cols-1 lg:grid-cols-2 md:mt-6 md:rounded-sm overflow-hidden">
          <PostDetail post={dataPost} avatar={avatar.url} isShowContent={false} />
          <Box variant="col-start" className="justify-between flex-1 p-3 md:p-4">
            <LiveChatWrapper id={id} showTitle={true} isDetail={true} />
          </Box>
        </Box>
      )}
      <div className="mt-6">
        <ChartSummarize loading={loading} data={summarize.summarize || {}} />
      </div>
      {previewData && <TablePreview data={previewData} />}
      <ScrollButton />
    </>
  );
};
export default LivePostConverterDetail;
