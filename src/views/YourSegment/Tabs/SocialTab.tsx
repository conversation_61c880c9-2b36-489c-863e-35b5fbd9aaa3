import { useEffect, useState } from "react";
import { segmentContext } from "../context/segmentContext";

import { DataTable } from "components/DataTable/TableCustom";
import Pagination from "components/Pagination";
import EmptyData from "../components/EmptyData";

import { SOCIAL_SEGMENT_COLUMN } from "constants/yourData/column";
import { yourSegmentApi } from "apis/yourSegment";
import { getUnreadType, TUnReadByType } from '../../../store/redux/notification/slice';
import { useAppDispatch, useAppSelector } from '../../../store';
import { AudienceSegmentItem } from '../../../types/yourSegment';

const SocialTableSegment = () => {
  const [pageIndex, setPageIndex] = useState<number>(1);
  const { socialSegment, setSocialSegment } = segmentContext();
  const { unreadByType, timeReadNoti } = useAppSelector((state) => state.notification);
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(getUnreadType({
      type: 'your_segment_social'
    }));
  }, [timeReadNoti]);
  
  useEffect(() => {
    fetchSocialSegment();
  }, [pageIndex, timeReadNoti]);

  const fetchSocialSegment = async () => {
    setSocialSegment((prev) => ({ ...prev, loading: true }));
    try {
      const res = await yourSegmentApi.getList(pageIndex, 10);
      setSocialSegment({
        loading: false,
        count: res.count,
        items: res.items,
      });
    } catch (error) {
      setSocialSegment((prev) => ({ ...prev, loading: true }));
    }
  };

  const handleUpdateData = () => {
    return socialSegment.items.map((social:AudienceSegmentItem) => {
      if (unreadByType?.some((item: TUnReadByType) => item.ref_id === social?.id?.toString() && item?.meta?.datatype === social.datatype)) {
        return { ...social, isHighLight: true };
      }
      return social;
    });
  };

  return socialSegment.count > 0 ? (
    <>
      <DataTable
        columns={SOCIAL_SEGMENT_COLUMN}
        data={handleUpdateData()}
        loading={socialSegment.loading}
      />
      <Pagination
        className="mt-4"
        totalCount={socialSegment.count}
        pageSize={10}
        currentPage={pageIndex}
        onPageChange={(page) => setPageIndex(page)}
      />
    </>
  ) : !socialSegment.loading ? (
    <EmptyData />
  ) : (
    <div className="min-h-[500px] w-full"></div>
  );
};
export default SocialTableSegment;
