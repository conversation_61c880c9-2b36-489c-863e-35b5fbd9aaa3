import { useEffect, useState } from "react";
import { segmentContext } from "../context/segmentContext";

import { DataTable } from "components/DataTable/TableCustom";
import Pagination from "components/Pagination";
import EmptyData from "../components/EmptyData";

import { personaAPI } from "apis/persona";
import { PERSONA_SEGMENT_COLUMN } from "constants/yourData/column";
import { Skeleton } from "components/ui/skeleton";
import { getUnreadType, TUnReadByType } from '../../../store/redux/notification/slice';
import { useAppDispatch, useAppSelector } from '../../../store';
import { PersonaSegment } from '../../../types/Persona';
import { useAbortController } from '../../../hooks/useAbortController';
const PersonaSegmentTable = () => {
  const [pageIndex, setPageIndex] = useState<number>(1);
  const { personaSegment, setPersonaSegment } = segmentContext();
  const { unreadByType, timeReadNoti } = useAppSelector((state) => state.notification);
  const dispatch = useAppDispatch();

  const { newAbortController } = useAbortController();

  useEffect(() => {
    dispatch(getUnreadType({
      type: 'your_segment_work'
    }));
  }, [timeReadNoti]);

  useEffect(() => {
    fetchPersonaSegment();
  }, [pageIndex, timeReadNoti]);

  const fetchPersonaSegment = async () => {
    setPersonaSegment((prev) => ({ ...prev, loading: true }));
    try {
      const controller = newAbortController();
      const res = await personaAPI.getPersonaSegment({
        page:pageIndex,
        limit: 10,
        signal: controller.signal
      });
      setPersonaSegment({
        loading: false,
        count: res.count,
        items: res.items,
      });
    } catch (error) {
      setPersonaSegment((prev) => ({ ...prev, loading: true }));
    }
  };

  const handleUpdateData = () => {
    return personaSegment.items.map((persona: PersonaSegment ) => {
      if (unreadByType?.some((item: TUnReadByType) => item.ref_id === persona.id.toString())) {
        return { ...persona, isHighLight: true };
      }
      return persona;
    });
  };

  return (
    <>
      {personaSegment.loading && <Skeleton className="h-[500px]" />}
      {personaSegment.count > 0 ? (
        <>
          <DataTable
            columns={PERSONA_SEGMENT_COLUMN}
            data={handleUpdateData()}
            loading={personaSegment.loading}
            // classTable={'[&_tbody>tr]:h-[80px]'}
          />
          <Pagination
            className="mt-4"
            totalCount={personaSegment.count}
            pageSize={10}
            currentPage={pageIndex}
            onPageChange={(page) => setPageIndex(page)}
          />
        </>
      ) : (
        !personaSegment.loading && <EmptyData />
      )}
    </>
  );
  //   personaSegment.loading ? (
  //   <Skeleton className="h-[500px]" />
  // ) : personaSegment.count > 0 ? (
  //   <>
  //     <DataTable
  //       columns={PERSONA_SEGMENT_COLUMN}
  //       data={personaSegment.items}
  //       loading={personaSegment.loading}
  //     />
  //     <Pagination
  //       className="mt-4"
  //       totalCount={personaSegment.count}
  //       pageSize={10}
  //       currentPage={pageIndex}
  //       onPageChange={(page) => setPageIndex(page)}
  //     />
  //   </>
  // ) : (
  //   <EmptyData type="persona" />
  // );
};

export default PersonaSegmentTable;
