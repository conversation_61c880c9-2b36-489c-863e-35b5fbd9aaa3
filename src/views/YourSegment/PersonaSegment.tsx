import { useContext, useEffect, useState } from 'react';
import { useNavigate, useParams } from "react-router-dom";

import Breadcrumb from "components/Breadcrumb";
import HeaderDetailModal from "components/HeaderDetailModal";
import Summarize from "components/PersonaDetail/Summarize";
import UserTable from "components/PersonaDetail/UserTable";
import ShowResult from "views/SocialDataDetail/components/ShowResult";

import { personaAPI } from "apis/persona";
import useFilters from "hooks/useFilters";
import { PersonaAudienceItem, PersonaAudiencePreview } from "types/Persona";
import { getCityData } from "utils/persona";
import { formatRegionData } from "utils/persona/formatRegion";
import { getUnreadType } from '../../store/redux/notification/slice';
import { useAppDispatch, useAppSelector } from '../../store';
import { notificationApi } from '../../apis/notification';
import { Button } from '../../components/ui/button';
import { RiAddLine, RiDownload2Line } from '@remixicon/react';
import LABEL from '../../constants/label';
import DownloadYourDataModal from '../../components/yourAudienceAndSegment/DownloadYourDataModal';
import AddYourDataModal from '../../components/yourAudienceAndSegment/AddYourDataModal';
import { ModalContext } from '../../providers/Modal';
import { useAbortController } from '../../hooks/useAbortController';
// import { FilterProvider } from "views/Persona/context/FilterProvider";
// import AudienceDetailFilterOption from "views/Persona/components/FilterOptions/AudienceDetailFilterOption";

const PersonaSegment = () => {
  const { id } = useParams<{ id: string }>();
  const { params } = useFilters();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { unreadByType } = useAppSelector((state) => state.notification);
  const modal = useContext(ModalContext);
  const { newAbortController } = useAbortController();
  const [overview, setOverview] = useState<{
    data: PersonaAudienceItem | null;
    loading: boolean;
  }>({
    data: null,
    loading: false,
  });
  const [preview, setPreview] = useState<{
    count: number;
    items: PersonaAudiencePreview[];
    loading: boolean;
  }>({
    count: 0,
    items: [],
    loading: false,
  });

  const handleViewDetail = async () => {
    const findNoti = unreadByType?.find((item) => item.ref_id === id);
    if (!findNoti) {
      return;
    }
    return await notificationApi.handleMarkAsRead(findNoti?.id);
  };

  useEffect(() => {
    if (!id) return;
    const fetchOverview = async () => {
      setOverview({ data: null, loading: true });

      const response = await personaAPI.getPersonaSegmentById(id);
      if (response.status === 422 || response.status === 404) {
        navigate("/404");
      }
      if (response && response.data) {
        setOverview({ data: response.data.data, loading: false });
      }
      setOverview((prev) => ({ ...prev, loading: false }));
    };
    fetchOverview();
    dispatch(getUnreadType({
      type: 'your_segment_work'
    }));
    handleViewDetail();
  }, [id]);

  useEffect(() => {
    if (!id) return;
    const fetchPreview = async () => {
      setPreview({ ...preview, loading: true });
      try {
        const controller = newAbortController();
        const res = await personaAPI.getPersonaSegmentPreview({
          id,
          page: Number(params.page) || 1,
          limit: 10,
          signal: controller.signal
        });
        setPreview({ ...res, loading: false });
      } catch (error) {
        setPreview({ ...preview, loading: false });
        return { error };
      }
    };
    fetchPreview();
  }, [id, params.page]);

  const handleDownload = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <DownloadYourDataModal
        id={overview.data?.id ?? ''}
        name={overview.data?.name ?? ''}
        segment_size={overview.data?.segment_size ?? 0}
      />,
      className: "max-w-[680px]",
    }));
  };

  const handleOpenModalAdd = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "max-w-[calc(100%-32px)] md:max-w-[666px] bg-[#F0F0F0] p-4 lg:p-6",
      isOpen: true,
      isShowTitle: false,
      content: (
        <AddYourDataModal id={Number(id)} count={overview.data?.segment_size} segmentName={overview.data?.name ?? ''} typeAdd={'persona'} />
      ),
    }));
  };

  return (
    <>
      <Breadcrumb path={overview?.data?.name} loading={!overview} />
      <HeaderDetailModal
        size={overview?.data?.segment_size ?? 0}
        description={overview.data?.description ?? ""}
        loading={overview.loading}
        name={overview?.data?.name ?? ""}
        classRightContent={'max-md:w-full'}
        contentRight={<>
          <Button
            className="max-md:w-full bg-primary hover:opacity-50 hover:bg-primary flex gap-1"
            onClick={() => handleOpenModalAdd()}
          >
            <RiAddLine size={14} />
            Add To ...
          </Button>
          {overview?.data?.datatype !== 'DATASET' && <Button
            onClick={() => handleDownload()}
            className="max-md:w-full bg-primary hover:opacity-50 hover:bg-primary flex gap-1"
          >
            <RiDownload2Line size={14} />
            {LABEL.download}
          </Button>}
        </>}
      />
      <Summarize
        cityData={getCityData(overview?.data?.summarize.person_province ?? {})}
        valuesRegion={formatRegionData(overview?.data?.summarize.person_region ?? {}).values}
        labelsRegion={formatRegionData(overview?.data?.summarize.person_region ?? {}).labels}
        loading={overview.loading}
        dataAge={overview.data?.summarize.age_gender}
        dataGender={overview.data?.summarize.gender}
      />
      <div className="flex flex-col gap-y-6">
        <ShowResult total={preview.count} />
      </div>
      <UserTable
        loading={preview.loading}
        rowData={preview.items ?? []}
        count={preview.count ?? 0}
        isDataset={overview.data?.datatype === 'DATASET'}
      />
    </>
  );
};
export default PersonaSegment;
