import { RiAccountPinCircleLine, RiCloseLine, RiGroup2Line } from '@remixicon/react';
import { SegmentProvider } from './context/segmentContext';
import { useContext, useEffect, useRef } from 'react';

import TabRouteWrap, { ITab } from 'components/Tabs/TabRouteWrap';
import Breadcrumb from 'components/Breadcrumb';
import HeaderWrapper from 'components/Header';

import { DATA_SEGMENT, ROOT_PATH } from 'types/Router';
import { TABS_SEGMENT, YOUR_DATA_TITLE } from 'constants/yourData/label';
import { Button } from '../../components/ui/button';
import { TutorialIcon } from '../../assets/icons/TutorialIcon';
import { ModalContext } from '../../providers/Modal';
import useOutsideClick from '../../hooks/useClickOutSide';
import handleCloseModal from '../../utils/handleCloseModal';
import { SegmentLog } from './components/SegmentLog';
import { handleGetSegmentLog } from '../../store/redux/SegmentLog/slice';
import { useAppDispatch } from '../../store';

const LIST_TAB: ITab[] = [
  {
    title: TABS_SEGMENT.SOCIAL_AUDIENCE,
    path: `${ROOT_PATH}/${DATA_SEGMENT.ROOT}/${DATA_SEGMENT.SOCIAL}`,
    type:'your_segment_social',
    tab_icon: RiGroup2Line,
  },
  {
    title: TABS_SEGMENT.PERSONA_AUDIENCE,
    path: `${ROOT_PATH}/${DATA_SEGMENT.ROOT}/${DATA_SEGMENT.PERSONA}`,
    type:'your_segment_work',
    tab_icon: RiAccountPinCircleLine,
  },
];

const YourSegment = () => {
  const modal = useContext(ModalContext);
  const ref = useRef(null);
  const refSecond = useRef(null);
  const dispatch = useAppDispatch();
  useOutsideClick(ref, refSecond, () => {
    if (!!modal?.dataDialog?.isPopupVideo) {
      handleCloseModal(modal);
    }
  });

  const handleOpenTutorial =()=>{
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      isPopupVideo: true,
      content: (
        <div ref={refSecond}>
          <div className="text-right">
            <Button
              onClick={() => handleCloseModal(modal)}
              className="p-[10px] text-sm font-semibold mb-1 bg-transparent hover:bg-transparent text-primary"
            >
              <RiCloseLine size={20} />
              Close
            </Button>
          </div>
          <iframe
            src="https://www.youtube.com/embed/w5gaDPI0oHg?autoplay=1"
            title="Big360 - Your Segment"
            className="rounded-2xl w-full max-w-[920px] aspect-video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
        </div>
      ),
      className: 'max-w-[936px] !p-2 max-lg:max-w-[calc(100%-12px)]'
    } ))
  }

  useEffect(() => {
    dispatch(handleGetSegmentLog()).unwrap();
  }, []);

  return (
    <SegmentProvider>
      <>
        <Breadcrumb />
        <HeaderWrapper
          leftChildren={{
            title: <div className="flex gap-2" ref={ref}>
              <h1>{YOUR_DATA_TITLE.SEGMENT.title}</h1>
              <Button
                onClick={() => handleOpenTutorial()}
                className="flex items-center justify-center bg-[#F0F0F0] h-[36px] gap-1 p-2 rounded-xl	hover:bg-[#F0F0F0]"
              >
                <TutorialIcon /><p className="text-[#8F5CFF] text-xs mt-[1px]">Guide</p>
              </Button>
            </div>,
            subTitle: YOUR_DATA_TITLE.SEGMENT.subTitle,
          }}
          rightChildren={<SegmentLog />}
        />
        <TabRouteWrap tabs={LIST_TAB} tabListClass='max-md:gap-10'/>
      </>
    </SegmentProvider>
  );
};
export default YourSegment;
