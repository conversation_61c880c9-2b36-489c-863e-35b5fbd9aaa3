import { RiArrowRightSFill, RiNumber8 } from "@remixicon/react";
import { useEffect, useRef } from "react";

import AvatarByName from "components/AvatarByName";
import TreeNodeItem from "../../YourSegment/components/Tree/TreeNodeItem";
import TypeProcess from "../../YourSegment/components/Tree/TypeProcess";
import { TypeTreeNode } from "types/YourData";
import { cn } from "utils/utils";
import useAvatar from "hooks/useAvatar";

type Props = { id: string; name: string; rootId: string; tree: TypeTreeNode; maxLevel?: number };

const ProcessedTree = ({ name, rootId, tree, maxLevel }: Props) => {
  const treeRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});
  const treeRoot = useRef<HTMLDivElement | null>(null);
  const { avatar } = useAvatar({ type: "group", uid: rootId });
  const nodeID = "ProcessedTree-node_";
  const arrowID = "ProcessedTree-arrow_";
  const treeLineClass = "ProcessedTree-line";
  // draw line between nodes
  useEffect(() => {
    Object.values(treeRefs.current).forEach((el, index) => {
      if (el) {
        const defaultWidth = index > 0 ? 60 + (index - 1) * 20 : 30;
        const nodes = treeRoot.current?.querySelectorAll(`#${nodeID}${index + 1}`);
        nodes?.forEach((nodeChild, _index) => {
          if (nodeChild.querySelector(`.${treeLineClass}`)) return;
          const line = `<div class="${treeLineClass} ${_index % 2 === 0 ? "bottom-0" : "top-0"} ${
            index === 0 ? " right-[-10px]" : "right-[-20px]"
          }"></div>`;
          nodeChild.insertAdjacentHTML("afterbegin", line);
        });
        const arrows = treeRoot.current?.querySelectorAll(`#${arrowID}${index + 1}`);
        arrows?.forEach((arrow) => {
          (arrow as HTMLElement).style.width = `${defaultWidth}px`;
        });
      }
    });
  }, [tree]);
  const renderTree = (node: TypeTreeNode, level: number = 0) => {
    if (maxLevel && level > maxLevel) return null;
    return (
      <div
        key={level}
        ref={(el) => (treeRefs.current[level] = el)}
        className="relative flex flex-col items-end justify-center py-1.5"
        style={{ marginRight: `${level * 20}px` }}
        id={`${nodeID}${level}`}
      >
        {node.audience ? (
          <>
            <div
              style={{
                width: `${level > 1 ? 20 : level * 10}px`,
              }}
              className="absolute h-0.5 left-full flex items-center justify-center bg-[#ae81ef] text-[#ae81ef]"
            >
              <RiArrowRightSFill className="flex-shrink-0" size={20} />
            </div>
            <TreeNodeItem audience={node.audience} filter={node.filter} position={""} />
          </>
        ) : (
          level > 0 && (
            <div
              id={`${arrowID}${level}`}
              style={{
                marginRight: `${level === 1 ? "-10px" : "-20px"}`,
              }}
              className="absolute transform -translate-y-1/2 top-1/2 h-0.5 flex items-center justify-center text-[#ae81ef] bg-[#ae81ef]"
            >
              <div className="absolute bg-white">
                <TypeProcess processType={node.op!} />
              </div>
              {maxLevel && level === maxLevel && (
                <div className="absolute h-4 w-4 rounded-full border right-full flex items-center justify-center">
                  <RiNumber8 className="rotate-90" size={10} />
                </div>
              )}
            </div>
          )
        )}

        {node.children?.map((child) => renderTree(child, level + 1))}
      </div>
    );
  };

  return (
    <div className="flex items-center justify-end gap-10">
      <div ref={treeRoot}>{renderTree(tree)}</div>
      <div
        className={cn("border relative flex-shrink-0 border-primary p-1 rounded-full text-[10px]")}
      >
        <div
          className={cn(
            "absolute w-[52px] [&_svg]:w-4 [&_svg]:bg-white [&_svg]:h-4 flex items-center justify-center right-full h-0.5 bg-[#ae81ef] top-1/2 transform -translate-y-1/2",
            tree.children?.length === 0 && "w-[42px]"
          )}
        >
          <TypeProcess processType={tree.op!} />
        </div>
        <AvatarByName className="w-[20px] h-[20px]" name={name} urlImage={avatar.url} />
      </div>
    </div>
  );
};

export default ProcessedTree;
