import AvatarByName from "components/AvatarByName";
import { FlatTreeNode } from "types/YourData";
import { Link } from "react-router-dom";
import { cn } from "utils/utils";
import { processedFilter } from "constants/yourData/label";
import useAvatar from "hooks/useAvatar";

const TreeNodeItem = ({ audience, filter }: FlatTreeNode) => {
  const { avatar } = useAvatar({ type: "group", uid: audience?.id.toString()! });
  const isAudience = Object.keys(filter!).length > 0;

  return (
    <div className="flex flex-col border rounded-lg w-36 shadow overflow-hidden">
      <Link
        to={
          isAudience
            ? `/social-data/your-audience/${audience?.id}`
            : `/social-data/detail/${audience?.id}`
        }
        className={cn(
          "flex items-center justify-end bg-primary text-white gap-2 px-2 py-1 w-full",
          isAudience && "border-b"
        )}
      >
        <span className="text-[10px] font-medium line-clamp-1">{audience?.name}</span>
        <AvatarByName className="w-[20px] h-[20px]" name={audience?.name!} urlImage={avatar.url} />
      </Link>
      {isAudience && (
        <div className="px-2 py-1 text-[9px] max-h-[66px] overflow-x-hidden">
          {Object.entries(filter || {}).map(([key, value]) => {
            return (
              <div key={key} className="flex items-center gap-1 ">
                <span className="font-medium">
                  {processedFilter[key as keyof typeof processedFilter]}:
                </span>
                <span className="text-gray-500">
                  {Array.from(value)
                    .map((item) => item.value)
                    .join(",")}
                </span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
export default TreeNodeItem;
