import GroupIntersect from "assets/icons/GroupIntersect";
import GroupMinus from "assets/icons/GroupMinus";
import GroupMix from "assets/icons/GroupMix";
import { cn } from "utils/utils";

type Props = {
  processType: number;
  className?: string;
};

const TypeProcess = ({ processType, className }: Props) => {
  const map: { [key: number]: { component: React.ElementType; type: string } } = {
    0: { component: GroupIntersect, type: "Intersect" },
    1: { component: GroupMix, type: "Mix" },
    2: { component: GroupMinus, type: "Minus" },
  };

  const Icon = map[processType];

  if (!Icon) return null;

  return (
    <div className={cn("relative [&_svg]:w-4 [&_svg]:h-4", className)}>
      <Icon.component active={true} />
      <span className="absolute font-bold inset-0 top-6 flex items-center justify-center z-[999] text-primary text-[9px]">
        {Icon.type}
      </span>
    </div>
  );
};

export default TypeProcess;
