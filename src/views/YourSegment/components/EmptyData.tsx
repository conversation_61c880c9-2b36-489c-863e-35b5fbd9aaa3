import { Box } from "components/Box";
// import { Button } from "components/ui/button";
// import { Link } from "react-router-dom";
// import { PATH_DASHBOARD } from "types/path";

const EmptyData = () => {
  return (
    <Box variant="col-start" className="w-full h-full items-center justify-center gap-4">
      <Box variant="col-start" className="gap-3  items-center justify-center">
        <div className="flex justify-center items-center flex-col h-[550px] text-center">
          <div className="mx-auto mb-4 flex items-center justify-center">
            <svg
              className="w-[202px] h-[103px] sm:w-[405px] sm:h-[203px]"
              width="405"
              height="203"
              viewBox="0 0 405 203"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g filter="url(#filter0_dd_188_16411)">
                <path
                  d="M32.5615 48.0092C32.5615 39.1726 39.725 32.0092 48.5615 32.0092H356.562C365.398 32.0092 372.562 39.1726 372.562 48.0092V154.625C372.562 163.461 365.398 170.625 356.562 170.625H48.5615C39.725 170.625 32.5615 163.461 32.5615 154.625V48.0092Z"
                  fill="#FDFDFD"
                  shapeRendering="crispEdges"
                />
                <path
                  d="M44.5615 97.8169C44.5615 80.1438 58.8884 65.8169 76.5615 65.8169C94.2346 65.8169 108.562 80.1438 108.562 97.8169C108.562 115.49 94.2346 129.817 76.5615 129.817C58.8884 129.817 44.5615 115.49 44.5615 97.8169Z"
                  fill="#F0F0F0"
                />
                <path
                  d="M77.2519 86.1502H91.5615C92.482 86.1502 93.2282 86.8964 93.2282 87.8169V111.15C93.2282 112.071 92.482 112.817 91.5615 112.817H61.5615C60.6411 112.817 59.8949 112.071 59.8949 111.15V84.4836C59.8949 83.5631 60.6411 82.8169 61.5615 82.8169H73.9185L77.2519 86.1502ZM63.2282 86.1502V109.484H89.8949V89.4836H75.8712L72.5378 86.1502H63.2282ZM69.8949 107.817C69.8949 104.135 72.8796 101.15 76.5615 101.15C80.2434 101.15 83.2282 104.135 83.2282 107.817H69.8949ZM76.5615 99.4836C74.2604 99.4836 72.3949 97.6181 72.3949 95.3169C72.3949 93.0157 74.2604 91.1502 76.5615 91.1502C78.8627 91.1502 80.7282 93.0157 80.7282 95.3169C80.7282 97.6181 78.8627 99.4836 76.5615 99.4836Z"
                  fill="#515667"
                />
                <rect x="87.5002" y="112.817" width="24" height="24" rx="12" fill="#FDFDFD" />
                <path
                  d="M98.5002 123.817V117.817H100.5V123.817H106.5V125.817H100.5V131.817H98.5002V125.817H92.5002V123.817H98.5002Z"
                  fill="#6B7183"
                />
                <g clipPath="url(#clip0_188_16411)">
                  <path
                    d="M123.5 56.0092C123.5 49.3818 128.873 44.0092 135.5 44.0092H348.669C355.297 44.0092 360.669 49.3817 360.669 56.0092V146.548C360.669 153.175 355.297 158.548 348.669 158.548H135.5C128.873 158.548 123.5 153.175 123.5 146.548V56.0092Z"
                    fill="#E1E2E3"
                  />
                  <rect x="123.5" y="59.9728" width="237.169" height="16.078" fill="#FDFDFD" />
                  <rect x="123.5" y="76.4722" width="237.169" height="16.078" fill="#FDFDFD" />
                  <rect x="123.5" y="92.9716" width="237.169" height="16.078" fill="#FDFDFD" />
                  <rect x="123.5" y="109.471" width="237.169" height="16.078" fill="#FDFDFD" />
                  <rect x="123.5" y="125.97" width="237.169" height="16.078" fill="#FDFDFD" />
                  <rect x="123.5" y="142.47" width="237.169" height="16.078" fill="#FDFDFD" />
                </g>
                <path
                  d="M123.5 56.0091C123.5 49.3817 128.873 44.0092 135.5 44.0092H348.562C355.189 44.0092 360.562 49.3817 360.562 56.0092V146.625C360.562 153.252 355.189 158.625 348.562 158.625H135.5C128.873 158.625 123.5 153.252 123.5 146.625V56.0091Z"
                  stroke="#E1E2E3"
                />
              </g>
              <defs>
                <filter
                  id="filter0_dd_188_16411"
                  x="0.561523"
                  y="0.00915527"
                  width="404"
                  height="202.615"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood floodOpacity="0" result="BackgroundImageFix" />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feMorphology
                    radius="8"
                    operator="erode"
                    in="SourceAlpha"
                    result="effect1_dropShadow_188_16411"
                  />
                  <feOffset dy="4" />
                  <feGaussianBlur stdDeviation="10" />
                  <feComposite in2="hardAlpha" operator="out" />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.1 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_188_16411"
                  />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feOffset />
                  <feGaussianBlur stdDeviation="16" />
                  <feComposite in2="hardAlpha" operator="out" />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.02 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="effect1_dropShadow_188_16411"
                    result="effect2_dropShadow_188_16411"
                  />
                  <feBlend
                    mode="normal"
                    in="SourceGraphic"
                    in2="effect2_dropShadow_188_16411"
                    result="shape"
                  />
                </filter>
                <clipPath id="clip0_188_16411">
                  <path
                    d="M123.5 56.0091C123.5 49.3817 128.873 44.0092 135.5 44.0092H348.562C355.189 44.0092 360.562 49.3817 360.562 56.0092V146.625C360.562 153.252 355.189 158.625 348.562 158.625H135.5C128.873 158.625 123.5 153.252 123.5 146.625V56.0091Z"
                    fill="white"
                  />
                </clipPath>
              </defs>
            </svg>
          </div>
          <p className="font-bold text-base text-primary-crm mb-2" children={"No Segment Found"} />
          <p
            className="font-normal text-sm text-secondary"
            children={
              "Create audience segments from Your Audience to organize and target the right people."
            }
          />
        </div>
        {/*<span className="text-secondary text-md">Add your Segment from Your Audience.</span>*/}
        {/*<Link*/}
        {/*  to={`/${*/}
        {/*    type == "social"*/}
        {/*      ? PATH_DASHBOARD.your_data.your_audience.social*/}
        {/*      : PATH_DASHBOARD.your_data.your_audience.persona*/}
        {/*  }`}*/}
        {/*>*/}
        {/*  <Button variant="main" className="w-[220px] font-medium">*/}
        {/*    Add Segment*/}
        {/*  </Button>*/}
        {/*</Link>*/}
      </Box>
    </Box>
  );
};

export default EmptyData;
