import { useContext } from "react";
import { ModalContext } from "providers/Modal";
import { segmentContext } from "views/YourSegment/context/segmentContext";

import EditPersona from "components/yourAudienceAndSegment/EditPersona";
import ActionColumn from "components/yourAudienceAndSegment/ActionColumn";
import DeleteYourDataModal from "components/yourAudienceAndSegment/DeleteYourDataModal";
import DownloadYourDataModal from "components/yourAudienceAndSegment/DownloadYourDataModal";

import { PersonaSegment } from "types/Persona";
import AddYourDataModal from 'components/yourAudienceAndSegment/AddYourDataModal';

const PersonaSegmentActionCol = (props: PersonaSegment) => {
  const modal = useContext(ModalContext);
  const { deleteSegmentPersona, updateSegmentPersona } = segmentContext();

  const handleOpenModalDelete = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "max-w-[512px]",
      isOpen: true,
      isShowTitle: false,
      content: (
        <DeleteYourDataModal
          handleDelete={() => deleteSegmentPersona(props.id as any)}
          title={"Are you sure you want to delete this social segment?"}
        />
      ),
    }));
  };
  const handleDownload = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <DownloadYourDataModal id={props.id} name={props.name} segment_size={props.segment_size} />,
      className: "max-w-[680px]",
    }));
  };

  const handleOpenModalEdit = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: (
        <EditPersona
          name={props.name}
          description={props.description}
          handleEdit={(id, data) => updateSegmentPersona(id, data)}
          id={props.id as any}
        />
      ),
    }));
  };

  const handleOpenModalAdd = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "max-w-[calc(100%-32px)] md:max-w-[666px] bg-[#F0F0F0] p-4 lg:p-6",
      isOpen: true,
      isShowTitle: false,
      content: (
        <AddYourDataModal id={props.id as any} count={props.segment_size} segmentName={props.name} typeAdd={'persona'}/>
      ),
    }));
  };

  return (
    <ActionColumn
      isDelete
      isDownload={props.datatype === 'AUDIENCE'}
      isEdit
      isAddContact
      handleDownload={handleDownload}
      handleDelete={handleOpenModalDelete}
      handleEdit={handleOpenModalEdit}
      handleAdd={handleOpenModalAdd}
    />
  );
};
export default PersonaSegmentActionCol;
