import { Link } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import { YOUR_SEGMENTS } from "types/Router";
import { TooltipPortal } from '@radix-ui/react-tooltip';

const InforSocialSegment = ({ id, name }: { id: string | number; name: string }) => {
  return (
    <TooltipProvider delayDuration={1000}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="max-w-[250px] w-[250px]">
            <Link
              to={`/${YOUR_SEGMENTS.ROOT}/${YOUR_SEGMENTS.SOCIAL}/${id}`}
              className="block truncate text-primary overflow-hidden font-medium"
            >
              {name}
            </Link>
          </div>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="top" sideOffset={-60}>
            <span className="p-2">{name}</span>
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};
export default InforSocialSegment;
