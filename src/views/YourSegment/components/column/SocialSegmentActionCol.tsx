import { useContext } from 'react';
import { ModalContext } from 'providers/Modal';
import { segmentContext } from 'views/YourSegment/context/segmentContext';

import DeleteYourDataModal from 'components/yourAudienceAndSegment/DeleteYourDataModal';
import ActionColumn from 'components/yourAudienceAndSegment/ActionColumn';
import DownloadYourDataModal from 'components/yourAudienceAndSegment/DownloadYourDataModal';
import AddYourDataModal from 'components/yourAudienceAndSegment/AddYourDataModal';
import EditPersona from '../../../../components/yourAudienceAndSegment/EditPersona';
import { AudienceSegmentItem } from '../../../../types/yourSegment';

const SocialSegmentActionCol = (props: AudienceSegmentItem) => {
  const { id, segment_size, datatype, name } = props;
  const { deleteSegmentSocial, updateSocialPersona } = segmentContext();

  const modal = useContext(ModalContext);


  const handleOpenModalAdd = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "max-w-[calc(100%-32px)] md:max-w-[666px] bg-[#F0F0F0] p-4 lg:p-6",
      isOpen: true,
      isShowTitle: false,
      content: (
        <AddYourDataModal id={Number(id)} count={segment_size} segmentName={name} typeAdd={'social'} />
      ),
    }));
  };

  const handleOpenModalDelete = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "max-w-[512px]",
      isOpen: true,
      isShowTitle: false,
      content: (
        <DeleteYourDataModal
          handleDelete={() => deleteSegmentSocial(id.toString())}
          title={"Are you sure you want to delete this social segment?"}
        />
      ),
    }));
  };
  const handleDownload = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <DownloadYourDataModal id={id} name={name} segment_size={segment_size} />,
      className: "max-w-[680px]",
    }));
  };

  const handleOpenModalEdit = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: (
        <EditPersona
          name={props.name}
          description={props.description}
          handleEdit={(id, data) => updateSocialPersona(id, data)}
          id={props.id as any}
        />
      ),
    }));
  };
  return (
    <ActionColumn
      isDelete
      isEdit
      isDownload={datatype === 'AUDIENCE'}
      isAddContact
      handleDownload={handleDownload}
      handleDelete={handleOpenModalDelete}
      handleAdd={handleOpenModalAdd}
      handleEdit={handleOpenModalEdit}
    />
  );
};
export default SocialSegmentActionCol;
