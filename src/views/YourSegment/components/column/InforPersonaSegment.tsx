import { Link } from "react-router-dom";

import AvatarByName from "components/AvatarByName";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import { YOUR_SEGMENTS } from "types/Router";
import { TooltipPortal } from '@radix-ui/react-tooltip';

const InfoPersonaSegment = ({ id, name }: { id: string | number; name: string }) => {
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="max-w-[350px] w-[350px] line-clamp-1">
            <Link
              to={`/${YOUR_SEGMENTS.ROOT}/${YOUR_SEGMENTS.PERSONA}/${id}`}
              className="text-sm hover:text-primary-hover items-center gap-2 flex"
            >
              <AvatarByName name={name} position="first" />
              <span className="w-full block truncate">{name}</span>
            </Link>
          </div>
        </TooltipTrigger>
       <TooltipPortal>
         <TooltipContent side="top" sideOffset={-60}>
           <span className="p-2">{name}</span>
         </TooltipContent>
       </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};

export default InfoPersonaSegment;
