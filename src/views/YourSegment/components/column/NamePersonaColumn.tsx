import AvatarByName from "components/AvatarByName";
import { Link } from "react-router-dom";
import { YOUR_AUDIENCES } from "types/Router";

const NamePersonaColumn = ({ name, id }: { name: string; id: string | number }) => {
  return (
    <div className="flex items-center gap-2 max-w-[300px] w-[300px]">
      <AvatarByName className="w-6 h-6" name={name} />
      <Link
        to={`/${YOUR_AUDIENCES.ROOT}/${YOUR_AUDIENCES.PERSONA}/${id}`}
        className="truncate hover:text-primary-hover block"
      >
        {name}
      </Link>
    </div>
  );
};
export default NamePersonaColumn;
