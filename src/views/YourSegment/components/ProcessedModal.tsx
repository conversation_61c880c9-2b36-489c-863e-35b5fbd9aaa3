import AvatarByName from "components/AvatarByName";
import { FlatTreeNode } from "types/YourData";
import GroupAvatar from "components/GroupAvatar";
import TypeProcess from "../../YourSegment/components/Tree/TypeProcess";
import useAvatar from "hooks/useAvatar";
import useListAvatar from "hooks/useListAvatar";
import { TYPE_SOCIAL_IMAGE } from 'constants/requestAudience';

type Props = {
  flatTree: FlatTreeNode[];
};

const ProcessedModal = ({ flatTree }: Props) => {
  const leftTree = flatTree.filter(
    (node) => node.position.split("/")[1] === "left" && Boolean(node.audience)
  );
  const rightTre = flatTree.filter(
    (node) => node.position.split("/")[1] === "right" && Boolean(node.audience)
  );
  if (flatTree.length === 1) return <DrawNode {...flatTree[0]} />;
  return (
    <div className="flex items-center flex-col gap-2 p-2">
      <div className="flex items-center gap-6">
        <div className="flex flex-col gap-2">
          {leftTree.length > 0 && <Content nodes={leftTree} />}
        </div>
        {flatTree.length > 0 && <TypeProcess processType={flatTree[0].op!} />}
        <div className="flex flex-col gap-2">
          {rightTre.length > 0 && <Content nodes={rightTre} />}
        </div>
      </div>
    </div>
  );
};

export default ProcessedModal;

const DrawNode = (node: FlatTreeNode) => {
  const { avatar } = useAvatar({
    type: node.audience?.type ? (TYPE_SOCIAL_IMAGE[node.audience?.type] as any) : "group",
    uid: node.audience?.id.toString()!,
  });
  return (
    <div className="flex items-center gap-1">
      <AvatarByName name={node.audience?.name!} className="w-5 h-5" urlImage={avatar.url} />
      <div className="flex-1 text-[10px] font-medium max-w-20 line-clamp-1 overflow-hidden">
        {node.audience?.name}
      </div>
    </div>
  );
};
type PropsGroups = {
  nodes: FlatTreeNode[];
};
const Content = ({ nodes }: PropsGroups) => {
  return (
    <div className="flex items-center text-[10px] gap-2">
      <RenderGroupTree nodes={nodes} />
    </div>
  );
};
const RenderGroupTree = ({ nodes }: PropsGroups) => {
  const { listAvatar } = useListAvatar({
    type: "group",
    uid: nodes.map((node) => node.audience?.id.toString()!),
  });

  return <GroupAvatar itemShow={2} size={30} imgUrls={listAvatar} type={"horizontal"} />;
};
