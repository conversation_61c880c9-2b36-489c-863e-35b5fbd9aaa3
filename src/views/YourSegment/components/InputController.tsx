import { Controller } from "react-hook-form";

type Props = {
  control: any;
  disabled?: boolean;
  readOnly?: boolean;
};

const InputController: React.FC<Props> = ({ control, disabled = false, readOnly = false }) => {
  return (
    <Controller
      name="email"
      control={control}
      render={({ field }) => (
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-primary">Location</span>
          <input
            className="border outline-none rounded-md px-2 text-[#0D112666] py-1.5 text-sm font-medium"
            {...field}
            disabled={disabled}
            readOnly={readOnly}
          />
        </div>
      )}
    />
  );
};
export default InputController;
