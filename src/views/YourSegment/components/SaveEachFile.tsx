import { Controller } from "react-hook-form";

type Props = {
  control: any;
};

const SaveEachFile: React.FC<Props> = ({ control }) => {
  return (
    <Controller
      name="saveEachFile"
      control={control}
      render={({ field }) => (
        <div className="flex items-center justify-between border rounded-md px-2 py-1.5 w-full">
          <label className="font-medium text-sm" htmlFor="saveEachFile">
            Ask where to save each file before downloading
          </label>
          <input
            {...field}
            className="peer relative appearance-none w-4 h-4 rounded-full border-[#0F132499] border-[2px] cursor-pointer checked:bg-[#924FE8]"
            type="checkbox"
            name="saveEachFile"
            id="saveEachFile"
          />
        </div>
      )}
    />
  );
};
export default SaveEachFile;
