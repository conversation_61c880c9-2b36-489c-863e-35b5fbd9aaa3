import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON>lt<PERSON><PERSON>rovider, TooltipTrigger } from "components/ui/tooltip";

import AvatarByName from "components/AvatarByName";
import GroupAvatar from "components/GroupAvatar";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import useListAvatar from "hooks/useListAvatar";
import { useProcessedSource } from "hooks/useProcessed";
import ProcessedModal from "./ProcessedModal";
import { TYPE_SOCIAL_IMAGE } from 'constants/requestAudience';

const GroupAvatarColumn = ({ id, name }: { id: string; name: string }) => {
  const { flatTree } = useProcessedSource({
    id: id,
  });
  const { listAvatar } = useListAvatar({
    type: TYPE_SOCIAL_IMAGE[
      flatTree.map((node) => (node.audience?.type ? node.audience?.type : 1))[0]
    ] as any,
    uid: flatTree
      .map((node) => node.audience?.id && node.audience?.id.toString())
      .filter((item) => Boolean(item)) as string[],
  });
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild className="w-full text-center justify-center">
          <div className="w-fit pr-2 flex items-start">
            {listAvatar.length > 1 ? (
              <GroupAvatar
                imgUrls={listAvatar.filter((item) => Boolean(item))}
                type={"horizontal"}
              />
            ) : (
              <AvatarByName name={name} urlImage={listAvatar[0]} />
            )}
          </div>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent>
            <div className="bg-white ">
              <ProcessedModal flatTree={flatTree} />
            </div>
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};
export default GroupAvatarColumn;
