import { useNavigate, useParams } from "react-router-dom";
import { useContext, useEffect, useState } from 'react';

import AvatarByName from "components/AvatarByName";
import BreadCrumb from "components/Breadcrumb";
import { Box } from "components/Box";
import HeaderDetailModal from "components/HeaderDetailModal";
import ShowSource from "views/Persona/components/ShowSource";
import TablePreview from "views/SocialDataDetail/components/TablePreview";
import AudienceFilterOptions from "views/SocialDataDetail/components/AudienceFilter/AudienceFilterOptions";

import useFilters from "hooks/useFilters";
import useListAvatar from "hooks/useListAvatar";
import { useProcessedSource } from "hooks/useProcessed";
import { yourSegmentApi } from "apis/yourSegment";
import { TDataPreview, TDataSummarize, TSocialSegment } from "types/SocialData";
import { getUnreadType } from '../../store/redux/notification/slice';
import { useAppDispatch, useAppSelector } from '../../store';
import { notificationApi } from '../../apis/notification';
import { RiAddLine, RiDownload2Line } from '@remixicon/react';
import LABEL from '../../constants/label';
import { Button } from '../../components/ui/button';
import DownloadYourDataModal from '../../components/yourAudienceAndSegment/DownloadYourDataModal';
import { ModalContext } from '../../providers/Modal';
import AddYourDataModal from '../../components/yourAudienceAndSegment/AddYourDataModal';

const SocialSegment = () => {
  const { id = "" } = useParams();
  const { params, setSearchParams } = useFilters();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const modal = useContext(ModalContext);

  const { unreadByType } = useAppSelector((state) => state.notification);

  const [summarize, setSummarize] = useState<TDataSummarize<TSocialSegment>>({
    data: {} as any,
    loading: false,
  });
  const [preview, setPreview] = useState<TDataPreview>({
    data: [],
    total: 0,
    loading: false,
  });

  const { flatTree, buildTree } = useProcessedSource({ id: summarize.data?.id || "" });

  const { listAvatar } = useListAvatar({
    type: "group",
    uid: flatTree
      .map((node) => node.audience?.id && node.audience?.id.toString())
      .filter((item) => Boolean(item)) as string[],
  });

  const handleViewDetail = async () => {
    const findNoti = unreadByType?.find((item) => item.ref_id === id);
    if (!findNoti) {
      return;
    }
    return await notificationApi.handleMarkAsRead(findNoti?.id);
  };

  useEffect(() => {
    getSummarize();
    dispatch(getUnreadType({
      type: 'your_segment_social'
    }));
    handleViewDetail();
  }, [id]);

  useEffect(() => {
    setSearchParams(buildTree.filter_params as any);
  }, [buildTree.filter_params]);

  useEffect(() => {
    if (id !== "") {
      getPreview();
    }
  }, [params.page, id]);

  const getPreview = async () => {
    setPreview((prev) => ({ ...prev, loading: true }));
    const response = await yourSegmentApi.getSegmentListUsers(id, Number(params.page ?? 1), 10);
    if (response) {
      setPreview((prev) => ({
        ...prev,
        data: response.items,
        total: response.count,
        loading: false,
      }));
    }
    setPreview((prev) => ({ ...prev, loading: false }));
  };

  const getSummarize = async () => {
    setSummarize((prev) => ({ ...prev, loading: true }));
    const response = await yourSegmentApi.getSummarizeDetail(id);

    if (response && response.data) {
      setSummarize((prev) => ({ ...prev, data: response.data, loading: false }));
    } else {
      navigate("/404");
    }
    setSummarize((prev) => ({ ...prev, loading: false }));
  };

  const handleDownload = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <DownloadYourDataModal id={summarize.data?.id} name={summarize.data?.name} segment_size={summarize.data?.segment_size} />,
      className: "max-w-[680px]",
    }));
  };

  const handleOpenModalAdd = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "max-w-[calc(100%-32px)] md:max-w-[666px] bg-[#F0F0F0] p-4 lg:p-6",
      isOpen: true,
      isShowTitle: false,
      content: (
        <AddYourDataModal id={Number(id)} count={summarize.data?.segment_size} segmentName={summarize.data.name} typeAdd={'social'} />
      ),
    }));
  };

  return (
    <>
      <BreadCrumb path={summarize.data?.name} loading={summarize.loading} />
      <HeaderDetailModal
        name={summarize.data?.name || ""}
        loading={summarize.loading}
        avatarUrl={listAvatar.filter((item) => Boolean(item))}
        size={summarize.data?.segment_size}
        description={summarize.data?.description}
        uid={summarize.data?.fb_uid}
        packageValue={summarize.data?.package || ""}
        classRightContent={'max-md:w-full'}
        contentRight={<>
          <Button
            className="max-md:w-full bg-primary hover:opacity-50 hover:bg-primary flex gap-1"
            onClick={() => handleOpenModalAdd()}
          >
            <RiAddLine size={14} />
            Add To ...
          </Button>
          {summarize.data.datatype !== 'DATASET' && <Button
            onClick={() => handleDownload()}
            className="max-md:w-full bg-primary hover:opacity-50 hover:bg-primary flex gap-1"
          >
            <RiDownload2Line size={14} />
            {LABEL.download}
          </Button>}
        </>}
      />
      <AudienceFilterOptions
        isFilterCity
        isRelationship
        isGender
        isFilterSocialAge
        isFilterDOB
        isInterest
        AudienceID={buildTree.audience?.id}
        summarize={summarize.data.summarize}
        isDisable
        loading={summarize.loading}
      />
      {buildTree.children?.length === 0 ? (
        <Box className="gap-1 text-sm font-medium justify-start">
          <span children="Source:" />
          <AvatarByName
            name={summarize.data.name || ""}
            className="w-6 h-6"
            urlImage={listAvatar[0]}
          />
          <span>{buildTree.audience?.name!}</span>
        </Box>
      ) : (
        <ShowSource
          id={summarize.data?.id}
          urls={listAvatar}
          tree={buildTree}
          name={summarize.data?.name || ""}
          rootId={summarize.data?.latest_audience?.toString()}
        />
      )}
      {preview && <TablePreview data={preview} isDataset={summarize?.data?.datatype === 'DATASET'} />}
    </>
  );
};
export default SocialSegment;
