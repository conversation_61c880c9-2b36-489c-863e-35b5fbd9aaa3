import { persona<PERSON><PERSON> } from "apis/persona";
import { socialAPI } from "apis/socialData";

import { toast } from "components/ui/use-toast";
import { createContext, Dispatch, ReactNode, SetStateAction, useContext, useState } from "react";
import { PersonaSegment } from "types/Persona";
import { CreateSegmentFormType } from "types/YourData";
import { AudienceSegmentItem } from "types/yourSegment";

type SegmentProps<T> = {
  count: number;
  items: T[];
  loading: boolean;
};

const DEFAULT_STATE = {
  count: 0,
  items: [],
  loading: false,
};

export interface SegmentContextProps {
  socialSegment: SegmentProps<AudienceSegmentItem>;
  personaSegment: SegmentProps<PersonaSegment>;
  setSocialSegment: Dispatch<SetStateAction<SegmentProps<AudienceSegmentItem>>>;
  setPersonaSegment: Dispatch<SetStateAction<SegmentProps<PersonaSegment>>>;
  updateSegmentPersona: (id: string, body: CreateSegmentFormType) => void;
  updateSocialPersona: (id: string, body: CreateSegmentFormType) => void;
  deleteSegmentSocial: (id: string) => void;
  deleteSegmentPersona: (id: string) => void;
}

const SegmentContext = createContext<SegmentContextProps>({
  socialSegment: DEFAULT_STATE,
  personaSegment: DEFAULT_STATE,
  setSocialSegment: () => {},
  setPersonaSegment: () => {},
  updateSegmentPersona: () => {},
  updateSocialPersona: ()=>{},
  deleteSegmentSocial: () => {},
  deleteSegmentPersona: () => {},
});

const SegmentProvider = ({ children }: { children: ReactNode }) => {
  const [socialSegment, setSocialSegment] =
    useState<SegmentProps<AudienceSegmentItem>>(DEFAULT_STATE);
  const [personaSegment, setPersonaSegment] = useState<SegmentProps<PersonaSegment>>(DEFAULT_STATE);

  const updateSegmentPersona = async (id: string, body: CreateSegmentFormType) => {
    try {
      const response = await personaAPI.updateSegment(id, body);
      setPersonaSegment((prev) => ({
        ...prev,
        items: prev.items.map((item) => (item.id === +id ? response : item)),
      }));
      toast({
        status: "success",
        description: "Update segment success",
      });
    } catch (error: any) {
      toast({
        title: String(error),
        status: "error",
        duration: 3000,
      });
    }
  };

  const updateSocialPersona = async (id: string, body: CreateSegmentFormType) => {
    try {
      const response = await socialAPI.updateSocialSegment(id, body);
      setSocialSegment((prev) => ({
        ...prev,
        items: prev.items.map((item) => (item.id === +id ? response : item)),
      }));
      toast({
        status: "success",
        description: "Update segment success",
      });
    } catch (error: any) {
      toast({
        title: String(error),
        status: "error",
        duration: 3000,
      });
    }
  };

  const deleteSegmentPersona = async (id: string) => {
    try {
      await personaAPI.deleteSegment(id);
      setPersonaSegment((prev) => ({
        ...prev,
        items: prev.items.filter((item) => item.id !== +id),
      }));
      toast({
        status: "success",
        description: "Delete segment success",
      });
    } catch (error: any) {
      toast({
        title: String(error.detail),
        status: "error",
        duration: 3000,
      });
    }
  };
  const deleteSegmentSocial = async (id: string) => {
    try {
      await socialAPI.deleteSegment(id);
      setSocialSegment((prev) => ({
        ...prev,
        items: prev.items.filter((item) => item.id !== +id),
      }));
      toast({
        status: "success",
        description: "Delete segment success",
      });
    } catch (error: any) {
      toast({
        title: String(error.detail),
        status: "error",
        duration: 3000,
      });
    }
  };
  return (
    <SegmentContext.Provider
      value={{
        setSocialSegment,
        setPersonaSegment,
        socialSegment,
        personaSegment,
        updateSegmentPersona,
        updateSocialPersona,
        deleteSegmentSocial,
        deleteSegmentPersona,
      }}
    >
      {children}
    </SegmentContext.Provider>
  );
};

const segmentContext = (): SegmentContextProps => useContext(SegmentContext);
export { SegmentProvider, segmentContext };
