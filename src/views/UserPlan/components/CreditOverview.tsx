import { useContext, useEffect, useMemo, useState } from "react";
import { useAppSelector } from "store";
import { useNavigate } from "react-router-dom";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import CreditCard from "./CreditCard";
import CreditModal from "components/Transaction/CreditModal";

import { creditAPI } from "apis/credit";
import { PATH_DASHBOARD } from "types/path";
import { CREDIT_LABEL } from "constants/PlanPayment/label";
import useResponsive from "../../../hooks/useResponsive.tsx";
import { cn } from "../../../utils/utils.ts";

interface CreditUsedProps {
  loading: boolean;
  num: number;
}

const CreditOverview = () => {
  const { user } = useAppSelector((state) => state.auth);
  const { isTablet } = useResponsive();
  const modal = useContext(ModalContext);
  const navigate = useNavigate();
  const [creditUsed, setCreditUsed] = useState<CreditUsedProps>({
    loading: false,
    num: 0
  });

  useEffect(() => {
    getSummarizeCredit();
  }, []);

  const getSummarizeCredit = async () => {
    setCreditUsed((prev) => ( { ...prev, loading: true } ));
    const response = await creditAPI.getSummarize();
    if (response && response.data) {
      setCreditUsed((prev) => ( { ...prev, num: Math.abs(response.data.credit_used) } ));
    }
    setCreditUsed((prev) => ( { ...prev, loading: false } ));
  };

  const handleOpenBuyCreditModal = () => {
    modal?.setDataDialog((prev) => ( {
      ...prev,
      isOpen: true,
      isShowTitle: true,
      title: "Customize Credits",
      content: <CreditModal
        classContainer={"mt-2 pl-3 pr-2 -ml-3 -mr-3 w-[calc(100vw-30px)] overflow-auto max-h-[500px] lg:gap-4 lg:mt-4 lg:pl-0 lg:pr-0 lg:ml-0 lg:mr-0 lg:w-full lg:max-h-auto lg:overflow-visible"}
      />,
      className: "!max-w-[calc(100%-24px)] p-3 lg:p-6 lg:!max-w-[1110px] bg-custom-secondary block lg:gird",
      footer: ""
    } ));
  };

  const groupButton = useMemo(() => (
    <div className={cn(isTablet ? "flex gap-2 mt-3" : "")}>
      <Button
        variant="secondary"
        onClick={() => navigate(`/${PATH_DASHBOARD.user.transaction}`)}
        className={cn("text-custom-tertiary text-sm md:text-md rounded-xl px-3 md:px-4 flex-1", isTablet ?
          "flex-1 w-full" :
          "mr-2")}
      >
        View Credit History
      </Button>
      <Button
        className={cn("rounded-xl text-sm md:text-md font-medium w-[133px]", isTablet && "flex-1")}
        onClick={handleOpenBuyCreditModal}
      >
        Buy More
      </Button>
    </div>
  ), [handleOpenBuyCreditModal]);

  return (
    <div className="p-3 md:p-6 border bg-custom-primary border-custom-primary rounded-2xl w-full">
      <Box className="w-full">
        <Box variant="col-start" className="flex-1 gap-y-2">
          <div className="capitalize text-[16px] md:text-[20px] text-primary font-semibold ">Credits Usage</div>
        </Box>
        {!isTablet && groupButton}
      </Box>
      {!creditUsed.loading && (
        <div className="grid grid-cols-2 gap-4 my-6">
          <CreditCard title="Remaining Credit" count={user?.credit} isActive />
          <CreditCard title="Credit used" count={creditUsed.num} />
        </div>
      )}
      {creditUsed.loading && (
        <Box className="w-full justify-center my-6">
          <LoadingButtonIcon />
        </Box>
      )}

      <div className="text-secondary text-sm">{CREDIT_LABEL.notice}</div>
      {isTablet && groupButton}
    </div>
  );
};

export default CreditOverview;
