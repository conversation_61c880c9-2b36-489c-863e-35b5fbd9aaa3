import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from "components/ui/table";
import { USER_PROFILE_LABEL } from "constants/UserProfile/label";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { subscriptionStore } from "store/redux/subscription/slice";
import { getPublishDate } from "utils/CrawlService";
import useResponsive from "hooks/useResponsive.tsx";

const PlanOverview = () => {
  const { last_sub } = useSelector(subscriptionStore);
  const { isMobile } = useResponsive();
  const planName = last_sub?.plan_code.toLowerCase();
  return (
    <div className="p-3 md:p-6 border bg-custom-primary border-custom-primary rounded-2xl w-full">
      <Box className="w-full">
        <div className="flex-1">
          <div className="capitalize text-[16px] md:text-[20px] text-primary font-semibold ">{planName} Plan</div>
          <div className="text-secondary text-sm">
            You are on a <strong>{planName}</strong> plan and your credits will refresh on
            <span className="m-1">
              {getPublishDate({ date: last_sub?.end_date || "", monthShort: true }).publishDate}
            </span>
          </div>
        </div>
        {!isMobile && <Link to={"/plan/upgrade"}>
          <Button
            variant="default"
            className="rounded-xl font-medium text-md"
            children={USER_PROFILE_LABEL.BILLING.change_plan}
          />
        </Link>}

      </Box>
      <div className="bg-custom-secondary p-3 md:p-6 mt-6 rounded-2xl">
        <Box className="w-full font-medium text-secondary text-sm">
          <div>What’s included</div>
          <div>Price</div>
        </Box>
        <Table>
          <TableHeader className="bg-secondary text-secondary font-medium ">
            <TableRow>
              <TableHead className="p-0 w-[154px] sm:w-1/3">User</TableHead>
              <TableHead className="text-center w-[70px] px-1 sm:px-4 sm:w-auto capitalize">{planName} User</TableHead>
              <TableHead className="text-right w-fit sm:w-auto p-0">${last_sub?.plan_price}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="w-full">
            <TableRow className="text-secondary">
              <TableCell className="p-0 w-[154px]">Credits (in your plan)</TableCell>
              <TableCell className="text-left sm:text-center px-1 sm:px-4">{last_sub?.plan_credit}</TableCell>
            </TableRow>
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell className="pt-4 pl-0 pb-0" colSpan={2}>
                Monthly Total
              </TableCell>
              <TableCell className="text-right pr-0 pt-4 pb-0">
                ${last_sub?.plan_price}/mo
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </div>
      {isMobile && <Link to={"/plan/upgrade"}>
        <Button
          variant="default"
          className="rounded-xl w-full font-medium text-md mt-4"
          children={USER_PROFILE_LABEL.BILLING.change_plan}
        />
      </Link>}
    </div>
  );
};

export default PlanOverview;
