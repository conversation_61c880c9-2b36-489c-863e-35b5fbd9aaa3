import { Box } from "components/Box";
import { fNumberToString } from "utils/number";
import { cn } from "utils/utils";

interface Props {
  title: string;
  count: number;
  isActive?: boolean;
  className?: string;
  classNameTitle?: string;
  classNameCredit?: string;
}

const CreditCard = ({ title, count, isActive, className, classNameTitle, classNameCredit }: Props) => {

  return (
    <Box
      variant="col-start"
      className={cn(
        "p-2 lg:p-4 rounded-2xl w-full gap-2 justify-center items-center",
        isActive ? "bg-brand-subtitle" : "bg-custom-secondary",
        className
      )}
    >
      <div
        className={cn(
          "text-primary text-xs xl:text-sm font-medium",
          isActive ? "text-primary" : "text-secondary",
          classNameTitle
        )}
        children={title}
      />
      <div
        className={cn(
          "text-sm lg:text-[24px] xl:text-[36px] font-semibold xl:leading-[44px] truncate w-full text-center",
          isActive ? "text-brand-strong" : "text-primary",
          classNameCredit
        )}
        children={fNumberToString(count)}
      />
    </Box>
  );
};

export default CreditCard;
