import Breadcrumb from "components/Breadcrumb";
import Container from "./components/Container";
import { EnrichmentProvider } from "./Context/enrichmentContext";
import HeaderWrapper from "components/Header";
import { ENRICHMENT_LABEL } from "constants/Enrichment/label";

const EnrichmentView = () => {
  return (
    <EnrichmentProvider>
      <>
        <Breadcrumb />
        <HeaderWrapper
          leftChildren={{
            title: ENRICHMENT_LABEL.title,
            subTitle: ENRICHMENT_LABEL.sub,
          }}
        />
        <Container />
      </>
    </EnrichmentProvider>
  );
};

export default EnrichmentView;
