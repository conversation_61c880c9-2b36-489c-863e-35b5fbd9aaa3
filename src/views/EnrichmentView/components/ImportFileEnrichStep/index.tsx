import { useEffect, useRef } from "react";
import { RiCloseLine, RiUploadCloud2Line } from "@remixicon/react";

import { Card, CardContent, CardTitle } from "components/ui/card";
import { Box } from "components/Box";
import { CARD_CLASS } from "constants/index";
import ImportAction from "./ImportAction";
import { enrichmentContext } from "views/EnrichmentView/Context/enrichmentContext";
import { toast } from "components/ui/use-toast";

import { cn } from "utils/utils";
import CsvIcon from "assets/icons/CsvIcon";
import { convertFileSize } from "utils/enrichment";
import { Button } from "components/ui/button";

interface Props {
  className?: string;
  file: File | undefined;
  setFile: React.Dispatch<React.SetStateAction<File | undefined>>;
  onUpload: () => void;
}

const ImportFileEnrichStep = ({ className, file, setFile, onUpload }: Props) => {
  const { requestTable } = enrichmentContext();
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleFileSizeCheck = (selectedFile: File) => {
    if (selectedFile.size > 25 * 1024 * 1024) {
      toast({
        description: "File size should be less than 25MB",
        status: "error",
      });
      setFile(undefined);
      return false;
    }
    return true;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const selectedFile = event.target.files[0];
      const isValidFile = handleFileSizeCheck(selectedFile);
      if (isValidFile) {
        setFile(selectedFile);
      }
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (event.dataTransfer.files) {
      const droppedFile = event.dataTransfer.files[0];
      const isValidFile = handleFileSizeCheck(droppedFile);
      if (isValidFile) {
        setFile(droppedFile);
      }
    }
  };

  const handleRemoveFile = () => {
    setFile(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  useEffect(() => {
    setFile(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [requestTable.table.items, setFile]);

  return (
    <Card className={cn(CARD_CLASS, "px-3 py-4 md:p-6 md:pt-[22px] mt-3 md:mt-6")}>
      <CardTitle className="text-base text-primary font-medium">Upload Files</CardTitle>
      <CardContent className="p-0">
        <Box variant={"col-center"} className="mt-[16px] gap-4">
          <section
            className={cn(
              "border border-dashed rounded-2xl w-full h-[170px] bg-[#F0F0F0]",
              className
            )}
          >
            <div
              className="text-primary px-0 py-[22px] h-full"
              onDrop={handleDrop}
              onDragOver={(event) => event.preventDefault()}
            >
              <Box variant="col-center" className="justify-center items-center h-full gap-2">
                <RiUploadCloud2Line size={68} color={"#515667"} />
                <div className="text-center">
                  <span>&nbsp;Drop your file here or</span>
                  <label
                    htmlFor="browse"
                    className="ml-1 underline cursor-pointer text-[#8F5CFF]"
                    children="browse"
                  />
                  <span>&nbsp;from your computer</span>
                </div>

                <p className="text-center text-secondary text-xs">Maximum file size 25MB</p>
              </Box>
              <input
                id="browse"
                type="file"
                onChange={handleFileChange}
                accept=".csv, text/csv, application/vnd.ms-excel"
                hidden
                ref={fileInputRef}
              />
            </div>
          </section>

          {file && (
            <div className="flex items-center justify-between p-2 rounded-md w-full bg-[#F0F0F0]">
              <div className="flex items-center gap-1">
                <CsvIcon size={40} />
                <div className="flex flex-col items-start gap-0.5">
                  <span className="text-center text-sm text-primary md:text-base font-medium">
                    {file.name}
                  </span>
                  <span className="text-secondary text-xs font-medium text-end">
                    {convertFileSize(file.size)}
                  </span>
                </div>
              </div>
              <Button
                onClick={handleRemoveFile}
                className="text-primary"
                size={"icon"}
                variant={"ghost"}
              >
                <RiCloseLine size={20} />
              </Button>
            </div>
          )}
          <ImportAction file={file} onUpload={onUpload} />
        </Box>
      </CardContent>
    </Card>
  );
};

export default ImportFileEnrichStep;
