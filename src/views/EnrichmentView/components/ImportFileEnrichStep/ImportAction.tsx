import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { checkIfExcelFile } from "utils/enrichment";
import { toast } from "components/ui/use-toast";

type Props = {
  file: File | undefined;
  onUpload: () => void;
};

const ImportAction = ({ file, onUpload }: Props) => {
  const handleUpLoad = () => {
    const isExcelFile = checkIfExcelFile(file!);
    if (!isExcelFile) {
      toast({
        title: "Invalid File",
        description: "Please upload a valid excel file with .csv extension",
        status: "error",
      });
      return;
    }
    onUpload();
  };

  const downloadTemplate = () => {
    const FileTemplate = "/templates/enrichment-template.csv";
    const downloadFile = (fileUrl: string, fileName: string) => {
      const alink = document.createElement("a");
      alink.href = fileUrl;
      alink.download = fileName;
      document.body.appendChild(alink);
      if (alink.click) alink.click();
      document.body.removeChild(alink);
    };
    downloadFile(FileTemplate, "EnrichmentTemplate.csv");
  };

  return (
    <Box className="w-full gap-3 grid grid-cols-1 sm:grid-cols-2">
      <Button
        onClick={handleUpLoad}
        variant="main"
        className="w-full h-[44px]"
        children=" Upload"
        disabled={!file}
      />
      <Button
        onClick={downloadTemplate}
        variant="tertiary"
        className="w-full h-[44px]"
        children="Download Template"
      />
    </Box>
  );
};

export default ImportAction;
