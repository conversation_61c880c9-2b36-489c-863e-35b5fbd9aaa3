import { RiLoader2Fill } from "@remixicon/react";
import { TypeTable } from "types/Enrichment";

import { charArray } from "constants/Enrichment/label";
import OverViewRow from "./OverViewRow";

import { cn } from "utils/utils";

const TableEnrich = ({
  body = [],
  loading,
  children,
  colWidth = "200",
  height = "250",
  chartData,
  totalRecords,
  defaultCol = 10,
}: TypeTable) => {
  const DEFAULT_COL = chartData ? defaultCol + 7 : defaultCol;
  const newHeader = Object.keys(body[0] || {});
  const emptyArray = Array.from({ length: charArray.length - newHeader.length }, () => "");

  return (
    <div
      className={cn(
        "relative flex rounded-[2px] font-medium shadow-sm  border-gray-400 w-full text-xs overflow-hidden border"
      )}
      style={{ height: `${height}px` }}
    >
      {loading && (
        <div className="absolute z-[9999] w-full h-full flex items-center justify-center bg-black/40">
          <RiLoader2Fill className="animate-spin" />
        </div>
      )}
      <div className="w-full h-full overflow-scroll scroll-bottom flex">
        <div className="sticky left-0 border-gray-400 z-30">
          {Array.from({
            length: (body.length > DEFAULT_COL ? body.length : DEFAULT_COL) + 2,
          }).map((_, index) => (
            <div
              key={index}
              className="p-1 bg-white px-4 border-b border-gray-400 border-r first:text-transparent"
            >
              {index}
            </div>
          ))}
        </div>
        <table className="min-w-full table-fixed">
          <thead className={cn("sticky -top-[0.5px] bg-white z-20")}>
            <tr className={cn("flex")}>
              {charArray.map((item, index) => (
                <th
                  key={index}
                  scope="col"
                  className={cn(
                    "p-1 bg-white text-xs text-left border-b border-l last:border-r border-gray-400 font-normal first:border-l-0"
                  )}
                  style={{ width: `${colWidth}px` }}
                >
                  {item}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {chartData && totalRecords !== 0 && (
              <OverViewRow
                colWidth={colWidth}
                totalRecords={totalRecords!}
                chartData={chartData}
                emptyArray={emptyArray}
              />
            )}
            <tr
              className={cn("hover:bg-gray-50 flex border-b last:border-0", children && "border-t")}
            >
              {[...newHeader, ...emptyArray].map((cell, cellIndex) => (
                <td
                  key={cellIndex}
                  className={cn(
                    "whitespace-nowrap last:border-r font-bold h-[26px] overflow-hidden p-1 capitalize border-l first:border-0"
                  )}
                  style={{ width: `${colWidth}px` }}
                >
                  <div className="truncate">{cell === "phone" ? "* phone" : cell}</div>
                </td>
              ))}
            </tr>
            {body.map((row, rowIndex) => {
              return (
                <tr key={rowIndex} className="hover:bg-gray-50 flex border-b">
                  {Object.keys(row)
                    .concat(emptyArray)
                    .map((key, cellIndex) => (
                      <td
                        key={cellIndex}
                        className={cn(
                          "whitespace-nowrap p-1 h-[26px] border-l last:border-r  first:border-0"
                        )}
                        style={{ width: `${colWidth}px` }}
                      >
                        <div className="truncate">
                          {key && row ? row[key as keyof typeof row] : ""}
                        </div>
                      </td>
                    ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TableEnrich;
