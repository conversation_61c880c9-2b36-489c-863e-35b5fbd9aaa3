import { Summarize<PERSON><PERSON> } from "types/Enrichment";

import EnrichmentChart from "../EnrichDetail/EnrichmentChart";

import { cn } from "utils/utils";

type Props = {
  colWidth: string;
  totalRecords: number;
  chartData: SummarizeField[];
  emptyArray: string[];
};

const OverViewRow = ({ chartData, colWidth, totalRecords, emptyArray }: Props) => {
  return (
    <tr className={cn("flex last:border-0")}>
      <td
        className={cn(
          "whitespace-nowrap flex flex-col font-bold justify-center items-center border-b border-r h-[189px]"
        )}
        style={{ width: `${colWidth}px` }}
      >
        <span className="text-sm">Total records</span>
        <span className="text-md text-blue-500">{totalRecords}</span>
      </td>
      {chartData.map((item, index) => (
        <td
          key={index}
          className={cn("whitespace-nowrap h-[189px] border-b")}
          style={{ width: `${colWidth}px` }}
        >
          <EnrichmentChart totalRecords={totalRecords || 0} data={item} />
        </td>
      ))}
      {emptyArray.map((row, rowIndex) => (
        <td
          key={rowIndex}
          className={cn("whitespace-nowrap border-b border-l last:border-r h-[189px]")}
          style={{ width: `${Number(colWidth)}px` }}
        >
          {row}
        </td>
      ))}
    </tr>
  );
};
export default OverViewRow;
