import { useState } from "react";

import { toast } from "components/ui/use-toast";
import ImportFileEnrichStep from "./ImportFileEnrichStep";
import EnrichmentPreview from "./EnrichmentPreview";

import { readFile } from "utils/xlsxFile";
import EnrichmentPreviewTable from "./EnrichmentPreviewTable";

const Container = () => {
  const [file, setFile] = useState<File>();
  const [isOpen, setIsOpen] = useState(false);

  const handleUpLoad = () => {
    readFile(file!, (data) => {
      if (data.length > 10000) {
        toast({
          title: "Invalid File",
          description: "Please upload a file with less than 10000 rows",
          status: "error",
        });
        return;
      } else if (!data[0].includes("phone")) {
        toast({
          title: "Invalid File",
          description: "Please upload a file with phone column",
          status: "error",
        });
        return;
      } else {
        setIsOpen(true);
      }
    });
  };
  return (
    <>
      <ImportFileEnrichStep file={file} setFile={setFile} onUpload={handleUpLoad} />
      <EnrichmentPreviewTable />
      <EnrichmentPreview openModal={isOpen} file={file} onCloseModal={() => setIsOpen(false)} />
    </>
  );
};

export default Container;
