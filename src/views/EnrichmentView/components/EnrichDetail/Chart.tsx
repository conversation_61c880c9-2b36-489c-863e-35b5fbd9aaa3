import { Chart, ArcElement, <PERSON><PERSON><PERSON>, Legend, Title } from "chart.js";
import { Doughnut } from "react-chartjs-2";

Chart.register(ArcElement, Tooltip, Legend, Title);

type Props = {
  values: number[];
  total: number;
  label: string[];
};

const EnrichChart = ({ values, label, total }: Props) => (
  <Doughnut
    className="!h-auto !w-full !m-auto"
    data={{
      labels: label,
      datasets: [
        {
          data: values,
          backgroundColor: [
            "hsla(266, 77%, 61%, 1)",
            "hsla(265, 77%, 72%, 1)",
            "hsla(29, 90%, 57%, 1)",
            "hsla(224, 8%, 65%, 1)",
          ],
        },
      ],
    }}
    options={{
      layout: {
        padding: 20,
      },
      elements: {
        arc: {
          borderWidth: 0,
        },
      },
      radius: "100%",
      cutout: "70%",
      responsive: true,
      plugins: {
        legend: {
          display: false,
        },
      },
    }}
    plugins={[
      {
        id: "afterDatasetsDraw",
        beforeDatasetsDraw(chart) {
          const { ctx } = chart;
          const xCoor = chart.getDatasetMeta(0).data[0].x;
          const yCoor = chart.getDatasetMeta(0).data[0].y;
          ctx.font = `600 12px Inter`;
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";
          ctx.fillStyle = "#14151A";
          ctx.fillText(
            Number(((values[1] / total) * 100).toFixed(0)).toString() + "%",
            xCoor,
            yCoor
          );
          ctx.restore();
        },
      },
    ]}
  />
);
export default EnrichChart;
