import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "components/ui/dialog";
import { Box } from "components/Box";
import CsvIcon from "assets/icons/CsvIcon";
import { But<PERSON> } from "components/ui/button";
import { enrichmentContext } from "views/EnrichmentView/Context/enrichmentContext";
import EnrichmentPreview from "./EnrichmentPreview";
import React, { useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../../store";
import { setIsViewEnrich } from "../../../../store/redux/notification/slice";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../../../components/ui/tooltip";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { tracking } from '../../../../utils/Tracking/tracking';

type Props = {
  name: string;
  trigger: React.ReactNode;
  id: string;
  onClick: () => void;
};
const EnrichPreview = ({ trigger, id, name, onClick }: Props) => {
  const { fieldsEnrich, fetchRequestTable } = enrichmentContext();
  const { unreadByType } = useAppSelector((state) => state.notification);
  const { user } = useAppSelector((state) => state.auth);
  const [open, setOpen] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const handleViewDetail = async () => {
    const findNoti = unreadByType?.find((item) => item.ref_id === id?.toString());
    if (findNoti) {
      await fetchRequestTable();
    }
  };
  const handleBuy = () => {
    setOpen(false);
    onClick();
  };
  const handleTracking = () => {
    setOpen(true);
    tracking({
      eventName: 'preview_data',
      params: {
        user_id: user?.uuid,
        valueTracking: JSON.stringify({
          data_id: id
        })
      }
    });
  }
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <Dialog
          open={open}
          onOpenChange={(value) => {
            dispatch(setIsViewEnrich(value));
            if (!value) {
              handleViewDetail().finally(() => setOpen(value));
            }
          }}
        >
          <TooltipTrigger asChild>
            <DialogTrigger asChild onClick={handleTracking}>
              {trigger}
            </DialogTrigger>
          </TooltipTrigger>
          <DialogContent isShowCloseIcon={false} className="sm:max-w-6xl p-4 flex flex-col gap-6">
            <DialogHeader>
              <DialogTitle className="flex items-center font-medium text-md  justify-between">
                <Box className="gap-2">
                  <CsvIcon />
                  {name}
                </Box>
                <Button className="hidden md:flex" onClick={handleBuy}>
                  Download
                </Button>
              </DialogTitle>
            </DialogHeader>
            <EnrichmentPreview id={id} fieldsEnrich={fieldsEnrich} />
            <Button className="md:hidden" onClick={handleBuy}>
              Download
            </Button>
          </DialogContent>
        </Dialog>
        <TooltipPortal>
          <TooltipContent
            align="center"
            side="top"
            sideOffset={5}
            className="flex items-baseline gap-1"
          >
            Preview
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};
export default EnrichPreview;
