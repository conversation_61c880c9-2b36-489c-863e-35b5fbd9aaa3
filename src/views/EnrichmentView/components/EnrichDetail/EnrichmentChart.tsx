import { formatChartValue } from "utils/enrichment";
import Enrich<PERSON>hart from "./Chart";

import { SummarizeField } from "types/Enrichment";

type Props = {
  data: SummarizeField;
  totalRecords: number;
};

const EnrichmentChart = ({ data, totalRecords }: Props) => {
  const chartResult = formatChartValue(data);

  return (
    <div className="min-w-[150px] w-[150px] border-r p-2">
      <LabelCompact label={"Filled value"} value={data.available} />
      <LabelCompact label={"Missing value"} value={data.missing} />
      <LabelCompact label={"Distinct value"} value={data.distinct} />
      <EnrichChart total={totalRecords} values={chartResult.values} label={chartResult.labels} />
    </div>
  );
};
export default EnrichmentChart;

const LabelCompact = ({ label, value }: { label: string; value: number }) => {
  return (
    <div className="flex items-center gap-2 justify-between">
      <span>&#x2022; {label}</span>
      <span>{value}</span>
    </div>
  );
};
