import { enrichment<PERSON><PERSON> } from "apis/enrichment";
import { useEffect, useState } from "react";
import { EnrichmentData, TypeEnrichmentPreview } from "types/Enrichment";
import { sortSummaryData, sortValueTable } from "utils/enrichment";

import { TypeField } from "views/EnrichmentView/Context/enrichmentContext";
import TableEnrich from "../TableEnrich";
import { notificationApi } from '../../../../apis/notification';
import { useAppDispatch, useAppSelector } from '../../../../store';
import { getCountUnread, getCountUnreadType } from '../../../../store/redux/notification/slice';

type Props = {
  id: string;
  fieldsEnrich: TypeField[];
};
const DEFAULT_VALUE = {
  data: {
    items: [],
    count: 0,
  },
  loading: false,
};

const EnrichmentPreview = ({ id, fieldsEnrich }: Props) => {
  const [preview, setPreview] = useState<{
    data: TypeEnrichmentPreview;
    loading: boolean;
  }>(DEFAULT_VALUE);

  const [summarize, setSummarize] = useState<{
    data: EnrichmentData | null;
    loading: boolean;
  }>({
    data: null,
    loading: true,
  });
  const { unreadByType } = useAppSelector((state) => state.notification);
  const dispatch = useAppDispatch();
  useEffect(() => {
    setPreview({ ...DEFAULT_VALUE, loading: true });
    const fetchPreview = async () => {
      try {
        const res = await enrichmentAPI.previewRequestEnrichment(id);
        const newValue = sortValueTable(fieldsEnrich, res.items);

        setPreview({
          data: {
            items: newValue,
            count: res.count,
          },
          loading: false,
        });
      } catch (error) {
        setPreview(DEFAULT_VALUE);
      }
    };
    fetchPreview();
  }, [id]);

  useEffect(() => {
    const fetchData = async () => {
      const findNoti = unreadByType?.find((item) => item.ref_id === id?.toString());
      if (findNoti) {
        await notificationApi.handleMarkAsRead(findNoti?.id);
      }
      setSummarize({ data: null, loading: true });
      try {
        const res = await enrichmentAPI.detailRequestEnrichment(id);
        const { summarize } = res;
        const newData = sortSummaryData(fieldsEnrich, summarize);
        const { phone, ...rest } = newData;
        setSummarize({ data: { ...res, summarize: rest }, loading: false });
      } catch (error) {
        setSummarize({ data: null, loading: false });
      }
    };
    fetchData().then(()=>{
      dispatch(getCountUnreadType()).unwrap();
      dispatch(getCountUnread()).unwrap();
    });
  }, [id]);

  return (
    <TableEnrich
      loading={preview.loading}
      body={preview?.data.items}
      totalRecords={summarize.data?.total_records || 0}
      chartData={Object.entries(summarize.data?.summarize || {}).map(([_, value]) => value)}
      colWidth="150"
      height="600"
      defaultCol={preview.data.count}
    />
  );
};
export default EnrichmentPreview;
