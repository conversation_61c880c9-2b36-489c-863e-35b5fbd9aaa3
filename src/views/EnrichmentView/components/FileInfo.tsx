import { enrichmentAPI } from "apis/enrichment";
import CsvIcon from "assets/icons/CsvIcon";
import { Skeleton } from "components/ui/skeleton";
import { useEffect, useState } from "react";
import { FileData } from "types/Enrichment";
import { convertFileSize } from "utils/enrichment";

type Props = {
  id: string;
};
const FileInfo = ({ id }: Props) => {
  const [file, setField] = useState<{ loading: boolean; data: FileData | null }>({
    loading: false,
    data: null,
  });

  useEffect(() => {
    setField({ loading: true, data: null });
    const fetchFile = async () => {
      try {
        const res = await enrichmentAPI.getFileInfo(id);
        setField({ loading: false, data: res });
      } catch (error) {
        setField({ loading: false, data: null });
      }
    };
    fetchFile();
  }, []);
  return (
    <div className="flex flex-col gap-2 w-full">
      <span>Do you want to save this file?</span>
      <div className="flex items-center gap-2">
        <CsvIcon />
        <div className="flex flex-col w-full">
          <Label loading={file.loading} title={"Name"} value={file.data?.file_name!} />
          <Label
            loading={file.loading}
            title={"Size"}
            value={convertFileSize(file.data?.file_size!)}
          />
          <Label loading={file.loading} title={"From"} value={"big360.ai"} />
        </div>
      </div>
    </div>
  );
};
export default FileInfo;

const Label = ({
  title = "",
  value = "",
  loading = false,
}: {
  title: string;
  value: string;
  loading: boolean;
}) => (
  <div className="text-xs flex items-center w-full max-w-[250px]">
    <span>{title}:</span>
    <span className="whitespace-nowrap overflow-hidden text-ellipsis ml-1 w-full">
      {loading ? <Skeleton className="h-[15px] w-full rounded-sm" /> : value || "N/A"}
    </span>
  </div>
);
