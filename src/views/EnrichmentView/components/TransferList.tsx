import { useCallback, useEffect, useState } from "react";
import { RiRepeatLine } from "@remixicon/react";
import { enrichmentContext } from "../Context/enrichmentContext";

import CheckBox from "./CheckBox";
import { cn } from "../../../utils/utils";

type Item = {
  key: string;
  label: string;
  selected: boolean;
};

type Props = {
  onTransfer: (value: string[]) => void;
};

export default function TransferList({ onTransfer }: Props) {
  const { fieldsEnrich } = enrichmentContext();
  const [templateSelected, setTemplateSelected] = useState<string[]>([]);
  const [fields, setFields] = useState<Item[]>([]);
  const [fieldsRight, setFieldsRight] = useState<Item[]>([]);

  useEffect(() => {
    onTransfer([]);
  }, []);

  useEffect(() => {
    const enrichedFields = fieldsEnrich.map((field) => ({
      ...field,
      selected: false,
    }));
    setFields(enrichedFields);
    setFieldsRight([]);
  }, [fieldsEnrich]);

  const handleSelectionChange = useCallback(
    (selectedKeys: string[]) => {
      const socialFields = fields.filter((item) => item.key.startsWith("social"));
      const personaFields = fields.filter((item) => item.key.startsWith("persona"));

      const selectedSet = new Set(selectedKeys);
      const allSocialSelected = socialFields.every((item) => selectedSet.has(item.key));
      const allPersonaSelected = personaFields.every((item) => selectedSet.has(item.key));

      if (allSocialSelected && !allPersonaSelected) {
        setTemplateSelected(["social"]);
      } else if (allPersonaSelected && !allSocialSelected) {
        setTemplateSelected(["persona"]);
      } else if (allSocialSelected && allPersonaSelected) {
        setTemplateSelected(["social", "persona"]);
      } else {
        setTemplateSelected([]);
      }
    },
    [fields]
  );

  useEffect(() => {
    onTransfer(fieldsRight.map((item) => item.key));
  }, [fieldsRight]);

  const onMove = useCallback(
    (key: string, label: string) => {
      setFields((prev) =>
        prev.map((item) => (item.key === key ? { ...item, selected: !item.selected } : item))
      );

      setFieldsRight((prev) => {
        const isAlreadySelected = prev.some((item) => item.key === key);
        const updatedFieldsRight = isAlreadySelected
          ? prev.filter((item) => item.key !== key)
          : [...prev, { key, label, selected: true }];

        handleSelectionChange(updatedFieldsRight.map((item) => item.key));
        return updatedFieldsRight;
      });
    },
    [handleSelectionChange]
  );

  const handleSelectTemplate = useCallback(
    (template: "social" | "persona") => {
      const isTemplateSelected = templateSelected.includes(template);

      const updatedFields = fields.map((item) => {
        if (item.key.startsWith(template)) {
          return { ...item, selected: !isTemplateSelected };
        }
        return item;
      });

      setFields(updatedFields);

      if (isTemplateSelected) {
        setFieldsRight((prev) => prev.filter((item) => !item.key.startsWith(template)));
      } else {
        setFieldsRight((prev) => {
          const newFieldsRight = fields
            .filter((item) => item.key.startsWith(template))
            .map((item) => ({ ...item, selected: true }));
          const result = newFieldsRight.filter((item) => !prev.some((i) => i.key === item.key));
          return [...prev, ...result];
        });
      }
      setTemplateSelected((prev) =>
        isTemplateSelected ? prev.filter((t) => t !== template) : [...prev, template]
      );
    },
    [fields, templateSelected]
  );

  return (
    <div className="flex items-center gap-3 md:items-start md:space-x-4 flex-col md:flex-row">
      <div className="shadow-sm border w-full md:w-1/2 rounded-lg p-4">
        <div className="mb-4 font-medium text-secondary">Enrichment fields.</div>
        <div className="w-full  flex flex-col justify-between p-1.5 bg-background border rounded-lg">
          <div className="w-full mt-auto grid grid-cols-1 gap-2 items-end md:grid-cols-2 border-b border-[#A7AAB1] pb-3 mb-4">
            <div
              className={cn(
                "p-2 rounded-lg bg-transparent",
                templateSelected.includes("social") && "bg-[#E2DAFF] text-[#5A18BF]"
              )}
            >
              <CheckBox
                label={"Social Fields"}
                onChange={() => handleSelectTemplate("social")}
                selected={templateSelected.includes("social")}
              />
            </div>
            <div
              className={cn(
                "p-2 rounded-lg bg-transparent",
                templateSelected.includes("persona") && "bg-[#E2DAFF] text-[#5A18BF]"
              )}
            >
              <CheckBox
                label={"Persona Fields"}
                onChange={() => handleSelectTemplate("persona")}
                selected={templateSelected.includes("persona")}
              />
            </div>
          </div>
          <ul className="h-[200px] overflow-y-scroll">
            {fields.map(
              (item) =>
                !item.selected && (
                  <li
                    className="flex items-center p-2 capitalize gap-1 text-sm hover:bg-muted rounded-sm"
                    key={item.key}
                  >
                    <CheckBox
                      label={item.label}
                      onChange={() => onMove(item.key, item.label)}
                      selected={false}
                    />
                  </li>
                )
            )}
          </ul>
        </div>
      </div>
      <div className="flex flex-col justify-center h-full gap-4">
        <RiRepeatLine />
      </div>
      <div className="shadow-sm border w-full h-full md:w-1/2 rounded-lg p-4">
        <div className="mb-4 text-secondary font-medium">Selected Fields</div>
        <div className="w-full  flex flex-col justify-between p-1.5 bg-background border rounded-lg">
          <ul className="h-[250px] py-1.5 overflow-x-hidden">
            {fieldsRight.map((item) => (
              <li
                className="flex items-center p-2 capitalize gap-1 text-sm hover:bg-muted rounded-sm"
                key={item.key}
              >
                <CheckBox
                  label={item.label}
                  onChange={() => onMove(item.key, item.label)}
                  selected={true}
                />
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
