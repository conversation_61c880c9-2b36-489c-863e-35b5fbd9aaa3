import { memo, useEffect, useMemo, useState } from "react";
import { RiInformation2Line, RiLoader2Line } from "@remixicon/react";
import { enrichmentContext } from "views/EnrichmentView/Context/enrichmentContext";

import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogTitle } from "components/ui/dialog";
import { Button } from "components/ui/button";
import TransferList from "../TransferList";
import { toast } from "components/ui/use-toast";
import TableEnrich from "../TableEnrich";

import { TypeTable } from "types/Enrichment";
import {
  convertKeysToLabels,
  createPayloadFormEnrich,
  getTemplate,
  sortTemplate,
} from "utils/enrichment";
import { Badge } from "components/ui/badge";

type Props = {
  file: File | undefined;
  openModal: boolean;
  onCloseModal: () => void;
};

const EnrichmentPreview = ({ file, openModal, onCloseModal }: Props) => {
  const { handleCreateItem, fieldsEnrich } = enrichmentContext();
  const [loading, setLoading] = useState<boolean>(false);
  const [sheetData, setSheetData] = useState<TypeTable>({ body: [] });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      const res = await getTemplate();
      setSheetData({
        body: res?.values || [],
      });
    };
    fetchData();
  }, []);

  const handleApply = async () => {
    if (selectedItems.length === 0) {
      return toast({
        title: "Error",
        description: "Please select at least one option",
        status: "error",
      });
    }
    setLoading(true);
    const form = createPayloadFormEnrich({
      fields: selectedItems.map((item) => item),
      file: file!,
    });
    await handleCreateItem(form);
    setLoading(false);
    onCloseModal();
  };

  const tableContent = useMemo(() => {
    const newTable = sortTemplate(["phone_number", ...selectedItems], sheetData.body);
    return {
      body: convertKeysToLabels(
        fieldsEnrich.concat([
          {
            label: "*Phone Number",
            key: "phone_number",
            selected: true,
          },
        ]),
        newTable
      ),
    };
  }, [selectedItems, sheetData]);

  return (
    <Dialog open={openModal}>
      <DialogContent
        isShowCloseIcon={false}
        className="xl:min-w-[1156px] w-full !max-w-screen-md p-3 md:p-6"
      >
        <DialogTitle>
          <div className="font-medium text-secondary text-base mb-2">Select Enrichment Fields</div>
          <p className="text-sm text-secondary font-normal">
            Select Social Fields or Persona Fields to enrich your data.
          </p>
        </DialogTitle>
        <TableEnrich body={tableContent.body} colWidth="120" />
        <div className="text-xs font-medium">
          <Badge variant={"process"} className={"font-normal rounded-xl py-2 border-none gap-1"}>
            <RiInformation2Line
              className="flex-shrink-0 mb-auto md:my-auto"
              color="#146BE1"
              size={20}
            />{" "}
            The feature charges Credits based on usage. The cost is calculated as the total of each
            field's price, which is 0.01 credits multiplied by the number of cells that have data.
          </Badge>
        </div>
        <TransferList onTransfer={(value: string[]) => setSelectedItems(value)} />
        <DialogFooter className="grid grid-cols-1 gap-2 md:flex">
          <Button onClick={onCloseModal} className="w-full md:w-[145px]" variant="tertiary">
            Cancel
          </Button>
          <Button
            variant="main"
            className="w-full md:w-[145px]"
            disabled={loading}
            onClick={handleApply}
          >
            {loading ? <RiLoader2Line size={20} className="animate-spin" /> : "Apply"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default memo(EnrichmentPreview);
