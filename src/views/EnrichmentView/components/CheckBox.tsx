type Props = {
  label: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  selected: boolean;
};

const CheckBox: React.FC<Props> = ({ label, onChange, selected }) => {
  return (
    <div className="flex items-center space-x-2">
      <input
        className="w-4 h-4 cursor-pointer text-primary accent-[#914fe8] border-gray-300 rounded focus:ring-purple-600 focus:ring-2"
        type="checkbox"
        id={label}
        checked={selected}
        onChange={onChange}
      />
      <label
        htmlFor={label}
        className="text-sm cursor-pointer leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {label}
      </label>
    </div>
  );
};

export default CheckBox;
