import { ENRICHMENT_COLUMN } from "constants/Enrichment/column";
import { enrichmentContext } from "../Context/enrichmentContext";
import { DataTable } from "components/DataTable/TableCustom";
import Pagination from "components/Pagination";

import useFilters from "hooks/useFilters";
import { useAppSelector } from 'store';
import { useEffect } from 'react';
import { TUnReadByType } from 'store/redux/notification/slice';
import { TypeEnrichmentRecord } from '../../../types/Enrichment';

const EnrichmentPreviewTable = () => {
  const { requestTable, fetchRequestTable } = enrichmentContext();
  const { params, searchParams, setSearchParams } = useFilters();
  const { timeReadNoti, unreadByType, isViewEnrich } = useAppSelector((state) => state.notification);

  useEffect(() => {
    if (timeReadNoti && !isViewEnrich) {
      fetchRequestTable().finally(()=>{});
    }
  }, [timeReadNoti, isViewEnrich]);

  const handleUpdateData = () => {
    return requestTable.table.items.map((request:TypeEnrichmentRecord ) => {
      if (unreadByType?.some((item: TUnReadByType) => item.ref_id === request.id.toString())) {
        return { ...request, isHighLight: true };
      }
      return request;
    });
  };
  return (
    <div className="mt-6">
      {/* <div className="border-b-2 border-primary w-fit mb-6">Result: {requestTable.table.count}</div> */}
      <DataTable
        columns={ENRICHMENT_COLUMN || []}
        data={handleUpdateData()}
        classTable={requestTable.loading ? "min-h-[500px]" : ""}
        loading={requestTable.loading}
      />
      <Pagination
        className="mt-4"
        currentPage={Number(searchParams.get("page")) || 1}
        pageSize={10}
        totalCount={requestTable.table.count}
        onPageChange={(page) => setSearchParams({ ...params, page: String(page) })}
      />
    </div>
  );
};

export default EnrichmentPreviewTable;
