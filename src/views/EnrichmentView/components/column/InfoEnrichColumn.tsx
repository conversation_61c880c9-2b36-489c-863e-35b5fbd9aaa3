import FileCSV from "assets/icons/FileCSV";
import { Box } from "components/Box";
import { TypeEnrichmentRecord } from "types/Enrichment";

import { convertDate } from "utils/utils";

interface Props extends Partial<Pick<TypeEnrichmentRecord, "file_name" | "date_created">> {}

const InfoEnrichColumn = ({ file_name, date_created }: Props) => {
  return (
    <Box className="gap-1 justify-start">
      <div>
        <FileCSV />
      </div>
      <div>
        <div className="text-sm font-medium text-primary">{file_name}</div>
        <span className="text-xs text-tertiary">
          Created: {date_created ? convertDate(date_created) : "--"}
        </span>
      </div>
    </Box>
  );
};
export default InfoEnrichColumn;
