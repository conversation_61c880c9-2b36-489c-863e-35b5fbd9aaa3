import { useContext, useEffect, useRef } from "react";
import { RiEyeLine } from "@remixicon/react";
import { ModalContext } from "providers/Modal";

import { Box } from "components/Box";
import EmailSending from "assets/icons/EmailSending";
import EnrichPreview from "../EnrichDetail";
import DownloadEnrichModal from "../../../../components/Transaction/DownloadEnrichModal";

import { TypeEnrichmentRecord } from "types/Enrichment";
import { NotificationAction } from "../../../../types/notification";
import { useAppDispatch, useAppSelector } from "../../../../store";
import { handleSaveRef } from "../../../../store/redux/notification/slice";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../../../components/ui/tooltip";
import { TooltipPortal } from '@radix-ui/react-tooltip';

interface Props
  extends Partial<Pick<TypeEnrichmentRecord, "status" | "id" | "file_name" | "date_created">> {}

interface TooltipContainerProps {
  trigger: React.ReactNode;
  content: React.ReactNode;
}

const TooltipContainer = ({ ...props }: TooltipContainerProps) => {
  const { trigger, content } = props;
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>{trigger}</TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="top" sideOffset={5}>
            {content}
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};

const DownloadEnrichColumn = ({ status, id = "", file_name = "", date_created }: Props) => {
  const modal = useContext(ModalContext);
  const { ref } = useAppSelector((state) => state.notification);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dispatch = useAppDispatch();
  const handleOpenEmailDownload = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <DownloadEnrichModal id={id} date_created={date_created} file_name={file_name} />,
      className: "max-w-[680px] md:p-6 p-4",
    }));
  };

  useEffect(() => {
    if (
      ref.id &&
      ref.type === NotificationAction.DATA_ENRICHMENT &&
      id == ref.id &&
      buttonRef.current
    ) {
      buttonRef?.current?.click();
      dispatch(handleSaveRef({ id: "", type: "" }));
    }
  }, [ref.id, ref.type]);

  return (
    <>
      {status === 1 ? (
        <Box className="gap-2">
          <div className="border rounded-sm p-1">
            <RiEyeLine size={20} />
          </div>
          <div className="border rounded-sm p-1">
            <EmailSending />
          </div>
        </Box>
      ) : (
        <Box className="gap-2">
          <EnrichPreview
            id={id}
            name={file_name}
            onClick={handleOpenEmailDownload}
            trigger={
              <button ref={buttonRef} className="border rounded-sm p-1">
                <RiEyeLine size={20} />
              </button>
            }
          />
          <TooltipContainer
            trigger={
              <button className="border rounded-sm p-1" onClick={handleOpenEmailDownload}>
                <EmailSending />
              </button>
            }
            content={<span className="p-2">Send to mail</span>}
          />
        </Box>
      )}
    </>
  );
};

export default DownloadEnrichColumn;
