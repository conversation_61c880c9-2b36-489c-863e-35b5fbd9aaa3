import { createContext, useContext, useEffect, useState } from "react";

import { TypeEnrichmentRequestTable } from "types/Enrichment";
import { enrichmentAPI } from "apis/enrichment";
import { toast } from "components/ui/use-toast";
import useFilters from "hooks/useFilters";
import { FIELDS_DEFAULT } from "constants/Enrichment/label";
import { getUnreadType } from '../../../store/redux/notification/slice';
import { useAppDispatch } from '../../../store';

export type TypeField = {
  key: string;
  label: string;
  selected: boolean;
};
interface TypeEnrichmentContext {
  requestTable: {
    loading: boolean;
    table: TypeEnrichmentRequestTable;
  };
  fieldsEnrich: TypeField[];
  handleCreateItem: (formPayload: FormData) => void;
  updateStateProcess: (status: number, id: string) => void;
  fetchRequestTable: () => Promise<void>;
}

const EnrichmentContext = createContext<TypeEnrichmentContext>({
  requestTable: {
    loading: false,
    table: {
      count: 0,
      items: [],
    },
  },
  handleCreateItem: () => {},
  fieldsEnrich: [],
  updateStateProcess: () => {},
  fetchRequestTable: async () => {}
});

const EnrichmentProvider = ({ children }: { children: React.ReactNode }) => {
  const { params } = useFilters();
  const dispatch = useAppDispatch();
  const [fields, setFields] = useState<TypeField[]>([]);
  const [requestTable, setRequestTable] = useState<{
    loading: boolean;
    table: TypeEnrichmentRequestTable;
  }>({
    loading: false,
    table: {
      count: 0,
      items: [],
    },
  });
  const updateStateProcess = (status: number, id: string) => {
    setRequestTable((prev: any) => {
      const currentItems = prev.table.items;
      const updatedItems = currentItems.map((item: any) => {
        if (item.id === id) {
          return {
            ...item,
            status: status,
          };
        }
        return item;
      });
      return {
        ...prev,
        table: {
          ...prev.table,
          items: updatedItems,
        },
      };
    });
  };
  const fetchFields = async () => {
    try {
      const res = await enrichmentAPI.getFieldsEnrich();
      const newObj = Object.keys(res).reduce<TypeField[]>((acc, key) => {
        const items = Object.entries(res[key]) ? Object.entries(res[key]) : [];
        return [
          ...acc,
          ...items.map((item: string[]) => ({
            key: item[0],
            label: item[1],
            selected: false,
          })),
        ];
      }, []);
      setFields(newObj);
    } catch (error: any) {
      setFields(FIELDS_DEFAULT);
      toast({
        title: "Error",
        description: error.detail || "Failed to fetch fields",
        status: "error",
      });
    }
  };
  const handleCreateItem = async (formPayload: FormData) => {
    try {
      const res = await enrichmentAPI.createEnrichmentRequest(formPayload).then((res)=>{
        dispatch(getUnreadType({
          type: 'data_enrichment'
        }));
        return res;
      });
      setRequestTable((prev) => {
        const currentItems = prev.table.items;
        const updatedItems = [
          res,
          ...(currentItems.length >= 10 ? currentItems.slice(0, -1) : currentItems),
        ];

        return {
          ...prev,
          table: {
            count: prev.table.count + 1,
            items: updatedItems,
          },
        };
      });

      toast({
        status: "success",
        description: "Create enrichment success",
        title: "Success",
      });
    } catch (error: any) {
      toast({
        status: "error",
        description: error.detail,
        title: "Error",
      });
    }
  };
  const fetchRequestTable = async () => {
    setRequestTable({ ...requestTable, loading: true });
    try {
      const res = await enrichmentAPI.getListRequestEnrich({
        page: params.page ? Number(params.page) : 1,
        limit: 10,
      }).then((res)=>{
        dispatch(getUnreadType({
          type: 'data_enrichment'
        }));
        return res
      });

      setRequestTable({
        table: {
          count: res.count,
          items: res.items.sort(
            (a, b) => new Date(b.date_created).getTime() - new Date(a.date_created).getTime()
          ),
        },
        loading: false,
      });
    } catch (error: any) {
      setRequestTable({ ...requestTable, loading: false });
      toast({
        status: "error",
        description: error.message,
        title: "Error",
      });
    }
  };
  useEffect(() => {
    fetchFields();
  }, []);

  useEffect(() => {
    fetchRequestTable();
  }, [params.page]);

  const value = {
    requestTable,
    handleCreateItem,
    fieldsEnrich: fields,
    updateStateProcess,
    fetchRequestTable
  };

  return <EnrichmentContext.Provider value={value}>{children}</EnrichmentContext.Provider>;
};

const enrichmentContext = (): TypeEnrichmentContext => useContext(EnrichmentContext);

export { enrichmentContext, EnrichmentProvider };
