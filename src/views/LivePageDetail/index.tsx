import { useContext, useEffect } from "react";
import { ShowDetailContext } from "providers/ShowDetailProvider";

import { Box } from "components/Box";
import Container from "views/LivePostView/components/Container";
import PageInfoAndFilter from "components/YourPage/PageInfoAndFilter";

import useGetListPost from "hooks/useGetListPost";
import useFilters from '../../hooks/useFilters';

const LivePostBrandDetail = () => {
  const { paramsNoPage } = useFilters();
  const context = useContext(ShowDetailContext);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const { data, lastElementRef } = useGetListPost({
    page_id: context?.showDetail.id,
    type: "live",
    paramsNoPage
  });

  return (
    <Box variant="col-start" className="gap-6 w-full mt-6">
      <PageInfoAndFilter isLive isDateTime />
      <Container data={data.posts} total={data.total} lastElementRef={lastElementRef} />
    </Box>
  );
};
export default LivePostBrandDetail;
