import { Link, useParams } from "react-router-dom";
import { RiGlobalLine } from "@remixicon/react";

import AvatarByName from "components/AvatarByName";
import { Box } from "components/Box";
import { Card } from "components/ui/card";
import MetricsCompact from "components/LiveAdsPost/MetricsCompact";

import useAvatar from "hooks/useAvatar";
import { PageFollow } from "types/LiveService";

const PageAbout = ({ data }: { data: PageFollow | undefined }) => {
  const { id = "" } = useParams();
  const { avatar } = useAvatar({ type: "page", uid: id });

  return (
    <Card className="shadow-sm py-[22px] px-6 my-6">
      <Box variant="default" className="justify-start">
        <AvatarByName urlImage={avatar.url} name={data?.name || ""} className="w-20 h-20" />
        <Box variant="col-start" className="gap-1">
          <Box className="gap-2">
            <Link
              className="font-semibold  text-primary text-2xl hover:text-primary-hover"
              to={`https://www.facebook.com/${data?.name}`}
              children={data?.name}
              target="_blank"
            />
            <span className="text-sm text-secondary">{data?.category}</span>
          </Box>
          {(data?.follow_count || data?.like_count) && (
            <Box className="text-secondary text-sm gap-4">
              <MetricsCompact num={data.like_count || 0} content="likes" />
              <MetricsCompact num={data.follow_count || 0} content="followers" />
            </Box>
          )}
          {data?.description && (
            <div className="text-primary text-sm lg:max-w-1/2 ">{data?.description}</div>
          )}
          {data?.website && data?.website.length > 0 && (
            <Link
              className="text-xs hover:text-primary-hover"
              to={data?.website[0] || ""}
              children={
                <Box className="gap-1">
                  <RiGlobalLine color="gray" size={18} />
                  <p className="text-primary hover:text-purple-500">{data?.website[0]}</p>
                </Box>
              }
              target="_blank"
            />
          )}
        </Box>
      </Box>
    </Card>
  );
};

export default PageAbout;
