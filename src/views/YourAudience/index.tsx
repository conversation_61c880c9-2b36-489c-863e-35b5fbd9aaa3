import { RiTeamLine, RiUserLine } from "@remixicon/react";
import { AudienceProvider } from "./context/audienceContext";

import TabRouteWrap, { ITab } from "components/Tabs/TabRouteWrap";
import Breadcrumb from "components/Breadcrumb";
import HeaderWrapper from "components/Header";

import { DATA_AUDIENCE, ROOT_PATH } from "types/Router";
import { TABS, YOUR_DATA_TITLE } from "constants/yourData/label";
import { useEffect, useContext, useRef } from 'react';
import { getAudienceProfilesSummarize } from '../../store/redux/AudienceProfile/slice';
import { useAppDispatch, useAppSelector } from '../../store';
import { Button } from '../../components/ui/button';
import { TutorialIcon } from '../../assets/icons/TutorialIcon';
import useOutsideClick from '../../hooks/useClickOutSide';
import handleCloseModal from '../../utils/handleCloseModal';
import { ModalContext } from '../../providers/Modal';
import { RiCloseLine } from '@remixicon/react';

const LIST_TAB: ITab[] = [
  {
    title: TABS.SOCIAL_AUDIENCE,
    path: `${ROOT_PATH}/${DATA_AUDIENCE.ROOT}/${DATA_AUDIENCE.SOCIAL}`,
    type: 'your_audience_social',
    tab_icon: RiTeamLine,
  },
  {
    title: TABS.PERSONA_AUDIENCE,
    path: `${ROOT_PATH}/${DATA_AUDIENCE.ROOT}/${DATA_AUDIENCE.PERSONA}`,
    type: 'your_audience_work',
    tab_icon: RiUserLine,
  },
];

const YourAudience = () => {
  const { data } = useAppSelector((state) => state.audienceProfiles);
  const modal = useContext(ModalContext);
  const ref = useRef(null);
  const refSecond = useRef(null);

  const dispatch = useAppDispatch();
  useEffect(() => {
    if (!data) {
      dispatch(getAudienceProfilesSummarize()).unwrap();
    }
  }, []);
  
  useOutsideClick(ref, refSecond, () => {
    if (!!modal?.dataDialog?.isPopupVideo) {
      handleCloseModal(modal);
    }
  });
  const handleOpenTutorial =()=>{
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      isPopupVideo: true,
      content: (
        <div ref={refSecond}>
          <div className="text-right">
            <Button
              onClick={() => handleCloseModal(modal)}
              className="p-[10px] text-sm font-semibold mb-1 bg-transparent hover:bg-transparent text-primary"
            >
              <RiCloseLine size={20} />
              Close
            </Button>
          </div>
          <iframe
            src="https://www.youtube.com/embed/DwqzjuxcFew?autoplay=1"
            title="Big360 - Your Audience"
            className="rounded-2xl w-full max-w-[920px] aspect-video"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
        </div>
      ),
      className: 'max-w-[936px] !p-2 max-lg:max-w-[calc(100%-12px)]'
    } ))
  }

  return (
    <AudienceProvider>
      <>
        <Breadcrumb />
        <HeaderWrapper
          leftChildren={{
            title: <div className="flex gap-2" ref={ref}>
              <h1>{YOUR_DATA_TITLE.AUDIENCE.title}</h1>
              <Button onClick={()=>handleOpenTutorial()} className='flex items-center justify-center bg-[#F0F0F0] h-[36px] gap-1 p-2 rounded-xl	hover:bg-[#F0F0F0]'>
                <TutorialIcon /><p className="text-[#8F5CFF] text-xs mt-[1px]">Guide</p>
              </Button>
            </div>,
            subTitle: YOUR_DATA_TITLE.AUDIENCE.subTitle,
          }}
        />
        <TabRouteWrap tabs={LIST_TAB} />
      </>
    </AudienceProvider>
  );
};
export default YourAudience;
