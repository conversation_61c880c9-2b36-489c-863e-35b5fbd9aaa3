import { useContext, useEffect, useState } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { ModalContext } from 'providers/Modal';
import { useLocation, useParams } from 'react-router-dom';

import HeaderDetailModal from 'components/HeaderDetailModal';
import BreadCrumb from 'components/Breadcrumb';
import TablePreview from 'views/SocialDataDetail/components/TablePreview';
import AudienceFilterOptions from 'views/SocialDataDetail/components/AudienceFilter/AudienceFilterOptions';
import ShowResult from 'views/SocialDataDetail/components/ShowResult';
import CreateSegment from './components/CreateSegment';
import AddSegmentButton from './components/ButtonAddSegment';

import { yourSegmentApi } from 'apis/yourSegment';
import useAvatar from 'hooks/useAvatar';
import useFilters from 'hooks/useFilters';

import { socialAPI } from 'apis/socialData';
import { TDataPreview, TSocialDetail } from 'types/SocialData';
import { CreateSegmentFormType } from 'types/YourData';
import { formatPayloadAvatar, toQueryString } from 'utils/utils';
import { YOUR_AUDIENCE_LABEL } from 'constants/yourData/label';
import { useAppDispatch, useAppSelector } from '../../store';
import { notificationApi } from '../../apis/notification';
import { getUnreadType } from '../../store/redux/notification/slice';
import { useAbortController } from '../../hooks/useAbortController';

const SocialAudience = () => {
  const { id = '' } = useParams();
  const location = useLocation();
  const parts = location.pathname;
  const datatype: 'AUDIENCE' | 'DATASET' = ( parts.includes('dataset') ?
    'DATASET' :
    parts.includes('audience') ? 'AUDIENCE' : '' ) as 'AUDIENCE' | 'DATASET';
  const { params, paramsNoPage, searchParams } = useFilters();
  const context = useContext(ModalContext);

  const { newAbortController } = useAbortController();
  const { newAbortController: abortPreview } = useAbortController();

  const [preview, setPreview] = useState<TDataPreview>({
    data: [],
    total: 0,
    loading: false
  });

  const [summarize, setSummarize] = useState<TSocialDetail>({} as any);
  const [loading, setLoading] = useState<boolean>(false);
  const [totalCount, setTotalCount] = useState<{
    total: number,
    loading: boolean
  }>({
    total: 0,
    loading: false
  });
  const { avatar } = useAvatar(
    formatPayloadAvatar(summarize.type || 1, summarize.fb_uid ?? '', summarize.actor_id ?? '')
  );

  const dispatch = useAppDispatch();
  const { unreadByType } = useAppSelector((state) => state.notification);

  const handleViewDetail = async () => {
    const findNoti = unreadByType?.find((item) => item.ref_id === id);
    if (!findNoti) {
      return;
    }
    return await notificationApi.handleMarkAsRead(findNoti?.id);
  };

  useEffect(() => {
    if (id !== '') {
      getPreview();
    }
  }, [params, id]);

  const getDetailAudience = async (id: string) => {
    setLoading(false);
    await socialAPI.getBoughtSummarizeDetail(id, datatype).then((res) => {
      setSummarize(res.data.data);
    }).finally(() => setLoading(false));
  };

  useEffect(() => {
    if (!!id) {
      dispatch(getUnreadType({
        type: 'your_audience_social'
      }));
      handleViewDetail();
      getDetailAudience(id);
      handleGetCount().then((res) => {
        setTotalCount({
          total: res.count, loading: false
        });
        if (preview.total !== res.total) {
          setPreview((prev) => ( {
            ...prev, total: res.count
          } ));
        }
      }).finally(() => {
        setTotalCount((prev) => ( {
          ...prev, loading: false
        } ));
      });
    }
  }, [id, JSON.stringify(paramsNoPage)]);

  const handleGetCount = async () => {
    const controller = newAbortController();
    setTotalCount((prev) => ( {
      ...prev, loading: true
    } ));
    const response = await socialAPI.get({
      endpoint: `user-audiences/${id}/profiles/count/`,
      params: {
        ...paramsNoPage,
        datatype: datatype
      },
      headers: {
        isv2: true
      },
      signal: controller.signal
    });
    return response?.data;
  };

  const getPreview = async () => {
    setPreview((prev) => ( { ...prev, loading: true } ));
    const controller = abortPreview();
    await socialAPI.getDetailYourAudience({
      id,
      page: Number(params.page ?? 1),
      query: `&datatype=${datatype}` + ( paramsNoPage ? '&' + toQueryString(paramsNoPage) : '' ),
      signal: controller.signal
    }).then((response) => {
      setPreview((prev) => ( {
        ...prev,
        data: response.items,
        loading: false
      } ));
    }).finally(() => {
      setPreview((prev) => ( { ...prev, loading: false } ));

    });

  };

  const handleOpenModal = () => {
    context?.setDataDialog((prev) => ( {
      ...prev,
      isOpen: true,
      title: YOUR_AUDIENCE_LABEL.CREATE_SEGMENT,
      content: (
        <CreateSegment audienceName={summarize.name} handleCreateSegment={handleCreateSegment} />
      )
    } ));
  };
  const handleCreateSegment: SubmitHandler<CreateSegmentFormType> = async (data) => {
    await yourSegmentApi.createSocialSegment({
      name: data.name,
      description: data.description ?? '',
      audience_id: id ?? '',
      datatype: datatype,
      query: searchParams.toString()
    });

    context?.setDataDialog((prev) => ( { ...prev, isOpen: false } ));
  };

  return (
    <>
      <BreadCrumb loading={loading} path={summarize.name} />
      <HeaderDetailModal
        className="items-start"
        typeAudience={summarize.type || 1}
        name={summarize.name || ''}
        loading={loading}
        avatarUrl={avatar.url}
        size={summarize.size}
        description={summarize.description}
        uid={summarize.fb_uid}
        packageValue={summarize.package || ''}
        subTypeAudience={summarize?.subtype}
      />

      {/* show box filter & chart summarize */}
      <AudienceFilterOptions
        isFilterCity
        isRelationship
        isGender
        isFilterSocialAge
        isFilterDOB
        isInterest
        summarize={summarize.summarize}
        isSocialPersona={true}
        loading={loading}
        contentLeft={<div className="flex justify-between items-center h-[32px]">
          <ShowResult total={preview.total} />
        </div>}
        contentRight={
          <div className="mb-6 text-right">
            <AddSegmentButton
              className="w-fit ml-auto"
              loading={totalCount.loading}
              total={totalCount.total ?? 0}
              handleClick={handleOpenModal}
            />
          </div>
        }
      />
      <div>{preview && <TablePreview
        data={{ ...preview, total: totalCount.total ?? 0 }}
        isDataset={summarize.datatype === 'DATASET'}
      />}</div>
    </>
  );
};

export default SocialAudience;
