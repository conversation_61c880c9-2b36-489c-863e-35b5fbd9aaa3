import { useContext } from 'react';
import { ModalContext } from 'providers/Modal';
import { audienceContext } from 'views/YourAudience/context/audienceContext';

import EditPersona from '../../../../components/yourAudienceAndSegment/EditPersona';
import ActionColumn from 'components/yourAudienceAndSegment/ActionColumn';
import DeleteYourDataModal from 'components/yourAudienceAndSegment/DeleteYourDataModal';
import DownloadYourDataModal from 'components/yourAudienceAndSegment/DownloadYourDataModal';
import { IYourAudienceItem } from '../../../../types/yourAudience';

const PersonaAudienceSocialActionCol = (props: IYourAudienceItem) => {
  const { handleDeletePersonaAudience, handleEditPersonaAudience } = audienceContext();
  const modal = useContext(ModalContext);

  const handleOpenModalDelete = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "max-w-[512px]",
      isOpen: true,
      isShowTitle: false,
      content: (
        <DeleteYourDataModal
          handleDelete={() => handleDeletePersonaAudience(props.fb_uid as any)}
          title={"Are you sure you want to delete this persona audience?"}
        />
      ),
    }));
  };

  const handleDownload = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: <DownloadYourDataModal id={props.fb_uid} name={props.name} segment_size={props.size} isAudience={true}/>,
      className: "max-w-[680px]",
    }));
  };

  const handleOpenModalEdit = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      isShowTitle: false,
      content: (
        <EditPersona
          name={props.name}
          description={props.description??''}
          handleEdit={(id, data) => handleEditPersonaAudience(id, data)}
          id={props.fb_uid as any}
        />
      ),
    }));
  };

  return (
    <ActionColumn
      isDownload={props.datatype==='AUDIENCE'}
      isDelete={false}
      isEdit={false}
      handleDownload={handleDownload}
      handleDelete={handleOpenModalDelete}
      handleEdit={handleOpenModalEdit}
    />
  );
};
export default PersonaAudienceSocialActionCol;
