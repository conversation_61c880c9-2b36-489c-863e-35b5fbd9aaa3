import { Link } from "react-router-dom";
import AvatarByName from "components/AvatarByName";
import useAvatar from "hooks/useAvatar";
import { YOUR_AUDIENCES } from "types/Router";
import { formatPayloadAvatar } from "utils/utils";
import { IYourAudienceItem } from "types/yourAudience";
import { Box } from '../../../../components/Box';

const InfoColumn = (props: IYourAudienceItem) => {
  const { fb_uid, actor_id,type, description, datatype, name } = props;
  const { avatar } = useAvatar(
    formatPayloadAvatar(type || 1, fb_uid ?? "", actor_id ?? "")
  );

  return (
    <div className="flex items-center gap-2">
      <AvatarByName className="w-6 h-6" name={name} urlImage={avatar.url ?? ""} />
      <Box variant={'col-start'} className='gap-0'>
        <Link
          to={`/${YOUR_AUDIENCES.ROOT}/${datatype === 'DATASET' ?
            YOUR_AUDIENCES.SOCIAL_DATASET :
            YOUR_AUDIENCES.SOCIAL}/${fb_uid}/`}
          className="truncate hover:text-primary-hover w-[350px] max-w-[350px]"
        >
          {props?.name}
        </Link>
        <span className='text-xs text-tertiary line-clamp-1'>{description}</span>
      </Box>
    </div>
  );
};

export default InfoColumn;
