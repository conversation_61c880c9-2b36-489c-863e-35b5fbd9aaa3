import { Checkbox } from "components/ui/checkbox";
import { audienceProfilesContext } from "../context/AudienceProfilesContext";

interface Props {
  id: number;
}

const ActionAddProfile = ({ id }: Props) => {
  const { selectedProfiles, setSelectedProfiles } = audienceProfilesContext();

  const handleChange = (isChecked: boolean) => {
    setSelectedProfiles((prev) =>
      isChecked ? [...prev, id] : prev.filter((profileId) => profileId !== id)
    );
  };

  return (
    <div>
      <Checkbox checked={selectedProfiles.includes(id)} onCheckedChange={handleChange} />
    </div>
  );
};

export default ActionAddProfile;
