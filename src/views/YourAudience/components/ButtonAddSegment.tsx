import { RiAddLine } from "@remixicon/react";
import { Button } from "components/ui/button";
import { cn } from "utils/utils";
import { fNumberToString } from "utils/number";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

type Props = {
  title?: string;
  className?: string;
  isDisable?: boolean;
  loading?: boolean;
  total?: number;
  handleClick: () => void;
};

const AddSegmentButton = ({
  className,
  isDisable = false,
  total = 0,
  loading = false,
  handleClick,
}: Props) => {
  return (
    <Button
      variant="main"
      disabled={loading || isDisable}
      onClick={() => handleClick()}
      className={cn(
        "shadow-xs h-10 gap-0.5 py-1.5 px-[10px] rounded-lg hover:bg-brand-default min-w-[265px]",
        className
      )}
    >
      {loading ? (
        <LoadingButtonIcon />
      ) : (
        <>
          <RiAddLine size={16} />
          <span>
            Add <strong>{fNumberToString(total)}</strong> profiles to segment
          </span>
        </>
      )}
    </Button>
  );
};

export default AddSegmentButton;
