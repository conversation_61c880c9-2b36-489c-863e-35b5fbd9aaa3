import { RiArrowDownSLine } from "@remixicon/react";
import * as Popover from "@radix-ui/react-popover";
import { useState } from "react";

import { Button } from "components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "components/ui/command";
import { IAllSegment, TypeSegment } from "./CreateSegmentCustom";
import { removeVietnameseTones } from "utils/utils";
import { Badge } from 'components/ui/badge';
import { Box } from 'components/Box';

interface Props extends IAllSegment {
  segmentId: number | undefined;
  typeSegment: TypeSegment | undefined;
  setSegmentId: React.Dispatch<React.SetStateAction<number | undefined>>;
  setTypeSegment: React.Dispatch<React.SetStateAction<TypeSegment | undefined>>;
}
const ListSegmentExit = (props: Props) => {
  const { data, segmentId, setSegmentId } = props;
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    props.setTypeSegment("exit");
  };
  const handleSelectSegment = (value: number) => {
    setSegmentId(value);
    setOpen(false);
  };

  const newData = data.map((item) => ({
    label: item.name,
    value: item.id,
    datatype: item.datatype
  }));

  const LABEL_TRIGGER =
    segmentId && props.typeSegment == 'exit'
      ? <Box className="gap-1">
        <Badge className="capitalize text-[8px]">{newData.find((segment) => segment.value === segmentId)?.datatype.toLocaleLowerCase()}</Badge>
        <p className="truncate">
          {newData.find((segment) => segment.value === segmentId)?.label}
        </p>
      </Box>
      : 'Existing Segment';

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <Popover.Trigger>
        <Button
          type="button"
          variant="tertiary"
          role="combobox"
          className="w-full justify-between max-w-[300px]"
          onClick={handleOpen}
        >
          <span className="truncate max-w-full">{LABEL_TRIGGER}</span>
          <RiArrowDownSLine size={24} />
        </Button>
      </Popover.Trigger>

      <Popover.Content className="z-[9999] bg-white w-[300px] rounded-xl shadow-sm border border-custom-primary mt-2 overflow-hidden p-2 cursor-pointer pointer-events-auto">
        <Command>
          <CommandInput placeholder="Enter segment name" className="h-9" autoFocus />
          <CommandList className="max-h-[150px] overflow-y-auto">
            <CommandEmpty>No Segment found.</CommandEmpty>
            <CommandGroup>
              {newData.map((segment) => (
                <CommandItem
                  className="cursor-pointer"
                  key={segment.value}
                  value={`${removeVietnameseTones(segment.label)}-${segment.label}-${
                    segment.value
                  }`}
                  onSelect={() => handleSelectSegment(segment.value)}
                >
                  <Badge className="capitalize text-[8px]">{segment.datatype.toLocaleLowerCase()}</Badge>
                  <p className="truncate">
                    {segment.label}
                  </p>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </Popover.Content>
    </Popover.Root>
  );
};

export default ListSegmentExit;
