import { useAppSelector } from "store";
import { Link } from "react-router-dom";
import * as Popover from "@radix-ui/react-popover";
import { RiInformation2Line } from "@remixicon/react";

import { Box } from "components/Box";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import RiUserFollowFile from "./RiUserFollowFile";

import { YOUR_AUDIENCES } from "types/Router";
import { fNumberToString } from "utils/number";
import { YOUR_DATA_TITLE } from "constants/yourData/label";
import LABEL from "constants/label";
import { formatDate } from "utils/utils";

const AudienceProfilesWrapper = () => {
  const { data, loading } = useAppSelector((state) => state.audienceProfiles);
  return (
    <Box className="w-full justify-between p-4 rounded-2xl border border-[#8F5CFF] bg-[#E2DAFF] shadow-sm gap-3 items-start">
      <Link
        className="flex-1 w-full"
        to={`/${YOUR_AUDIENCES.ROOT}/${YOUR_AUDIENCES.PERSONA}/${YOUR_AUDIENCES.ARCHIVE}`}
      >
        <Box className="gap-3">
          {loading && (
            <Box className="w-full justify-center items-center">
              <LoadingButtonIcon />
            </Box>
          )}
          {!loading && (
            <>
              <div className="bg-[#FDFDFD] p-2 rounded-2xl">
                <RiUserFollowFile />
              </div>
              <Box variant="col-start" className="gap-1">
                <div className="text-primary text-md font-medium w-full">
                  {YOUR_DATA_TITLE.AUDIENCE.title_archive}
                </div>
                <Box className="flex-col items-start gap-0 text-primary text-sm font-normal leading-5 md:gap-4 md:flex-row">
                  <div>
                    <span>{LABEL.audience_size}:</span>
                    <span className="ml-1">{data?.size ? fNumberToString(data?.size) : 0}</span>
                  </div>
                  <div>{YOUR_DATA_TITLE.AUDIENCE.type_archive} </div>
                  <div>
                    Last added:{" "}
                    {formatDate(data?.last_added || "2024-12-30T17:00:03.904431+07:00", true)}
                  </div>
                </Box>
              </Box>
            </>
          )}
        </Box>
      </Link>
      <Information />
    </Box>
  );
};

export default AudienceProfilesWrapper;

const Information = () => (
  <Popover.Root>
    <Popover.Trigger className="w-5">
      <RiInformation2Line size={20} className="text-primary" />
    </Popover.Trigger>
    <Popover.Portal>
      <Popover.Content
        side="top"
        align="end"
        alignOffset={-10}
        className="w-[200px] md:w-[450px] bg-black overflow-hidden z-10 rounded-xl "
      >
        <div className="text-sm text-white relative z-10 px-2 py-1">
          {YOUR_DATA_TITLE.AUDIENCE.infor_archive}
        </div>
        <Popover.Arrow className="relative left-0.5 z-0 bg-transparent" />
      </Popover.Content>
    </Popover.Portal>
  </Popover.Root>
);
