import { Box } from "components/Box";

const EmptyData = () => {
  return (
    <Box variant="col-start" className="w-full h-full items-center justify-center gap-4">
      <Box variant="col-start" className="gap-3  items-center justify-center">
        <div className="flex justify-center items-center flex-col h-[550px] text-center">
          <div className="mx-auto mb-4 flex items-center justify-center">
            <svg width="404" height="213" viewBox="0 0 404 213" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g filter="url(#filter0_dd_186_4835)">
                <rect x="32" y="32.3623" width="340" height="147.909" rx="12" fill="#FDFDFD" shapeRendering="crispEdges"/>
                <path d="M44 99.796C44 88.1478 53.4427 78.7051 65.0909 78.7051C76.7391 78.7051 86.1818 88.1478 86.1818 99.796C86.1818 111.444 76.7391 120.887 65.0909 120.887C53.4427 120.887 44 111.444 44 99.796Z" fill="#F0F0F0"/>
                <path d="M65.546 92.1065H74.9774C75.5841 92.1065 76.0759 92.5983 76.0759 93.205V108.584C76.0759 109.19 75.5841 109.682 74.9774 109.682H55.2047C54.598 109.682 54.1062 109.19 54.1062 108.584V91.008C54.1062 90.4014 54.598 89.9095 55.2047 89.9095H63.3491L65.546 92.1065ZM56.3032 92.1065V107.485H73.8789V94.3035H64.6361L62.4391 92.1065H56.3032ZM60.6971 106.387C60.6971 103.96 62.6643 101.993 65.0911 101.993C67.5177 101.993 69.485 103.96 69.485 106.387H60.6971ZM65.0911 100.894C63.5744 100.894 62.3448 99.6649 62.3448 98.1482C62.3448 96.6315 63.5744 95.402 65.0911 95.402C66.6077 95.402 67.8373 96.6315 67.8373 98.1482C67.8373 99.6649 66.6077 100.894 65.0911 100.894Z" fill="#515667"/>
                <rect x="72" y="109.929" width="24" height="24" rx="12" fill="#FDFDFD"/>
                <path d="M88.6267 125.442L92 128.815L90.8861 129.929L87.5129 126.555C86.3 127.526 84.7618 128.106 83.0888 128.106C79.1758 128.106 76 124.931 76 121.018C76 117.104 79.1758 113.929 83.0888 113.929C87.0018 113.929 90.1776 117.104 90.1776 121.018C90.1776 122.69 89.5971 124.229 88.6267 125.442ZM87.0465 124.857C88.0096 123.865 88.6023 122.511 88.6023 121.018C88.6023 117.971 86.135 115.504 83.0888 115.504C80.0426 115.504 77.5753 117.971 77.5753 121.018C77.5753 124.064 80.0426 126.531 83.0888 126.531C84.5819 126.531 85.9358 125.938 86.9284 124.975L87.0465 124.857Z" fill="#6B7183"/>
                <g clipPath="url(#clip0_186_4835)">
                  <path d="M108 52.2714C108 47.9033 111.541 44.3623 115.909 44.3623H352.205C356.573 44.3623 360.114 47.9033 360.114 52.2714V160.279C360.114 164.647 356.573 168.188 352.205 168.188H115.909C111.541 168.188 108 164.647 108 160.279V52.2714Z" fill="#E1E2E3"/>
                  <rect x="108" y="61.6205" width="252.114" height="17.3817" fill="#FDFDFD"/>
                  <rect x="108" y="79.4576" width="252.114" height="17.3817" fill="#FDFDFD"/>
                  <rect x="108" y="97.2949" width="252.114" height="17.3817" fill="#FDFDFD"/>
                  <rect x="108" y="115.132" width="252.114" height="17.3817" fill="#FDFDFD"/>
                  <rect x="108" y="132.969" width="252.114" height="17.3817" fill="#FDFDFD"/>
                  <rect x="108" y="150.807" width="252.114" height="17.3817" fill="#FDFDFD"/>
                </g>
                <path d="M108 52.2714C108 47.9033 111.541 44.3623 115.909 44.3623H352.091C356.459 44.3623 360 47.9033 360 52.2714V160.362C360 164.73 356.459 168.271 352.091 168.271H115.909C111.541 168.271 108 164.73 108 160.362V52.2714Z" stroke="#E1E2E3" strokeWidth="0.659091"/>
              </g>
              <defs>
                <filter id="filter0_dd_186_4835" x="0" y="0.362305" width="404" height="211.909" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feFlood floodOpacity="0" result="BackgroundImageFix"/>
                  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                  <feMorphology radius="8" operator="erode" in="SourceAlpha" result="effect1_dropShadow_186_4835"/>
                  <feOffset dy="4"/>
                  <feGaussianBlur stdDeviation="10"/>
                  <feComposite in2="hardAlpha" operator="out"/>
                  <feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.1 0"/>
                  <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_186_4835"/>
                  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                  <feOffset/>
                  <feGaussianBlur stdDeviation="16"/>
                  <feComposite in2="hardAlpha" operator="out"/>
                  <feColorMatrix type="matrix" values="0 0 0 0 0.0352941 0 0 0 0 0.0392157 0 0 0 0 0.0509804 0 0 0 0.02 0"/>
                  <feBlend mode="normal" in2="effect1_dropShadow_186_4835" result="effect2_dropShadow_186_4835"/>
                  <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_186_4835" result="shape"/>
                </filter>
                <clipPath id="clip0_186_4835">
                  <path d="M108 52.2714C108 47.9033 111.541 44.3623 115.909 44.3623H352.091C356.459 44.3623 360 47.9033 360 52.2714V160.362C360 164.73 356.459 168.271 352.091 168.271H115.909C111.541 168.271 108 164.73 108 160.362V52.2714Z" fill="white"/>
                </clipPath>
              </defs>
            </svg>

          </div>
          <p className="font-bold text-base text-primary-crm mb-2" children={'No Audience Found'} />
          <p
            className="font-normal text-sm text-secondary"
            children={'Get started by creating or purchasing an audience using Audience Finder.'}
          />
        </div>
      </Box>
    </Box>
  );
};

export default EmptyData;
