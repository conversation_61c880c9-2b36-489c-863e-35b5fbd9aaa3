import omitBy from "lodash/omitBy";
import { RiDeleteBin6Line } from "@remixicon/react";

import { Box } from "components/Box";
import { Button } from "components/ui/button";

import { TSelectedOption } from "types/Select";
import { cn } from "utils/utils";
import LABEL from "constants/label";
import { YOUR_AUDIENCE_LABEL } from "constants/yourData/label";
import useFilters from "hooks/useFilters";
import isEqual from "lodash/isEqual";
import { useEffect } from "react";
import { FILTER_KEY } from "constants/persona";
import usePrevious from "hooks/usePrevious";
import FilterInformation from "./FilterInformation";

interface Props {
  isDisableFilter?: boolean;
  selectedOption: TSelectedOption;
  setSelectedOption: React.Dispatch<React.SetStateAction<TSelectedOption>>;
}

const FilterHeader = ({
  selectedOption,
  setSelectedOption,
  isDisableFilter = false,
}: Props) => {
  const { paramsNoPage, setSearchParams } = useFilters();
  const prevCompanyName = usePrevious(paramsNoPage[FILTER_KEY.company_name]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if company_name__ilike
      if (
        event.key === "Enter" &&
        selectedOption[FILTER_KEY.company_name] &&
        selectedOption[FILTER_KEY.company_name].trim() !== "" &&
        !isEqual(selectedOption[FILTER_KEY.company_name], prevCompanyName)
      ) {
        handleFilter();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedOption, paramsNoPage]);

  const handleFilter = () => {
    if (isEqual(selectedOption, paramsNoPage)) {
      return;
    }
    const _paramsFilter = omitBy(selectedOption, (x) => x === undefined);
    setSelectedOption(_paramsFilter);
    setSearchParams({ ..._paramsFilter });
  };

  const handleClearFilter = () => {
    setSelectedOption({});
    setSearchParams({});
  };

  const isFilterDisabled = isEqual(selectedOption, paramsNoPage);

  return (
    <Box className="justify-between w-full">
      <Box className="gap-1 h-8">
        <span className="text-primary text-md font-medium">{YOUR_AUDIENCE_LABEL.FILTER}</span>
        <FilterInformation />
      </Box>
      {!isDisableFilter && (
        <>
          <Box className="gap-2">
            {Object.keys(selectedOption).length > 0 && (
              <Button
                className="px-2 py-1 min-w-20 text-xs h-8 rounded-sm"
                variant="default"
                disabled={isFilterDisabled}
                onClick={handleFilter}
              >
                {YOUR_AUDIENCE_LABEL.FILTER}
              </Button>
            )}

            <button
              className={cn(
                "flex items-center gap-1 text-sm text-red-400 hover:text-red-500",
                Object.keys(paramsNoPage).length == 0 && "hidden"
              )}
              onClick={() => handleClearFilter()}
            >
              <span children={LABEL.clear_all} />
              <RiDeleteBin6Line size={16} />
            </button>
          </Box>
        </>
      )}
    </Box>
  );
};

export default FilterHeader;
