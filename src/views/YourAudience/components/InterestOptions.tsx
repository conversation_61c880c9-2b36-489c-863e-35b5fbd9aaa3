import { RiStarFill } from '@remixicon/react';
import { useAppSelector } from 'store';

import { HandleSelectChangeProps, TSelectedOption, TSelectOption } from 'types/Select';
import useFilters from 'hooks/useFilters';

import { filterIsShowOptions } from 'utils/options';

import { cn, toDataOptions } from 'utils/utils';
import { FILTER_KEY, ScoreOptionsAtt } from 'constants/socialPersona';
import SOCIAL_PERSONA_LABEL from 'constants/socialPersona/label';
import React, { useEffect, useState } from 'react';
import { TRange } from 'components/AgeRangeFilter';
import useDebounce from '../../../hooks/useDebounce';
import { convertRangeToValue } from '../../../utils/socialPersona';
import { Box } from '../../../components/Box';
import { styled } from 'styled-components';
import FilterInterestAudience from '../../../components/FilterOptions/FilterInterestAudience';

type TInterestOptions = {
  onChange: (value: any) => void;
  selectedOption: TSelectedOption;
  isDisabled: boolean;
  isSegmentDetail?: boolean;
  defaultValue?: (TSelectedOption| undefined)[];
}

const InterestOptions: React.FC<TInterestOptions> = ({ ...props }: TInterestOptions) => {
  const { onChange, selectedOption, isDisabled, isSegmentDetail } = props;
  const keyOfCategory = FILTER_KEY.categoryDefault;
  const keyOfScore = FILTER_KEY.score;


  const categorySelect = selectedOption.category;
  const scoreRangeSelect = selectedOption.score_range;

  const { paramsNoPage, searchSchema } = useFilters();
  const { data: categories } = useAppSelector((state) => state.category);
  const filterCategory = filterIsShowOptions(
    toDataOptions(categories.items, 'code', 'name'),
    [categorySelect]
  );
  const [categorySelected, setCategorySelected] = useState<string>(categorySelect);
  const filterScore = filterIsShowOptions(ScoreOptionsAtt, searchSchema[FILTER_KEY.score]);
  const paramsScore = convertRangeToValue(searchSchema[FILTER_KEY.score]?.[0] ?? '');

  const [rangeSelected, setRangeSelected] = useState<TRange>({
    min: paramsScore.min / 10 ?? 0,
    max: paramsScore.max / 10 ?? 5
  });
  const [enable, setEnable] = useState<{rank: boolean, range: boolean}>({
    rank: false,
    range: false
  });

  const handleChange = ({ selectedNodes, key }: HandleSelectChangeProps) => {
    if (!enable.rank && !enable.range) {
      setEnable({
        rank: true,
        range: true
      });
    }
    if (![keyOfCategory, keyOfScore].includes(key)) {
      return;
    }

    const paramCategory = paramsNoPage[keyOfCategory] || filterCategory[0]?.value;
    const paramScore = paramsNoPage[keyOfScore] || filterScore[0]?.value;
    if (key === keyOfCategory) {
      if (paramsNoPage[keyOfScore]) {
        handleUpdateSlider(paramScore.toString());
      } else {
        handleUpdateSlider(`${rangeSelected.min === 0 ? 4 * 10 : rangeSelected.min * 10}-${rangeSelected.max * 10}`);
      }

    } else {
      const [min, max] = selectedNodes[0].value.toString().split('-').map(value => parseInt(value) / 10);
      setRangeSelected({ min, max });
    }
    setCategorySelected(key === keyOfCategory ? selectedNodes[0].value.toString() : categorySelect);
    onChange({
      [keyOfCategory]: key === keyOfCategory ? selectedNodes[0].value.toString() : categorySelect??paramCategory.toString(),
      [keyOfScore]: key === keyOfScore ?
        selectedNodes[0].value.toString() :
        !!rangeSelected ? `${( rangeSelected.max - 1 ) * 10}-${rangeSelected.max * 10}` : paramScore.toString()
    });
  };

  const handleChangeRange = (range: TRange) => {
    if (range.max === 0) {
      return;
    }
    setRangeSelected({
      min: range.min === range.max ? range.min - 1 : range.min,
      max: range.max
    });
  };

  const handleUpdateSlider = (str: string) => {
    const [start, end] = str.split(',').reduce(
      ([min, max]: [number, number], range: string): [number, number] => {
        const [rangeStart, rangeEnd] = range.split('-').map(Number);
        return [Math.min(min, rangeStart), Math.max(max, rangeEnd)];
      },
      [Infinity, -Infinity]
    );
    setRangeSelected({
      min: start / 10,
      max: end / 10
    });
  };

  const renderStar = () =>
    ScoreOptionsAtt.map((item, index) => {
      const { min, max } = convertRangeToValue(item.name ?? '');
      const checked = rangeSelected.min === min && rangeSelected.max === max;
      const stars = Array.from({ length: max }, (_, i) => (
        <RiStarFill key={i} size={20} color={(i === max - 1 || min === i + 1) ? '#FBCA24' : '#FCEAAA'} />
      ));
      return (
        <Box
          key={index}
          className={cn('justify-start gap-2 mb-4 mt-2 mx-2 cursor-pointer w-fit', { 'opacity-50 cursor-not-allowed': !enable.rank })}
          onClick={() => {
            if (enable.rank) {
              handleChange({
                selectedNodes: [{
                  checked: true,
                  dept: 0,
                  label: item.name ?? '',
                  value: item.id ?? ''
                }],
                key: FILTER_KEY.score
              });
            }
          }}
        >
          <ItemRadio
            type="radio"
            className={cn(enable.rank ? 'cursor-pointer' : 'cursor-not-allowed')}
            value={item.value}
            checked={checked}
            readOnly
            disabled={!enable.rank}
          />
          <Box className="gap-2">{stars as React.ReactNode}</Box>
        </Box>
      );
    });

  const debouncedSearchQuery: TRange = useDebounce(rangeSelected, 300) ?? '';

  useEffect(() => {
    if (debouncedSearchQuery.min !== undefined && debouncedSearchQuery.max !== undefined && searchSchema[FILTER_KEY.score]) {
      const rangeValue = debouncedSearchQuery.min * 10 + '-' + debouncedSearchQuery.max * 10;
      onChange({
        [keyOfCategory]: categorySelected,
        [keyOfScore]: rangeValue
      });
    }
  }, [debouncedSearchQuery.min, debouncedSearchQuery.max]);

  useEffect(() => {
    const isCategoryOrScoreEmpty = !searchSchema[FILTER_KEY.categoryDefault] && !searchSchema[FILTER_KEY.score];
    setEnable({
      rank: !isCategoryOrScoreEmpty,
      range: !isCategoryOrScoreEmpty
    });
    if (isCategoryOrScoreEmpty) {
      setRangeSelected({ min: 0, max: 5 });
    }
  }, [searchSchema]);

  const handleConvertActive = () => {
    if (!categorySelect && !scoreRangeSelect) {
      return undefined;
    }
    return {
      category: filterCategory.find((cate) => cate.value === categorySelect)?.label || '',
      score_range: ScoreOptionsAtt.find((score) => score.id === scoreRangeSelect)?.name || ''
    };
  };
  return (
    <FilterInterestAudience
      isEnable={!isDisabled}
      isSegmentDetail={isSegmentDetail}
      label={SOCIAL_PERSONA_LABEL.category_filter}
      activeValue={handleConvertActive()}
      activeCount={(categorySelect && scoreRangeSelect) ? 1 : undefined}
      onTagRemove={() => {
        onChange({
          [keyOfCategory]: '',
          [keyOfScore]: ''
        });
        setRangeSelected({
          min: 0,
          max: 5
        });
        setEnable({
          range: false,
          rank: false
        });
      }}
      groupSelect={[
        {
          placeholderOptions: 'Categories',
          isSearchable: true,
          isSingleSelect: true,
          options: filterCategory,
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.categoryDefault })
        },
        {
          isSingleSelect: true,
          placeholderOptions: 'Score Range',
          options: filterScore,
          isEnable: enable.rank,
          isCustomOptions: true,
          customOptions: renderStar(),
          rangeOption: {
            min: 0,
            max: 5,
            hideInput: true,
            className: '!w-[140px]',
            isRangeEnable: enable.range,
            rangeSelected: rangeSelected,
            onRangeChange: (value) => {
              handleChangeRange(value);
            }
          },
          onChange: (_currentNode: TSelectOption, selectedNodes: TSelectOption[]) =>
            handleChange({ selectedNodes, key: FILTER_KEY.score })
        }
      ]}
    />
  );
};

export default InterestOptions;

const ItemRadio = styled.input`
  position: relative;
  width: 16px;
  height: 16px;
  appearance: none;

  &:before {
    content: "";
    display: block;
    position: absolute;
    min-width: 16px;
    min-height: 16px;
    width: 16px;
    height: 16px;
    border: 1px solid #dee0e3;
    border-radius: 30px;
    background-color: white;
    z-index: 10;
    box-shadow: 0 1px 2px 0 #14151a0d;
  }

  &:checked:after {
    content: "";
    display: block;
    min-width: 6px;
    min-height: 6px;
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }

  &:checked:before {
    background-color: #924fe8;
    border: 1px solid #924fe8;
  }
`;
