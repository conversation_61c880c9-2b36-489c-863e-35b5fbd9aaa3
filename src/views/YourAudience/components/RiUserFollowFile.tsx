const RiUserFollowFile = () => {
  return (
    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M24.0043 29.504V33.6828C22.7531 33.2406 21.4069 33 20.0043 33C13.3769 33 8.00427 38.3726 8.00427 45H4.00427C4.00427 36.1634 11.1677 29 20.0043 29C21.3855 29 22.7259 29.175 24.0043 29.504ZM20.0043 27C13.3743 27 8.00427 21.63 8.00427 15C8.00427 8.37 13.3743 3 20.0043 3C26.6343 3 32.0043 8.37 32.0043 15C32.0043 21.63 26.6343 27 20.0043 27ZM20.0043 23C24.4243 23 28.0043 19.42 28.0043 15C28.0043 10.58 24.4243 7 20.0043 7C15.5843 7 12.0043 10.58 12.0043 15C12.0043 19.42 15.5843 23 20.0043 23Z"
        fill="#20232C"
      />
      <path
        d="M29.0958 31.7V42.9H41.8958V33.3H35.1644L33.5644 31.7H29.0958ZM35.8272 31.7H42.6958C43.1376 31.7 43.4958 32.0582 43.4958 32.5V43.7C43.4958 44.1418 43.1376 44.5 42.6958 44.5H28.2958C27.854 44.5 27.4958 44.1418 27.4958 43.7V30.9C27.4958 30.4582 27.854 30.1 28.2958 30.1H34.2272L35.8272 31.7Z"
        fill="#20232C"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.2958 30.6C28.1301 30.6 27.9958 30.7343 27.9958 30.9V43.7C27.9958 43.8657 28.1301 44 28.2958 44H42.6958C42.8615 44 42.9958 43.8657 42.9958 43.7V32.5C42.9958 32.3343 42.8615 32.2 42.6958 32.2H35.6201L34.0201 30.6H28.2958ZM26.9958 30.9C26.9958 30.182 27.5778 29.6 28.2958 29.6H34.4343L36.0343 31.2H42.6958C43.4138 31.2 43.9958 31.782 43.9958 32.5V43.7C43.9958 44.418 43.4138 45 42.6958 45H28.2958C27.5778 45 26.9958 44.418 26.9958 43.7V30.9ZM28.5958 31.2H33.7715L35.3716 32.8H42.3958V43.4H28.5958V31.2ZM29.5958 32.2V42.4H41.3958V33.8H34.9573L33.3573 32.2H29.5958Z"
        fill="#20232C"
      />
    </svg>
  );
};

export default RiUserFollowFile;
