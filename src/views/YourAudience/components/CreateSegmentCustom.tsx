import { useContext, useEffect, useState } from "react";
import { ModalContext } from "providers/Modal";
import { SubmitHandler, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { AudienceProfilesContextProps } from "../context/AudienceProfilesContext";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { toast } from "components/ui/use-toast";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";
import { ISegment } from "views/DataProcessing/context/ProcessingTabsContext";
import InputController from "components/InputController";
import TextAreaController from "components/TextAreaController";
import ListSegmentExit from "./ListSegmentExit";

import useFilters from "hooks/useFilters";
import { personaCustomAPI } from "apis/personaCustom";
import { CreateSegmentFormBody, CreateSegmentFormType } from "types/YourData";

import handleCloseModal from "utils/handleCloseModal";
import { toQueryString } from "utils/utils";
import { convertDescriptionAudience } from "utils/persona/formatAudienceAutoFill";
import { processedFilter, YOUR_AUDIENCE_LABEL } from "constants/yourData/label";

export interface IAllSegment {
  data: ISegment[];
  loading: boolean;
}
export type TypeSegment = "new" | "exit";

const CreateSegmentCustom = ({
  selectedProfiles,
  setSelectedProfiles,
}: AudienceProfilesContextProps) => {
  const modal = useContext(ModalContext);
  const { paramsNoPage } = useFilters();

  const [typeSegment, setTypeSegment] = useState<TypeSegment>();
  const [segmentId, setSegmentId] = useState<number>();
  const [loading, setLoading] = useState<boolean>(false);
  const [allSegment, setAllSegment] = useState<IAllSegment>({
    data: [],
    loading: false,
  });
  const {
    handleSubmit,
    control,

    formState: { errors, isValid },
  } = useForm<CreateSegmentFormType>({
    resolver: yupResolver(CreateSegmentFormBody),
    defaultValues: {
      name: "",
      description: convertDescriptionAudience(paramsNoPage, processedFilter),
    },
    mode: "onChange",
  });

  useEffect(() => {
    getPersonaSegment();
  }, []);

  const getPersonaSegment = async () => {
    setAllSegment((prev) => ({ ...prev, loading: true }));
    const response: any = await personaCustomAPI.getAllPersonaSegment();
    if (response && response.data) {
      setAllSegment((prev) => ({ ...prev, data: response.data.items }));
    }
    setAllSegment((prev) => ({ ...prev, loading: true }));
  };

  const handleCreateSegment = async (data?: { description?: string; name: string }) => {
    setLoading(true);
    const query = paramsNoPage || !isAddProfiles ? toQueryString(paramsNoPage) : "";

    const payload = {
      is_full_profiles: query === "" && !isAddProfiles,
      profile_uids: isAddProfiles ? selectedProfiles : [],
      ...(typeSegment === "exit" && {
        segment_id: segmentId,
      }),
      ...(typeSegment === "new" &&
        data && {
          new_segment: {
            name: data.name,
            description: data.description ?? "",
          },
        }),
    };

    if (!payload) {
      console.error("Invalid typeSegment value:", typeSegment);
      return;
    }

    try {
      await personaCustomAPI.addSegmentProfileCustom({
        query: query,
        payload: payload,
      });
      handleCloseModal(modal);
      setSelectedProfiles([]);
      toast({
        title: "Segment Custom Profile Upsert Successfully",
        status: "success",
        duration: 3000,
      });
    } catch (error) {
      console.error("Error creating segment:", error);
    } finally {
      setLoading(false);
    }
  };

  const onSubmit: SubmitHandler<CreateSegmentFormType> = async (data) => {
    await handleCreateSegment(data);
  };

  const isAddProfiles = selectedProfiles.length > 0;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box variant="col-start">
        <div className="w-full text-center text-primary text-lg font-semibold">
          {YOUR_AUDIENCE_LABEL.ADD_TO_MY_SEGMENT}
        </div>
        <LABEL />
        <Box variant="col-start" className="gap-3">
          <div className="grid grid-cols-2 gap-4 w-full">
            <ListSegmentExit
              {...allSegment}
              segmentId={segmentId}
              typeSegment={typeSegment}
              setSegmentId={setSegmentId}
              setTypeSegment={setTypeSegment}
            />
            <Button
              type="button"
              variant="tertiary"
              className="rounded-xl"
              onClick={() => setTypeSegment("new")}
            >
              {YOUR_AUDIENCE_LABEL.CREATE_SEGMENT}
            </Button>
          </div>
        </Box>
        {typeSegment == "new" && (
          <>
            <InputController
              className="gap-2 text-sm w-full"
              control={control}
              name="name"
              label={YOUR_AUDIENCE_LABEL.SEGMENT_NAME}
              placeholder="Enter segment name"
              required
              error={errors.name?.message}
            />
            <TextAreaController
              className="w-full"
              name={"description"}
              control={control}
              label={YOUR_AUDIENCE_LABEL.SEGMENT_DESCRIPTION}
              maxLength={200}
            />
          </>
        )}
        <Box className="grid grid-cols-2 text-md font-semibold w-full">
          <Button
            className="text-md font-semibold text-primary border-custom-primary rounded-xl"
            variant="secondary"
            children="Cancel"
            type="button"
            onClick={() => handleCloseModal(modal)}
          />
          <Button
            className="text-md font-semibold text-custom-brand rounded-xl"
            variant="default"
            children={loading ? <LoadingButtonIcon /> : "Add"}
            type="submit"
            onClick={() => typeSegment === "exit" && handleCreateSegment()}
            disabled={loading ||
              ( typeSegment === 'exit' && !segmentId ) ||
              ( typeSegment == 'new' && !isValid ) ||
              !typeSegment
            }
          />
        </Box>
      </Box>
    </form>
  );
};

export default CreateSegmentCustom;

const LABEL = () => (
  <label className="text-primary text-sm font-medium">
    Add to<strong className="text-error-default">*</strong>:
  </label>
);
