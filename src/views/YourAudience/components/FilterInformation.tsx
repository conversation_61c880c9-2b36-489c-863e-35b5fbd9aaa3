import * as Popover from "@radix-ui/react-popover";
import { RiInformation2Line } from "@remixicon/react";
import { YOUR_AUDIENCE_LABEL } from "constants/yourData/label";

const FilterInformation = () => {
  return (
    <Popover.Root>
      <Popover.Trigger>
        <RiInformation2Line size={16} className="text-secondary" />
      </Popover.Trigger>
      <Popover.Portal>
        <Popover.Content
          side="right"
          align="center"
          className="w-[200px] md:w-[450px] bg-black overflow-hidden z-10 rounded-xl "
        >
          <div className="text-sm text-white relative z-10 px-2 py-1 ">
            {YOUR_AUDIENCE_LABEL.MESSAGE_FILTER}
          </div>
          <Popover.Arrow className="relative left-1 z-0 bg-transparent" />
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};

export default FilterInformation;
