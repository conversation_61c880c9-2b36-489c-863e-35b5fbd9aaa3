import { useContext, useState } from "react";
import { RiLoader2Fill } from "@remixicon/react";
import { SubmitHandler, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { ModalContext } from "providers/Modal";

import { Button } from "components/ui/button";
import TextAreaController from "components/TextAreaController";
import InputController from "components/InputController";

import { CreateSegmentFormBody, CreateSegmentFormType } from "types/YourData";
import { processedFilter, YOUR_AUDIENCE_LABEL } from "constants/yourData/label";
import useFilters from "hooks/useFilters";
import { convertDescriptionAudience } from "utils/persona/formatAudienceAutoFill";
import { useAppSelector } from '../../../store';
interface Props {
  audienceName: string;
  handleCreateSegment: SubmitHandler<{
    description?: string | undefined;
    name: string;
  }>;
}

const CreateSegment = ({ audienceName, handleCreateSegment }: Props) => {
  const context = useContext(ModalContext);
  const { data: categories } = useAppSelector((state) => state.category);
  const { paramsNoPage } = useFilters();
  const [loading, setLoading] = useState(false);
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<CreateSegmentFormType>({
    resolver: yupResolver(CreateSegmentFormBody),
    defaultValues: {
      name: audienceName,
      description: convertDescriptionAudience(paramsNoPage, processedFilter, categories.items),
    },
  });

  const handleCloseModal = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    context?.setDataDialog((prev) => ({
      ...prev,
      isOpen: false,
    }));
  };
  const onSubmit: SubmitHandler<CreateSegmentFormType> = async (data) => {
    setLoading(true);
    try {
      await handleCreateSegment(data);
    } finally {
      setLoading(false);
    }
  };
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="space-y-5 text-primary font-medium">
        <InputController
          className="gap-2 text-sm"
          control={control}
          name="name"
          label={YOUR_AUDIENCE_LABEL.SEGMENT_NAME}
          placeholder="Enter segment name"
          required
          error={errors.name?.message}
        />
        <TextAreaController
          control={control}
          label={YOUR_AUDIENCE_LABEL.SEGMENT_DESCRIPTION}
          name={"description"}
        />
      </div>
      <div className="grid grid-cols-3 gap-[15px] mt-5">
        <Button
          variant="main"
          className="col-span-2"
          type="submit"
          disabled={loading}
          children={
            loading ? (
              <RiLoader2Fill className="animate-spin" size={14} />
            ) : (
              YOUR_AUDIENCE_LABEL.CREATE
            )
          }
        />
        <Button
          variant="secondary"
          className="border shadow-xs rounded-lg py-1.5 px-2.5 text-sm"
          children={YOUR_AUDIENCE_LABEL.CANCEL}
          onClick={(e) => handleCloseModal(e)}
        />
      </div>
    </form>
  );
};

export default CreateSegment;
