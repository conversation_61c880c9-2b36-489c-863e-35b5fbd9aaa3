import { useAppSelector } from "store";
import { useContext, useEffect, useState } from "react";
import { audienceProfilesContext } from "./context/AudienceProfilesContext";
import { SegmentProvider } from "views/YourSegment/context/segmentContext";
import { ModalContext } from "providers/Modal";

import Breadcrumb from "components/Breadcrumb";
import HeaderDetailModal from "components/HeaderDetailModal";
import Summarize from "components/PersonaDetail/Summarize";
import UserTable from "components/PersonaDetail/UserTable";
import ShowResult from "views/SocialDataDetail/components/ShowResult";
import AddSegmentButton from "./components/ButtonAddSegment";
import CreateSegmentCustom from "./components/CreateSegmentCustom";
import AudienceFilterWrapper from "views/Persona/components/FilterOptions/AudienceFilterWrapper";

import useFilters from "hooks/useFilters";
import { personaCustomAPI } from "apis/personaCustom";
import { PersonaAudiencePreview } from "types/Persona";

import { formatRegionData } from "utils/persona/formatRegion";
import { getCityData } from "utils/persona";
import { cn, toQueryString } from "utils/utils";
import { YOUR_AUDIENCE_LABEL, YOUR_DATA_TITLE } from "constants/yourData/label";
import useDelayRenderSummarize from "hooks/useDelayRenderSummarize";

interface IPreviewData {
  count: number;
  items: PersonaAudiencePreview[];
  loading: boolean;
}

const AudienceProfiles = () => {
  const modal = useContext(ModalContext);
  const { selectedProfiles, setSelectedProfiles } = audienceProfilesContext();
  const { data, loading } = useAppSelector((state) => state.audienceProfiles);
  const { params, paramsNoPage } = useFilters();
  const { isVisible, isRendered } = useDelayRenderSummarize(paramsNoPage);

  const [preview, setPreview] = useState<IPreviewData>({
    count: 0,
    items: [],
    loading: false,
  });

  useEffect(() => {
    fetchPreview();
    setSelectedProfiles([]);
  }, [params.page, JSON.stringify(paramsNoPage)]);

  const fetchPreview = async () => {
    setPreview((prev) => ({ ...prev, loading: true }));
    const response = await personaCustomAPI.getAudienceProfiles(
      params.page ?? 1,
      paramsNoPage ? "&" + toQueryString(paramsNoPage) : ""
    );
    if (response && response.data) {
      setPreview({ ...response.data, loading: false });
    }
    setPreview((prev) => ({ ...prev, loading: false }));
  };

  const handleOpenModal = () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      className: "min-w-[680px]",
      isOpen: true,
      isShowTitle: false,
      title: YOUR_AUDIENCE_LABEL.CREATE_SEGMENT,
      content: (
        <CreateSegmentCustom
          selectedProfiles={selectedProfiles}
          setSelectedProfiles={setSelectedProfiles}
        />
      ),
    }));
  };

  const TITLE_BUTTON =
    selectedProfiles.length > 0
      ? YOUR_AUDIENCE_LABEL.ADD_PROFILE_SEGMENT
      : Object.keys(paramsNoPage).length > 0
      ? YOUR_AUDIENCE_LABEL.ADD_FILTER_SEGMENT
      : YOUR_AUDIENCE_LABEL.ADD_ALL_SEGMENT;

  return (
    <SegmentProvider>
      <div className="w-full h-full min-w-[300px]">
        <Breadcrumb path={YOUR_DATA_TITLE.AUDIENCE.title_archive} loading={loading} />
        <HeaderDetailModal
          className="items-start"
          size={data?.size ?? 0}
          description={YOUR_DATA_TITLE.AUDIENCE.des_archive}
          loading={loading}
          name={YOUR_DATA_TITLE.AUDIENCE.title_archive}
          contentRight={
            <AddSegmentButton
              title={TITLE_BUTTON}
              total={selectedProfiles.length || preview.count}
              loading={preview.loading}
              handleClick={handleOpenModal}
              className="min-w-[200px]"
            />
          }
        />
        <AudienceFilterWrapper
          isFilterProvince
          isFilterGender
          isFilterDOB
          isFilterPersonaAge
          isFilterCompanyName
          isFilterCompanySize
          isFilterPosition
          isFilterIndustry
          isFilterDepartment
        />
        {/* show chart summarize */}
        <div
          className={cn(
            "transition-all duration-500 ease-in-out",
            isVisible ? "xl:max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
          )}
        >
          {isRendered && (
            <Summarize
              loading={loading}
              cityData={getCityData(data?.summarize?.person_province ?? {})}
              valuesRegion={formatRegionData(data?.summarize?.person_region ?? {}).values}
              labelsRegion={formatRegionData(data?.summarize?.person_region ?? {}).labels}
              dataAge={data?.summarize?.age_gender}
              dataGender={data?.summarize?.gender}
            />
          )}
        </div>

        <div className="mt-6">
          <ShowResult total={preview.count} />
          <UserTable
            loading={preview.loading}
            rowData={preview.items}
            count={preview.count}
            isAudienceArchive
          />
        </div>
      </div>
    </SegmentProvider>
  );
};
export default AudienceProfiles;
