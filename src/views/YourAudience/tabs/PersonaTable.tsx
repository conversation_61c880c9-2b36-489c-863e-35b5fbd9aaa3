import { useEffect, useState } from 'react';
import { audienceContext } from '../context/audienceContext';

import { Box } from 'components/Box';
import { DataTable } from 'components/DataTable/TableCustom';
import Pagination from 'components/Pagination';
import EmptyData from '../components/EmptyData';
import AudienceProfilesWrapper from '../components/AudienceProfilesWrapper';

import { personaAPI } from 'apis/persona';
import { PERSONA_AUDIENCE_COLUMN } from 'constants/yourData/column';
import { getUnreadType, TUnReadByType } from '../../../store/redux/notification/slice';
import { useAppDispatch, useAppSelector } from '../../../store';
import { PersonaAudienceItem } from '../../../types/Persona';
import { useAbortController } from '../../../hooks/useAbortController';

const PersonaTable = () => {
  const { personaAudience, setPersonaAudience } = audienceContext();
  const [pageIndex, setPageIndex] = useState<number>(1);
  const dispatch = useAppDispatch();
  const { unreadByType, timeReadNoti } = useAppSelector((state) => state.notification);
  const { newAbortController } = useAbortController();

  useEffect(() => {
    dispatch(getUnreadType({
      type: 'your_audience_work'
    }));
  }, [timeReadNoti]);

  useEffect(() => {
    fetchData();
  }, [pageIndex, timeReadNoti]);

  const fetchData = async () => {
    try {
      const controller = newAbortController();
      setPersonaAudience({ ...personaAudience, loading: true });
      const response = await personaAPI.getPersonaAudience({
        page: pageIndex,
        limit: 10,
        signal: controller.signal
      });
      if (response && response.data) {
        setPersonaAudience({ ...response.data, loading: false });
      }
    } catch (error) {
      setPersonaAudience({ ...personaAudience, loading: false });
    }
  };

  const handleUpdateData = () => {
    return personaAudience.items.map((persona: PersonaAudienceItem) => {
      if (unreadByType?.some((item: TUnReadByType) => item.ref_id === persona.id.toString())) {
        return { ...persona, isHighLight: true };
      }
      return persona;
    });
  };

  return (
    <Box variant="col-start" className="w-full h-full gap-6">
      <AudienceProfilesWrapper />
      <div className="flex-1 w-full">
        {personaAudience.count > 0 ? (
          <>
            <DataTable
              loading={personaAudience.loading}
              columns={PERSONA_AUDIENCE_COLUMN}
              data={handleUpdateData()}
            />
            <Pagination
              className="mt-4"
              totalCount={personaAudience.count}
              pageSize={10}
              currentPage={pageIndex}
              onPageChange={(page) => setPageIndex(page)}
            />
          </>
        ) : !personaAudience.loading ? (
          <EmptyData />
        ) : (
          <div className="min-h-[500px] w-full"></div>
        )}
      </div>
    </Box>
  );
};
export default PersonaTable;
