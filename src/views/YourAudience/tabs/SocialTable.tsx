import { memo, useEffect, useState } from "react";
import { audienceContext } from "../context/audienceContext";

import { DataTable } from "components/DataTable/TableCustom";
import EmptyData from "../components/EmptyData";
import Pagination from "components/Pagination";

import { socialAPI } from "apis/socialData";
import { SOCIAL_AUDIENCE_COLUMN } from "constants/yourData/column";
import { useAppDispatch, useAppSelector } from '../../../store';
import { getUnreadType, TUnReadByType } from '../../../store/redux/notification/slice';
import { IYourAudienceItem } from '../../../types/yourAudience';

const SocialTable = () => {
  const [pageIndex, setPageIndex] = useState<number>(1);
  const { socialAudience, setSocialAudience } = audienceContext();
  const dispatch = useAppDispatch();
  const { unreadByType, timeReadNoti } = useAppSelector((state) => state.notification);

  useEffect(() => {
    dispatch(getUnreadType({
      type:'your_audience_social'
    }))
  }, [timeReadNoti]);
  
  useEffect(() => {
    fetchData();
  }, [pageIndex, timeReadNoti]);

  const fetchData = async () => {
    setSocialAudience({ ...socialAudience, loading: true });
    const response = await socialAPI.getListYourAudience({ page: pageIndex });
    setSocialAudience({ ...response, loading: false });
  };

  const handleUpdateData = () => {
    return socialAudience.items.map((social: IYourAudienceItem) => {
      if (unreadByType?.some((item: TUnReadByType) => item.ref_id === social.fb_uid && item.meta.datatype === social.datatype)) {
        return { ...social, isHighLight: true };
      }
      return social;
    });
  };

  return socialAudience.count > 0 ? (
    <>
      <DataTable
        columns={SOCIAL_AUDIENCE_COLUMN}
        data={handleUpdateData()}
        loading={socialAudience.loading}
      />
      <Pagination
        className="mt-4"
        totalCount={socialAudience.count}
        pageSize={10}
        currentPage={pageIndex}
        onPageChange={(value) => setPageIndex(value)}
      />
    </>
  ) : !socialAudience.loading ? (
    <EmptyData />
  ) : (
    <div className="min-h-[500px] w-full"></div>
  );
};
export default memo(SocialTable);
