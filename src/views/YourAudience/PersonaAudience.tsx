import { useContext, useEffect, useState } from "react";
import { useNavigate, useParams } from 'react-router-dom';
import { SubmitHandler } from "react-hook-form";
import { ModalContext } from "providers/Modal";

import CreateSegment from "./components/CreateSegment";
import AddSegmentButton from "./components/ButtonAddSegment";
import Breadcrumb from "components/Breadcrumb";
import HeaderDetailModal from "components/HeaderDetailModal";
import Summarize from "components/PersonaDetail/Summarize";
import UserTable from "components/PersonaDetail/UserTable";
import ShowResult from "views/SocialDataDetail/components/ShowResult";
import AudienceFilterWrapper from "views/Persona/components/FilterOptions/AudienceFilterWrapper";

import { personaAPI } from "apis/persona";
import { yourSegmentApi } from "apis/yourSegment";
import useFilters from "hooks/useFilters";
import useDelayRenderSummarize from "hooks/useDelayRenderSummarize";
import { CreateSegmentFormType } from "types/YourData";
import { PersonaAudienceItem, PersonaAudiencePreview } from "types/Persona";

import { getCityData } from "utils/persona";
import { formatRegionData } from "utils/persona/formatRegion";
import { cn, toQueryString } from "utils/utils";
import handleCloseModal from "utils/handleCloseModal";
import { YOUR_AUDIENCE_LABEL } from "constants/yourData/label";
import { getUnreadType } from '../../store/redux/notification/slice';
import { useAppDispatch, useAppSelector } from '../../store';
import { notificationApi } from '../../apis/notification';
import { useAbortController } from '../../hooks/useAbortController';

interface IOverviewView {
  data: PersonaAudienceItem | null;
  loading: boolean;
}
export interface IPreviewData {
  count: number;
  items: PersonaAudiencePreview[];
  loading: boolean;
}
const PersonaAudience = () => {
  const context = useContext(ModalContext);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { params, paramsNoPage, searchParams } = useFilters();
  const { isVisible, isRendered } = useDelayRenderSummarize(paramsNoPage);
  const { unreadByType } = useAppSelector((state) => state.notification);
  const dispatch = useAppDispatch();

  const { newAbortController } = useAbortController();

  const [overview, setOverview] = useState<IOverviewView>({
    data: null,
    loading: false,
  });
  const [preview, setPreview] = useState<IPreviewData>({
    count: 0,
    items: [],
    loading: false,
  });


  const handleViewDetail = async () => {
    const findNoti = unreadByType?.find((item) => item.ref_id === id);
    if (!findNoti) {
      return;
    }
    return await notificationApi.handleMarkAsRead(findNoti?.id);
  };

  useEffect(() => {
    if (!id) return;
    fetchOverview();
    dispatch(getUnreadType({
      type:'your_audience_work'
    }))
    handleViewDetail();
  }, [id]);

  useEffect(() => {
    if (!id) return;
    fetchPreview();
  }, [id, params.page, JSON.stringify(paramsNoPage)]);

  const fetchOverview = async () => {
    setOverview({ data: null, loading: true });
    const response: any = await personaAPI.getPersonaAudienceById(id || "");
    if (response.status == 422 || response.status == 404) {
      navigate("/404");
    }
    if (response?.data) {
      setOverview({ data: response?.data?.data, loading: false });
    }
    setOverview((prev) => ({ ...prev, loading: false }));
  };

  const fetchPreview = async () => {
    setPreview((prev) => ({ ...prev, loading: true }));
    try {
      const controller = newAbortController();
      const res = await personaAPI.getPersonaAudiencePreviewById({
        id: id || "",
        page: params?.page ?? 1,
        query: paramsNoPage ? "&" + toQueryString(paramsNoPage) : "",
        signal: controller.signal
      });
      setPreview({ ...res, loading: false });
    } catch (error) {
      setPreview((prev) => ({ ...prev, loading: false }));
    }
  };
  const handleOpenModal = () => {
    context?.setDataDialog((prev) => ({
      ...prev,
      isOpen: true,
      title: YOUR_AUDIENCE_LABEL.ADD_TO_MY_SEGMENT,
      content: (
        <CreateSegment
          audienceName={overview?.data?.audience_name || ""}
          handleCreateSegment={handleCreateSegment}
        />
      ),
    }));
  };
  const handleCreateSegment: SubmitHandler<CreateSegmentFormType> = async (data) => {
    await yourSegmentApi.createPersonaSegment({
      name: data.name,
      description: data.description ?? "",
      audience_id: id ?? "",
      query: searchParams.toString(),
    });
    //close Modal
    handleCloseModal(context);
  };

  return (
    <>
      <Breadcrumb path={overview?.data?.audience_name} loading={overview.loading} />
      <HeaderDetailModal
        className="items-start"
        size={(overview?.data?.size || overview?.data?.segment_size) ?? 0}
        description={overview.data?.description ?? ""}
        loading={overview.loading}
        name={overview?.data?.audience_name ?? ""}
      />
      <AudienceFilterWrapper
        isFilterProvince
        isFilterGender
        isFilterDOB
        isFilterPersonaAge
        isFilterCompanyName
        isFilterCompanySize
        isFilterIndustry
        isFilterPosition
        isFilterDepartment
      />
      <div className='mt-6 text-right'><AddSegmentButton
        loading={preview.loading}
        total={preview.count}
        handleClick={handleOpenModal}
      /></div>
      {/* show chart summarize */}
      <div
        className={cn(
          "transition-all duration-500 ease-in-out mt-6",
          isVisible ? "xl:max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
        )}
      >
        {isRendered && (
          <Summarize
            cityData={getCityData(overview?.data?.summarize.person_province ?? {})}
            valuesRegion={formatRegionData(overview?.data?.summarize.person_region ?? {}).values}
            labelsRegion={formatRegionData(overview?.data?.summarize.person_region ?? {}).labels}
            loading={overview.loading}
            dataAge={overview.data?.summarize.age_gender}
            dataGender={overview.data?.summarize.gender}
            className="mt-0"
          />
        )}
      </div>
      <div className={cn('flex items-center h-[32px]', !isVisible && '-mt-[56px]')}>
        <ShowResult total={preview.count} />
      </div>
      <UserTable
        loading={preview.loading}
        rowData={preview.items ?? []}
        count={preview.count ?? 0}
        isDataset={overview.data?.datatype === 'DATASET'}
      />
    </>
  );
};
export default PersonaAudience;
