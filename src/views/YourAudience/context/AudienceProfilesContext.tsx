import { createContext, ReactNode, useContext, useState } from "react";

export interface AudienceProfilesContextProps {
  selectedProfiles: number[];
  setSelectedProfiles: React.Dispatch<React.SetStateAction<number[]>>;
}

const AudienceProfilesContext = createContext<AudienceProfilesContextProps>({
  selectedProfiles: [],
  setSelectedProfiles: () => {},
});
const AudienceProfilesProvider = ({ children }: { children: ReactNode }) => {
  const [selectedProfiles, setSelectedProfiles] = useState<number[]>([]);

  return (
    <AudienceProfilesContext.Provider
      value={{
        selectedProfiles,
        setSelectedProfiles,
      }}
    >
      {children}
    </AudienceProfilesContext.Provider>
  );
};
const audienceProfilesContext = (): AudienceProfilesContextProps =>
  useContext(AudienceProfilesContext);

export { AudienceProfilesProvider, audienceProfilesContext };
