import { persona<PERSON><PERSON> } from "apis/persona";
import { socialAPI } from "apis/socialData";
import { toast } from "components/ui/use-toast";
import { createContext, Dispatch, ReactNode, SetStateAction, useContext, useState } from "react";
import { PersonaAudienceItem } from "types/Persona";
import { IYourAudienceItem } from "types/yourAudience";
import { CreateSegmentFormType, TypeDataState } from "types/YourData";

export interface AudienceContextProps {
  socialAudience: TypeDataState<IYourAudienceItem>;
  personaAudience: TypeDataState<PersonaAudienceItem>;
  setSocialAudience: Dispatch<SetStateAction<TypeDataState<IYourAudienceItem>>>;
  setPersonaAudience: Dispatch<SetStateAction<TypeDataState<PersonaAudienceItem>>>;
  handleDeleteSocialAudience: (id: string) => void;
  handleDeletePersonaAudience: (id: string) => void;
  handleEditPersonaAudience: (id: string, payload: CreateSegmentFormType) => void;
}

export const AUDIENCE_DEFAULT = {
  count: 0,
  items: [],
  loading: false,
};

const AudienceContext = createContext<AudienceContextProps>({
  socialAudience: AUDIENCE_DEFAULT,
  personaAudience: AUDIENCE_DEFAULT,
  setSocialAudience: () => {},
  setPersonaAudience: () => {},
  handleDeleteSocialAudience: () => {},
  handleDeletePersonaAudience: () => {},
  handleEditPersonaAudience: () => {},
});
const AudienceProvider = ({ children }: { children: ReactNode }) => {
  const [socialAudience, setSocialAudience] =
    useState<TypeDataState<IYourAudienceItem>>(AUDIENCE_DEFAULT);
  const [personaAudience, setPersonaAudience] =
    useState<TypeDataState<PersonaAudienceItem>>(AUDIENCE_DEFAULT);
  const handleDeleteSocialAudience = async (id: string) => {
    try {
      await socialAPI.deleteAudience(id);
      setSocialAudience((prev) => ({
        ...prev,
        items: prev.items.filter((item) => item.fb_uid !== id),
      }));
    } catch (error: any) {
      toast({
        title: String(error.detail),
        status: "error",
        duration: 3000,
      });
    }
  };
  const handleEditPersonaAudience = async (id: string, payload: CreateSegmentFormType) => {
    try {
      const response = await personaAPI.updateAudience(id, payload);
      setPersonaAudience((prev) => ({
        ...prev,
        items: prev.items.map((item) => (item.id === response.id ? { ...response } : item)),
      }));
      toast({
        title: "Update Persona Audience Success",
        status: "success",
        duration: 3000,
      });
    } catch (error: any) {
      toast({
        title: String(error.detail),
        status: "error",
        duration: 3000,
      });
    }
  };
  const handleDeletePersonaAudience = async (id: string) => {
    try {
      await personaAPI.deleteAudience(id);
      setPersonaAudience((prev) => ({
        ...prev,
        items: prev.items.filter((item) => item.id !== +id),
      }));
      toast({
        title: "Delete Persona Audience Success",
        status: "success",
        duration: 3000,
      });
    } catch (error: any) {
      toast({
        title: String(error.detail),
        status: "error",
        duration: 3000,
      });
    }
  };

  return (
    <AudienceContext.Provider
      value={{
        socialAudience,
        personaAudience,
        setSocialAudience,
        setPersonaAudience,
        handleDeleteSocialAudience,
        handleDeletePersonaAudience,
        handleEditPersonaAudience,
      }}
    >
      {children}
    </AudienceContext.Provider>
  );
};
const audienceContext = (): AudienceContextProps => useContext(AudienceContext);
export { AudienceProvider, audienceContext };
