import { RiMenu2Line } from "@remixicon/react";
import { Sheet, SheetContent, SheetTrigger } from "components/ui/sheet";
import MainSidebar from "./MainSidebar";
import { useState } from "react";

const SidebarSheet = () => {
  const [open, setOpen] = useState(false);

  const toggleSidebar = () => setOpen((prev) => !prev);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger>
        <RiMenu2Line color="#14151A" size={20} />
      </SheetTrigger>
      <SheetContent isCloseIcon={false} className="p-0 overflow-auto !max-w-[269px]" side={"left"}>
        <MainSidebar onCloseSidebar={toggleSidebar} />
      </SheetContent>
    </Sheet>
  );
};
export default SidebarSheet;
