import { Suspense } from "react";
import styled from "styled-components";
import { Link, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { sidebarStore } from "store/redux/sidebar/slice";
import { subscriptionStore } from "store/redux/subscription/slice";

import { Box } from "components/Box";
import LoadingFavicon from "components/Loading/LoadingFavicon";

import useFeatures from "hooks/useFeatures";
import { NavListProps } from "layout/type";
import { cn, handleCheckRouter } from "utils/utils";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { useAppSelector } from "../../store";
import { TNotificationType } from "../../types/notification";
import { toast } from "../../components/ui/use-toast";
import { tracking } from '../../utils/Tracking/tracking';

interface Props {
  item: NavListProps;
  setPathActive?: any;
  showLine?: boolean;
  permission_code?: FEATURE_PERMISSION_KEY;
  onCloseSidebar?: () => void;
}

const NavItem = (props: Props) => {
  const { item, showLine } = props;
  const { title, icon, path, permission_code } = item;
  const { user } = useAppSelector((state) => state.auth);
  const { collapse } = useSelector(sidebarStore);
  const { is_active } = useSelector(subscriptionStore);
  const { countNoti } = useAppSelector((state) => state.notification);
  const { last_sub } = useAppSelector(subscriptionStore);
  const { pathname } = useLocation();
  const isActiveItem = handleCheckRouter(pathname).path === path;

  //check permission feature
  const isEnable = permission_code
    ? useFeatures(FEATURE_PERMISSION_KEY[permission_code])?.is_enabled
    : true;

  const trackingSidebar = (path: string) => {
    if (isEnable) {
      tracking(
        {
          eventName: 'nav_click',
          params: {
            user_id: user?.uuid,
            valueTracking: path
          }
        }
      );
    }
  };

  const handleRenderNoti = (item: NavListProps) => {
    const count =
      item.name?.reduce(
        (acc, name) => acc + (countNoti[name as keyof TNotificationType] || 0),
        0
      ) ?? 0;
    return count > 0 ? (
      <Link
        onClick={()=>{
          props.onCloseSidebar && props.onCloseSidebar();
          trackingSidebar(path);
        }}
        to={is_active ? path : "plan/upgrade"}
        target={item.titleCode === 'user_manual' ? '_blank' : '_self'}
        className={cn(
          "text-sm block w-full px-2 py-1.5",
          icon ? "flex flex-row gap-2 items-center" : "",
          showLine ? "text-secondary" : "text-foreground",
          isActiveItem && "text-foreground hover:text-[#5314a3]"
          // !isEnable && "pointer-events-none"
        )}
      >
        <Box className="gap-1">
          {icon && <span className="w-5 h-5">{icon}</span>}
          {!collapse && <span className={cn("text-left flex-1")}>{title}</span>}
          {!isEnable && <span>{isEnable}</span>}
          {count > 0 && (
            <div className="py-0.5 px-1 rounded-sm bg-[#ecdffb] text-[#5A18BF] mr-5">+{count}</div>
          )}
        </Box>
      </Link>
    ) : (
      <Link
        to={is_active ? path : "plan/upgrade"}
        className={cn(
          "text-sm block w-full px-2 py-1.5",
          icon ? "flex flex-row gap-2 items-center" : "",
          showLine ? "text-secondary" : "text-foreground",
          isActiveItem && "text-foreground hover:text-[#5314a3]"
          // !isEnable && "pointer-events-none"
        )}
        target={item.titleCode === 'user_manual' ? '_blank' : '_self'}
        onClick={() => {
          props.onCloseSidebar && props.onCloseSidebar();
          trackingSidebar(path);
          if (!is_active) {
            toast({
              title: `Your ${
                last_sub?.plan_code ?? ""
              } account has expired. Please upgrade your plan or re-subscribe.`,
              status: "error",
              duration: 3000,
            });
          }
        }}
      >
        <Box className="gap-1">
          {icon && <span className="w-5 h-5">{icon}</span>}
          {!collapse && <span className={cn("text-left flex-1")}>{title}</span>}
          {!isEnable && <span>{isEnable}</span>}
        </Box>
      </Link>
    );
  };

  return (
    <NavItemWrapperStyle
      className={cn(
        "relative group rounded-md",
        isActiveItem && "active",
        isActiveItem && !collapse && "rounded-[8px] bg-active",
        showLine && "ml-1.5",
        isActiveItem ? "hover:bg-[#ecdffb]" : "hover:bg-[rgba(10,15,41,0.04)]"
      )}
    >
      <Suspense fallback={<LoadingFavicon />}>{handleRenderNoti(item)}</Suspense>

      {showLine && (
        <div
          className={cn(
            "w-[2px] h-2 rounded-full absolute bg-primary -left-[7px] top-1/2 transform -translate-y-1/2",
            `${isActiveItem ? "block" : "hidden group-hover:block"}`
          )}
        />
      )}
    </NavItemWrapperStyle>
  );
};

const NavItemWrapperStyle = styled("div")({
  "&.active svg path": {
    fill: "#924FE8",
    fillOpacity: 1,
  },
});
export default NavItem;
