import styled from "styled-components";
import map from "lodash/map";

import { useSelector } from "react-redux";
import { sidebarStore } from "store/redux/sidebar/slice";

import {
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
} from "components/ui/accordion";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "components/ui/tooltip";
import NavItem from "./Navtem";

import { NavListProps, NavSectionProps } from "../type";
import { cn } from "utils/utils";
import SidebarCompact from "layout/SidebarCompact";
import { Fragment } from "react/jsx-runtime";
import { handleCheckRouter } from "utils/utils";
import { useLocation } from "react-router-dom";
import React from "react";
import { TooltipPortal } from '@radix-ui/react-tooltip';

type TIcon = React.ReactElement | undefined;

interface INavHeader {
  icon: TIcon;
  subheader: string;
  parentActive: boolean;
}

const NavSection = ({ navConfig, onCloseSidebar }: NavSectionProps) => {
  const { collapse } = useSelector(sidebarStore);
  const { pathname } = useLocation();

  const renderIcon = (icon: TIcon, active: boolean) => {
    return icon && <IconActiveStyle className={cn(active && "active")}>{icon}</IconActiveStyle>;
  };

  const renderNavItem = (items: NavListProps[]) => {
    return (
      <div className="border-l-[1px] ml-[18px] flex flex-col gap-1">
        {map(items, (item: NavListProps, idx: number) => {
          return <NavItem onCloseSidebar={onCloseSidebar} key={idx} item={item} showLine={true} />;
        })}
      </div>
    );
  };

  const renderFullSidebar = ({
    icon,
    subheader,
    parentActive,
    items,
  }: INavHeader & { items: NavListProps[] }) => {
    return (
      <Accordion defaultValue={subheader} type="single" collapsible className="w-full">
        <AccordionItem className="py-0.5 px-2" value={subheader}>
          {collapse ? (
            <div className="w-full px-2 py-2">
              <SidebarCompact
                trigger={
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>{renderIcon(icon, parentActive)}</TooltipTrigger>
                      <TooltipPortal>
                        <TooltipContent>
                          <p>{subheader}</p>
                        </TooltipContent>
                      </TooltipPortal>
                    </Tooltip>
                  </TooltipProvider>
                }
                subHeader={subheader}
                items={items}
              />
            </div>
          ) : (
            <AccordionTrigger className="flex px-2 py-1.5 flex-row items-center justify-between w-full">
              <div className={cn(icon && !collapse && "flex flex-row gap-2 items-center ")}>
                {renderIcon(icon, parentActive)}
                <span className={cn(collapse && "opacity-0")}>{subheader}</span>
              </div>
            </AccordionTrigger>
          )}
          <AccordionContent className={cn(collapse ? "hidden" : "block")}>
            {renderNavItem(items)}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    );
  };

  return (
    <div className="flex-1">
      {map(navConfig, (list, index) => {
        const { subheader, items, icon } = list;
        const parentActive = handleCheckRouter(pathname).subHeader === subheader;
        return (
          <Fragment key={index}>
            {subheader ? (
              renderFullSidebar({ icon, subheader, parentActive, items })
            ) : (
              <div className="px-2 py-0.5">
                <NavItem
                  item={{
                    title: items[0].title,
                    path: items[0].path,
                    icon: items[0].icon,
                    permission_code: items[0].permission_code,
                    titleCode: items[0]?.titleCode ?? ''
                  }}
                  showLine={false}
                  onCloseSidebar={onCloseSidebar}
                />
              </div>
            )}
          </Fragment>
        );
      })}
    </div>
  );
};

const IconActiveStyle = styled("div")({
  width: "1.25rem",
  height: "1.25rem",
  "&.active svg path": {
    stroke: "#924FE8",
    fillOpacity: 1,
  },
});

export default NavSection;
