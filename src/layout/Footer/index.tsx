import FooterTop from "./FooterTop";
import FooterBottom from "./FooterBottom";
import { useCallback, useEffect, useState } from "react";
import debounce from "lodash/debounce";
import { cn } from "utils/utils";

const FooterWrapper = () => {
  const [_isScrollBottom, setIsScrollBottom] = useState(false);

  const handleScroll = useCallback(
    debounce(() => {
      const scrollPosition = window.scrollY + window.innerHeight;
      const pageHeight = document.documentElement.scrollHeight;

      if (scrollPosition >= pageHeight - 100) {
        setIsScrollBottom(true);
      } else {
        setIsScrollBottom(false);
      }
    }, 100),
    []
  );

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  return (
    <div
      className={cn(
        "mt-6 w-full bg-custom-secondary z-1 overflow-hidden transition-all duration-300 rounded-2xl"
      )}
    >
      <FooterTop />
      <FooterBottom />
    </div>
  );
};

export default FooterWrapper;
