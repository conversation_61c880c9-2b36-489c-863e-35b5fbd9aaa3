import { RiMailLine, RiMapPinLine, Ri<PERSON><PERSON>engerFill, RiPhoneLine } from '@remixicon/react';
import IconFavicon from 'assets/icons/IconFavicon';
import LogoSvg from 'assets/icons/LogoSvg';
import { Box } from 'components/Box';
import { Link } from 'react-router-dom';

const FooterTop = () => {
  const facebook_link = import.meta.env.REACT_APP_FACEBOOK;
  const landingPage = 'https://big360.ai';
  return (
    <Box className="w-full justify-between flex-col lg:flex-row items-start mb-2 pt-4 px-8">
      <Box variant="col-start" className="gap-2 flex-1">
        <div className="relative items-center gap-2 flex flex-row md:flex">
          <IconFavicon />
          <LogoSvg />
        </div>
        <div className="text-primary text-sm font-medium">Big360 Inc.</div>
        <p className="text-secondary font-medium text-sm">
          EIN Number: 98-1841781
        </p>
        <Box className="gap-2 justify-start text-secondary font-medium text-sm">
          <RiMapPinLine size={16} />
          <span>1001 S. Main St. STE 700, Kalispell, MT 59901</span>
        </Box>
        <a href="tel:+14063087751">
          <Box className="gap-2 justify-start text-secondary font-medium text-sm">
            <RiPhoneLine size={16} />
            <span>+14063087751</span>
          </Box>
        </a>
        <a href="mailto:<EMAIL>">
          <Box className="gap-2 justify-start text-secondary font-medium text-sm">
            <RiMailLine size={16} />
            <EMAIL>
          </Box>
        </a>
      </Box>
      <Box
        variant={'col-start'}
        className="gap-6 max-[1200px]:w-[270px] max-[1200px]:max-w-[270px] xl:max-w-full lg:flex-1 lg:gap-3"
      >
        <Box className="items-start flex-col lg:flex-row max-[1200px]:w-full min-[1200px]:w-4/5">
          <Box variant="col-start" className="gap-2 w-fit">
            <div className="text-primary text-sm font-medium">Our Company</div>
            <Link to={landingPage + '/about-us'} className="text-secondary text-sm font-normal">About Us</Link>
            <Link to={landingPage + '/contact'} className="text-secondary text-sm font-normal">Contact</Link>
            <Link
              to={landingPage + '/term-of-service'}
              className="text-secondary text-sm font-normal"
            >Terms of Service</Link>
          </Box>
          <Box variant="col-start" className="gap-2 w-fit">
            <div className="text-primary text-sm font-medium">Policy</div>
            <Link
              to={landingPage + '/refund-policy'}
              className="text-secondary text-sm font-normal"
            >Refund Policy</Link>
            <Link
              to={landingPage + '/cancellation-policy'}
              className="text-secondary text-sm font-normal"
            >Cancellation Policy</Link>
            <Link
              to={landingPage + '/privacy-policy'}
              className="text-secondary text-sm font-normal"
            >Privacy Policy</Link>
          </Box>
        </Box>
        <Box className="flex-col items-start gap-2">
          <div className="text-primary text-sm font-semibold">Join our community</div>
          <SocialLink
            url={facebook_link}
            icon={<RiMessengerFill size={16} />}
            text="Messenger Support"
          />
        </Box>
      </Box>
    </Box>
  );
};

export default FooterTop;

const SocialLink = ({ url, icon, text }: {url: string; icon: JSX.Element; text: string}) => (
  <Link to={url} target="_blank">
    <Box className="gap-2 justify-start text-primary font-medium text-sm">
      {icon}
      <span>{text}</span>
    </Box>
  </Link>
);
