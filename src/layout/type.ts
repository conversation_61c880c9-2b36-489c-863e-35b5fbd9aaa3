import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import { ReactElement } from "react";

export type NavListProps = {
  title: string;
  path: string;
  icon?: ReactElement;
  info?: ReactElement;
  name?: string[];
  caption?: string;
  disabled?: boolean;
  roles?: string[];
  children?: any;
  permission_code?: FEATURE_PERMISSION_KEY;
  titleCode?: string;
};

export type NavItemProps = {
  item: NavListProps;
  depth: number;
  open: boolean;
  active: boolean;
  isCollapse?: boolean;
};

export interface NavConfig {
  subheader: string;
  items: NavListProps[];
  icon?: ReactElement;
}
export interface NavSectionProps {
  isCollapse?: boolean;
  navConfig: NavConfig[];
  onCloseSidebar?: () => void;
}
