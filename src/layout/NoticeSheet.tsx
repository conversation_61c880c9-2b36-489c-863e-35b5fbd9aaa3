import { RiNotification3Line } from "@remixicon/react";
import { DrawerWrap } from "components/DrawerWrap";
import { useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "store";
import { setIsNewNoti } from "store/redux/notification/slice";
import { cn, handlePostMessage } from "utils/utils";
import { Notification } from "components/Notification";
import { useEffect } from "react";
import { getMe } from "store/redux/auth/slice";

const NoticeSheet = () => {
  const { pathname } = useLocation();
  const dispatch = useAppDispatch();
  const { unreadCount, isNewNoti } = useAppSelector((state) => state.notification);

  const checkPathCrm = pathname.includes("crm360");

  useEffect(() => {
    if (isNewNoti) {
      dispatch(getMe()).unwrap();
      dispatch(setIsNewNoti(false));
    }
  }, [isNewNoti]);

  return (
    <>
      {checkPathCrm ? (
        <div
          className={
            "cursor-pointer relative before:absolute before:top-0 before:right-[1px] before:rounded-full before:bg-[#F53E3E] before:border-[2px] before:border-white before:w-[10px] before:h-[10px] "
          }
          onClick={() => {
            handlePostMessage("notification/openNotiDrawer", true);
          }}
        >
          <RiNotification3Line size={24} color="#0F1324" className="p-1  rounded-full" />
        </div>
      ) : (
        <DrawerWrap
          trigger={
            <div
              className={cn(
                "cursor-pointer relative",
                unreadCount > 0 &&
                  "before:absolute before:top-0 before:right-[1px] before:rounded-full before:bg-[#F53E3E] before:border-[2px] before:border-white before:w-[10px] before:h-[10px]"
              )}
              onClick={() => {
                dispatch(setIsNewNoti(false));
              }}
            >
              <RiNotification3Line size={24} color="#0F1324" className="p-1  rounded-full" />
            </div>
          }
          content={<Notification />}
        />
      )}
    </>
  );
};

export default NoticeSheet;
