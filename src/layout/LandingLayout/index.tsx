import IconFavicon from 'assets/icons/IconFavicon';
import LogoSvg from 'assets/icons/LogoSvg';
import { Outlet } from 'react-router-dom';
import React from 'react';

interface ILandingLayoutProps {
  hideNav?: boolean;
}

const LandingLayout: React.FC<ILandingLayoutProps> = ({ ...props }: ILandingLayoutProps) => {
  const { hideNav = false } = props;
  return (
    <div className="w-screen h-screen flex flex-col">
      {!hideNav && <a href={'/'} className="flex px-6 gap-2 p-[1px] py-4 items-center sm:px-[66px]">
        <IconFavicon />
        <LogoSvg color="hsla(229, 41%, 10%, 0.6)" />
      </a>}
      <div className="w-full min-w-[200px] mx-auto p-6 flex-1 max-w-[440px]">
        <Outlet />
      </div>
    </div>
  );
};

export default LandingLayout;
