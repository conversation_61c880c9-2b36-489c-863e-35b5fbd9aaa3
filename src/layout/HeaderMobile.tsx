import { useEffect, useRef, useState } from "react";
import { RiCloseFill } from "@remixicon/react";
import { useDispatch } from "react-redux";
import { disableCollapse } from "store/redux/sidebar/slice";

import Logo from "components/Logo";

import useOutsideClick from "hooks/useClickOutSide";
import { cn } from "utils/utils";
import MainSidebar from "./MainSidebar";
import UserSheet from "./UserLayout/UserSheet";
import NoticeSheet from "./NoticeSheet";
import SidebarSheet from "./SidebarSheet";

const HeaderMobile = ({ isMobile }: { isMobile: boolean }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const dispatch = useDispatch();
  const ref = useRef(null);
  const refSecond = useRef(null);

  useOutsideClick(ref, refSecond, () => {
    setIsOpen(false);
  });

  useEffect(() => {
    if (isMobile) {
      dispatch(disableCollapse());
    }
  }, [isMobile]);

  return (
    <>
      <div className="sticky top-0 z-50 bg-background w-full flex flex-row items-center">
        <div className="flex items-center relative justify-center w-full">
          <div className="absolute left-0 right-0 px-4 w-full items-center justify-between flex">
            <SidebarSheet />
            <div className="flex items-center gap-2">
              <NoticeSheet />
              <UserSheet />
            </div>
          </div>
          <Logo className="flex flex-1 justify-center" />
        </div>
      </div>

      <div
        ref={ref}
        className={cn(
          "fixed top-0 left-0 z-[9999] px-4 py-5 bg-white rounded-lg transition-transform duration-700 h-screen",
          !isOpen && "-translate-x-full"
        )}
      >
        <MainSidebar />
        <div
          className="absolute top-0 right-0 -translate-x-full translate-y-1/2 cursor-pointer"
          onClick={() => setIsOpen(false)}
        >
          <RiCloseFill color="#14151A" />
        </div>
      </div>
    </>
  );
};

export default HeaderMobile;
