import { RiBook2Line, RiDatabase2Line, RiSparklingLine } from '@remixicon/react';
import AvatarIcon from 'assets/icons/AvatarIcon';
import CRMIcon from 'assets/icons/CRMIcon';
import SocialDataIcon from 'assets/icons/SocialDataIcon';
import { FEATURE_PERMISSION_KEY } from 'constants/permission/permissionPlan';
import { NotificationAction } from '../types/notification';

export const IS_DEV = import.meta.env.REACT_APP_DEPLOY_TAG === 'development';
export const IS_PRODUCTION = import.meta.env.REACT_APP_DEPLOY_TAG === 'production';

const SidebarConfig = () => {
  const userManualLink = import.meta.env.REACT_APP_USER_MANUAL;

  return [
    // {
    //   subheader: "",
    //   items: [
    //     {
    //       title: "Dashboard",
    //       path: "/dashboard",
    //       icon: <DashboardIcon />,
    //     },
    //   ],
    // },
    {
      subheader: "Audiences Finder",
      items: [
        {
          title: "Social Data",
          path: "/social-data",
        },
        {
          title: "Social Persona",
          path: "/social-persona",
        },
        {
          title: "Live Post Analysis",
          path: "/live-post",
        },
        {
          title: "Ads Post Analysis",
          path: "/ads-post",
          permission_code: FEATURE_PERMISSION_KEY.SCD_VIEW_ADPOST_COMMUNITY,
        },
        {
          title: "Request Audience",
          path: "/request-audience",
          name: [NotificationAction.REQUEST_AUDIENCE],
          permission_code: FEATURE_PERMISSION_KEY.SCD_REQUEST_AUDIENCE,
        },
        {
          title: "Work Persona",
          path: "/work-persona",
        }
      ],
      icon: <SocialDataIcon />,
    },
    {
      subheader: "Data management",
      items: [
        {
          title: "Your Audience",
          path: "/your-audience",
          name: [NotificationAction.YOUR_AUDIENCE_SOCIAL, NotificationAction.YOUR_AUDIENCE_WORK],
          permission_code: FEATURE_PERMISSION_KEY.YOUR_AUDIENCE,
        },
        {
          title: "Your Segment",
          path: "/your-segments",
          name: [NotificationAction.YOUR_SEGMENT_SOCIAL, NotificationAction.YOUR_SEGMENT_WORK],
          permission_code: FEATURE_PERMISSION_KEY.YOUR_SEGMENT,
        },
        {
          title: "Data Processing",
          path: "/data-processing/social-processing",
        },
      ],
      icon: <AvatarIcon />,
    },
    {
      subheader: "Enrichment Solution",
      items: [
        {
          title: "Data enrichment",
          path: "/enrichment",
          name: [NotificationAction.DATA_ENRICHMENT],
        },
      ],
      icon: <RiDatabase2Line color="#0F1324" opacity={0.6} size={20} />,
    },
    {
      subheader: "CRM360",
      items: [
        {
          title: "Contact List",
          path: "/crm360/contact",
        },
        {
          title: "Facebook Ads",
          path: "/crm360/facebook",
        },
        {
          title: "Tiktok Ads",
          path: "/crm360/tiktok-ads",
        },
        // {
        //   title: "Zalo Ads",
        //   path: "/crm360/zalo",
        // },
        // {
        //   title: "Email",
        //   path: "/crm360/email",
        // },
      ],
      icon: <CRMIcon />,
    },
    ...(IS_DEV ? [{
      subheader: "",
      items: [
        {
          title: "Big360 Assistant",
          path: "/big360-assistant",
          icon: <RiSparklingLine color={'#0F1324'} fillOpacity={'0.6'} size={20} />,
        },
      ],
    }] : []),

    {
      subheader: "",
      items: [
        {
          title: "User Manual",
          path: `${userManualLink ?? '/not-found'}`,
          titleCode: "user_manual",
          icon: <RiBook2Line color={'#0F1324'} fillOpacity={'0.6'} size={20} />,
        },
      ],
    },
  ];
};

export default SidebarConfig;
