import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";
import { NavListProps } from "./type";
import { Link, useLocation } from "react-router-dom";
import { handleCheckRouter } from "utils/utils";

type Props = {
  subHeader: string;
  items: NavListProps[];
  trigger: React.ReactNode;
};

const SidebarCompact: React.FC<Props> = ({ items, subHeader, trigger }) => {
  const { pathname } = useLocation();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>{trigger}</DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="min-w-40 ml-10 -translate-y-9 rounded-sm">
        <DropdownMenuLabel>{subHeader}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {items.map((item, index) => (
          <Link key={index} to={item.path}>
            <DropdownMenuItem
              className={
                handleCheckRouter(pathname).path === item.path
                  ? "bg-gray-200 relative focus:text-[#873DE680] focus:bg-[#ECDFFB] after:absolute after:bg-primary-hover after:top-0 after:left-0 after:rounded-[10px] after:contents-[''] after:w-1 after:h-full"
                  : ""
              }
            >
              {item.title}
            </DropdownMenuItem>
          </Link>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default SidebarCompact;
