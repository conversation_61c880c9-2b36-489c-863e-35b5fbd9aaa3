import {
  RiA<PERSON>untCircleLine,
  RiBankCardLine,
  RiCurrencyLine,
  RiLogoutBoxLine,
} from "@remixicon/react";
import AvatarByName from "components/AvatarByName";
import { Button } from "components/ui/button";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "components/ui/sheet";
import UserAvatarInfo from "layout/UserAvatarInfo";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "store";
import { logout } from "store/redux/auth/slice";
import { subscriptionStore } from "store/redux/subscription/slice";
import { PATH_DASHBOARD } from "types/path";

const routerConfig = [
  {
    name: "Profile",
    path: PATH_DASHBOARD.user.profile,
    icon: <RiAccountCircleLine size={16} className="flex-shrink-0" />,
  },
  {
    name: "Upgrade Plan",
    path: PATH_DASHBOARD.plan[""],
    icon: <RiBankCardLine size={16} className="flex-shrink-0" />,
  },
  {
    name: "Transaction",
    path: PATH_DASHBOARD.user.transaction,
    icon: <RiCurrencyLine size={16} className="flex-shrink-0" />,
  },
];

const UserSheet = () => {
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const { last_sub, is_active } = useSelector(subscriptionStore);

  const handleLogout = () => dispatch(logout());

  return (
    <Sheet>
      <SheetTrigger>
        <AvatarByName name={user.full_name} urlImage={user.avatar} />
      </SheetTrigger>
      <SheetContent
        isCloseIcon={false}
        className="p-4 overflow-auto !max-w-[269px] flex flex-col"
        side={"right"}
      >
        <div className="flex-1">
          <div className="flex flex-col items-start border-b pb-4">
            <UserAvatarInfo user={user} last_sub={last_sub} is_active={is_active} />
            <span className="font-medium mt-2">{user.full_name}</span>
            <span className="text-[#6B7183] text-xs">{user.email}</span>
          </div>
          <div className="flex flex-col mt-2.5 gap-2 px-1">
            {routerConfig.map((item) => (
              <Link
                to={item.path}
                key={item.name}
                className="flex gap-0.5 text-sm rounded-md hover:bg-secondary font-semibold items-center px-1 py-2.5"
              >
                <div className="p-0.5">{item.icon}</div>
                {item.name}
              </Link>
            ))}
          </div>
        </div>
        <Button
          onClick={handleLogout}
          variant={"destructive"}
          className="text-[#F53E3E] bg-[#FFD9D9] hover:bg-[#eaa3a3] w-full"
        >
          <RiLogoutBoxLine size={20} />
          Logout
        </Button>
      </SheetContent>
    </Sheet>
  );
};
export default UserSheet;
