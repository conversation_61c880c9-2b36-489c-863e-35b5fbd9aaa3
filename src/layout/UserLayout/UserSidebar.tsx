import { useSelector } from "react-redux";
import { sidebarStore } from "store/redux/sidebar/slice";
import { RiArrowRightLine } from "@remixicon/react";

import { Card, CardContent, CardDescription, CardFooter, CardHeader } from "components/ui/card";
import { But<PERSON> } from "components/ui/button";
import { Progress } from "components/ui/progress";
import UserSidebarPanel from "./UserSidebarPanel";
import { Link } from "react-router-dom";
import { subscriptionStore } from "store/redux/subscription/slice";

const UserSidebar = () => {
  const { collapse } = useSelector(sidebarStore);
  const { last_sub } = useSelector(subscriptionStore);

  // Initialize variables for totalDays and percentage
  let percentage = 0;
  let daysLeft = 0;

  if (last_sub?.end_date && last_sub?.start_date) {
    const endDate = new Date(last_sub.end_date);
    const startDate = new Date(last_sub.start_date);
    const now = new Date();
    const usedTime = now.getTime() - startDate.getTime();
    const totalTime = endDate.getTime() - startDate.getTime();
    daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    // Ensure percentage stays within 0-100
    percentage = Math.min(Math.max((usedTime / totalTime) * 100, 0), 100);
  }

  return (
    <>
      {!collapse && last_sub?.plan_code == "TRIAL" && (
        <Card className="bg-[#EDFDF4] border-none rounded-lg mx-4">
          <CardHeader className="pb-2 px-4 pt-3">
            <CardDescription className="flex flex-row justify-between">
              <span>Free trial</span>
              <span>{daysLeft > 0 ? `${daysLeft} days left` : "Trial expired"}</span>
            </CardDescription>
          </CardHeader>
          <CardContent className="px-4 py-0">
            <Progress className="bg-[#E9EAEC]" value={percentage} />
          </CardContent>
          <CardFooter className="p-4">
            <Link to={"/plan/upgrade"} className="w-full">
              <Button
                size="icon"
                className="w-full bg-white rounded-[16px] border-[1px] flex flex-row items-center gap-2 text-gray-950 hover:bg-[#0a0f2911]"
              >
                <span>Upgrade Plan</span>
                <RiArrowRightLine color="#0F1324" size={16} opacity={0.6} />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      )}
      <UserSidebarPanel />
    </>
  );
};

export default UserSidebar;
