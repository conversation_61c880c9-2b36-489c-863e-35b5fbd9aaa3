import { useRef, useState } from "react";
import {
  RiAccountCircleLine,
  RiBankCardLine,
  RiCurrencyLine,
  RiLogoutBoxRLine,
  RiMore2Line,
  RiNotification3Line,
} from "@remixicon/react";
import { useSelector } from "react-redux";
import { logout } from "store/redux/auth/slice";
import { sidebarStore } from "store/redux/sidebar/slice";
import { useAppDispatch, useAppSelector } from "store";
import { useNavigate } from "react-router-dom";
import { subscriptionStore } from "store/redux/subscription/slice";

import { Box } from "components/Box";
import { Button } from "components/ui/button";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "components/ui/hover-card";
import { Card } from "components/ui/card";
import AvatarByName from "components/AvatarByName";

import useOutsideClick from "hooks/useClickOutSide";
import { cn } from "utils/utils";
import { PATH_DASHBOARD } from "types/path";
import { DrawerWrap } from "../../components/DrawerWrap";
import { setIsNewNoti } from "../../store/redux/notification/slice";
import { Notification } from "../../components/Notification";
import UserAvatarInfo from "layout/UserAvatarInfo";

const UserSidebarPanel = () => {
  const { collapse } = useSelector(sidebarStore);
  const { user } = useAppSelector((state) => state.auth);
  const { unreadCount } = useAppSelector((state) => state.notification);
  const { last_sub, is_active } = useSelector(subscriptionStore);

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [open, setOpen] = useState<boolean>(false);

  const triggerRef = useRef<any>(null);
  const bellRef = useRef<any>(null);
  const menuRef = useRef<any>(null);

  useOutsideClick(triggerRef, menuRef, () => {
    setOpen(false);
  });

  return (
    <Card
      className={cn(
        "border-none mt-2 text-primary",
        collapse ? "" : "bg-custom-secondary mx-4 p-4  rounded-lg flex flex-col gap-3"
      )}
    >
      {collapse && (
        <AvatarByName urlImage={user.avatar} name={user.full_name} className="w-12 h-12" />
      )}
      {!collapse && (
        <Box className="justify-between w-full gap-3">
          <UserAvatarInfo user={user} last_sub={last_sub} is_active={is_active} />
          <div className="flex flex-row gap-0 items-center w-fit">
            <DrawerWrap
              trigger={
                <div
                  ref={bellRef}
                  className={cn(
                    "cursor-pointer relative",
                    unreadCount > 0 &&
                      "before:absolute before:top-0 before:right-[1px] before:rounded-full before:bg-[#F53E3E] before:border-[2px] before:border-white before:w-[10px] before:h-[10px]"
                  )}
                  onClick={() => {
                    dispatch(setIsNewNoti(false));
                  }}
                >
                  <RiNotification3Line size={24} color="#0F1324" className="p-1  rounded-full" />
                </div>
              }
              content={<Notification />}
            />
            <HoverCard openDelay={100} open={open}>
              <HoverCardTrigger
                ref={triggerRef}
                className="hover:bg-tertiary cursor-pointer rounded p-1"
                onClick={() => setOpen(!open)}
              >
                <RiMore2Line size={18} className="hover:text-tertiary rounded-full" />
              </HoverCardTrigger>
              <HoverCardContent
                ref={menuRef}
                align="end"
                side="right"
                className="min-w-[100px] px-2 py-3 shadow-sm rounded-xl border border-custom-primary z-[99999]"
              >
                <div className="border-l p-2 w-[174px]">
                  <ItemPanel
                    icon={<RiAccountCircleLine size={18} className="text-secondary" />}
                    content="Account"
                    onClick={() => {
                      navigate(`${PATH_DASHBOARD.user.profile}`);
                      setOpen(false);
                    }}
                  />
                  <ItemPanel
                    icon={<RiBankCardLine size={18} />}
                    content="Upgrade Plan"
                    onClick={() => {
                      navigate(`${PATH_DASHBOARD.plan[""]}`);
                      setOpen(false);
                    }}
                  />
                  <ItemPanel
                    icon={<RiCurrencyLine size={18} />}
                    content="Transaction"
                    onClick={() => {
                      navigate(`${PATH_DASHBOARD.user.transaction}`);
                      setOpen(false);
                    }}
                  />
                  <ItemPanel
                    icon={<RiLogoutBoxRLine size={18} />}
                    content="Logout"
                    onClick={() => dispatch(logout())}
                    className="hover:text-red-500 hover:fill-red-500"
                  />
                </div>
              </HoverCardContent>
            </HoverCard>
          </div>
        </Box>
      )}
      {!collapse && (
        <Box variant="col-start" className="w-full gap-0 font-semibold">
          <span className="text-sm text-primary truncate block w-[200px]">{user.full_name}</span>
          <div className="truncate text-xs w-[200px] text-tertiary">{user.email}</div>
        </Box>
      )}
    </Card>
  );
};
export default UserSidebarPanel;

interface ItemPanelProps {
  icon: JSX.Element | React.ReactNode;
  content: string;
  className?: string;
  onClick?: () => void;
}

const ItemPanel = ({ content, icon, onClick, className }: ItemPanelProps) => (
  <Button
    variant="ghost"
    className={cn(
      "flex items-center text-sm gap-1  w-full rounded-xl p-2 justify-start text-secondary fill-[#515667] hover:text-primary hover:bg-secondary",
      className
    )}
    onClick={onClick}
  >
    {icon}
    <span children={content} />
  </Button>
);
