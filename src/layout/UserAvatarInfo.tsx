import { RiCopperCoinLine } from "@remixicon/react";
import AvatarByName from "components/AvatarByName";
import { Badge } from "components/ui/badge";
import { ILastSub } from "store/redux/subscription/slice";
import { fNumberToString } from "utils/number";
import { cn } from "utils/utils";

type Props = {
  user: {
    avatar?: string;
    full_name: string;
    credit: number;
  };
  last_sub: ILastSub | null;
  is_active: boolean | null;
};

const UserAvatarInfo = ({ user, last_sub, is_active }: Props) => (
  <div className="relative">
    <AvatarByName
      urlImage={user.avatar}
      name={user.full_name}
      className="w-16 h-16 md:w-12 md:h-12"
    />
    <div className="w-[100px] absolute -top-3 -right-[20px] translate-x-2/3">
      <Badge
        className={cn(
          "px-1 text-xs bg-success-primary text-success-subtitle rounded-sm capitalize hover:bg-success-primary w-fit",
          !is_active && "bg-error-default hover:bg-error-default text-white"
        )}
      >
        {last_sub?.plan_code?.toLocaleLowerCase() || "--"}
      </Badge>
    </div>
    <div className="w-[100px] absolute -bottom-3 -right-[20px] translate-x-2/3">
      <Badge className="px-1 text-xs rounded-sm bg-secondary md:bg-white text-primary hover:bg-white">
        <RiCopperCoinLine size={16} color="#5A18BF" />
        {fNumberToString(user.credit)}
      </Badge>
    </div>
  </div>
);
export default UserAvatarInfo;
