/**
 * Utility for loading remote modules with cache busting and retry logic
 */

interface RemoteConfig {
  url: string;
  scope: string;
  module: string;
}

interface LoadRemoteOptions {
  maxRetries?: number;
  retryDelay?: number;
  cacheBusting?: boolean;
}

/**
 * Load a remote module dynamically with cache busting support
 */
export const loadRemoteModule = async (
  config: RemoteConfig,
  options: LoadRemoteOptions = {}
): Promise<any> => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    cacheBusting = process.env.NODE_ENV === 'production'
  } = options;

  const loadWithRetry = async (attempt: number = 1): Promise<any> => {
    try {
      // Add cache busting parameter if enabled
      const remoteUrl = cacheBusting 
        ? `${config.url}?v=${Date.now()}&attempt=${attempt}`
        : config.url;

      // Check if the remote is already loaded
      if (!(config.scope in window)) {
        // Load the remote entry script
        await loadScript(remoteUrl);
      }

      // Get the container
      const container = (window as any)[config.scope];
      if (!container) {
        throw new Error(`Remote container ${config.scope} not found`);
      }

      // Initialize the container
      await container.init(__webpack_share_scopes__.default);

      // Get the module factory
      const factory = await container.get(config.module);
      
      // Return the module
      return factory();
    } catch (error) {
      console.error(`Failed to load remote module (attempt ${attempt}):`, error);
      
      if (attempt < maxRetries) {
        // Clear any cached scripts on retry
        if (cacheBusting) {
          clearRemoteCache(config.scope);
        }
        
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        return loadWithRetry(attempt + 1);
      }
      
      throw error;
    }
  };

  return loadWithRetry();
};

/**
 * Load a script dynamically
 */
const loadScript = (url: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    const existingScript = document.querySelector(`script[src="${url}"]`);
    if (existingScript) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.async = true;
    
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
    
    document.head.appendChild(script);
  });
};

/**
 * Clear cached remote modules
 */
const clearRemoteCache = (scope: string) => {
  // Remove from window
  if ((window as any)[scope]) {
    delete (window as any)[scope];
  }
  
  // Remove script tags
  const scripts = document.querySelectorAll(`script[src*="${scope}"]`);
  scripts.forEach(script => script.remove());
  
  // Clear browser cache if available
  if ('caches' in window) {
    caches.keys().then(names => {
      names.forEach(name => {
        if (name.includes(scope) || name.includes('remoteEntry')) {
          caches.delete(name);
        }
      });
    });
  }
};

/**
 * Create a dynamic import function for remote modules
 */
export const createRemoteImport = (baseUrl: string, scope: string) => {
  return (module: string, options?: LoadRemoteOptions) => {
    return loadRemoteModule(
      {
        url: `${baseUrl}/remoteEntry.js`,
        scope,
        module
      },
      options
    );
  };
};

/**
 * Check if remote is available
 */
export const isRemoteAvailable = async (url: string, timeout: number = 5000): Promise<boolean> => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
      cache: 'no-cache'
    });
    
    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.warn(`Remote not available at ${url}:`, error);
    return false;
  }
};

// Global declarations for webpack federation
declare global {
  interface Window {
    [key: string]: any;
  }
  
  const __webpack_share_scopes__: {
    default: any;
  };
}
