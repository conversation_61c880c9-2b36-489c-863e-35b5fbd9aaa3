export const getAccessTokenFromLS = () => localStorage.getItem("access-token") || "";
export const getRefreshTokenFromLS = () => localStorage.getItem("refresh-token") || "";

export const getTokens = () => ({
  accessToken: getAccessTokenFromLS(),
  refreshToken: getRefreshTokenFromLS(),
});

export const clearLS = () => {
  localStorage.removeItem("access-token");
  localStorage.removeItem("refresh-token");
  localStorage.removeItem("profile");
};

export const setAccessTokenToLS = (access_token: string) => {
  localStorage.setItem("access-token", access_token);
};
export const setRefreshTokenToLS = (refresh_token: string) => {
  localStorage.setItem("refresh-token", refresh_token);
};
export const setTokensToLS = (access_token: string, refresh_token: string) => {
  setAccessTokenToLS(access_token);
  setRefreshTokenToLS(refresh_token);
};

export const redirectTo = (document: any, path: string) => {
  document.location.href = path;
};
