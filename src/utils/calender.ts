import { addZero } from "utils/number";

const getDaysInMonth = (month: number, year: number): { label: string; value: string }[] => {
  const daysInMonth = new Date(year, month, 0).getDate();

  return Array.from({ length: daysInMonth }, (_, index) => {
    const day = index + 1;
    return {
      label: addZero(day).toString(),
      value: addZero(day),
    };
  });
};
const getMonths = () => {
  // Lấy tên tháng đầy đủ
  const formatter = new Intl.DateTimeFormat("en-US", { month: "long" });
  return Array.from({ length: 12 }, (_, i) => i + 1).map((month) => ({
    label: formatter.format(new Date(0, month - 1)),
    value: addZero(month),
  }));
};
const getYears = () =>
  Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i).map((year) => ({
    label: String(year),
    value: year,
  }));

const thisMonth = new Date().getMonth() + 1;
const thisYear = new Date().getFullYear();

export const getDateList = {
  getDaysInMonth,
  getMonths,
  getYears,
  thisMonth,
  thisYear,
};
