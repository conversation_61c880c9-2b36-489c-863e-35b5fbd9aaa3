import numeral from "numeral";

export function fNumber(number: string | number = 0) {
  return numeral(number).format();
}

export const fNumberToString = (number: string | number = 0) => {
  return number.toLocaleString();
};
export const removeFormatNumber = (numberString: string): number => {
  return Number(numberString.replace(/,/g, ""));
};

export const getTwoDecimal = (num: number) => {
  return Math.floor(num * 100) / 100;
};

export const addZero = (value: number): string => (value < 10 ? `0${value}` : value.toString());
export const isNumeric = (value: string) => {
  return /^\d+$/.test(value);
};

export function fNumberToCompact(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, "") + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, "") + "K";
  } else {
    return num.toString();
  }
}

export const fMapRadius = (num: number) => {
  return num % 1 === 0 ? num / 1000 : (num / 1000).toFixed(2);
};
