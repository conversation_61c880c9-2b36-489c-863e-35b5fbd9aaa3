import { TSelectOption } from "types/Select";

const checkAllChildren = (node: TSelectOption, isChecked: boolean): void => {
  node.checked = isChecked;
  node.children?.forEach((child: TSelectOption) => {
    checkAllChildren(child, isChecked);
  });
};

export const updateParentStatus = (nodeList: TSelectOption[]): void => {
  nodeList.forEach((node) => {
    if (node.children) {
      const allChildrenChecked = node.children.every((child: TSelectOption) => child.checked);
      node.checked = allChildrenChecked;
      updateParentStatus(node.children);
    }
  });
};

export const updateNodeStatus = ({
  nodeList,
  name,
  checked,
}: {
  nodeList: TSelectOption[];
  name: string;
  checked: boolean;
}): TSelectOption[] => {
  return nodeList.map((node) => {
    if (node.label === name) {
      node.checked = checked;
      if (node.children) {
        checkAllChildren(node, checked);
      }
    } else if (node.children) {
      node.children = updateNodeStatus({ nodeList: node.children, name, checked });
    }
    return node;
  });
};

export const collectSelectedNodes = (nodes: TSelectOption[]): TSelectOption[] => {
  const selectedNodes: TSelectOption[] = [];

  nodes.map((node) => {
    if (node.children) {
      const childChecked = node.children.filter((child) => child.checked);

      if (childChecked.length === node.children.length) {
        selectedNodes.push({ ...node, checked: true, dept: 0 });
      } else if (!node.checked && childChecked.length > 0) {
        childChecked.map(
          (child) =>
            !selectedNodes.some((selected) => selected.value === child.value) &&
            selectedNodes.push({ ...child, dept: 1 })
        );
      }
    }
    //flat tree
    if (node.checked && !node.children) {
      selectedNodes.push({ ...node, dept: 0 });
    }
  });

  return selectedNodes;
};

export const flattenTree = (
  nodes: TSelectOption[],
  parent: TSelectOption | null = null
): TSelectOption[] => {
  let flatList: TSelectOption[] = [];

  nodes.forEach((node) => {
    flatList.push({
      ...node,
      parent: parent ? parent.label : null,
    });

    if (node.children && node.children.length > 0) {
      flatList = flatList.concat(flattenTree(node.children, node));
    }
  });

  return flatList;
};
