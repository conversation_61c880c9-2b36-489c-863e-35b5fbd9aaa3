import { TSelectedDate } from "./getLabelValue";
import { TSelectOption } from "types/Select";
import { keyDOBFilter } from "types/yourAudience";
import { DATE_OPTIONS } from "constants/yourData/label";

export const formatDateSelected = (dateArray: TSelectOption[]) => {
  const { year, month, date } = dateArray.reduce((acc: any, { label, value }) => {
    if (label === "year") acc.year = value;
    if (label === "month") acc.month = value;
    if (label === "date") acc.date = value;
    return acc;
  }, {});

  return {
    YYMMDD: `${year}-${month}-${date}`,
    DDMMYY: `${date}/${month}/${year}`,
  };
};
interface TGetValueFilterProps {
  keyFilter: keyDOBFilter;
  option: string;
  obj: TSelectedDate;
}
const getValueFilter = ({ keyFilter, option, obj }: TGetValueFilterProps) => {
  let valueFilter;

  switch (option) {
    case DATE_OPTIONS.FROM_TO:
      if (obj[keyFilter.from]?.length === 3 && obj[keyFilter.to]?.length === 3) {
        const dobGte = formatDateSelected(obj[keyFilter.from]).YYMMDD;
        const dobLte = formatDateSelected(obj[keyFilter.to]).YYMMDD;

        // Only set if both are non-empty
        if (dobGte && dobLte) {
          valueFilter = {
            [keyFilter.from]: dobGte,
            [keyFilter.to]: dobLte,
          };
        }
      }
      break;

    case DATE_OPTIONS.MONTH:
      if (obj[keyFilter.month]) {
        valueFilter = {
          [keyFilter.month]: obj[keyFilter.month]
            .map((item: TSelectOption) => item.value)
            .join(","),
        };
      }
      break;

    case DATE_OPTIONS.YEAR:
      if (obj[keyFilter.year]) {
        valueFilter = {
          [keyFilter.year]: obj[keyFilter.year].map((item: TSelectOption) => item.value).join(","),
        };
      }
      break;

    default:
      valueFilter = undefined;
      break;
  }

  return valueFilter || undefined;
};

export default getValueFilter;
