import { keyDOBFilter } from "types/yourAudience";
import { TSelectedOption } from "types/Select";

const formatDateYYMMDDToObject = (dateString: string) => {
  //default value by params url
  const [year, month, day] = dateString.split("-");
  return [
    { label: "date", value: day },
    { label: "month", value: month },
    { label: "year", value: year },
  ];
};

export const formatDefaultFilter = (value: string) => {
  return value
    ? value.split(",").map((item) => ({
        label: item,
        value: item,
      }))
    : [];
};

const getDefaultFiler = (keyFilter: keyDOBFilter, objSelected: TSelectedOption) => ({
  [keyFilter.from]: objSelected[keyFilter.from]
    ? formatDateYYMMDDToObject(objSelected[keyFilter.from])
    : [],

  [keyFilter.to]: objSelected[keyFilter.to]
    ? formatDateYYMMDDToObject(objSelected[keyFilter.to])
    : [],
  [keyFilter.month]: formatDefaultFilter(objSelected[keyFilter.month]),
  [keyFilter.year]: formatDefaultFilter(objSelected[keyFilter.year]),
});

export default getDefaultFiler;
