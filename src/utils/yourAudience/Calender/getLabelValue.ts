import { formatDateSelected } from "./getFilterValue";
import { TSelectOption } from "types/Select";
import { keyDOBFilter } from "types/yourAudience";
import { DATE_OPTIONS } from "constants/yourData/label";

export type TSelectedDate = { [key: string]: TSelectOption[] };
interface formatOptionProps {
  option: string;
  objDate: TSelectedDate;
  keyFilter: keyDOBFilter;
}
const formatOptionLabel = ({ option, keyFilter, objDate }: formatOptionProps) => {
  switch (option) {
    case DATE_OPTIONS.FROM_TO:
      const labelGTE =
        objDate[keyFilter.from]?.length == 3 && formatDateSelected(objDate[keyFilter.from]).DDMMYY;

      const labelLTE =
        objDate[keyFilter.to]?.length == 3 && formatDateSelected(objDate[keyFilter.to]).DDMMYY;
      return labelGTE && labelLTE ? `${labelGTE} - ${labelLTE}` : "";

    case DATE_OPTIONS.MONTH:
      const formatter = new Intl.DateTimeFormat("en-US", { month: "long" });
      const labelMonth =
        objDate[keyFilter.month]
          ?.map((item: TSelectOption) =>
            formatter.format(new Date(0, parseInt(String(item.value)) - 1))
          )
          .join(", ") ?? "";
      return labelMonth ? `Month: ${labelMonth}` : "";

    case DATE_OPTIONS.YEAR:
      const labelYear =
        objDate[keyFilter.year]?.map((item: TSelectOption) => item.value).join(",") ?? "";
      return labelYear ? `Year: ${labelYear}` : "";

    default:
      return "";
  }
};

const optionsDate = ({
  objDate,
  keyFilter,
}: {
  objDate: TSelectedDate;
  keyFilter: keyDOBFilter;
}) => [
  {
    key: DATE_OPTIONS.FROM_TO,
    label: formatOptionLabel({ option: DATE_OPTIONS.FROM_TO, objDate, keyFilter }),
  },
  {
    key: DATE_OPTIONS.MONTH,
    label: formatOptionLabel({ option: DATE_OPTIONS.MONTH, objDate, keyFilter }),
  },
  {
    key: DATE_OPTIONS.YEAR,
    label: formatOptionLabel({ option: DATE_OPTIONS.YEAR, objDate, keyFilter }),
  },
];

export default optionsDate;
