import {
  AUDIENCE_SOCIAL_FILTER,
  FILTER_LABEL,
  GENDER_OPTIONS,
} from "./../../constants/yourData/label";
import { handleSelectedValue } from "utils/filter";
import { TSelectedOption, TSelectOption } from "types/Select";
import getDefaultFiler from "utils/yourAudience/Calender/getDefaultFilter";
import formatRange from "./formatRange";
import { RELATIONSHIP_GENDER_OPTIONS } from "constants/yourData";

export interface TSocialAudienceFilter {
  isFilterCity?: boolean;
  isFilterSocialAge?: boolean;
  isFilterDOB?: boolean;
  isRelationship?: boolean;
  isGender?: boolean;
  isInterest?: boolean;
  AudienceID?: string;
  isDisable?: boolean;
}
interface Props extends TSocialAudienceFilter {
  params?: any;
  city: TSelectOption[];
  selectedOption: TSelectedOption;
  onSetParams: (key: string, value: TSelectOption[]) => void;
  setSelectedOption: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
}
const filterOptions = ({
  isFilterCity,
  isFilterSocialAge,
  isFilterDOB,
  isGender,
  isInterest,
  isRelationship,
  params,
  city,
  selectedOption,
  onSetParams,
  setSelectedOption,
}: Props) => {
  return [
    isFilterCity
      ? {
          type: "select-multi",
          isSearchable: true,
          isTruncateLabel: true,
          key: AUDIENCE_SOCIAL_FILTER.CITY,
          placeholder: FILTER_LABEL.CITY,
          options: city,
          selected: handleSelectedValue(params, AUDIENCE_SOCIAL_FILTER.CITY),
          onChange: (value: any) => onSetParams(AUDIENCE_SOCIAL_FILTER.CITY, value),
          className: "max-md:w-full w-[160px]",
        }
      : null,
    isFilterDOB
      ? {
          className: "max-md:w-full w-[220px]",
          type: "calender",
          selectedOption,
          placeholder: FILTER_LABEL.DOB,
          key_from: AUDIENCE_SOCIAL_FILTER.DOB_GTE,
          key_to: AUDIENCE_SOCIAL_FILTER.DOB_LTE,
          key_month: AUDIENCE_SOCIAL_FILTER.AGE_MONTH,
          key_year: AUDIENCE_SOCIAL_FILTER.AGE_YEAR,
          defaultValue: (() =>
            getDefaultFiler(
              {
                from: AUDIENCE_SOCIAL_FILTER.DOB_GTE,
                to: AUDIENCE_SOCIAL_FILTER.DOB_LTE,
                month: AUDIENCE_SOCIAL_FILTER.AGE_MONTH,
                year: AUDIENCE_SOCIAL_FILTER.AGE_YEAR,
              },
              params
            ))(),
          onChange: (value: any) => {
            value &&
              setSelectedOption((prev) => {
                // Remove old filter keys based on the current type of `valueFilter`
                const { dob__gte, dob__lte, month__in, year__in, ...rest } = prev;
                return {
                  ...rest,
                  ...value,
                };
              });
          },
        }
      : null,
    isGender
      ? {
          key: AUDIENCE_SOCIAL_FILTER.GENDER,
          placeholder: FILTER_LABEL.GENDER,
          type: "select-multi",
          options: GENDER_OPTIONS,
          selected: handleSelectedValue(params, AUDIENCE_SOCIAL_FILTER.GENDER),
          onChange: (value: any) => onSetParams(AUDIENCE_SOCIAL_FILTER.GENDER, value),
          className: "max-md:w-full w-[150px]",
          isTruncateLabel: true,
        }
      : null,

    isFilterSocialAge
      ? {
          className: "max-md:w-full min-w-[120px]",
          key: "filter_age_range",
          type: "age-range",
          selectedOption,
          placeholder: FILTER_LABEL.AGE_SEGMENT,
          defaultValue: {
            min: params[AUDIENCE_SOCIAL_FILTER.AGE]?.split(",")[0],
            max: params[AUDIENCE_SOCIAL_FILTER.AGE]?.split(",")[1],
          },
          onChange: (value: any) => {
            const newValue = formatRange(value.min, value.max);
            setSelectedOption((prev: any) => ({
              ...prev,
              [AUDIENCE_SOCIAL_FILTER.AGE]: newValue,
            }));
          },
        }
      : null,

    isRelationship
      ? {
          type: "select-multi",
          isTruncateLabel: true,
          key: AUDIENCE_SOCIAL_FILTER.RELATIONSHIP,
          placeholder: FILTER_LABEL.RELATIONSHIP,
          options: RELATIONSHIP_GENDER_OPTIONS,
          selected: handleSelectedValue(params, AUDIENCE_SOCIAL_FILTER.RELATIONSHIP),
          onChange: (value: any) => onSetParams(AUDIENCE_SOCIAL_FILTER.RELATIONSHIP, value),
          className: "max-md:w-full w-[170px]",
        }
      : null,
    isInterest ?
      {
        type: 'interest',
        selectedOption: {
          [AUDIENCE_SOCIAL_FILTER.CATEGORY]: selectedOption.category || params[AUDIENCE_SOCIAL_FILTER.CATEGORY],
          [AUDIENCE_SOCIAL_FILTER.SCORE_RANGE]: selectedOption.score_range || params[AUDIENCE_SOCIAL_FILTER.SCORE_RANGE]
        },
        onChange: (value: {category:string, score_range:string}) => {
          setSelectedOption((prev: any) => ({
            ...prev,
            [AUDIENCE_SOCIAL_FILTER.CATEGORY]: value.category,
            [AUDIENCE_SOCIAL_FILTER.SCORE_RANGE]: value.score_range,
          }));
        },
      }
      : null
  ];
};

export default filterOptions;
