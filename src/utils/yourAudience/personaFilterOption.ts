import { useAppSelector } from "store";
import {
  AUDIENCE_PERSONA_FILTER,
  FILTER_LABEL,
  GENDER_OPTIONS,
  regionNames,
} from "./../../constants/yourData/label";
import { handleSelectedValue } from "utils/filter";
import { TSelectedOption, TSelectOption } from "types/Select";
import getDefaultFiler from "utils/yourAudience/Calender/getDefaultFilter";

export interface TPersonaAudienceFilter {
  isFilterProvince?: boolean;
  isFilterGender?: boolean;
  isFilterDOB?: boolean;
  isFilterPersonaAge?: boolean;
  isFilterCompanyName?: boolean;
  isFilterCompanySize?: boolean;
  isFilterIndustry?: boolean;
  isFilterDepartment?: boolean;
  isFilterPosition?: boolean;
  //disable filter
  isDisable?: boolean;
}
interface Props extends TPersonaAudienceFilter {
  params?: any;
  selectedOption: TSelectedOption;
  onSetParams: (key: string, value: TSelectOption[]) => void;
  setSelectedOption: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
}
const filterOptions = ({
  isFilterProvince,
  isFilterGender,
  isFilterDOB,
  isFilterPersonaAge,
  isFilterCompanyName,
  isFilterCompanySize,
  isFilterPosition,
  isFilterIndustry,
  isFilterDepartment,
  params,
  selectedOption,
  onSetParams,
  setSelectedOption,
}: Props) => {
  const { industry, level, department, company_size } = useAppSelector((state) => state.dimensions);

  const formatPersonaOptions = (data: any[]) => {
    return data.filter((item) => Boolean(item)).map((item) => ({ label: item, value: item }));
  };
  return [
    isFilterProvince
      ? {
          className: "max-md:w-full w-[200px]",
          isTruncateLabel: true,
          type: "select-multi",
          key: AUDIENCE_PERSONA_FILTER.PERSON_PROVINCE,
          placeholder: FILTER_LABEL.PERSON_PROVINCE,
          isSearchable: true,
          options: regionNames.map((item) => ({ label: item, value: item })),
          selected: handleSelectedValue(params, AUDIENCE_PERSONA_FILTER.PERSON_PROVINCE),
          onChange: (value: any) => onSetParams(AUDIENCE_PERSONA_FILTER.PERSON_PROVINCE, value),
        }
      : null,
    isFilterDOB
      ? {
          className: "max-md:w-full w-[220px]",
          type: "calender",
          placeholder: FILTER_LABEL.DOB,
          key_from: AUDIENCE_PERSONA_FILTER.DOB_GTE,
          key_to: AUDIENCE_PERSONA_FILTER.DOB_LTE,
          key_month: AUDIENCE_PERSONA_FILTER.DOB_MONTH,
          key_year: AUDIENCE_PERSONA_FILTER.DOB_YEAR,
          selectedOption,
          defaultValue: (() =>
            getDefaultFiler(
              {
                from: AUDIENCE_PERSONA_FILTER.DOB_GTE,
                to: AUDIENCE_PERSONA_FILTER.DOB_LTE,
                month: AUDIENCE_PERSONA_FILTER.DOB_MONTH,
                year: AUDIENCE_PERSONA_FILTER.DOB_YEAR,
              },
              params
            ))(),
          onChange: (value: any) => {
            value &&
              setSelectedOption((prev) => {
                const { dob__gte, dob__lte, dob_month__in, dob_year__in, ...rest } = prev;
                return {
                  ...rest,
                  ...value,
                };
              });
          },
        }
      : null,
    isFilterPersonaAge
      ? {
          className: "max-md:w-full w-fit",
          key: "filter_age_range",
          type: "age-range",
          placeholder: FILTER_LABEL.AGE_SEGMENT,
          selectedOption,
          defaultValue: {
            min: params[AUDIENCE_PERSONA_FILTER.AGE_GTE],
            max: params[AUDIENCE_PERSONA_FILTER.AGE_LTE],
          },
          onChange: (value: any) => onSetParams(AUDIENCE_PERSONA_FILTER.AGE_GTE, value),
        }
      : null,
    isFilterGender
      ? {
          className: "max-md:w-full w-[180px]",
          isMulti: false,
          type: "select-multi",
          key: AUDIENCE_PERSONA_FILTER.GENDER,
          placeholder: FILTER_LABEL.GENDER,
          options: GENDER_OPTIONS.filter((item) => item.label !== "Other"),
          selected: handleSelectedValue(params, AUDIENCE_PERSONA_FILTER.GENDER),
          onChange: (value: any) => onSetParams(AUDIENCE_PERSONA_FILTER.GENDER, value),
        }
      : null,

    isFilterIndustry
      ? {
          type: "select-multi",
          className: "max-md:w-full w-[200px]",
          isTruncateLabel: true,
          key: AUDIENCE_PERSONA_FILTER.COMPANY_INDUSTRY,
          placeholder: FILTER_LABEL.COMPANY_INDUSTRY,
          isSearchable: true,
          options: formatPersonaOptions(industry),
          selected: handleSelectedValue(params, AUDIENCE_PERSONA_FILTER.COMPANY_INDUSTRY),
          onChange: (value: any) => onSetParams(AUDIENCE_PERSONA_FILTER.COMPANY_INDUSTRY, value),
        }
      : null,
    isFilterPosition
      ? {
          className: "max-md:w-full w-[200px]",
          type: "select-multi",
          isTruncateLabel: true,
          key: AUDIENCE_PERSONA_FILTER.POSITION_LEVEL,
          placeholder: FILTER_LABEL.POSITION_LEVEL,
          isSearchable: true,
          options: formatPersonaOptions(level),
          selected: handleSelectedValue(params, AUDIENCE_PERSONA_FILTER.POSITION_LEVEL),
          onChange: (value: any) => onSetParams(AUDIENCE_PERSONA_FILTER.POSITION_LEVEL, value),
        }
      : null,
    isFilterDepartment
      ? {
          className: "max-md:w-full w-[200px]",
          type: "select-multi",
          isTruncateLabel: true,
          key: AUDIENCE_PERSONA_FILTER.POSITION_DEPARTMENT,
          placeholder: FILTER_LABEL.POSITION_DEPARTMENT,
          options: formatPersonaOptions(department),
          selected: handleSelectedValue(params, AUDIENCE_PERSONA_FILTER.POSITION_DEPARTMENT),
          onChange: (value: any) => onSetParams(AUDIENCE_PERSONA_FILTER.POSITION_DEPARTMENT, value),
        }
      : null,

    isFilterCompanySize
      ? {
          className: "max-md:w-full w-[190px]",
          type: "select-multi",
          isTruncateLabel: true,
          key: AUDIENCE_PERSONA_FILTER.COMPANY_SIZE,
          placeholder: FILTER_LABEL.COMPANY_SIZE,
          options: formatPersonaOptions(company_size),
          selected: handleSelectedValue(params, AUDIENCE_PERSONA_FILTER.COMPANY_SIZE),
          onChange: (value: any) => onSetParams(AUDIENCE_PERSONA_FILTER.COMPANY_SIZE, value),
        }
      : null,
    isFilterCompanyName
      ? {
          className: "max-md:w-full w-[200px]",
          type: "input-filter",
          key: AUDIENCE_PERSONA_FILTER.COMPANY_NAME,
          defaultValue: params[AUDIENCE_PERSONA_FILTER.COMPANY_NAME],
          placeholder: FILTER_LABEL.COMPANY_NAME,
          selected: handleSelectedValue(params, AUDIENCE_PERSONA_FILTER.COMPANY_NAME),
          onChange: (value: any) =>
            onSetParams(AUDIENCE_PERSONA_FILTER.COMPANY_NAME, [{ label: value, value }]),
          handleFilter: () => {},
        }
      : null,
  ];
};

export default filterOptions;
