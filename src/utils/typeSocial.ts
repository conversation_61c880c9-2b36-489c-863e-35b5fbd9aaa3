import { TYPE_SOCIAL_LABEL, TYPE_SOCIAL, SUB_TYPE_SOCIAL_LABEL } from "constants/requestAudience";
import { AVATAR_TYPE } from "types";

export const avatarType = (type: number) => {
  return TYPE_SOCIAL[type || 1] as AVATAR_TYPE;
};

export const audienceType = (type: number) => {
  return TYPE_SOCIAL_LABEL.find((typeSocial) => typeSocial.id === type);
};

export const audienceSubType = (subType: number) => {
  return SUB_TYPE_SOCIAL_LABEL.find((typeSocial) => typeSocial.id === subType);
};
