import { TSocialFilterProps } from "types/SocialData";
import { filterIsShowOptions } from "utils/options";
import { toDataOptions } from "utils/utils";
import { packageOptions, TypeSelectMS } from 'constants/socialData';
import { useAppSelector } from "store";
import { handleSelectedValue } from "utils/filter";

const filterOptions = ({
  isFilterType,
  isFilterCategory,
  isFilterPackage,
  params,
}: TSocialFilterProps & { params?: any }) => {
  const { data } = useAppSelector((state) => state.category);

  return [
    isFilterType
      ? {
          key: "type__in",
          placeholder: "Type",
          type: "select",
          options: filterIsShowOptions(TypeSelectMS),
          selected: handleSelectedValue(params, "type__in"),
        }
      : null,
    isFilterCategory
      ? {
          key: "category__in",
          placeholder: "Category",
          type: "select",
          options: filterIsShowOptions(toDataOptions(data.items, "code", "name")),
          selected: handleSelectedValue(params, "category__in"),
        }
      : null,
    isFilterPackage
      ? {
          key: "package__in",
          placeholder: "Package",
          type: "select",
          options: filterIsShowOptions(packageOptions),
          selected: handleSelectedValue(params, "package__in"),
        }
      : null,
  ];
};

export default filterOptions;
