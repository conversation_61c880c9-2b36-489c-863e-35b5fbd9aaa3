type RelationshipData = {
  labels: string[];
  value: number[];
  otherItems: { label: string; value: number }[];
  total: number;
};

export const formatLabel = (label: string | null | undefined): string => {
  return label
    ? label
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    : "--";
};

export const formatRelationShipsData = (
  relationShip: Record<string, number | undefined | null> | null | undefined
): RelationshipData => {
  const safeRelationShip = relationShip ?? {};

  const validEntries = Object.entries(safeRelationShip).filter(
    ([key, value]) => key !== "NULL" && value !== null && value !== undefined
  ) as [string, number][];

  const sortedEntries = validEntries.sort(([, a], [, b]) => b - a);

  const top3 = sortedEntries.slice(0, 3);

  const otherEntries = sortedEntries.slice(3);
  const otherValue = otherEntries.reduce((acc, [, value]) => acc + value, 0);

  const labels = [...top3.map(([key]) => formatLabel(key)), "Other"];
  const values = [...top3.map(([, value]) => value), otherValue];
  const otherItems = otherEntries.map(([label, value]) => ({
    label: formatLabel(label),
    value,
  }));

  const total = validEntries.reduce((sum, [, value]) => sum + value, 0);

  return {
    labels,
    value: values,
    otherItems,
    total,
  };
};
