import omit from "lodash/omit";
import { FlatTreeNode, TreeN<PERSON>, TypeFilter, TypeTreeNode } from "types/YourData";

export const buildTreeProcess = (nodes: FlatTreeNode[]): TypeTreeNode => {
  const nodeMap: { [key: string]: TypeTreeNode } = {};

  nodes.forEach((node) => {
    const { position, ...rest } = node;
    if (!nodeMap[position]) {
      nodeMap[position] = { ...rest, children: [] };
    } else {
      Object.assign(nodeMap[position], rest);
    }

    const path = position.split("/").slice(0, -1).join("/");
    if (path) {
      if (!nodeMap[path]) {
        nodeMap[path] = { children: [] };
      }
      nodeMap[path].children?.push(nodeMap[position]);
    }
  });

  return nodeMap["root"] || { children: [] };
};
export const treeProcessArray = (node: TreeNode, path: string = ""): FlatTreeNode[] => {
  if (!node) return [];

  const currentPath = path ? `${path}` : "root";
  const params = omit(node, ["op", "audience", "left", "right"]);
  const result: FlatTreeNode[] = [
    {
      op: node.op,
      audience: node.audience,
      position: currentPath,
      filter_params: params,
      filter: { ...FormatTree(params) },
    },
  ];

  if (node.left) {
    result.push(...treeProcessArray(node.left, `${currentPath}/left`));
  }

  if (node.right) {
    result.push(...treeProcessArray(node.right, `${currentPath}/right`));
  }

  return result;
};
const FormatTree = (node: TreeNode): TypeFilter => {
  if (!node) return {};

  const newNode: TypeFilter = Object.entries(node).reduce((acc, [key, value]) => {
    acc[key] = String(value)
      .split(",")
      .map((item) => ({ label: formatTreeValue(item), value: formatTreeValue(item) }));
    return acc;
  }, {} as TypeFilter);

  return newNode;
};

const formatTreeValue = (input: string): string => {
  if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
    const [year, month, day] = input.split("-");
    return `${day}-${month}-${year}`;
  }
  const map: Record<string, string> = {
    F: "Female",
    M: "Male",
  };
  return map[input] ?? input;
};
