import { TypeField } from "views/EnrichmentView/Context/enrichmentContext";
import { getDataFromFile } from "./xlsxFile";
import { toast } from "components/ui/use-toast";
import { SummarizeEnrichment, SummarizeField } from "types/Enrichment";

export const createPayloadFormEnrich = ({
  file,
  fields,
}: {
  file: File;
  fields: string[];
}): FormData => {
  const form = new FormData();
  form.append("file", file!);
  fields.forEach((field) => {
    form.append("fields_enrich", field);
  });
  return form;
};
export const checkIfExcelFile = (file: File) => {
  const validExcelTypes = ["text/csv", "application/vnd.ms-excel"];
  const fileExtension = file.name.split(".").pop()?.toLowerCase();
  const validExtensions = ["csv"];
  return validExcelTypes.includes(file.type) || validExtensions.includes(fileExtension!);
};
export const getTemplate = async () => {
  try {
    const res = await fetch(`/templates/TableEnrichTemplate.csv`);
    if (!res.ok) {
      throw new Error(`Failed to fetch template: ${res.statusText}`);
    }

    const arrayBuffer = await res.arrayBuffer();
    const data = await getDataFromFile(arrayBuffer);
    return data;
  } catch (error) {
    toast({
      title: "Error",
      description: "Reload the page and try again",
      status: "error",
    });
  }
};

export function convertFileSize(bytes: number): string {
  const KB = 1024;
  const MB = 1024 * 1024;

  if (bytes < KB) return bytes + " Bytes";
  if (bytes < MB) return (bytes / KB).toFixed(2) + " KB";
  return (bytes / MB).toFixed(2) + " MB";
}

export function sortValueTable(objectA: TypeField[], objectB: PreviewItem[]): PreviewItem[] {
  return objectB.map((item) => {
    const sortedItem: { [key: string]: string } = {
      ["*Phone Number"]: item.phone || "",
    };
    const keys = Object.keys(item);
    objectA.forEach(({ label }) => {
      if (keys.includes(label)) sortedItem[label] = item[label] || "";
    });
    return sortedItem;
  });
}
export function sortSummaryData(
  objectA: TypeField[],
  summaryData: SummarizeEnrichment
): SummarizeEnrichment {
  const sortedData: SummarizeEnrichment = {};

  objectA.forEach(({ key }) => {
    if (summaryData.hasOwnProperty(key)) {
      sortedData[key] = summaryData[key];
    }
  });

  return sortedData;
}
type PreviewItem = { [key: string]: string };

export function sortTemplate(objectA: string[], objectB: PreviewItem[]): PreviewItem[] {
  return objectB.map((item) => {
    const sortedItem: PreviewItem = {};

    objectA.forEach((key) => {
      if (item.hasOwnProperty(key)) {
        sortedItem[key] = item[key];
      }
    });

    return sortedItem;
  });
}
type Field = { key: string; label: string; selected: boolean };

export function convertKeysToLabels(objectA: Field[], objectB: PreviewItem[]): PreviewItem[] {
  return objectB.map((item) => {
    const labeledItem: PreviewItem = {};

    Object.keys(item).forEach((key) => {
      const field = objectA.find((field) => field.key === key);
      if (field) {
        labeledItem[field.label] = item[key];
      } else {
        labeledItem[key] = item[key];
      }
    });

    return labeledItem;
  });
}

export const formatChartValue = (data: SummarizeField) => {
  const arrObj = Object.keys(data).map((key) => {
    return {
      label: key,
      value: data[key as keyof SummarizeField],
    };
  });
  return {
    values: arrObj.map((item) => item.value),
    labels: arrObj.map((item) => item.label),
  };
};
