/**
 * Remote Cache Manager for handling service worker and cache invalidation
 */

class RemoteCacheManager {
  private serviceWorker: ServiceWorker | null = null;
  private isRegistered = false;

  /**
   * Initialize the remote cache manager
   */
  async init(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      console.warn('Service Worker not supported');
      return;
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw-remote-cache.js', {
        scope: '/'
      });

      console.log('Remote cache service worker registered:', registration);
      this.isRegistered = true;

      // Wait for the service worker to be ready
      await navigator.serviceWorker.ready;
      this.serviceWorker = registration.active;

      // Listen for service worker updates
      registration.addEventListener('updatefound', () => {
        console.log('New service worker version found');
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'activated') {
              console.log('New service worker activated');
              this.serviceWorker = newWorker;
            }
          });
        }
      });

    } catch (error) {
      console.error('Failed to register remote cache service worker:', error);
    }
  }

  /**
   * Clear all remote module caches
   */
  async clearRemoteCache(): Promise<void> {
    if (!this.isRegistered) {
      console.warn('Service worker not registered, clearing cache manually');
      await this.clearCacheManually();
      return;
    }

    try {
      // Send message to service worker
      if (this.serviceWorker) {
        this.serviceWorker.postMessage({
          type: 'CLEAR_REMOTE_CACHE'
        });
      }

      // Also clear cache manually as backup
      await this.clearCacheManually();
    } catch (error) {
      console.error('Failed to clear remote cache:', error);
    }
  }

  /**
   * Force refresh a specific remote module
   */
  async forceRefreshRemote(url: string): Promise<void> {
    if (!this.isRegistered) {
      console.warn('Service worker not registered, cannot force refresh');
      return;
    }

    try {
      if (this.serviceWorker) {
        this.serviceWorker.postMessage({
          type: 'FORCE_REFRESH_REMOTE',
          url
        });
      }
    } catch (error) {
      console.error('Failed to force refresh remote:', error);
    }
  }

  /**
   * Manually clear cache without service worker
   */
  private async clearCacheManually(): Promise<void> {
    if (!('caches' in window)) {
      return;
    }

    try {
      const cacheNames = await caches.keys();
      const remotePatterns = [
        /remote/i,
        /crm360/i,
        /federation/i
      ];

      await Promise.all(
        cacheNames.map(async (cacheName) => {
          const shouldDelete = remotePatterns.some(pattern => pattern.test(cacheName));
          if (shouldDelete) {
            console.log('Deleting cache:', cacheName);
            await caches.delete(cacheName);
          }
        })
      );

      console.log('Manual cache cleanup completed');
    } catch (error) {
      console.error('Failed to clear cache manually:', error);
    }
  }

  /**
   * Check if a remote URL is accessible
   */
  async checkRemoteHealth(url: string, timeout: number = 5000): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache',
        mode: 'cors'
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      console.warn(`Remote health check failed for ${url}:`, error);
      return false;
    }
  }

  /**
   * Get cache status for debugging
   */
  async getCacheStatus(): Promise<any> {
    if (!('caches' in window)) {
      return { supported: false };
    }

    try {
      const cacheNames = await caches.keys();
      const remoteCaches = cacheNames.filter(name => 
        /remote|crm360|federation/i.test(name)
      );

      const cacheDetails = await Promise.all(
        remoteCaches.map(async (cacheName) => {
          const cache = await caches.open(cacheName);
          const keys = await cache.keys();
          return {
            name: cacheName,
            entries: keys.length,
            urls: keys.map(key => key.url)
          };
        })
      );

      return {
        supported: true,
        serviceWorkerRegistered: this.isRegistered,
        caches: cacheDetails
      };
    } catch (error) {
      console.error('Failed to get cache status:', error);
      return { supported: true, error: error.message };
    }
  }

  /**
   * Setup periodic cache cleanup
   */
  setupPeriodicCleanup(intervalMinutes: number = 30): void {
    const intervalMs = intervalMinutes * 60 * 1000;
    
    setInterval(async () => {
      console.log('Running periodic remote cache cleanup');
      await this.clearRemoteCache();
    }, intervalMs);
  }
}

// Create singleton instance
export const remoteCacheManager = new RemoteCacheManager();

// Auto-initialize when module is imported
if (typeof window !== 'undefined') {
  remoteCacheManager.init().catch(console.error);
}

export default remoteCacheManager;
