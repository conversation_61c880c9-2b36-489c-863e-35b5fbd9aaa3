import ReactGA from 'react-ga4';

interface ITracking {
  eventName:
    | 'view_category'
    | 'search_data'
    | 'preview_data'
    | 'request_data'
    | 'purchase_preview'
    | 'purchase'
    | 'buy_credits_button'
    | 'buy_plan_button'
    | 'nav_click'
    | 'social_persona_filter'
    | 'social_data_filter'
    | 'work_persona_filter';
  params: {
    user_id: string;
    valueTracking: any
  };
}

export function tracking(data: ITracking) {
  try {
    const { eventName, params } = data;
    const { user_id, valueTracking } = params;
    ReactGA.event(`big360_${eventName}`, { user_id, valueTracking });
  } catch (error) {
    console.log('Error tracking event:', error);
  }
}
