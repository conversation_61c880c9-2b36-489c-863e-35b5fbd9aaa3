import { PERMISSION_FEATURE } from "constants/permission";
import { FEATURE_PLAN_PERMISSIONS, PLAN_TYPE } from "constants/permission/permissionPlan";

export const checkPermission = ({
  keyFeature,
  keyCode,
  userPlan,
}: {
  keyFeature: PERMISSION_FEATURE;
  userPlan: PLAN_TYPE;
  keyCode: any;
}) => {
  const FEATURE_LIST = FEATURE_PLAN_PERMISSIONS[userPlan][keyFeature];
  return FEATURE_LIST.filter((feature) => feature.name === keyCode)[0];
};
