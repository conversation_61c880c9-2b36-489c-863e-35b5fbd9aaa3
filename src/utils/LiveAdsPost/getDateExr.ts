function getDateExpiredISOFormat(daysToAdd: number) {
  const currentDate = new Date();

  currentDate.setDate(currentDate.getDate() + daysToAdd);

  // const isoDate = currentDate.toISOString();

  return toISOStringWithTimezone(currentDate);
}

export default getDateExpiredISOFormat;

const pad = (n: any) => `${Math.floor(Math.abs(n))}`.padStart(2, "0");
const toISOStringWithTimezone = (date: any) => {
  return (
    date.getFullYear() +
    "-" +
    pad(date.getMonth() + 1) +
    "-" +
    pad(date.getDate()) +
    "T" +
    pad(date.getHours()) +
    ":" +
    pad(date.getMinutes()) +
    ":" +
    pad(date.getSeconds()) +
    getTimezoneOffset(date)
  );
};
const getTimezoneOffset = (date: any) => {
  const tzOffset = -date.getTimezoneOffset();
  const diff = tzOffset >= 0 ? "+" : "-";
  return diff + pad(tzOffset / 60) + ":" + pad(tzOffset % 60);
};
