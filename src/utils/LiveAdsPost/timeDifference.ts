import { convertDate } from "utils/utils";

function timeDifference(timeString: string) {
  const time: any = new Date(timeString);
  const now: any = new Date();
  const diffInMillis = now - time;

  const seconds = Math.floor(diffInMillis / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const weeks = Math.floor(days / 7);

  if (days < 1) {
    if (hours > 0) {
      return `${hours} tiếng trước`;
    } else if (minutes > 0) {
      return `${minutes} phút trước`;
    } else {
      return `${seconds} giây trước`;
    }
  } else if (days < 7) {
    return `${days} ngày trước`;
  } else if (days < 30) {
    return `${weeks} tuần trước`;
  } else {
    return convertDate(timeString);
  }
}

export default timeDifference;
