import omit from "lodash/omit";
import omitBy from "lodash/omitBy";
import { SetURLSearchParams } from "react-router-dom";

import { TSelectedOption, TSelectOption } from "types/Select";
import { tracking } from '../Tracking/tracking';

type KEY = { [k: string]: string };

export const onSetParamsChange = ({
  key,
  value,
  setSelectedOption,
}: {
  key: string;
  value: TSelectOption[];
  setSelectedOption: (value: React.SetStateAction<TSelectedOption>) => void;
}) => {
  const newValue = value.map((item: any) => item?.value).join(",");
  setSelectedOption((prev: any) => ({ ...prev, [key]: newValue ? newValue : undefined }));
};

export const handleSearch = ({
  params,
  searchTerm,
  setSearchTerm,
  setSearchParams,
  user_id,
}: {
  params: KEY;
  searchTerm: string;
  setSearchParams: SetURLSearchParams;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  user_id: string;
}) => {
  const _params = omit(params, ["q", "page"]);
  if (searchTerm !== "") {
    setSearchParams({ ...params, q: searchTerm, page: "1" });
    tracking({
      eventName: 'search_data',
      params: {
        user_id: user_id,
        valueTracking: JSON.stringify({
          search_term: searchTerm,
          path: { ...params,pathName: location.pathname, page: "1", q: searchTerm }
        })
      }
    });
  } else {
    setSearchParams({ ..._params });
  }
  setSearchTerm("");
};

export const handleFilter = ({
  params,
  searchTerm,
  selectedOption,
  setSearchParams,
}: {
  params: KEY;
  selectedOption: TSelectedOption;
  searchTerm: string;
  setSearchParams: SetURLSearchParams;
}) => {
  const _paramsFilter = omitBy(selectedOption, (x) => x === undefined);
  if (searchTerm !== "") {
    setSearchParams({ q: params?.q || searchTerm, ..._paramsFilter });
  } else {
    setSearchParams({ ..._paramsFilter });
  }
};
