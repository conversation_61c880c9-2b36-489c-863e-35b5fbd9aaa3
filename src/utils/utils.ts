import axios from "axios";
import { type ClassValue, clsx } from "clsx";
import { TYPE_SOCIAL } from "constants/requestAudience";
import SidebarConfig from "layout/SidebarConfig";
import { useMatch } from "react-router-dom";

import { twMerge } from "tailwind-merge";
import { AVATAR_TYPE } from "types";

const CancelToken = axios.CancelToken;

export const convertCancelToken = (params: any) => {
  const cancelToken = params?.cancelToken
    ? params?.cancelToken
    : new CancelToken(function () {
        return;
      });
  delete params?.cancelToken;
  delete params?.cancelRequest;

  return { cancelToken, paramsNoneCancelToken: params };
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getTotalPerson(data: any) {
  let totalSum = 0;

  for (let key in data) {
    if (data.hasOwnProperty(key)) {
      totalSum += data[key];
    }
  }
  return totalSum;
}

export function getLastCharName(name: string, position: "first" | "last") {
  if (!name) return "";
  const nameArr = name.split(" ");
  return position === "first" ? nameArr[0][0] : nameArr[nameArr.length - 1][0];
}
export const toDataOptions = (data: any, idKey = "id", nameKey = "name") => {
  return (data || []).map((dataItem: any) => {
    return {
      id: dataItem[idKey],
      name: dataItem[nameKey],
    };
  });
};

export const rangePagination = (start: number, end: number) => {
  let length = end - start + 1;

  return Array.from({ length }, (_, idx) => idx + start);
};

export function getAgeValue(age: string) {
  if (age === null) {
    // sort follow ASCII
    // Extended ASCII
    return 128;
  }
  if (age.match(/^<\d+$/)) {
    // Trường hợp có ký tự '<'
    return parseInt(age.substring(1)) - 1;
  } else if (age.match(/^>\d+$/)) {
    // Trường hợp có ký tự '>'
    return parseInt(age.substring(1)) + 1;
  } else if (age.match(/^\d+-\d+$/)) {
    // Trường hợp có ký tự '-'
    let parts = age.split("-");
    return parseInt(parts[1]);
  } else {
    // Trường hợp chuỗi không chứa các ký tự đặc biệt
    return parseInt(age, 10);
  }
}

export function capitalizeOfEachWord(str: string) {
  if (!str) return "";
  return str
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

export const formatDate = (dateString: string | Date, isTime?: boolean) => {
  if (!dateString) return "-";
  const date = new Date(dateString);

  // Lấy năm, tháng, ngày
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  // Lấy giờ và phút
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return isTime ? `${day}/${month}/${year} ${hours}:${minutes}` : `${day}/${month}/${year}`;
};
export function convertDateFormat(date: string) {
  if (!date) return "-";
  const dateParts = date.split("-");
  const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
  return formattedDate;
}

export const counterNumberPhone = (className: string): number => {
  const listPhone = document.querySelectorAll(className);
  let count = 0;
  listPhone.forEach((item) => {
    !String(item.textContent).includes("*") && count++;
  });
  return count;
};

// Using Intl.DateTimeFormat
export const dateFormatter = new Intl.DateTimeFormat("en-US", {
  timeZone: "America/New_York",
  year: "numeric",
  month: "2-digit",
  day: "2-digit",
  hour: "2-digit",
  minute: "2-digit",
  second: "2-digit",
});

export const handleCheckRouter = (pathname: string) => {
  const configRouter = SidebarConfig();
  const arrPath = checkCrmRouter(pathname)
  .split("/")
  .filter((item) => item !== "");
  const match = useMatch(`/${arrPath[0]}/:children`);
  const children = match?.params.children;
  const filterPath = children ? arrPath.filter((item) => item !== children) : arrPath;
  const list = filterPath
  .map((item) => {
    return configRouter.find((value) =>
      value.items.some((val) => {
        const pathSegments = val.path.split("/").filter((seg) => seg !== "");
        return pathSegments.includes(item);
      })
    );
  })
  .filter(Boolean);
  const item = list[list.length - 1]?.items.find((value) =>
    value.path.split("/").includes(arrPath[list.length - 1])
  );

  return {
    subHeader: list[list.length - 1]?.subheader,
    path: item?.path,
  };
};

const checkCrmRouter = (pathname: string) => {
  const remoteRouter = ["crm360"];
  return pathname.split("/").filter((item) => !remoteRouter.includes(item)).join("/");
};

const formatDateSafari = (dateString: string) => {
  let dateObject: Date;

  if (dateString.includes(",")) {
    const [datePart, timePart] = dateString.split(",").map((part) => part.trim());
    const [day, month, year] = datePart.split("/").map(Number);
    const [hours, minutes] = timePart.split(":").map(Number);

    dateObject = new Date(year, month - 1, day, hours, minutes);
  } else if (dateString.includes("T")) {
    dateObject = new Date(dateString);
  } else if (dateString.includes("/")) {
    const [datePart, timePart] = dateString.split(" ");
    const [day, month, year] = datePart.split("/").map(Number);
    const [hours, minutes] = timePart.split(":").map(Number);

    dateObject = new Date(year, month - 1, day, hours, minutes);
  } else {
    dateObject = new Date(dateString);
  }

  if (isNaN(dateObject.getTime())) {
    return "";
  }
  const day = String(dateObject.getDate()).padStart(2, "0");
  const month = String(dateObject.getMonth() + 1).padStart(2, "0");
  const year = dateObject.getFullYear();
  const hours = String(dateObject.getHours()).padStart(2, "0");
  const minutes = String(dateObject.getMinutes()).padStart(2, "0");

  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

export const convertDate = (dateString: string): string => {
  if (!dateString) return "";
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  if (isSafari) return formatDateSafari(dateString);
  const dateObject = new Date(dateString);
  const day = String(dateObject.getUTCDate()).padStart(2, "0");
  const month = String(dateObject.getUTCMonth() + 1).padStart(2, "0");
  const year = dateObject.getUTCFullYear();
  const hours = String(dateObject.getHours()).padStart(2, "0");
  const minutes = String(dateObject.getMinutes()).padStart(2, "0");
  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

export function removeVietnameseTones(str: string) {
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  str = str.replace(/đ/g, "d");
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
  str = str.replace(/Đ/g, "D");
  // Some system encode vietnamese combining accent as individual utf-8 characters
  // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
  // Remove extra spaces
  // Bỏ các khoảng trắng liền nhau
  str = str.replace(/ + /g, " ");
  str = str.trim();
  // Remove punctuations
  // Bỏ dấu câu, kí tự đặc biệt
  str = str.replace(
    /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
    " "
  );
  return str;
}
export function toQueryString(params: { [key: string]: string }): string {
  return Object.keys(params)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join("&");
}

export function formatPayloadAvatar(
  type: TYPE_SOCIAL,
  fb_uid: string,
  actor_id: string
): {
  uid: string;
  type: AVATAR_TYPE;
} {
  const mapType: Record<TYPE_SOCIAL, { uid: string; type: AVATAR_TYPE }> = {
    [TYPE_SOCIAL.group]: {
      uid: fb_uid,
      type: "group",
    },
    [TYPE_SOCIAL.fanpage]: {
      uid: fb_uid,
      type: "page",
    },
    [TYPE_SOCIAL.post]: {
      uid: actor_id,
      type: "page",
    },
    [TYPE_SOCIAL.profile]: {
      uid: fb_uid,
      type: "profile",
    },
  };

  return mapType[type];
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const handlePostMessage = (type: string, value: any) => {
  const postMessage = {
    type: type,
    payload: value
  };
  window.postMessage(postMessage, "*");
};

export const handleReceiveMessage = (data: {
  type: string;
  dispatch: (e: MessageEvent) => void;
}) => {
  const { type, dispatch } = data;
  window.addEventListener("message", (e: MessageEvent) => {
    if (e.data.type === type) {
      dispatch(e);
    }
  });
};
