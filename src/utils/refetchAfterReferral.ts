import { getMe } from '../store/redux/auth/slice';
import { getCategories } from '../store/redux/category/slice';
import { getFeatures } from '../store/redux/features/slice';
import { fetchActiveSubscription, fetchLastSubscription } from '../store/redux/subscription/slice';
import { AppDispatch } from '../store';
import { getDimensions, getLocationArea } from '../store/redux/dimensions/slice';
import { dimensionFieldPersona } from '../constants/socialPersona';

const GetAllDimensions = async (dispatch: AppDispatch) => {
  await dispatch(getLocationArea());
  await Promise.allSettled(
    Object.keys(dimensionFieldPersona).map((field) => dispatch(getDimensions(field)))
  );
};
export const refetchAfterReferral = async (dispatch: AppDispatch) => {
  await Promise.all([
    dispatch(getMe()).unwrap()
  ]).then(() => {
    dispatch(getCategories()).unwrap();
    dispatch(getFeatures()).unwrap();
    dispatch(fetchLastSubscription()).unwrap();
    dispatch(fetchActiveSubscription()).unwrap();
  });
  await GetAllDimensions(dispatch);
};
