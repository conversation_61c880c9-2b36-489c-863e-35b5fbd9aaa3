import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";
import useFeatures from "hooks/useFeatures";

const handleCheckConcurrent = (count?: number) => {
  // show toast khi số page follow hiện tại vượt quá số page đượ<PERSON> phép đồng thời follow
  const extra_limit = useFeatures(FEATURE_PERMISSION_KEY.SCD_LIVEPOST_FOLLOW_PAGE)?.extra_limit
    ?.concurrent;
  const isMax = count && extra_limit ? count >= extra_limit : false;
  return isMax;
};

export default handleCheckConcurrent;
