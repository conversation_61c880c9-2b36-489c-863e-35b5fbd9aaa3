import { ModalContextProps } from "providers/Modal";
import ConfirmFollowPage from "views/YourPageView/components/ConfirmFollowPage";

interface Props {
  context: ModalContextProps;
  dateExpired: number;
  callBack: () => Promise<void>;
}

export const handleConfirmPageFollow = ({ context, dateExpired, callBack,  }: Props) => {
  context?.setDataDialog((prev) => ({
    ...prev,
    isOpen: true,
    className: "max-w-[680px] gap-1 px-6 py-4",
    title: "",
    message: "",
    content: <ConfirmFollowPage dateExpired={dateExpired} callBack={callBack}/>,
  }));
};
