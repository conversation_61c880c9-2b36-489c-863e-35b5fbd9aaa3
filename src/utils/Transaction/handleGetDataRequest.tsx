import { ModalContextProps } from "providers/Modal";
import RequestLimitModal from "views/RequestAudienceView/components/RequestLimitModal";
import { Link } from "react-router-dom";
import { Button } from "components/ui/button";
import LABEL from "constants/label";
import { LoadingButtonIcon } from "assets/icons/LoadingButtonIcon";

interface Props {
  context: ModalContextProps;
  loading: boolean;
  is_overage: boolean;
  is_trial: boolean;
  callBack: () => Promise<void>;
}

const handleGetDataRequest = ({ context, loading, is_overage, is_trial, callBack }: Props) => {
  switch (is_overage) {
    case true:
      context?.setDataDialog((prev) => ({
        ...prev,
        className: "max-w-[566px] gap-1 px-6 py-4",
        isOpen: true,
        isShowTitle: true,
        title: "",
        content: <RequestLimitModal isTrial={is_trial} />,
        footer: is_trial ? (
          <Link to="plan/upgrade" className="w-full">
            <Button
              className="rounded-xl text-md font-semibold mt-6 w-full"
              onClick={() => context?.setDataDialog((prev) => ({ ...prev, isOpen: false }))}
              children={LABEL.upgrade}
            />
          </Link>
        ) : (
          <Button
            className="rounded-xl text-md font-semibold mt-6"
            onClick={callBack}
            disabled={loading}
            children={loading ? <LoadingButtonIcon /> : LABEL.purchase}
          />
        ),
      }));
      break;
    default:
      callBack();
      break;
  }
};

export default handleGetDataRequest;
