import { RegionDistribution } from "types/Persona";

export const formatRegionData = (obj: RegionDistribution) => {
  const arrObj = Object.keys(obj ?? {})
    .map((key: string) => ({
      city: key,
      count: obj[key],
    }))
    .sort((a, b) => (b.count ?? 0) - (a.count ?? 0));
  const labels =
    arrObj.length > 3
      ? arrObj
          .slice(0, 3)
          .map((item) => item.city)
          .concat("Others")
      : arrObj.map((item) => item.city);
  const totalOthers = arrObj.slice(3).reduce((acc, cur) => acc + cur.count, 0);
  const values =
    arrObj.length > 3
      ? arrObj
          .slice(0, 3)
          .map((item) => item.count)
          .concat(totalOthers)
      : arrObj.map((item) => item.count);

  return {
    labels,
    values,
  };
};
