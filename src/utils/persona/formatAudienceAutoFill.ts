import { FILTER_TITLE } from "constants/persona";
import formatLabelChip from "./formatChipLabel";
import { formatDate } from "utils/utils";
import { fMapRadius } from "utils/number";

const formatValue = (value: string | null, keyFilter: string, categoryOption: any[]): string => {
  if (!value || value === "null") {
    return "unknown";
  }
  //map radius
  if (keyFilter === "person_geo") {
    const [latitude, longitude, radius] = value.split(",").map((v) => v.trim());
    const formattedLatitude = parseFloat(latitude).toFixed(2);
    const formattedLongitude = parseFloat(longitude).toFixed(2);
    const formattedRadius = `${fMapRadius(Number(radius))}km`;
    return `${formattedLatitude}-${formattedLongitude}-${formattedRadius}`;
  }
  if (keyFilter === "unit") {
    return "";
  }
  if (keyFilter === "age__in") {
    const ageValues = value.split(",").map((v) => parseInt(v.trim(), 10));
    const minAge = Math.min(...ageValues);
    const maxAge = Math.max(...ageValues);
    return `${minAge}-${maxAge}`;
  }
  if (keyFilter === "score_range") {
    const range = value.split("-").map((v) => parseInt(v.trim(), 10));
    const min = Math.min(...range)/10;
    const max = Math.max(...range)/10;
    return `${min}-${max}`;
  }
  return value
    .split(",")
    .map((v) =>
      formatLabelChip({
        value: v.trim(),
        categoryOption,
        keyFilter,
      })
    )
    .join(", ");
};
export const convertAudienceName = (obj: Record<string, string | null>,   categoryOption?: any[]
): string => {
  const entries = Object.entries(obj).map(([key, value]) =>
    formatValue(value, key, categoryOption ?? []).replace(/, /g, "-")
  );
  return entries.join("_").replace(/_$/, "");
};
export const convertDescriptionAudience = (
  obj: Record<string, string | null>,
  FILTER_LABEL?: { [key: string]: string },
  categoryOption?: any[]
): string => {
  return Object.entries(obj)
    .filter(([key]) => !["dob__gte", "age__gte"].includes(key))
    .map(([key, value]) => {
      const label = FILTER_LABEL
        ? FILTER_LABEL[key as keyof typeof FILTER_LABEL]
        : FILTER_TITLE[key as keyof typeof FILTER_TITLE];

      if (!label) return null;

      if (key === "dob__lte" || key === "dob__gte") {
        const dobLte = obj["dob__lte"];
        const dobGte = obj["dob__gte"];

        if (dobLte && dobGte) {
          const formattedValue = `${formatDate(dobGte)} - ${formatDate(dobLte)} `;
          return `Date: ${formattedValue}`;
        }

        return null;
      }

      if (key === "age__gte" || key === "age__lte") {
        const ageLte = obj["age__lte"];
        const ageGte = obj["age__gte"];

        if (ageLte && ageGte) {
          return `Age: ${ageGte} -  ${ageLte}`;
        }

        return null;
      }

    if (key === "category") {
      const category = obj["category"];
      const formattedValue = formatValue(value, key, categoryOption ?? []);

      if (category) {
        return `Category: ${formattedValue} `;
      }

      return null;
    }
    if (key === "score_range") {
      const score_range = obj["score_range"];

      if (score_range) {
        return `Score range: ${Number(score_range.split('-')[0])/10}-${Number(score_range.split('-')[1])/10}`;
      }

      return null;
    }

      const formattedValue = formatValue(value, key, categoryOption ?? []);
      return `${label}: ${formattedValue}`;
    })
    .filter((line) => line !== null)
    .join("\n");
};
