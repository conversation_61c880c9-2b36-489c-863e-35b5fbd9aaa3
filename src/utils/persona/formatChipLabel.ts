import { PlatformOptions } from 'constants/LiveAdsPost';
import { FormatOptions } from 'constants/postComment';
import { ICategoryItem } from 'types/SocialData';
import { ScoreOptionsAtt } from '../../constants/socialPersona';
import { RELATIONSHIP_GENDER_OPTIONS } from '../../constants/yourData';
import { TAttribute } from '../../types/Attribute';

interface Props {
  keyFilter: string;
  value: any;
  categoryOption: ICategoryItem[];
  categoryRanking?: TAttribute[];
}
const formatLabelChip = ({ keyFilter, value, categoryOption, categoryRanking }: Props) => {
  value = value === null || value === "null" ? "Unknown" : value;
  switch (keyFilter) {
    case "gender__in":
      value = value === "F" ? "Female" : value === "M" ? "Male" : "Unknown";
      break;
    case "category__in":
    case "category":
      value = categoryOption.find((category) => category.code === value)?.name || "";
      break;
    case "att_type__in":
      value = FormatOptions.find((format) => format.value === value)?.label || "";
      break;
    case "publisher_platform__in":
      value = PlatformOptions.find((platform) => platform.id === value)?.name || "";
      break;
    case "score_range":
      value =
        categoryRanking ?
          categoryRanking.find((score) => score.id === value)?.name || '' :
          ScoreOptionsAtt.find((score) => score.id === value)?.name || '';
      break;
    case "is_active":
    case "is_live":
      value = value === "true" ? "Active" : "Inactive";
      break;
    case "relationships__in":
      value = RELATIONSHIP_GENDER_OPTIONS.find((relationship) => relationship.value === value)?.label || "";
      break;
    default:
      value = value;
      break;
  }
  return value;
};

export default formatLabelChip;
