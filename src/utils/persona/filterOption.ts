import { FILTER_KEY } from "constants/persona";
import { PERSONA_LABEL } from "constants/persona/label";
import { TAttribute } from "types/Attribute";

export const GenderOptionsAtt: TAttribute[] = [
  { id: "F", name: "Female" },
  { id: "M", name: "Male" },
  { id: "null", name: "Other" },
];

export const FILTER_CHIP_OPTIONS = [
  {
    name: PERSONA_LABEL.information_filter,
    fields: [FILTER_KEY.age, FILTER_KEY.gender],
  },
  {
    name: PERSONA_LABEL.job_title_filter,
    fields: [FILTER_KEY.department, FILTER_KEY.level],
  },
  {
    name: PERSONA_LABEL.company_size,
    fields: [FILTER_KEY.company_size],
  },
  {
    name: PERSONA_LABEL.location,
    fields: [FILTER_KEY.region, FILTER_KEY.province],
  },
  {
    name: PERSONA_LABEL.map_radius,
    fields: [FILTER_KEY.map_radius],
  },
];
