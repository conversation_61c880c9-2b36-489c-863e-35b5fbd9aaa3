import { fMapRadius } from "utils/number";
import { ScoreOptionsAtt } from '../../constants/socialPersona';
import { RELATIONSHIP_GENDER_OPTIONS } from '../../constants/yourData';

const formatFilterHistory = (data: Record<string, string>, label: Record<string, string>, categoryOption?: any[]) => {
  return Object.keys(data)
    .filter((key) => key !== "unit")
    .map((key) => {
      let newValue = "";
      switch (true) {
        case label[key] === "Gender":
          newValue = data[key]
            .split(",")
            .map((item) => {
              switch (item) {
                case "F":
                  return "Female";
                case "M":
                  return "Male";
                default:
                  return "Other";
              }
            })
            .join(", ");
          break;

        case label[key] === "Map radius":
          // Tách giá trị "Map radius"
          const values = data[key].split(",").map((item) => Number(item).toFixed(2));
          const radius = fMapRadius(Number(values[2]));
          newValue = `${values[0]} - ${values[1]} - ${radius}km`;
          break;
        case label[key] === 'Category':
          newValue = categoryOption?.find((category) => category.code === data[key])?.name || '';
          break;
        case label[key] === 'Score Range':
          newValue = ScoreOptionsAtt.find((score) => score.id === data[key])?.name || '';
          break;
        case label[key] === 'Relationships':
          newValue = RELATIONSHIP_GENDER_OPTIONS.find((relationship) => relationship.value === data[key])?.label || '';
          break;
        default:
          newValue = data[key].replace(/,(?=[^\s])/g, ", ");
          break;
      }
      return {
        name: label[key] || key,
        value: newValue,
      };
    });
};

export default formatFilterHistory;
