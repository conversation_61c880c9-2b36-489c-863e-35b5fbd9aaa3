import _ from "lodash";

const sortEmployees = (ranges: string[]) => {
  const rangesWithoutUnknown = _.filter(ranges, (r) => r !== null);
  const unknown = _.filter(ranges, (r) => r === null);

  const sortedRanges = _.sortBy(rangesWithoutUnknown, (range) => {
    if (range && typeof range === "string") {
      const parts = range.split("-");
      return parseInt(parts[0], 10);
    }
    return false;
  });

  return [...sortedRanges, ...unknown] as [];
};
export default sortEmployees;
