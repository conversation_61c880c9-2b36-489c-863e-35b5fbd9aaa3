import { CityValue, TDataNumber } from 'types/Persona';
import { GenderGroup } from '../../types/ResponseApi';

export const getCityData = (data: TDataNumber, type?: string): CityValue[] => {
  const cityValueArray: CityValue[] = Object.keys(data).map((key) => ({
    city: key === "NULL" ? "Unknown" : key,
    count: data[key],
  }));
  cityValueArray.sort((a, b) => b.count - a.count);

  return type == "social"
    ? cityValueArray.filter((item) => item.city !== "Unknown").slice(0, 10) : type ==='all'?cityValueArray.filter((item) => item.city !== "Unknown")
    : cityValueArray.slice(0, 10);
};

export const getGenderData = (data: GenderGroup) => {
  if (!data) {
    return data;
  }
  return Object.keys(data).filter((key) => key !== 'NULL').reduce((acc, key) => {
    acc[key as keyof GenderGroup] = data[key as keyof GenderGroup];
    return acc;
  }, {} as GenderGroup);
};
