import { SetURLSearchParams } from "react-router-dom";
import pickBy from "lodash/pickBy";
import { TSelectOption } from "types/Select";

interface Props {
  selectedNodes: TSelectOption[];
  key: string | undefined;
  params: { [k: string]: string } | undefined;
  parentParams?: { [k: string]: string };
  setSearchParams: SetURLSearchParams;
  isSingle?: boolean;
}

export const onChangeDimensions = ({
  selectedNodes,
  key,
  params,
  parentParams = {},
  // isSingle = false,
  setSearchParams,
}: Props) => {
  if (key) {
    const selectValue = selectedNodes.map((item) => item.value);

    const newSearch = {
      ...params,
      ...parentParams,
      [key]: selectValue.length > 0 ? selectValue.join(",") : [],
    };

    setSearchParams(pickBy(newSearch, (value) => value !== ""));
  }
};
