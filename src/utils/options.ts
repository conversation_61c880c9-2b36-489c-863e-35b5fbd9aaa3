import isArray from "lodash/isArray";
import reduce from "lodash/reduce";
import { TAttribute } from "types/Attribute";
import { TSelectOption } from "types/Select";

export const isChecked = (key: any, checkedArr: any) =>
  key == null || key === "null"
    ? !!checkedArr?.includes("null")
    : isArray(checkedArr) && !!checkedArr.includes(key);

//option default
export const formatOptionSelect = (option: TAttribute = {}, checked: boolean): TSelectOption => {
  const { name = "", id = "" } = option;
  return { label: name, value: id, checked };
};

//filter checked
export const filterIsShowOptions = (attributes: TAttribute[], checkedArr: any = []) => {
  return attributes?.length
    ? attributes.reduce((prevArr: TSelectOption[], current: TAttribute) => {
        const checked = isArray(checkedArr) && !!checkedArr?.includes(current.id);
        return [...prevArr, formatOptionSelect(current, checked)];
      }, [])
    : [];
};

//format data filter
export const formatDataToOptionSelect = (options: any, checkedArr: any = []) => {
  return reduce(
    options,
    (prev: TSelectOption[], cur: any) => {
      const checked = isChecked(cur, checkedArr);
      return [
        ...prev,
        {
          label: cur !== null ? cur : "Unknown",
          value: cur !== null ? cur : "null",
          checked,
        },
      ];
    },
    []
  );
};

export const formatDataTreeSelect = (
  options: Record<string, string[]>,
  checkedArr: any = [],
  checkedChildrenArr: any = []
): TSelectOption[] => {
  return reduce(
    Object.keys(options),
    (prev: TSelectOption[], cur: any) => {
      const checked = isChecked(cur, checkedArr);

      const children = reduce(
        options[cur],
        (childAcc: TSelectOption[], child: any) => {
          const checkedChildren = isChecked(child, checkedChildrenArr);
          return [
            ...childAcc,
            {
              label: child !== null ? child : "Unknown",
              value: child !== null ? child : "null",
              checked: checked || checkedChildren,
            },
          ];
        },
        []
      );

      const allChildrenChecked = children.every((c) => c.checked);

      return [
        ...prev,
        {
          label: cur !== null ? cur : "Unknown",
          value: cur !== null ? cur : "null",
          checked: checked || allChildrenChecked,
          children,
        },
      ];
    },
    []
  );
};
