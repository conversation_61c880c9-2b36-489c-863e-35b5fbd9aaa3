interface Props {
  date: string | undefined;
  duration?: number;
  monthShort?: boolean;
}

export const getPublishDate = ({ date, duration, monthShort = true }: Props) => {
  let timeEnd;
  let fPublishTime = new Date();

  if (date) {
    fPublishTime = new Date(date);
  }

  // <PERSON><PERSON><PERSON> dạng ngày theo kiểu "Dec 12, 2024"
  const publishDate = fPublishTime.toLocaleDateString(undefined, {
    month: monthShort ? "short" : "long",
    day: "2-digit",
    year: "numeric",
  });

  const timeStart = fPublishTime.toLocaleTimeString(undefined, {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });

  if (duration) {
    const endTime = new Date(fPublishTime.getTime() + duration * 1000);

    timeEnd = endTime.toLocaleTimeString(undefined, {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  }

  return {
    timeStart,
    publishDate,
    timeEnd,
  };
};
