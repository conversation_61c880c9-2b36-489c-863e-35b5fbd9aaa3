import { useEffect, useState } from 'react';
import ReactGA from 'react-ga4';
import Hotjar from '@hotjar/browser';
import router from 'routers';
import { RouterProvider } from 'react-router-dom';
import { ModalAppProvider } from 'providers/Modal';

import { Tooltip } from 'chart.js';

import { AuthProvider } from 'providers/AuthProvider';
import { CookiesProvider } from 'react-cookie';
import RemoteDebugger from 'components/RemoteDebugger';

//table grid
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { NotiProvider } from './providers/NotiProvider';
//@ts-ignore
Tooltip.positioners.custom = function(elements, eventPosition) {
  return {
    x: eventPosition.x,
    y: eventPosition.y
  };
};

function App() {
  const trackingID = import.meta.env.REACT_APP_TRACKING_ID;
  const siteId = import.meta.env.REACT_APP_HOTJAR_ID;
  const [showRemoteDebugger, setShowRemoteDebugger] = useState(false);

  useEffect(() => {
    const hotjarVersion = 6;
    Hotjar.init(siteId, hotjarVersion);
    ReactGA.initialize(trackingID);

    // Enable remote debugger in development or with debug flag
    const isDev = process.env.NODE_ENV === 'development';
    const hasDebugFlag = new URLSearchParams(window.location.search).has('debug');

    if (isDev || hasDebugFlag) {
      // Add keyboard shortcut to toggle debugger (Ctrl+Shift+D)
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
          setShowRemoteDebugger(prev => !prev);
        }
      };

      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, []);

  return (
    <AuthProvider>
      <NotiProvider>
        <ModalAppProvider>
          <CookiesProvider>
            <RouterProvider router={router} />
            {/* Remote Debugger - only in dev or with debug flag */}
            {(process.env.NODE_ENV === 'development' ||
              new URLSearchParams(window.location.search).has('debug')) && (
              <RemoteDebugger
                isVisible={showRemoteDebugger}
                onToggle={() => setShowRemoteDebugger(prev => !prev)}
              />
            )}
          </CookiesProvider>
        </ModalAppProvider>
      </NotiProvider>
    </AuthProvider>
  );
}

export default App;
