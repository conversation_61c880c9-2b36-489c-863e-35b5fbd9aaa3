import { useEffect } from 'react';
import ReactGA from 'react-ga4';
import Hotjar from '@hotjar/browser';
import router from 'routers';
import { RouterProvider } from 'react-router-dom';
import { ModalAppProvider } from 'providers/Modal';

import { Tooltip } from 'chart.js';

import { AuthProvider } from 'providers/AuthProvider';
import { CookiesProvider } from 'react-cookie';

//table grid
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { NotiProvider } from './providers/NotiProvider';
//@ts-ignore
Tooltip.positioners.custom = function(elements, eventPosition) {
  return {
    x: eventPosition.x,
    y: eventPosition.y
  };
};

function App() {
  const trackingID = import.meta.env.REACT_APP_TRACKING_ID;
  const siteId = import.meta.env.REACT_APP_HOTJAR_ID;

  useEffect(() => {
    const hotjarVersion = 6;
    Hotjar.init(siteId, hotjarVersion);
    ReactGA.initialize(trackingID);
  }, []);

  return (
    <AuthProvider>
      <NotiProvider>
        <ModalAppProvider>
          <CookiesProvider>
            <RouterProvider router={router} />
          </CookiesProvider>
        </ModalAppProvider>
      </NotiProvider>
    </AuthProvider>
  );
}

export default App;
