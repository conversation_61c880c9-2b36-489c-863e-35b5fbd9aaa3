export const USER_PROFILE_LABEL = {
  title: "Account",
  sub_title:
    "Manage your account settings, view your plans details and update your profile information.",
  account_tab: "Account",
  edit: "Edit",
  PROFILE: {
    change_password: "Change password",
    current_password: "Current password",
    confirm_password: "Confirm password",
    new_password: "New password",
    contact_title: "Contact Information",
    persona_title: "Personal Information",
    full_name: "Full Name",
    phone_number: "Phone Number",
    personal_edit_title: "Change Personal Information",
    contact_edit_title: "Change Contact Information",
    upload_text_before: "Drag and drop your files here or",
    update_text_after: "from your computer",
    browse: "browse",
    upload_img: "Upload Avatar",
    upload_notice: "Allowed *.jpeg, *.jpg, *.png, *.webp. Max size of 10 Mb",
  },
  BILLING: {
    title: "Billing",
    subtitle:
      "Manage all subscription plan invoices, maintain active subscriptions, and purchase additional credits.",
    title_invoice: "Invoices History",
    nodata:
      "You haven’t made any purchases yet! Explore our offering and start your journey today.",
    purchase: "Purchase Plan",
    change_plan: "Change Plan",
    auto_renew: "Cancel Subscriptions",
    message_renew:
      "Your monthly subscription payment will end before your next billing period and you can continue to use your subscription until the end of your current billing period.",
    billing_profile: "Billing Information",
    billing_profile_mess:
      "This information will be included in the 'Bill to' section of your invoice details.",
    cancel_sub: "Cancel Subscription",
    confirm_cancel:
      "Your monthly subscription payment will end before your next billing period and you can continue to use your subscription until the end of your current billing period.",
  },
  PLAN: {
    title: "Plan Overview",
    subtitle: "Manage and review an overview of your subscription plan information.",
  },
  TRANSACTION: {
    title: "Transaction",
    subtitle:
      "Manage all transactions for the number of profiles in the dataset and apply feature-based charges.",
  },
  CREDIT: {
    credits: "Credits",
    title_card: "Customize Credits",
    buy_credit: "Buy Credit",
  },
};
