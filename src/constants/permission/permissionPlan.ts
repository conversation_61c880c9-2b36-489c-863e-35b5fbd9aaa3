import {
  PERMISSION_ADS_POST,
  PERMISSION_FEATURE,
  PERMISSION_LIVE_POST,
  PERMISSION_PERSONA,
  PERMISSION_REQUEST_AUDIENCE,
  PERMISSION_SOCIAL,
} from ".";

export enum PLAN_TYPE {
  TRIAL = "TRIAL",
  BASIC = "BASIC",
  PRO = "PRO",
}
type TPermissionFeature = {
  [key in PERMISSION_FEATURE]: {
    name: string;
    isHasPermission: boolean;
    isHasLimit?: boolean;
    isExceedLimit?: boolean;
    isLimitRecord?: number;
  }[];
};

export type TPermissionPlan = {
  [key in PLAN_TYPE]: TPermissionFeature;
};

// Business case planing
export const FEATURE_PLAN_PERMISSIONS: TPermissionPlan = {
  TRIAL: {
    [PERMISSION_FEATURE.PERSONA]: [
      {
        name: PERMISSION_PERSONA.PERSONAL_DEMOGRAPHIC,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_LOCATION,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_MAP_RADIUS,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_INDUSTRY_KEYWORD,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_DEPARTMENT,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_MANAGEMENT,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_COMPANY_NAME,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_COMPANY_SIZE,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.SOCIAL_DATA]: [
      {
        name: PERMISSION_SOCIAL.SOCIAL_SEARCH_DATA,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_SOCIAL.SOCIAL_VIEW_PHONE,
        isHasPermission: true,
        isHasLimit: true,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.REQUEST_AUDIENCE]: [
      {
        name: PERMISSION_REQUEST_AUDIENCE.REQUEST_IMPORT,
        isHasPermission: true,
        isHasLimit: true,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.LIVE_POST]: [
      {
        name: PERMISSION_LIVE_POST.LIVE_COMMUNITY,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_LIVE_POST.FOLLOW_PAGE,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.ADS_POST]: [
      {
        name: PERMISSION_ADS_POST.ADS_COMMUNITY,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.DATA_PROCESSING]: [
      {
        name: PERMISSION_FEATURE.DATA_PROCESSING,
        isHasPermission: true,
        isLimitRecord: 10000,
      },
    ],
    [PERMISSION_FEATURE.ENRICHMENT]: [],
  },
  BASIC: {
    [PERMISSION_FEATURE.PERSONA]: [
      {
        name: PERMISSION_PERSONA.PERSONAL_DEMOGRAPHIC,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_LOCATION,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_MAP_RADIUS,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_INDUSTRY_KEYWORD,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_DEPARTMENT,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_MANAGEMENT,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_COMPANY_NAME,
        isHasPermission: false,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_COMPANY_SIZE,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.SOCIAL_DATA]: [
      {
        name: PERMISSION_SOCIAL.SOCIAL_SEARCH_DATA,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_SOCIAL.SOCIAL_VIEW_PHONE,
        isHasPermission: true,
        isHasLimit: true,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.REQUEST_AUDIENCE]: [
      {
        name: PERMISSION_REQUEST_AUDIENCE.REQUEST_IMPORT,
        isHasPermission: true,
        isHasLimit: true,
        isExceedLimit: true,
      },
    ],
    [PERMISSION_FEATURE.LIVE_POST]: [
      {
        name: PERMISSION_LIVE_POST.LIVE_COMMUNITY,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_LIVE_POST.FOLLOW_PAGE,
        isHasPermission: true,
        isHasLimit: true,
        isExceedLimit: true,
      },
    ],
    [PERMISSION_FEATURE.ADS_POST]: [
      {
        name: PERMISSION_ADS_POST.ADS_COMMUNITY,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.DATA_PROCESSING]: [
      {
        name: PERMISSION_FEATURE.DATA_PROCESSING,
        isHasPermission: true,
        isLimitRecord: 100000,
      },
    ],
    [PERMISSION_FEATURE.ENRICHMENT]: [],
  },
  PRO: {
    [PERMISSION_FEATURE.PERSONA]: [
      {
        name: PERMISSION_PERSONA.PERSONAL_DEMOGRAPHIC,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_LOCATION,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_MAP_RADIUS,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_INDUSTRY_KEYWORD,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_DEPARTMENT,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_MANAGEMENT,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_COMPANY_NAME,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_PERSONA.PERSONAL_COMPANY_SIZE,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.SOCIAL_DATA]: [
      {
        name: PERMISSION_SOCIAL.SOCIAL_SEARCH_DATA,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_SOCIAL.SOCIAL_VIEW_PHONE,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.REQUEST_AUDIENCE]: [
      {
        name: PERMISSION_REQUEST_AUDIENCE.REQUEST_IMPORT,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.LIVE_POST]: [
      {
        name: PERMISSION_LIVE_POST.LIVE_COMMUNITY,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
      {
        name: PERMISSION_LIVE_POST.FOLLOW_PAGE,
        isHasPermission: true,
        isHasLimit: true,
        isExceedLimit: true,
      },
    ],
    [PERMISSION_FEATURE.ADS_POST]: [
      {
        name: PERMISSION_ADS_POST.ADS_COMMUNITY,
        isHasPermission: true,
        isHasLimit: false,
        isExceedLimit: false,
      },
    ],
    [PERMISSION_FEATURE.DATA_PROCESSING]: [
      {
        name: PERMISSION_FEATURE.DATA_PROCESSING,
        isHasPermission: true,
        isLimitRecord: 1000000,
      },
    ],
    [PERMISSION_FEATURE.ENRICHMENT]: [],
  },
};
export enum FEATURE_PERMISSION_KEY {
  //PERSONA
  PSN_FT_DEMOGRAPHIC = "PSN_FT_DEMOGRAPHIC",
  PSN_FT_LOCATION = "PSN_FT_LOCATION",
  PSN_FT_MAP_RADIUS = "PSN_FT_MAP_RADIUS",
  PSN_FT_DEPARTMENT = "PSN_FT_DEPARTMENT",
  PSN_FT_INDUSTRY = "PSN_FT_INDUSTRY",
  PSN_FT_MANAGEMENT_LEVEL = "PSN_FT_MANAGEMENT_LEVEL",
  PSN_SEARCH_COMPANY = "PSN_SEARCH_COMPANY",
  PSN_FT_COMPANY_SIZE = "PSN_FT_COMPANY_SIZE",
  //SOCIAL DATA
  SCD_SEARCH_AUDIENCE = "SCD_SEARCH_AUDIENCE",
  SCD_VIEW_PHONE = "SCD_VIEW_PHONE",
  SCD_REQUEST_AUDIENCE = "SCD_REQUEST_AUDIENCE",
  SCD_VIEW_LIVEPOST_COMMUNITY = "SCD_VIEW_LIVEPOST_COMMUNITY",
  SCD_LIVEPOST_FOLLOW_PAGE = "SCD_LIVEPOST_FOLLOW_PAGE",
  SCD_VIEW_YOUR_PAGE = "SCD_VIEW_YOUR_PAGE",
  SCD_VIEW_ADPOST_COMMUNITY = "SCD_VIEW_ADPOST_COMMUNITY",
  SCD_VIEW_YOUR_REQUEST = "SCD_VIEW_YOUR_REQUEST",

  YOUR_AUDIENCE = "YOUR_AUDIENCE",
  YOUR_SEGMENT = "YOUR_SEGMENT",
  SEGMENT_PROCESSING = "SEGMENT_PROCESSING",
}
