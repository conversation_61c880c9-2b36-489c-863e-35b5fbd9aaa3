export type PERMISSION_TYPE = "EXCEED_LIMIT" | "LIMIT" | "NO_PERMISSION" | "NO_LIMIT";

export const PERMISSION_VALUE: { [key in PERMISSION_TYPE]: PERMISSION_TYPE } = {
  EXCEED_LIMIT: "EXCEED_LIMIT",
  LIMIT: "LIMIT",
  NO_LIMIT: "NO_LIMIT",
  NO_PERMISSION: "NO_PERMISSION",
};

export enum PERMISSION_FEATURE {
  PERSONA = "persona",
  SOCIAL_DATA = "social-data",
  REQUEST_AUDIENCE = "request-audience",
  LIVE_POST = "live-post",
  ADS_POST = "ads-post",
  DATA_PROCESSING = "data-processing",
  ENRICHMENT = "enrichment",
}

export enum PERMISSION_PERSONA {
  PERSONAL_DEMOGRAPHIC = "persona-filter-demographic",
  PERSONAL_LOCATION = "persona-filter-location",
  PERSONAL_MAP_RADIUS = "persona-filter-radius",
  PERSONAL_INDUSTRY_KEYWORD = "persona-filter-industry",
  PERSONAL_DEPARTMENT = "persona-filter-department",
  PERSONAL_MANAGEMENT = "persona-filter-management",
  PERSONAL_COMPANY_NAME = "persona-filter-company_name",
  PERSONAL_COMPANY_SIZE = "persona-filter-company_size",
}
export enum PERMISSION_SOCIAL {
  SOCIAL_SEARCH_DATA = "social-search-data",
  SOCIAL_VIEW_PHONE = "social-view-phone",
}

export enum PERMISSION_REQUEST_AUDIENCE {
  REQUEST_IMPORT = "request-import",
  ADD_MORE_REQUEST = "add-more-request",
}
export enum PERMISSION_LIVE_POST {
  LIVE_COMMUNITY = "live-community",
  FOLLOW_PAGE = "follow-page",
}

export enum PERMISSION_ADS_POST {
  ADS_COMMUNITY = "ads-community",
}
