import { ColDef, ICellRendererParams, ValueFormatterParams } from "ag-grid-community";

import InformationColumn from "views/SocialDataView/columns/InformationColumn";
import RatingColumn from "views/SocialDataView/columns/RatingColumn";
import PackageColumn from "views/SocialDataView/columns/PackageColumn";

import InfoDetailCol from "views/SocialDataView/columns/InfoDetailCol";

import PhoneColumn from "components/column/PhoneColumn";
import GenderColumn from "components/column/GenderColumn";
import EmailColumn from "components/column/EmailColumn";
import DefaultColumn from "components/column/DefaultColumn";
import { formatDate } from "utils/utils";
import AudiencesSizeColumn from "components/column/AudiencesSizeColumn";
import AddAudienceColumn from "views/SocialDataView/columns/AddAudienceColumn";
import { SortType } from "types";
import ScoresColumn from "views/SocialDataDetail/components/ScoresColumn";
import { ColumnDef } from "@tanstack/react-table";
import { IUserPreview } from "types/SocialData";
import { formatLabel } from "utils/socialData/formatRelationShips";
import TypeColumn from "views/SocialDataView/columns/TypeColumn";
import { fNumberToString } from '../../utils/number';

export const SOCIAL_AUDIENCES_COLUMN = ({
  handleSortChange,
  onRefresh,
}: {
  handleSortChange: (sortType: SortType, field: string) => void;
  onRefresh: () => Promise<void>;
}) => [
  {
    field: "name",
    headerName: "People/Fanpage/Group",
    width: 250,
    minWidth: 180,
    maxWidth: 438,
    cellClass: '!px-4 overflow-hidden',
    flex: 1,
    cellRenderer: InformationColumn,
  },
  {
    field: "size",
    headerName: "Sizes",
    headerClass:'[&>.ag-header-cell-comp-wrapper]:justify-end [&>.ag-header-cell-comp-wrapper]:pr-4',
    cellRenderer: (params: ICellRendererParams) => AudiencesSizeColumn({ size: params.data.size }),
    sortable: true,
    headerComponentParams: {
      handleSortChange,
    },
    cellClass: "justify-end !px-4",
    width: 100,
    minWidth: 100,
    maxWidth: 180,
  },
  {
    field: "type",
    headerName: "Type",
    cellRenderer: TypeColumn,
    width: 130,
    minWidth: 130,
    maxWidth: 130,
    cellClass: '!px-4',
  },
  {
    field: "rating",
    headerName: "Rating",
    headerClass:'custom-ag-header--center [&>.ag-header-cell-comp-wrapper]:justify-end [&>.ag-header-cell-comp-wrapper]:!w-[100px] [&>.ag-header-cell-comp-wrapper]:mx-auto [&>.ag-header-cell-comp-wrapper]:px-3',
    cellRenderer: RatingColumn,
    cellClass: "justify-center !px-4",
    sortable: true,
    headerComponentParams: {
      handleSortChange,
    },
    width: 130,
    minWidth: 130,
    maxWidth: 180,
  },
  {
    field: "package",
    headerName: "Package",
    width: 100,
    minWidth: 100,
    maxWidth: 180,
    cellClass: '!px-4',
    cellRenderer: (params: ValueFormatterParams) => (
      <PackageColumn package_value={params.data.package} />
    ),
  },
  {
    field: "actions",
    headerName: "",
    width: 290,
    minWidth: 290,
    maxWidth: 290,
    cellClass: '!px-4',
    cellRenderer: (params: ICellRendererParams) => (
      <AddAudienceColumn params={params} onRefresh={onRefresh} />
    ),
  },
];

export const TRENDING_COLUMN: ColDef[] = [
  {
    field: "name",
    headerName: "Trending",
    cellClass: '!px-4 overflow-hidden border border-r-[#E1E2E3]',
    minWidth: 180,
    maxWidth: 438,
    flex: 1,
    cellRenderer: InformationColumn,
  },
  {
    field: "size",
    headerName: "Sizes",
    headerClass:'[&>.ag-header-cell-comp-wrapper]:justify-end [&>.ag-header-cell-comp-wrapper]:pr-4',
    cellRenderer: (params: ICellRendererParams) => AudiencesSizeColumn({ size: params.data.size }),
    cellClass: "justify-end !px-4",
    width: 100,
    minWidth: 100,
    maxWidth: 180,
  },
  {
    field: "type",
    headerName: "Type",
    cellRenderer: TypeColumn,
    cellClass:'items-end !px-4',
    width: 100,
    minWidth: 100,
    maxWidth: 100,
  },
  {
    field: "rating",
    headerName: "Rating",
    headerClass:'custom-ag-header--center [&>.ag-header-cell-comp-wrapper]:justify-end [&>.ag-header-cell-comp-wrapper]:!w-[100px] [&>.ag-header-cell-comp-wrapper]:mx-auto [&>.ag-header-cell-comp-wrapper]:px-3',
    cellRenderer: RatingColumn,
    cellClass: "justify-center  !px-4",
    width: 130,
    minWidth: 130,
    maxWidth: 180,
  },
  {
    field: "package",
    headerName: "Package",
    cellClass: '!px-4',
    width: 100,
    minWidth: 100,
    maxWidth: 180,
    cellRenderer: (params: ValueFormatterParams) => (
      <PackageColumn package_value={params.data.package} />
    ),
  },
];

export const USER_PREVIEW_COLUMNS: ColDef[] = [
  {
    field: "fullname",
    headerName: "Full Name",
    flex: 2,
    minWidth: 250,
    cellRenderer: InfoDetailCol,
    cellRendererParams: {
      name: "fullname",
    },
  },
  {
    field: "phone",
    headerName: "Phone",
    cellRenderer: (params: ICellRendererParams) =>
      PhoneColumn({
        id: params.data?.fb_uid,
        endpoint: "phonenumber",
        phoneNumber: params.data?.phone,
        type: "social",
      }),
    width: 150,
    cellRendererParams: {
      name: "phone",
    },
  },
  {
    field: "gender",
    headerName: "Gender",
    cellRenderer: GenderColumn,
    cellRendererParams: {
      name: "gender",
    },
    width: 150,
    cellClass: "capitalize",
  },
  {
    field: "dob",
    headerName: "Date Of Birth",
    cellRenderer: DefaultColumn,
    cellRendererParams: {
      name: "dob",
    },
    width: 150,
    valueFormatter: (params: ValueFormatterParams) => {
      return formatDate(params.value);
    },
  },
  {
    field: "city",
    headerName: "City",
    cellRenderer: DefaultColumn,
    cellRendererParams: {
      name: "city",
    },
    width: 150,
    cellClass: "capitalize line-clamp-1",
    valueFormatter: ({ value }) => {
      return String(value).replace("province", "");
    },
  },
  {
    field: "scores",
    headerName: "Interest Score",
    cellRenderer: ScoresColumn,
    width: 250,
    cellClass: "capitalize line-clamp-1",
  },
  {
    field: "location",
    headerName: "Location",
    cellRenderer: DefaultColumn,
    width: 150,
    cellRendererParams: {
      name: "location",
    },
  },
  {
    field: "username",
    headerName: "Relationships",
    cellRenderer: DefaultColumn,
    cellRendererParams: {
      name: "username",
    },
    cellClass: "lowercase",
  },
  {
    field: "email",
    headerName: "Email",
    cellRenderer: EmailColumn,
    cellRendererParams: {
      name: "email",
    },
    cellClass: "lowercase",
    width: 200,
  },
];


interface Props {
  isDataset?: boolean;
}
export const getSocialAudienceColumns = (props: Props): ColumnDef<IUserPreview>[] => [
  {
    accessorKey: "fullname",
    header: "Full Name",
    cell: ({ row }) => (
      <div className="w-[200px]">
        <InfoDetailCol {...row.original} />
      </div>
    ),
    meta: {
      sticky: 'left',
      "w-[300px] text-left h-[60px]": "w-[300px] text-left h-[60px]",
    },
  },
  {
    accessorKey: "phone",
    header: "Phone",
    enableHiding: props.isDataset,
    cell: ({ row }) => (
      <PhoneColumn
        id={row.original?.fb_uid}
        endpoint="phonenumber"
        phoneNumber={row.original?.phone}
        type="social"
      />
    ),
  },
  {
    accessorKey: "gender",
    header: "Gender",
    cell: ({ row }) => <GenderColumn value={row.original?.gender} />,
  },
  {
    accessorKey: "dob",
    header: "Date of Birth",
    cell: ({ row }) => (
      <div className="w-[110px]">
        <DefaultColumn value={formatDate(row.original.dob)} />
      </div>
    ),
    size: 150,
    meta: {
      "w-[150px] text-left": "w-[150px] text-left",
    },
  },
  {
    accessorKey: "city",
    header: "City",
    cell: ({ row }) => (
      <div className="w-[150px] capitalize">
        <DefaultColumn value={row.original.city} />
      </div>
    ),
    meta: {
      "w-[150px] text-left": "w-[150px] text-left",
    },
  },
  {
    accessorKey: "scores",
    header: "Interest Score",
    cell: ({ row }) => <ScoresColumn {...row.original} />,
    meta: {
      "!w-[250px] text-left": "!w-[250px] text-left",
    },
  },
  // {
  //   accessorKey: "location",
  //   header: "Location",
  //   cell: ({ row }) => <DefaultColumn value={row.original.location} />,
  //   meta: {
  //     "text-left": "text-left",
  //   },
  // },
  {
    accessorKey: "username",
    header: "Relationship",
    cell: ({ row }) => {
      const newData = formatLabel(row.original?.relationships);
      return (
        <div className="capitalize">
          <DefaultColumn value={newData} />
        </div>
      );
    },
    meta: {
      "text-left": "text-left",
    },
  },
  {
    accessorKey: "followers",
    header: "Followers",
    cell: ({ row }) => (
      <div className="w-[110px] text-center">
        {row.original.followers ? <DefaultColumn value={fNumberToString(row.original.followers)} /> : "-"}
      </div>
    ), meta: {
      "w-[110px]": "w-[110px]"
    }
  },
  {
    accessorKey: "friends",
    header: "Friends",
    cell: ({ row }) => (
      <div className="w-[110px] text-center">
        {row.original.friends ? <DefaultColumn value={fNumberToString(row.original.friends)} /> : "-"}
      </div>
    ), meta: {
      "w-[110px]": "w-[110px]"
    }
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({ row }) => <DefaultColumn value={row.original.email} isLowercase />,
    meta: {
      "text-left": "text-left",
    },
  },
];
