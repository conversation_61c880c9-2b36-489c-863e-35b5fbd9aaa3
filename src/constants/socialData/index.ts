import { TAttribute } from "types/Attribute";

export const TypeOptions: TAttribute[] = [
  { id: "0", name: "Social Persona" },
  { id: "1", name: "Group" },
  { id: "2", name: "Fanpage" },
  { id: "3", name: "Profile" },
  { id: "4", name: "Post" },
];

export const TypeSelectMS: TAttribute[] = [
  { id: "1", name: "Group" },
  { id: "2", name: "Fanpage" },
  { id: "3", name: "Profile" },
  { id: "4", name: "Post" },
];

export const packageOptions: TAttribute[] = [
  { id: "bronze", name: "Bronze" },
  { id: "silver", name: "Silver" },
  { id: "gold", name: "Gold" },
  { id: "platinum", name: "Platinum" },
  { id: "diamond", name: "Diamond" },
  { id: "titanium", name: "Titanium" },
];

export const maxExceeded = 300000;
