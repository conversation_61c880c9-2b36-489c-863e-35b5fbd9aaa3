import { ColumnDef } from "@tanstack/react-table";
import GenderColumn from "components/column/GenderColumn";

import { capitalizeOfEachWord, formatDate } from "utils/utils";
import Information from "views/SocialPersona/column/Information";
import PhoneColumn from "views/SocialPersona/column/PhoneColumn";
import DefaultColumn from "components/column/DefaultColumn";
import { SocialPersonaPreview } from "types/SocialPersona";
import { fNumberToString } from "../../utils/number.ts";
import ScoresColumn from '../../views/SocialDataDetail/components/ScoresColumn';


export const getSocialScoreColumn = (): ColumnDef<SocialPersonaPreview>[] => [
  {
    accessorKey: "fullName",
    header: "Full Name",
    cell: ({ row }) => <Information {...row.original} />,
    meta: {
      sticky: 'left',
      "text-left": "text-left"
    }
  },
  {
    accessorKey: "phone",
    header: "Phone",
    cell: ({ row }) => (
      <PhoneColumn
        endpoint="phonenumber"
        name={row?.original?.fullname}
        phoneNumber={row?.original?.phone}
      />
    ),
    meta: {
      "w-[150px]": "w-[150px]"
    }
  },
  {
    accessorKey: "gender",
    header: "Gender",
    cell: ({ row }) => <GenderColumn value={row?.original?.gender} />
  },
  // {
  //   accessorKey: "age",
  //   header: "Age",
  //   cell: ({ row }) => (
  //     <div className="w-[110px] text-center">
  //       <DefaultColumn value={row.original.age} />
  //     </div>
  //   ),
  //   meta: {
  //     "w-[110px]": "w-[110px]"
  //   }
  // },
  {
    accessorKey: "dob",
    header: "Date of Birth",
    cell: ({ row }) => (
      <div className="w-[110px] text-center">
        <DefaultColumn value={formatDate(row.original.dob)} />
      </div>
    ),
    meta: {
      "w-[150px]": "w-[150px]"
    }
  },
  {
    accessorKey: "city",
    header: "City",
    cell: ({ row }) => (
      <div className="w-[150px] capitalize">
        <DefaultColumn value={row.original.city} />
      </div>
    ),
    meta: {
      "w-[150px] text-left": "w-[150px] text-left",
    },
  },
  {
    accessorKey: "scores",
    header: "Interest Score",
    cell: ({ row }) => ScoresColumn({ scores: row.original.scores }),
    meta: {
      "w-[250px] text-left": "w-[250px] text-left",
    },
  },
  {
    accessorKey: "relationship",
    header: "Relationship",
    cell: ({ row }) => (
      <div className="w-[110px] text-center">
        {row.original.relationships ? <DefaultColumn value={capitalizeOfEachWord(row.original.relationships)} /> : "-"}
      </div>
    ), meta: {
      "w-[110px]": "w-[110px]"
    }
  },
  {
    accessorKey: "followers",
    header: "Followers",
    cell: ({ row }) => (
      <div className="w-[110px] text-center">
        {row.original.followers ? <DefaultColumn value={fNumberToString(row.original.followers)} /> : "-"}
      </div>
    ), meta: {
      "w-[110px]": "w-[110px]"
    }
  },
  {
    accessorKey: "friends",
    header: "Friends",
    cell: ({ row }) => (
      <div className="w-[110px] text-center">
        {row.original.friends ? <DefaultColumn value={fNumberToString(row.original.friends)} /> : "-"}
      </div>
    ), meta: {
      "w-[110px]": "w-[110px]"
    }
  },
  {
    accessorKey: "email",
    header: "Email",
    cell: ({ row }) => (
      <div className="text-center">
        <DefaultColumn value={row.original.email} className={'xl:max-w-[200px]'} isLowercase />
      </div>
    )
  }
];
