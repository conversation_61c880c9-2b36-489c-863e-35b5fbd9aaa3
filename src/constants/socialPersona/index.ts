import { IDimensionsPersona } from "store/redux/dimensions/slice";
import { TAttribute } from "types/Attribute";

export const dimensionFieldPersona: Record<string, keyof IDimensionsPersona> = {
  // age_segment: "age",
  position_department: "department",
  // position_level: "level",
  // company_size_segment: "company_size",
  company_industry: "industry",
};

export const FILTER_KEY = {
  age: "age_segment__in",
  gender: "gender__in",
  province: "person_province__in",
  region: "person_region__in",
  category: "category__in",
  categoryRanking: "category_ranking",
  categoryDefault: "category",
  score: "score_range",
  relationships: "relationships__in",
  city: "city__in",
  friend: "friend_segment__in",
  follower: "follower_segment__in"
};

export const FILTER_TITLE = {
  age_segment__in: "Age",
  gender__in: "Gender",
  person_province__in: "Province",
  person_region__in: "Region",
  category__in: "Categories",
  category_ranking: "Category Ranking",
  category: "Category",
  score_range: "Score Range",
  relationships__in: "Relationships",
  city__in: "City",
  friend_segment__in: "Friend Segment",
  follower_segment__in: "Follower Segment"
};

export const ScoreOptionsAtt: TAttribute[] = [
  { name: '4-5', id: '40-50' },
  { name: '3-4', id: '30-40' },
  { name: '2-3', id: '20-30' },
  { name: '1-2', id: '10-20' },
  { name: '0-1', id: '0-10' }
];

const CRM_360 = "/crm360";
export const CRM_ROUTE = {
  contactList: `${CRM_360}/contact`
};
