import { IOperationTabs, Operator } from "types/YourData";
import { DATAPROCESSING_LABEL, regionNames } from "./label";
import GroupIntersect from "assets/icons/GroupIntersect";
import GroupMix from "assets/icons/GroupMix";
import GroupMinus from "assets/icons/GroupMinus";
import { removeVietnameseTones } from "utils/utils";

export const DATA_OPERATION_TABS: IOperationTabs[] = [
  {
    title: DATAPROCESSING_LABEL.operation_intersect,
    content: DATAPROCESSING_LABEL.operation_intersect_content,
    iconComponent: GroupIntersect,
    value: Operator.intersect,
  },
  {
    title: DATAPROCESSING_LABEL.operation_mix,
    content: DATAPROCESSING_LABEL.operation_mix_content,
    iconComponent: GroupMix,
    value: Operator.mix,
  },
  {
    title: DATAPROCESSING_LABEL.operation_minus,
    content: DATAPROCESSING_LABEL.operation_minus_content,
    iconComponent: GroupMinus,
    value: Operator.minus,
  },
];

export const sortCity = (city: string[]) => {
  const VNCity: string[] = [];
  const otherCity: string[] = [];
  const newTemp = regionNames.map((item) => removeVietnameseTones(item.toLocaleLowerCase()));
  city.forEach((item) => {
    if (newTemp.includes(removeVietnameseTones(item.toLowerCase()))) {
      VNCity.push(item);
    } else {
      otherCity.push(item);
    }
  });
  return VNCity.sort().concat(otherCity);
};

export const RELATIONSHIP_GENDER_OPTIONS = [
  {
    label: "Dating",
    value: "dating",
  },
  {
    label: "Single",
    value: "single",
  },
  {
    label: "Married",
    value: "married",
  },
  {
    label: "Widowed",
    value: "widowed",
  },
  {
    label: "Divorced",
    value: "divorced",
  },
  {
    label: "Separated",
    value: "separated",
  },
  {
    label: "Complicated",
    value: "complicated",
  },
  {
    label: "Registered Partnership",
    value: "registered_partnership",
  },
  {
    label: "Unknown",
    value: "null",
  },
];

export const PlatFormArr = [
  {
    data: {
      title: 'CRM360',
      description: "Connect your sales and marketing teams with customers across all channels (Call center, Email, Facebook Ads, Tiktok Ads, Google Ads, Zalo ZNS, and more)",
      isComingSoon: false
    },
    type: "crm360"
  },
  // {
  //   data: {
  //     title: "Hubspot",
  //     description: "An all-in-one CRM platform that helps businesses manage customer relationships, automate marketing, and optimize sales processes.",
  //     isComingSoon: true
  //   },
  //   type: "hubspot"
  // },
  // {
  //   data: {
  //     title: "Saleforce",
  //     description: "A leading CRM platform that helps businesses manage customer relationships, automate sales processes, and drive growth.",
  //     isComingSoon: true
  //   },
  //   type: "saleforce"
  // },
  // {
  //   data: {
  //     title: "Pipedrive",
  //     description: "A sales-focused CRM that helps businesses streamline their pipelines, track deals, and close more sales efficiently.",
  //     isComingSoon: true
  //   },
  //   type: "pipedrive"
  // }
];
