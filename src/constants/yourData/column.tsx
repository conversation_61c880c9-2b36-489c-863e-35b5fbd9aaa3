import { ColumnDef } from '@tanstack/react-table';

import GroupAvatarColumn from 'views/YourSegment/components/GroupAvatarColumn';
import BadgePackage from 'components/BadgePackage';
import DescriptionColumn from 'components/yourAudienceAndSegment/DescriptionColumn';
import AudiencesSizeColumn from 'components/column/AudiencesSizeColumn';
import PersonaAudienceActionCol from 'views/YourAudience/components/column/PersonaAudienceActionCol';
import NamePersonaColumn from 'views/YourSegment/components/column/NamePersonaColumn';
import SocialSegmentActionCol from 'views/YourSegment/components/column/SocialSegmentActionCol';
import PersonaSegmentActionCol from 'views/YourSegment/components/column/PersonaSegmentActionCol';
import InfoPersonaSegment from 'views/YourSegment/components/column/InforPersonaSegment';
import InfoColumn from 'views/YourAudience/components/column/InfoColumn';
import InforSocialSegment from 'views/YourSegment/components/column/InforSocialSegment';

import { AudienceSegmentItem } from 'types/yourSegment';
import { IYourAudienceItem } from 'types/yourAudience';

import { PersonaAudienceItem, PersonaSegment } from 'types/Persona';
import { formatDate } from 'utils/utils';
import { TypeOptions } from 'constants/socialData';
import PersonaAudienceSocialActionCol from '../../views/YourAudience/components/column/PersonaAudienceSocialActionCol';

export const SOCIAL_AUDIENCE_COLUMN: ColumnDef<IYourAudienceItem>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => <InfoColumn {...row?.original} />,
    size: 200,
    maxSize: 200,
    meta: {
      "text-left font-medium": "text-left font-medium",
    },
  },
  {
    accessorKey: "dataType",
    header: "Data Type",
    cell: ({ row }) => <div className="max-w-[80px] w-[80px] capitalize">{row.original?.datatype?.toLocaleLowerCase()}</div>,
    size: 80,
    meta: {
      "text-left font-medium": "text-left font-medium",
    },
  },
  {
    accessorKey: "size",
    header: "Size",
    cell: ({ row }) => <div className='w-[80px]'>
      <AudiencesSizeColumn disableProfile={true} size={row?.original.size} />
    </div>,
    size: 80,
    meta: {
      "text-right": "text-right",
    },
  },
  {
    accessorKey: "package",
    header: "Package",
    cell: ({ row }) => (
      <div className="text-center">
        <BadgePackage packageValue={row?.original?.package} />
      </div>
    ),
    size: 200,
    meta: {
      "text-center": "text-center",
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => (
      <div className="text-center w-full">
        {TypeOptions.find((option) => option.id == (row?.original?.type as any))?.name as string}
      </div>
    ),
    size: 120,
  },
  {
    accessorKey: "date_created",
    header: "Add Date",
    meta: {
      "min-w-[150px]": "min-w-[150px]",
    },
    size: 180,
    cell: ({ row }) => (
      <div className="text-center min-w-[150px]">{formatDate(row.original.date_created, true)}</div>
    ),
  },
  {
    accessorKey: "action",
    header: "",
    meta: {
      sticky: 'right',
      'min-w-[50px]': 'min-w-[50px]'
    },
    cell: ({ row }) => {
      const isAudience = row.original?.datatype == 'AUDIENCE';
      return isAudience?<PersonaAudienceSocialActionCol {...row.original} />:<></>
    },
    size: 50,
  },
];

export const PERSONA_AUDIENCE_COLUMN: ColumnDef<PersonaAudienceItem>[] = [
  {
    accessorKey: "audience_name",
    header: "Name",
    cell: ({ row }) => (
      <NamePersonaColumn name={row?.original.audience_name} id={row.original?.id} />
    ),
    size: 200,
    meta: {
      "text-left font-medium": "text-left font-medium",
    },
  },
  {
    accessorKey: "datatype",
    header: "Data Type",
    cell: ({ row }) => <div className="max-w-[80px] w-[80px] capitalize">{row.original?.datatype?.toLocaleLowerCase()}</div>,
    size: 80,
    meta: {
      "text-left font-medium": "text-left font-medium",
    },
  },
  {
    accessorKey: "size",
    header: "Size",
    cell: ({ row }) => <div className="w-[80px]">
      <AudiencesSizeColumn disableProfile={true} size={row?.original.size} />
    </div>,
    size: 80,
    meta: {
      "text-right": "text-right",
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: () => <div className="text-center w-full">Persona</div>,
    size: 120,
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => <DescriptionColumn description={row?.original.description} className={'text-left'}/>,
    size: 250,
    meta: {
      "w-[250px]": "w-[250px]",
    },
  },
  {
    accessorKey: "date_created",
    meta: {
      "min-w-[150px]": "min-w-[150px]",
    },
    size: 180,
    header: "Add Date",
    cell: ({ row }) => (
      <div className="text-center min-w-[150px]">{formatDate(row.original.date_created, true)}</div>
    ),
  },
  {
    accessorKey: "action",
    header: "",
    meta: {
      sticky: 'right',
      'min-w-[50px]': 'min-w-[50px]'
    },
    cell: ({ row }) => <PersonaAudienceActionCol {...row.original} />,
    size: 50,
  },
];

//SEGMENT TABLE
export const SOCIAL_SEGMENT_COLUMN: ColumnDef<AudienceSegmentItem>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => <InforSocialSegment id={row.original.id} name={row?.original.name} />,
    size: 250,
    maxSize: 250,
    meta: {
      "text-left w-[250px]": "text-left w-[250px]",
    },
  },
  {
    accessorKey: "latest_audience",
    header: "Source",
    cell: ({ row }) => <GroupAvatarColumn id={row.original.id as any} name={row?.original.name} />,
    size: 150,
  },
  {
    accessorKey: "dataType",
    header: "Data Type",
    cell: ({ row }) => <div className="max-w-[80px] w-[80px] capitalize">{row.original?.datatype?.toLocaleLowerCase()}</div>,
    size: 80,
    meta: {
      "text-left font-medium": "text-left font-medium",
    },
  },
  {
    accessorKey: "size",
    header: "Size",
    cell: ({ row }) => (
      <div className="w-[80px]">
        <AudiencesSizeColumn disableProfile={true} size={row?.original.segment_size} />
      </div>
    ),
    size: 80,
    meta: {
      "text-right": "text-right",
    },
  },
  {
    accessorKey: "date_created",
    header: "Create date",
    meta: {
      "min-w-[150px]": "min-w-[150px]",
    },
    cell: ({ row }) => (
      <div className="text-center w-[150px]">{formatDate(row.original.date_created, true)}</div>
    ),
    size: 150,
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <div className="w-[250px]">
        <DescriptionColumn description={row?.original.description} className={'text-left'}/>
      </div>
    ),
    meta: {
      "text-left": "text-left",
    },
    size: 250,
  },
  {
    accessorKey: "action",
    header: "",
    meta:{
      sticky: "right",
      'min-w-[165px]': 'min-w-[165px]',
    },
    cell: ({ row }) => <SocialSegmentActionCol {...row.original}/>,
    size: 165,
  },
];

export const PERSONA_SEGMENT_COLUMN: ColumnDef<PersonaSegment>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => <InfoPersonaSegment id={row.original.id} name={row?.original.name} />,
    size: 250,
    maxSize:250,
    meta: {
      "text-left w-[250px]": "text-left w-[250px]",
    },
  },
  {
    accessorKey: "dataType",
    header: "Data Type",
    cell: ({ row }) => <div className="max-w-[80px] w-[80px] capitalize">{row.original?.datatype?.toLocaleLowerCase()}</div>,
    size: 120,
    meta: {
      "text-left font-medium": "text-left font-medium",
    },
  },
  {
    accessorKey: "segment_size",
    header: "Size",
    cell: ({ row }) => (
      <div className="w-[80px]">
        <AudiencesSizeColumn disableProfile={true} size={row?.original.segment_size} />
      </div>
    ),
    size: 120,
    meta: {
      "text-right": "text-right",
    },
  },
  {
    accessorKey: "date_created",
    header: "Create date",
    meta: {
      "min-w-[170px]": "min-w-[170px]",
    },
    cell: ({ row }) => (
      <div className="text-center ">{formatDate(row.original.date_created, true)}</div>
    ),
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <div className="w-[200px] text-center">
        <DescriptionColumn description={row?.original.description} className={'text-left'}/>
      </div>
    ),
    size: 250,
    minSize: 250,
    maxSize: 250,
    meta: {
      "w-[200px]": "w-[200px]",
      "text-left": "text-left",
    },
  },
  {
    accessorKey: "action",
    header: "",
    meta:{
      sticky: "right",
      'min-w-[50px]': 'min-w-[50px]',
    },
    cell: ({ row }) => <PersonaSegmentActionCol {...row.original} />,
    size: 50,
  },
];
