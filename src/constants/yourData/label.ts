export const DATAPROCESSING_LABEL = {
  title: "Data Processing",
  subtitle: "Create a new Segment",
  your_segment: "Your Segment",
  social_title_tab: "Social Processing",
  persona_title_tab: "Work Persona Processing",
  placeholder_search: "Search segment name...",
  operation_intersect: "Intersect",
  operation_intersect_content: "Data set will take the intersections from both segment",
  operation_mix: "Mix (+)",
  operation_mix_content:
    "Data set will combine all profile from both segment and remove duplicates",
  operation_minus: "Minus (-)",
  operation_minus_content:
    "Data set will be taken from Segment A and exclude the intersects with Segment B",
  add_segment: "+ Add your segment",
  segment_A: "Segment A",
  segment_B: "Segment B",
  add_segment_notice: "It requires at least two Segments for Processing",
  result_notice: "profile that match with your data. Do you want to create an Segment?",
  step_1: "Step 1: Select your processing method to create your new segment",
  step_2: "Step 2: Add your segment to start analyze the process data.",
  step_3: "Step 3: Analyze your data.",
};

export enum AUDIENCE_SOCIAL_FILTER {
  CITY = "city__in",
  GENDER = "gender__in",
  AGE_MONTH = "month__in",
  AGE_YEAR = "year__in",
  DOB_GTE = "dob__gte",
  DOB_LTE = "dob__lte",
  RELATIONSHIP = "relationships__in",
  AGE = "age__in",
  CATEGORY = "category",
  SCORE_RANGE = "score_range"
}

export enum AUDIENCE_PERSONA_FILTER {
  PERSON_PROVINCE = "person_province__in",
  GENDER = "gender__in",
  PERSON_REGION = "person_region__in",
  DOB_GTE = "dob__gte",
  DOB_LTE = "dob__lte",
  DOB_MONTH = "dob_month__in",
  DOB_YEAR = "dob_year__in",
  AGE_LTE = "age__lte",
  AGE_GTE = "age__gte",
  COMPANY_NAME = "company_name__ilike",
  COMPANY_SIZE = "company_size_segment__in",
  SCORE_RANGE = "score_range",
  CATEGORY = 'CATEGORY',
  POSITION_DEPARTMENT = "position_department__in",
  POSITION_LEVEL = "position_level__in",
  COMPANY_INDUSTRY = "company_industry__in",
}

export enum FILTER_KEY {
  CITY = "city__in",
  GENDER = "gender__in",
  AGE_SEGMENT = "age_segment__in",
  PERSON_PROVINCE = "person_province__in",
  PERSON_REGION = "person_region__in",
  POSITION_DEPARTMENT = "position_department__in",
  POSITION_LEVEL = "position_level__in",
  PERSON_GEO = "person_geo",
  AGE_LTE = "age__lte",
  AGE_GTE = "age__gte",
  AGE_MONTH = "month__in",
  AGE_YEAR = "year__in",
  DOB_GTE = "dob__gte",
  DOB_LTE = "dob__lte",
  RELATIONSHIP = "relationships__in",
  AGE = "age__in",
  DOB_MONTH = "dob_month__in",
  DOB_YEAR = "dob_year__in",
  COMPANY_SIZE = "company_size_segment__in",
  COMPANY_NAME = "company_name__ilike",
  COMPANY_INDUSTRY = "company_industry__in",
  COMPANY_KEYWORDS = "company_keywords__in",
  DOB = "dob__in",
  CATEGORY = "category",
  SCORE_RANGE = "score_range",
}
export enum FILTER_LABEL {
  CITY = "City",
  GENDER = "Gender",
  AGE_SEGMENT = "Age",
  PERSON_PROVINCE = "Province",
  PERSON_REGION = "Region",
  POSITION_DEPARTMENT = "Department",
  POSITION_LEVEL = "Position",
  COMPANY_SIZE_SEGMENT = "Company",
  PERSON_GEO = "Geo",
  DOB_LTE = "To",
  DOB_GTE = "From",
  AGE_MONTH = "Month",
  AGE_YEAR = "Year",
  RELATIONSHIP = "Relationships",
  AGE_RANGE = "Age Range",
  COMPANY_SIZE = "Company Size",
  COMPANY_NAME = "Enter Company name",
  COMPANY_INDUSTRY = "Industry",
  COMPANY_KEYWORDS = "Company Keywords",
  DOB = "Date of Birth",
  CATEGORY = "category",
  SCORE_RANGE = "Score Range"
}
export const processedFilter = {
  [FILTER_KEY.CITY]: FILTER_LABEL.CITY,
  [FILTER_KEY.GENDER]: FILTER_LABEL.GENDER,
  [FILTER_KEY.AGE_SEGMENT]: FILTER_LABEL.AGE_SEGMENT,
  [FILTER_KEY.PERSON_PROVINCE]: FILTER_LABEL.PERSON_PROVINCE,
  [FILTER_KEY.PERSON_REGION]: FILTER_LABEL.PERSON_REGION,
  [FILTER_KEY.POSITION_DEPARTMENT]: FILTER_LABEL.POSITION_DEPARTMENT,
  [FILTER_KEY.POSITION_LEVEL]: FILTER_LABEL.POSITION_LEVEL,
  [FILTER_KEY.PERSON_GEO]: FILTER_LABEL.PERSON_GEO,
  [FILTER_KEY.DOB_GTE]: FILTER_LABEL.DOB_GTE,
  [FILTER_KEY.DOB_LTE]: FILTER_LABEL.DOB_LTE,
  [FILTER_KEY.AGE_GTE]: FILTER_LABEL.AGE_SEGMENT,
  [FILTER_KEY.AGE_LTE]: FILTER_LABEL.AGE_SEGMENT,
  [FILTER_KEY.AGE]: FILTER_LABEL.AGE_SEGMENT,
  [FILTER_KEY.AGE_MONTH]: FILTER_LABEL.AGE_MONTH,
  [FILTER_KEY.AGE_YEAR]: FILTER_LABEL.AGE_YEAR,
  [FILTER_KEY.RELATIONSHIP]: FILTER_LABEL.RELATIONSHIP,
  [FILTER_KEY.COMPANY_SIZE]: FILTER_LABEL.COMPANY_SIZE,
  [FILTER_KEY.COMPANY_NAME]: FILTER_LABEL.COMPANY_NAME,
  [FILTER_KEY.COMPANY_INDUSTRY]: FILTER_LABEL.COMPANY_INDUSTRY,
  [FILTER_KEY.COMPANY_KEYWORDS]: FILTER_LABEL.COMPANY_KEYWORDS,
  [FILTER_KEY.DOB]: FILTER_LABEL.DOB,
  [FILTER_KEY.CATEGORY]: FILTER_LABEL.CATEGORY,
  [FILTER_KEY.SCORE_RANGE]: FILTER_LABEL.SCORE_RANGE,
};

export const GENDER_OPTIONS = [
  {
    label: "Female",
    value: "F",
  },
  {
    label: "Male",
    value: "M",
  },
  {
    label: "Other",
    value: "null",
  },
];

export enum ROUTER_TYPE {
  AUDIENCE = "your-audience",
  SEGMENT = "your-segments",
}

export const ROUTER_MAP = Object.values(ROUTER_TYPE);

export enum YOUR_AUDIENCE_LABEL {
  TITLE = "Put Big360’s Team of Data Experts to Work",
  DESCRIPTION = "Our team of data experts offer white-glove data services to help you identify the path.",
  BUTTON = "Go now",
  VIEW_NOW = "View now",
  CREATE_SEGMENT = "Create new segment",
  SEGMENT_NAME = "Segment Name",
  SEGMENT_DESCRIPTION = "Description",
  ADD = "Add",
  CREATE = "Create",
  CANCEL = "Cancel",
  EDIT = "Edit",
  ADD_TO_MY_SEGMENT = "Add to my segment",
  //profile custom
  ADD_ALL_SEGMENT = "Add all to segment",
  ADD_FILTER_SEGMENT = "Add results to segment",
  ADD_PROFILE_SEGMENT = "Add profiles to segment",
  FILTER = "Filter",
  MESSAGE_FILTER = "Another way is to use filters to refine the results you want to create a Segment from this Audience file",
}

export const LABEL_EDIT = {
  [ROUTER_TYPE.AUDIENCE]: "Edit Persona Audience",
  [ROUTER_TYPE.SEGMENT]: "Edit Persona Segment",
};
export enum TABS {
  SOCIAL_AUDIENCE = "Social Audience",
  PERSONA_AUDIENCE = "Work Persona Audience",
}
export enum TABS_SEGMENT {
  SOCIAL_AUDIENCE = "Social Segment",
  PERSONA_AUDIENCE = "Work Persona Segment",
}

export const YOUR_DATA_TITLE = {
  AUDIENCE: {
    title: "Your Audience",
    subTitle: "Audience data you added",
    title_archive: "Profile Collection",
    type_archive: "Type: Persona Audience",
    des_archive:
      "Profile Collection is where the profiles you have purchased from Persona are stored, allowing you to easily manage and access your audience data.",
    infor_archive:
      "Profile Collection is where the profiles you have purchased from Persona are stored, allowing you to easily manage and access your audience data.",
  },
  SEGMENT: {
    title: "Your Segment",
    subTitle: "Segment data you created from Your Audience",
  },
};

export enum DATE_OPTIONS {
  FROM_TO = "From - To",
  MONTH = "Month",
  YEAR = "Year",
}
export const OptionsDatePicker = [
  { label: DATE_OPTIONS.FROM_TO, value: DATE_OPTIONS.FROM_TO },
  { label: DATE_OPTIONS.MONTH, value: DATE_OPTIONS.MONTH },
  { label: DATE_OPTIONS.YEAR, value: DATE_OPTIONS.YEAR },
];
export const regionNames = [
  "An Giang",
  "Bà Rịa Vũng Tàu",
  "Bạc Liêu",
  "Bắc Kạn",
  "Bắc Giang",
  "Bắc Ninh",
  "Bến Tre",
  "Bình Dương",
  "Bình Định",
  "Bình Phước",
  "Bình Thuận",
  "Cà Mau",
  "Cao Bằng",
  "Cần Thơ",
  "Đà Nẵng",
  "Đắk Lắk",
  "Đắk Nông",
  "Điện Biên",
  "Đồng Nai",
  "Đồng Tháp",
  "Gia Lai",
  "Hà Giang",
  "Hà Nam",
  "Hà Nội",
  "Hà Tây",
  "Hà Tĩnh",
  "Hải Dương",
  "Hải Phòng",
  "Hòa Bình",
  "Hồ Chí Minh",
  "Hậu Giang",
  "Hưng Yên",
  "Khánh Hòa",
  "Kiên Giang",
  "Kon Tum",
  "Lai Châu",
  "Lào Cai",
  "Lạng Sơn",
  "Lâm Đồng",
  "Long An",
  "Nam Định",
  "Nghệ An",
  "Ninh Bình",
  "Ninh Thuận",
  "Phú Thọ",
  "Phú Yên",
  "Quảng Bình",
  "Quảng Nam",
  "Quảng Ngãi",
  "Quảng Ninh",
  "Quảng Trị",
  "Sóc Trăng",
  "Sơn La",
  "Tây Ninh",
  "Thái Bình",
  "Thái Nguyên",
  "Thanh Hóa",
  "Thừa Thiên Huế",
  "Tiền Giang",
  "Trà Vinh",
  "Tuyên Quang",
  "Vĩnh Long",
  "Vĩnh Phúc",
  "Yên Bái",
];
