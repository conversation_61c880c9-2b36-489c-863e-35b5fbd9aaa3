import { ColumnDef } from "@tanstack/react-table";

import AudiencesSizeColumn from "components/column/AudiencesSizeColumn";
import Status from "views/RequestAudienceView/components/column/Status";
import CountdownTimer from "views/RequestAudienceView/components/column/CountDownTimer";
import TypeColumn from "views/RequestAudienceView/components/column/TypeColumn";
import InformationWrapper from "views/RequestAudienceView/components/column/InformationWrapper";

import { convertDate } from "utils/utils";
import { TDataResponse } from "types/RequestAudience";

export const REQUEST_AUDIENCE_COLUMN: ColumnDef<TDataResponse>[] = [
  {
    accessorKey: "name",
    header: "Audience Name",
    cell: ({ row }) => <InformationWrapper {...row.original} />,
    meta: {
      "text-left h-[60px]": "text-left h-[60px]",
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => <TypeColumn {...row.original} />,
    meta: {
      "w-[150px]": "w-[150px]",
    },
  },
  {
    accessorKey: "size",
    header: "Size",
    cell: ({ row }) => (
      <AudiencesSizeColumn
        disableProfile={true}
        size={row?.original?.audience?.size}
        status={row?.original?.status}
        isCheckStatus={true}
      />
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => <div className='flex justify-center items-center'><Status status={row?.original?.status} /></div>,
  },
  {
    accessorKey: "date_created",
    header: "Create time",
    cell: ({ row }) => {
      const date = new Date(row.original?.audience?.date_created).toLocaleString();
      return (
        <div className="text-center text-xs md:text-sm">
          {row.original?.audience?.date_created ? convertDate(date ?? "").replace(",", " ") : "--"}
        </div>
      );
    },
    meta: {
      "min-w-[152px]": "min-w-[152px]",
    },
  },
  {
    enableResizing: false,
    accessorKey: "eta_time",
    header: "Estimate time",
    cell: ({ row }) => (
      <CountdownTimer status={row?.original?.status} eta_time={row?.original?.eta_time} />
    ),
    meta: {
      "min-w-[152px]": "text-right min-w-[152px]",
    },
  },
];
