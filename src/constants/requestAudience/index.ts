export enum TYPE_SOCIAL {
  "group" = 1,
  "fanpage" = 2,
  "profile" = 3,
  "post" = 4,
}

export enum TYPE_SOCIAL_IMAGE {
  "group" = 1,
  "page" = 2,
  "profile" = 3,
  "post" = 4,
}

export enum SUB_TYPE_SOCIAL {
  "private_group" = 1,
  "public_group" = 2,
  "ad_post" = 3,
  "live_post" = 4,
}
export const TYPE_SOCIAL_LABEL = [
  { id: 1, name: "Group" },
  { id: 2, name: "Fanpage" },
  { id: 3, name: "Profile" },
  { id: 4, name: "Post" },
];
export const SUB_TYPE_SOCIAL_LABEL = [
  { id: 1, name: "Private Group" },
  { id: 2, name: "Public Group" },
  { id: 3, name: "Ads Post" },
  { id: 4, name: "Live Post" },
];
