import { Link } from "react-router-dom";
import { RiDownload2Line } from "@remixicon/react";
import { ColumnDef } from "@tanstack/react-table";

import { Badge } from "components/ui/badge";

import { InvoicesResponse } from "types/Payment";
import { convertDate } from "utils/utils";
import InvoiceType from "views/UserBillingView/components/InvoiceType";

export const INVOICES_COLUMN: ColumnDef<InvoicesResponse>[] = [
  {
    accessorKey: "invoice_code",
    header: "Invoice ID",
    cell: ({ row }) => <p className="text-primary"> #{row?.original?.invoice_code}</p>,
    meta: {
      "text-left min-w-[200px]": "text-left min-w-[200px]",
    },
  },
  {
    accessorKey: "billing_date",
    header: "Billing Date",
    cell: ({ row }) => {
      const date = new Date(row.original?.billing_date).toLocaleString();
      return convertDate(date ?? "").replace(",", " ");
    },
    meta: {
      "min-w-[190px]": "min-w-[190px]",
    },
  },
  {
    accessorKey: "plan_code",
    header: "Plan",
    cell: ({ row }) => (
      <p className="capitalize"> {row?.original?.plan_code?.toLocaleLowerCase() || "--"}</p>
    ),
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => <InvoiceType type={row?.original?.type} />,
    meta: {
      "min-w-[170px]": "min-w-[170px]",
    },
  },
  {
    accessorKey: "total_amount",
    header: "Amount",
    cell: ({ row }) => <p> ${row?.original?.total_amount}</p>,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <Badge
        className="border-none p-2 rounded-xl font-normal capitalize"
        variant="success"
        children={row?.original?.status}
      />
    ),
  },

  {
    enableResizing: false,
    accessorKey: "action",
    header: "",
    cell: ({ row }) => (
      <Link to={row?.original?.invoice_pdf}>
        <RiDownload2Line size={16} />
      </Link>
    ),
    meta: {
      "min-w-[50px]": "min-w-[50px]",
    },
  },
];
