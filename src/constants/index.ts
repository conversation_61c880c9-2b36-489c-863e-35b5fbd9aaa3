import { buttonVariants } from "components/ui/button";
import { cn, getAgeValue } from 'utils/utils';

export const LABEL = {
  filter: "Filter",
  view_more: "View more",
  view_less: "View less",
  request_audiences: "Request Audience",
  clear: "Clear",
  result: "Result",
};
export const CARD_CLASS =
  "mt-6 pt-[22px] px-6 pb-6 rounded-2xl border-[0.5px] border-[#0A0F2914] shadow-[0_10px_16px_-3px_#14151A0D,0_3px_10px_-2px_#14151A05]";

export const OptionsDatePicker = {
  months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
  month: "space-y-4",
  caption: "flex justify-center pt-1 relative items-center",
  caption_label: "text-sm font-medium",
  nav: "space-x-1 flex items-center",
  nav_button: cn(
    buttonVariants({ variant: "outline" }),
    "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
  ),
  nav_button_previous: "absolute left-1",
  nav_button_next: "absolute right-1",
  table: "w-full border-collapse space-y-1",
  head_row: "flex",
  head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
  row: "flex w-full mt-2 rounded-lg overflow-hidden",
  cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-[#ecdffb] first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
  day: cn(
    buttonVariants({ variant: "ghost" }),
    "h-8 w-8 p-0 font-normal aria-selected:opacity-100"
  ),
  day_selected:
    "first:rounded-l-md bg-primary hover:bg-primary focus:bg-primary text-white rounded-md",
  day_today: "bg-accent text-accent-foreground",
  day_outside: "text-muted-foreground opacity-40 invisible",
  day_disabled: "text-muted-foreground opacity-40",
  day_range_middle: "aria-selected:bg-[#ecdffb] aria-selected:text-primary rounded-none",
  day_hidden: "invisible",
};

export const AGE_RANGE = ['18-24', '25-34', '35-44', '45-54', '>54', null].sort((a: string| any, b: string| any) => getAgeValue(a) - getAgeValue(b));
export const COMPANY_SIZE =  ['0-10', '10-100', '100-200', '200-500', '500-1000', '1000+', null]
// eslint-disable-next-line
export const LEVEL= ['Lãnh Đạo', 'Nhân Viên', 'Quản Lý', 'Trợ Lý', null]

export const SOLIDS_COLOR = [
  // // Row 1
  // "#000000", "#1A1A1A", "#333333", "#4D4D4D", "#666666", "#808080",
  // // Row 2
  // "#FFFF00", "#FFFF1A", "#FFFF33", "#FFFF4D", "#FFFF66", "#FFFF80",
  // // Row 3
  // "#FF8000", "#FF8C1A", "#FF9933", "#FFA64D", "#FFB366", "#FFC080",
  // // Row 4
  // "#FF0000", "#FF1A1A", "#FF3333", "#FF4D4D", "#FF6666", "#FF8080",
  // // Row 5
  // "#FF0080", "#FF1A8C", "#FF3399", "#FF4DA6", "#FF66B3", "#FF80C0",
  // // Row 6
  // "#8000FF", "#8C1AFF", "#9933FF", "#A64DFF", "#B366FF", "#C080FF",
  // // Row 7
  // "#0000FF", "#1A1AFF", "#3333FF", "#4D4DFF", "#6666FF", "#8080FF",
  // // Row 8
  // "#0080FF", "#1A8CFF", "#3399FF", "#4DA6FF", "#66B3FF", "#80C0FF",
  // // Row 9
  // "#00FF00", "#1AFF1A", "#33FF33", "#4DFF4D", "#66FF66", "#80FF80",
  // // Row 10
  // "#80FF00", "#8CFF1A", "#99FF33", "#A6FF4D", "#B3FF66", "#C0FF80",

  // Row 1
  
  '#000000',
  '#FFFF00',
  '#FF8000',
  '#FF0000',
  '#FF0080',
  '#8000FF',
  '#0000FF',
  '#0080FF',
  '#00FF00',
  '#80FF00',
  // Row 2
  
  '#1A1A1A',
  '#FFFF1A',
  '#FF8C1A',
  '#FF1A1A',
  '#FF1A8C',
  '#8C1AFF',
  '#1A1AFF',
  '#1A8CFF',
  '#1AFF1A',
  '#8CFF1A',
  // Row 3
  
  '#333333',
  '#FFFF33',
  '#FF9933',
  '#FF3333',
  '#FF3399',
  '#9933FF',
  '#3333FF',
  '#3399FF',
  '#33FF33',
  '#99FF33',
  // Row 4
  
  '#4D4D4D',
  '#FFFF4D',
  '#FFA64D',
  '#FF4D4D',
  '#FF4DA6',
  '#A64DFF',
  '#4D4DFF',
  '#4DA6FF',
  '#4DFF4D',
  '#A6FF4D',
  // Row 5
  
  '#666666',
  '#FFFF66',
  '#FFB366',
  '#FF6666',
  '#FF66B3',
  '#B366FF',
  '#6666FF',
  '#66B3FF',
  '#66FF66',
  '#B3FF66',
  // Row 6
  
  '#808080',
  '#FFFF80',
  '#FFC080',
  '#FF8080',
  '#FF80C0',
  '#C080FF',
  '#8080FF',
  '#80C0FF',
  '#80FF80',
  '#C0FF80',
];
