import { ColumnDef } from "@tanstack/react-table";
import { TypeEnrichmentRecord } from "types/Enrichment";
import DefaultRecord from "views/EnrichmentView/components/column/DefaultRecord";
import DownloadEnrichColumn from "views/EnrichmentView/components/column/DownloadEnrichColumn";
import InfoEnrichColumn from "views/EnrichmentView/components/column/InfoEnrichColumn";
import CountdownTimer from "views/RequestAudienceView/components/column/CountDownTimer";
import Status from "views/RequestAudienceView/components/column/Status";

export const ENRICHMENT_COLUMN: ColumnDef<TypeEnrichmentRecord>[] = [
  {
    accessorKey: "date_created",
    header: "Create time",
    cell: ({ row }) => <InfoEnrichColumn {...row.original} />,
    meta: {
      "text-left": "text-left",
    },
  },
  {
    accessorKey: "action",
    header: "",
    cell: ({ row }) => <DownloadEnrichColumn {...row.original} />,
  },
  {
    accessorKey: "total_records",
    header: "Total record",
    cell: ({ row }) => (
      <DefaultRecord status={row.original?.status} record={row.original?.total_records} />
    ),
    meta: {
      "text-right": "text-right",
    },
  },
  {
    accessorKey: "matched_records",
    header: "Matched record",
    cell: ({ row }) => (
      <DefaultRecord status={row.original?.status} record={row.original?.matched_records} />
    ),
    meta: {
      "text-right": "text-right",
    },
  },
  {
    accessorKey: "duplicated_records",
    header: "Duplicate record",
    cell: ({ row }) => (
      <DefaultRecord status={row.original?.status} record={row.original?.duplicated_records} />
    ),
    meta: {
      "text-right": "text-right",
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <div className="text-center">
        <Status status={row?.original?.status} />
      </div>
    ),
  },

  {
    enableResizing: false,
    accessorKey: "eta_time",
    header: "Estimate time",
    cell: ({ row }) => (
      <CountdownTimer status={row?.original?.status} eta_time={row?.original?.eta_time || ""} />
    ),
    meta: {
      "w-[152px]": "text-right w-[152px]",
    },
  },
];
