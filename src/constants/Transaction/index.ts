import { CREDIT_LABEL, CREDIT_TYPE_ENUM } from "types/Transaction";

export const CREDIT_TYPE_LABEL: CREDIT_LABEL[] = [
  {
    code: CREDIT_TYPE_ENUM.ADD_CREDIT,
    label: "Add-on Credit",
  },
  {
    code: CREDIT_TYPE_ENUM.SUBSCRIPTION,
    label: "Credit in plan",
  },
  {
    code: CREDIT_TYPE_ENUM.M_SUBSCRIPTION,
    label: "Credit in custom plan",
  },
  {
    code: CREDIT_TYPE_ENUM.ADD_MORE_RQA,
    label: "Add Request",
  },
  {
    code: CREDIT_TYPE_ENUM.BUY_PN_AUDIENCE,
    label: "Buy Profile",
  },
  {
    code: CREDIT_TYPE_ENUM.BUY_SC_DATASET,
    label: "Buy Dataset",
  },
  {
    code: CREDIT_TYPE_ENUM.BUY_SP_DATASET,
    label: "Buy Dataset",
  },
  {
    code: CREDIT_TYPE_ENUM.BUY_PN_DATASET,
    label: "Buy Dataset",
  },
  {
    code: CREDIT_TYPE_ENUM.BUY_SC_AUDIENCE,
    label: "Buy Audience",
  },
  {
    code: CREDIT_TYPE_ENUM.BUY_SP_AUDIENCE,
    label: "Buy Audience",
  },
  {
    code: CREDIT_TYPE_ENUM.BUY_PN_PROFILE,
    label: "Buy Profile",
  },
  {
    code: CREDIT_TYPE_ENUM.ENRICHMENT,
    label: "Enrichment",
  },
  {
    code: CREDIT_TYPE_ENUM.FOLLOW_PAGE,
    label: "Tracking Live",
  },
  {
    code: CREDIT_TYPE_ENUM.REFERRAL,
    label: "Referral Code",
  },
  {
    code: CREDIT_TYPE_ENUM.RESTORE,
    label: "Restore",
  },
];

export const CREDIT_POINTS = [
  600,
  700,
  800,
  900,
  1000,
  1500,
  2000,
  2500,
  3000,
  3500,
  4000,
  4500,
  5000,
];

export const calculatePrice = (val: number | string, percent: number): number => {
  const _val = val as number;
  return Math.round(_val - (percent * _val) / 100);
};

export const formatValuePrice = (val: number): number | string => {
  if (val === 100) return ">5000";
  const index = Math.round((val / 100) * (CREDIT_POINTS.length - 1));
  return CREDIT_POINTS[index];
};

export const formatPointShowSlider = (point: number | string): number => {
  if (point === ">5000") return 100;
  const index = CREDIT_POINTS.indexOf(point as number);
  return (index / (CREDIT_POINTS.length - 1)) * 100;
};
