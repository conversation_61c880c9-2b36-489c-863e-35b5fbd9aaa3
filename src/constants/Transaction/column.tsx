import { ColumnDef } from "@tanstack/react-table";

import CreditAmount from "views/UserTransaction/components/column/CreditAmount";

import { CREDIT_TYPE_LABEL } from ".";
import { ITransactionResponse } from "types/Transaction";
import { convertDate } from "utils/utils";
import TransactionInfo from "views/UserTransaction/components/column/TransactionInfo";

export const TRANSACTION_COLUMN: ColumnDef<ITransactionResponse>[] = [
  // {
  //   accessorKey: "id",
  //   header: "ID",
  //   cell: ({ row }) => <p className="text-primary"> #{row?.original?.id}</p>,
  //   meta: {
  //     "text-left min-w-[100px]": "text-left min-w-[100px]",
  //   },
  // },
  {
    accessorKey: "info",
    header: "Transaction",
    cell: ({ row }) => <TransactionInfo {...row?.original} />,
    meta: {
      "text-left min-w-[300px] xl:min-w-[350px]": "text-left min-w-[300px] xl:min-w-[350px]",
    },
  },
  {
    accessorKey: "credit_amount",
    header: "Credit",
    cell: ({ row }) => (
      <CreditAmount
        credit_amount={row?.original?.credit_amount}
        credit_change_reason={row?.original?.credit_change_reason}
      />
    ),
    meta: {
      "min-w-[150px] text-right": "min-w-[150px] text-right",
    },
  },
  {
    accessorKey: "credit_change_reason",
    header: "Type",
    cell: ({ row }) => {
      const label = CREDIT_TYPE_LABEL.find(
        (item) => item.code === row?.original?.credit_change_reason
      )?.label;
      return <div className="text-secondary normal-case min-w-[120px]">{label}</div>;
    },
    meta: {
      "min-w-[120px]": "min-w-[120px]",
    },
  },
  {
    accessorKey: "created_at",
    header: "Date & Time",
    cell: ({ row }) => {
      const date = new Date(row.original?.created_at).toLocaleString();
      return <div className="text-right min-w-[160px]">{convertDate(date ?? "").replace(",", " ")}</div>;
    },
    meta: {
      "min-w-[160px]": "min-w-[160px]",
      "text-right w-[160px]": "text-right w-[160px]",
    },
  },
];
