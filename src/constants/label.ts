const LABEL = {
  age: 'Age',
  save: 'Save',
  edit: 'Edit',
  cancel: 'Cancel',
  keep_plan: 'Keep Plan',
  delete: 'Delete',
  download: 'Download',
  select: 'Select',
  result: 'Result',
  add: 'Add',
  confirm: 'Confirm',
  buy: 'Buy',
  apply: 'Apply',
  search: 'Search',
  gender: 'Gender',
  null: 'NaN',
  clear_all: 'Clear All',
  description: 'Description',
  audience_name: 'Audience’s Name',
  audience_size: 'Audience size',
  dataset_name: 'Dataset’s Name',
  dataset_size: 'Dataset size',
  create: 'Create',
  update: 'Update',
  buy_audience: 'Buy Audience',
  buy_profile: 'Buy Profile',
  buy_dataset: 'Buy Dataset',
  buy_credit: 'Buy Credit',
  purchase: 'Purchase',
  upgrade: 'Upgrade Now',
  new_releases: 'New Releases',
  talk_to_sale: 'Talk to sales'
};
export const AUTHEN_LABEL = {
  sign_up: 'Sign up',
  login_google: 'Login with google',
  no_have_account: 'Don\'t have an account?',
  login: {
    title: 'Login to Big360 AI',
    subTitle: 'Enter your details below'
  },
  register: {
    title: 'Sign up to Big360 AI',
    subTitle: 'Enter your details below'
  },
  verifyEmail: {
    success: {
      title: 'Verify email success full',
      subTitle: 'You have successfully verified your email. You can now log in to your account.'
    },
    verified: {
      title: 'User Account is already verified',
      subTitle: 'You can now log in to your account.'
    },
    error: {
      title: 'Failed to verify email',
      subTitle:
        'You have to 10 minutes to verify your email. Please request a new verification link.'
    },
    invalid: {
      title: 'Invalid verification link'
      //   subTitle: "The token is invalid or expired",
    },
    null: {
      title: 'Verify your email',
      subTitle: 'We have sent you an email with a verification link. Please check email to verify.'
    }
  },
  forgotPassword: {
    title: 'Forgot Password',
    subTitle: 'Enter your email address below',
    subtitle_change: 'Enter your new password below',
    checkEmail: 'Check your email.',
    sentPassword: 'We’ve sent a password reset link to your email.',
    checkYourInbox: 'Please check your inbox and follow the instructions to reset your password.',
    didntReceiveEmail: 'Didn’t receive the email?',
    resend: 'resend',
    passwordReset: 'Password Reset',
    yourPasswordHasBeenSuccess: 'Your password has been successfully reset.',
    youCanNowLogin: 'You can now log in with your new password.',
    goToLogin: 'Go to login',
  }
};

export const GENDER = {
  female: 'Female',
  male: 'Male',
  null: 'Unknown'
};

export const DEFAULT_LABELS_GENDER: string[] = [
  '18-24',
  '25-34',
  '35-44',
  '45-54',
  '>54',
  'Unknown'
];

export const OptionsDatePicker = [
  {
    label: 'Date ranger',
    value: 'date_ranger'
  },
  {
    label: 'From',
    value: 'from'
  },
  {
    label: 'To',
    value: 'to'
  }
];

export enum ENDPOINT_TYPE {
  DETAIL = 'detail',
  YOUR_SEGMENT = 'your-segments',
  YOUR_AUDIENCE = 'your-audience',
}

export const ENDPOINT = [
  ENDPOINT_TYPE.DETAIL,
  ENDPOINT_TYPE.YOUR_SEGMENT,
  ENDPOINT_TYPE.YOUR_AUDIENCE
];

export default LABEL;
