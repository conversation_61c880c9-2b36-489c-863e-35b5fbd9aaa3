export const PERSONA_LABEL = {
  root: "Audiences Finders",
  title: "Work Persona",
  subTitle: "Find people precisely with the help of our advanced filtering options",
  information_filter: "Demographic",
  job_title_filter: "Job Titles",
  company_size: "Company Size",
  company_infor: "Company",
  company_name: "Company Name",
  industry: "Industry",
  industry_and_keywords: "Industry & Keywords",
  keywords: "Keywords",
  location: "Location",
  map_radius: "Map Radius",
  no_data: "Start searching your people by applying any filter in the above tool.",
  recent_search: "Recent Search",
  saved_search: "Saved Filter",
  save_new_search: "Create Filter Name",
  search_name_label: "Filter Name",
  work_info: "Work Information",
  status: "Status: ",
  role: "Role: ",
  results_found: "results found",
  filter_history: "Filter history",
  confirm_delete: "Are you sure you want to delete this filter set?",
  message_limit_min:
    "The audience file must contain at least 100 records to proceed with the purchase.",
  message_limit_max:
    "The audience file must contain fewer than 10,000 records to proceed with the purchase.",
  message_limit_filter: "Please select at least one filter",
};
