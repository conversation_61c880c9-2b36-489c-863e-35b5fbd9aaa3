import { TAttribute } from "types/Attribute";
import { TAgeRange } from "types/Persona";

export const genderOptionsAtt: TAttribute[] = [
  { id: "F", name: "Female" },
  { id: "M", name: "Male" },
  { id: "null", name: "Unknown" },
];

export const mapRadiusOptionsAtt: TAttribute[] = [
  { id: "35m", name: "35m" },
  { id: "50m", name: "50m" },
  { id: "100m", name: "100m" },
  { id: "300m", name: "300m" },
  { id: "500m", name: "500m" },
];

export const optionIconFilter = {
  size: 20,
  color: "#0F132499",
  color_active: "#924FE8",
  disable: "#a7aab1",
};

export const GENDER_COLOR = {
  female: "#924FE8",
  male: "#F48E2F",
  other: "#5E636E",
};

export const FILTER_KEY = {
  age: "age_segment__in",
  gender: "gender__in",
  province: "person_province__in",
  region: "person_region__in",
  department: "position_department__in",
  level: "position_level__in",
  company_size: "company_size_segment__in",
  company_name: "company_name__ilike",
  map_radius: "person_geo",
  industry: "company_industry__in",
  keywords: "company_keywords",
  categoryRanking: "category_ranking",
  categoryDefault: "category",
  score: "score_range",
  relationships: "relationships__in",
  city: "city__in"
};

export const FILTER_TITLE = {
  age_segment__in: "Age",
  gender__in: "Gender",
  person_province__in: "Province",
  person_region__in: "Region",
  position_department__in: "Department",
  position_level__in: "Management Level",
  company_size_segment__in: "Employee Size",
  person_geo: "Map radius",
  page: "Page",
  company_industry__in: "Industry",
  company_name__ilike: "Company Name",
  company_keywords: "Keywords",
  city__in: "City",
  category: "Category",
  score_range: "Score Range",
  relationships__in: "Relationships",
  friend_segment__in: "Friend",
  follower_segment__in: "Follower"
};

export const INITIAL_LOCATION = {
  "Bắc Trung Bộ": ["Hà Tĩnh", "Nghệ An", "Quảng Bình", "Quảng Trị", "Thanh Hóa", "Thừa Thiên Huế"],
  "Đông Bắc Bộ": [
    "Bắc Giang",
    "Bắc Kạn",
    "Cao Bằng",
    "Hà Giang",
    "Lạng Sơn",
    "Phú Thọ",
    "Thái Nguyên",
    "Tuyên Quang",
  ],
  "Đồng bằng sông Cửu Long": [
    "An Giang",
    "Bạc Liêu",
    "Bến Tre",
    "Cà Mau",
    "Cần Thơ",
    "Đồng Tháp",
    "Hậu Giang",
    "Kiên Giang",
    "Long An",
    "Sóc Trăng",
    "Tiền Giang",
    "Trà Vinh",
    "Vĩnh Long",
  ],
  "Đồng bằng sông Hồng": [
    "Bắc Ninh",
    "Hải Dương",
    "Hải Phòng",
    "Hà Nam",
    "Hà Nội",
    "Hưng Yên",
    "Nam Định",
    "Ninh Bình",
    "Quảng Ninh",
    "Thái Bình",
    "Vĩnh Phúc",
  ],
  "Đông Nam Bộ": [
    "Bà Rịa Vũng Tàu",
    "Bình Dương",
    "Bình Phước",
    "Đồng Nai",
    "Hồ Chí Minh",
    "Tây Ninh",
  ],
  "Duyên hải Nam Trung Bộ": [
    "Bình Định",
    "Bình Thuận",
    "Đà Nẵng",
    "Khánh Hòa",
    "Ninh Thuận",
    "Phú Yên",
    "Quảng Nam",
    "Quảng Ngãi",
  ],
  "Tây Bắc Bộ": ["Điện Biên", "Lai Châu", "Lào Cai", "Sơn La", "Yên Bái", "Hoà Bình"],
  "Tây Nguyên": ["Đắk Lắk", "Đắk Nông", "Gia Lai", "Kon Tum", "Lâm Đồng"],
  null: [null],
};
// constants/persona.ts
export const AGE_RANGE_OPTIONS: TAgeRange[] = [
  {
    label: "< 18",
    value: {
      min: 0,
      max: 18,
    },
  },
  {
    label: "18 - 24",
    value: {
      min: 18,
      max: 24,
    },
  },
  {
    label: "25 - 34",
    value: {
      min: 25,
      max: 34,
    },
  },
  {
    label: "35 - 44",
    value: {
      min: 35,
      max: 44,
    },
  },
  {
    label: "45 - 54",
    value: {
      min: 45,
      max: 54,
    },
  },
  {
    label: ">54 ",
    value: {
      min: 55,
      max: 100,
    },
  },
];

export const minExceeded = 100;
export const maxExceeded = 100000;
