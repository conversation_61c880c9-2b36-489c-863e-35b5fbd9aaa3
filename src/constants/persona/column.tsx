import { ColumnDef } from "@tanstack/react-table";

import Information from "views/Persona/column/Information";
import GenderColumn from "components/column/GenderColumn";
import Address from "views/Persona/column/Address";
import Company from "views/Persona/column/Company";
import Position from "views/Persona/column/Position";
import PhoneColumn from "views/Persona/column/PhoneColumn";

import { PersonaAudiencePreview } from "types/Persona";
import { formatDate } from "utils/utils";
import { SortType } from "types";

interface Props {
  buyProfile?: boolean;
  handleSortChange: (sortType: SortType, field: string) => void;
  isDataset?: boolean;
}

export const getPersonaColumns = (props: Props): ColumnDef<PersonaAudiencePreview>[] => [
  {
    accessorKey: "full_name",
    header: "Full Name",
    cell: ({ row }) => <Information {...row.original} />,
    meta: {
      sticky: 'left',
      "text-left h-[60px]": "text-left h-[60px]",
    },
  },
  {
    accessorKey: "phone",
    header: "Phone",
    enableHiding: props.isDataset,
    cell: ({ row }) => (
      <PhoneColumn
        id={row?.original?.uid}
        endpoint="phonenumber"
        name={row?.original?.full_name}
        phoneNumber={row?.original?.phone}
        buyProfile={props.buyProfile}
      />
    ),
    meta: {
      "w-[150px]": "w-[150px]",
    },
  },
  {
    accessorKey: "gender",
    header: "Gender",
    cell: ({ row }) => <GenderColumn value={row?.original?.gender} />,
  },
  {
    accessorKey: "dob",
    header: "DOB",
    cell: ({ row }) => formatDate(row?.original.dob),
  },
  {
    accessorKey: "person_district",
    header: "Address",
    cell: ({ row }) => <Address {...row?.original} />,
    meta: {
      "text-left": "text-left",
    },
  },
  {
    accessorKey: "company_name",
    header: "Company",
    cell: ({ row }) => <Company {...row?.original} />,
    meta: {
      "text-left": "text-left",
    },
  },
  {
    accessorKey: "position_depatment",
    header: "Position",
    cell: ({ row }) => <Position {...row?.original} />,
    meta: {
      "text-left": "text-left",
    },
  },
];
