export enum POST_FILTER {
  CATEGORIES = "category__in",
  FORMAT = "att_type__in",
  STATUS = "is_active",
  PLATFORM = "publisher_platform__in",
  SEARCH = "q",
  DATE_FROM = "publish_time__gte",
  DATE_TO = "publish_time__lte",
  IS_LIVE = "is_live",
}

export const POST_LABEL: Record<POST_FILTER, string> = {
  [POST_FILTER.CATEGORIES]: "Categories",
  [POST_FILTER.FORMAT]: "Format",
  [POST_FILTER.STATUS]: "Status",
  [POST_FILTER.PLATFORM]: "Platform",
  [POST_FILTER.SEARCH]: "Search",
  [POST_FILTER.DATE_FROM]: "Date From",
  [POST_FILTER.DATE_TO]: "Date To",
  [POST_FILTER.IS_LIVE]: "Status",
};

export const FILTER_CHIP_LABEL = {
  [POST_FILTER.CATEGORIES]: "Categories",
  [POST_FILTER.FORMAT]: "Format",
  [POST_FILTER.STATUS]: "Status",
  [POST_FILTER.PLATFORM]: "Platform",
  [POST_FILTER.SEARCH]: "Search",
  [POST_FILTER.DATE_FROM]: "Date Time",
  [POST_FILTER.IS_LIVE]: "Status",
};
export const StatusOptions = [
  { label: "Active", value: "true" },
  { label: "Inactive", value: "false" },
];

export const FORMAT_TYPE_LABEL = {
  IMAGE: "IMAGE",
  MULTI_IMAGES: "MULTI_IMAGES",
  VIDEO: "VIDEO",
  CAROUSEL: "CAROUSEL",
};
export const FormatOptions = [
  { label: "Image", value: FORMAT_TYPE_LABEL.IMAGE },
  { label: "Multi Image", value: FORMAT_TYPE_LABEL.MULTI_IMAGES },
  { label: "Video", value: FORMAT_TYPE_LABEL.VIDEO },
  { label: "Carousel", value: FORMAT_TYPE_LABEL.CAROUSEL },
];
