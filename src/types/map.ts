export interface IPersonMap {
  uid: number;
  company_id: number | null;
  full_name: string;
  phone: string;
  gender: "M" | "F" | string;
  dob: string;
  uid_fb: string;
  person_address: string;
  person_province: string | null;
  person_region: string | null;
  position: string | null;
  salary: number | null;
  position_department: string | null;
  position_level: string | null;
  company_name: string | null;
  company_address: string | null;
  company_size: string | null;
  date_created: string;
  person_address_latitude: number;
  person_address_longitude: number;
}
export interface IUserMap {
  items: {
    uid: string;
    person_address_latitude: number;
    person_address_longitude: number;
  }[];
  count: number;
}
export type LocationData = {
  place_id: number;
  licence: string;
  osm_type: string;
  osm_id: number;
  lat: string;
  lon: string;
  class: string;
  type: string;
  place_rank: number;
  importance: number;
  addresstype: string;
  name: string;
  display_name: string;
  boundingbox: string[];
};
