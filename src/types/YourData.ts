import { TYPE_SOCIAL } from "constants/requestAudience";
import * as yup from "yup";

export enum Operator {
  intersect = 0,
  mix = 1,
  minus = 2,
}
export interface IOperationTabs {
  title: string;
  content: string;
  iconComponent: React.FC;
  value: Operator;
}

export type ComponentType<P = { color?: string; size?: number | string }> =
  | React.ComponentClass<P>
  | React.FunctionComponent<P>;

export const CreateSegmentFormBody = yup.object().shape({
  name: yup.string().required("Name is required"),
  description: yup.string(),
});

export type CreateSegmentFormType = yup.InferType<typeof CreateSegmentFormBody>;

export const DownloadFilesBody = yup.object().shape({
  email: yup.string().email().required("Email is required").transform((value) => value ? value.toLowerCase().trim() : value),
  saveEachFile: yup.boolean().default(false),
});
export type DownloadFilesType = yup.InferType<typeof DownloadFilesBody>;
export interface TreeNode {
  op?: number;
  audience?: {
    id: string;
    name: string;
    type: TYPE_SOCIAL;
  };
  left?: TreeNode;
  right?: TreeNode;
  [key: string]: any;
}

export interface FlatTreeNode {
  op?: number;
  audience?: {
    id: string;
    name: string;
    type: TYPE_SOCIAL;
  };
  position: string;
  filter?: TypeFilter;
  filter_params?: TypeFilter;
}

export interface TypeTreeNode {
  op?: number;
  audience?: {
    id: string;
    name: string;
    type: TYPE_SOCIAL;
  };
  filter?: TypeFilter;
  children?: TypeTreeNode[];
  filter_params?: TypeFilter;
}

export interface TypeFilter {
  [key: string]: { label: string; value: string }[];
}

export interface TypeDataState<T> {
  count: number;
  items: T[];
  loading: boolean;
}
