export interface TypeTable {
  body: { [key: string]: string }[];
  loading?: boolean;
  children?: React.ReactNode;
  colWidth?: string;
  height?: string;
  totalRecords?: number;
  chartData?: SummarizeField[];
  defaultCol?: number;
}

export interface TypeFieldsEnrich {
  [key: string]: {
    [key: string]: string;
  };
}
export interface TypeEnrichmentRecord {
  id: string;
  file_name: string;
  fields_enrich: string;
  status: 1 | 2;
  total_records?: number;
  matched_records?: number;
  duplicated_records?: number;
  eta_time?: string;
  completed_time?: string;
  date_created: string;
  date_updated: string;
}

export interface TypeEnrichmentRequestTable {
  count: number;
  items: TypeEnrichmentRecord[];
}
export interface TypePayloadEnrichRequest {
  file_name: string;
  fields_enrich: string[];
}

export interface PreviewItem {
  [key: string]: string;
}
export interface TypeEnrichmentPreview {
  count: number;
  items: PreviewItem[];
}
export interface TypePayloadDownload {
  id: string;
}
export interface SummarizeField {
  missing: number;
  distinct: number;
  available: number;
}

export interface SummarizeEnrichment {
  [key: string]: SummarizeField;
}

export interface EnrichmentData {
  id: number;
  file_name: string;
  fields_enrich: string;
  status: number;
  total_records: number;
  matched_records: number;
  duplicated_records: number;
  eta_time: string;
  completed_time: string;
  date_created: string;
  date_updated: string;
  summarize: SummarizeEnrichment;
}
export interface FileData {
  file_name: string;
  file_size: number;
  file_updated: string; // or Date, depending on how you plan to use it
  cells_enriched: number;
  credit_cost: number;
  is_paid: boolean;
}

export interface FileInfoProps {
  loading: boolean;
  data: FileData | null;
}
