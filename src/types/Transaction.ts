import React from 'react';

export type CREDIT_TYPE =
  | "ADD_CREDIT"
  | "ADD_MORE_RQA"
  | "FOLLOW_PAGE"
  | "ENRICHMENT"
  | "BUY_SC_AUDIENCE"
  | "BUY_PN_AUDIENCE"
  | "SUBSCRIPTION"
  | "M_SUBSCRIPTION"
  | "BUY_SP_AUDIENCE"
  | "BUY_SP_DATASET"
  | "BUY_PN_DATASET"
  | "BUY_SC_DATASET"
  | "BUY_PN_PROFILE"
  | "REFERRAL"
  | "RESTORE";

export enum CREDIT_TYPE_ENUM {
  ADD_CREDIT = "ADD_CREDIT",
  ADD_MORE_RQA = "ADD_MORE_RQA",
  FOLLOW_PAGE = "FOLLOW_PAGE",
  ENRICHMENT = "ENRICHMENT",
  BUY_SC_AUDIENCE = "BUY_SC_AUDIENCE",
  BUY_SP_AUDIENCE = "BUY_SP_AUDIENCE",
  BUY_SP_DATASET = "BUY_SP_DATASET",
  BUY_PN_AUDIENCE = "BUY_PN_AUDIENCE",
  BUY_PN_DATASET = "BUY_PN_DATASET",
  SUBSCRIPTION = "SUBSCRIPTION",
  M_SUBSCRIPTION = "M_SUBSCRIPTION",
  BUY_SC_DATASET = "BUY_SC_DATASET",
  BUY_PN_PROFILE = "BUY_PN_PROFILE",
  REFERRAL = "REFERRAL",
  RESTORE = "RESTORE"
}

export interface CREDIT_LABEL {
  code: CREDIT_TYPE;
  label: string;
  icon?: React.ReactNode;
}
export interface ICreditCostProps {
  credit_type: CREDIT_TYPE;
  n?: number;
  is_dataset?: boolean;
}

export interface ITransactionResponse {
  id: number;
  credit_change_reason: CREDIT_TYPE;
  credit_amount: number;
  created_at: string;
  ref_id: string;
  info: string;
}
