export interface IFilterOptions {
  isCategory?: boolean;
  isFormat?: boolean;
  isStatus?: boolean;
  isPLatform?: boolean;
  isDateTime?: boolean;
}

export interface PageFollow {
  page_id: string;
  page_ad_id: string;
  name: string;
  description?: string;
  category?: string;
  phone?: string;
  website?: string[];
  email?: string;
  follow_count?: number;
  like_count?: number;
  is_following: boolean;
  status: string;
  type: string;
  start_date_follow: string;
  last_update_follow: string;
  expired_date_follow: string;
  end_date_follow?: string;
}

export interface PageSuggest
  extends Partial<
    Pick<
      PageFollow,
      | "page_id"
      | "description"
      | "like_count"
      | "name"
      | "category"
      | "follow_count"
      | "is_following"
      | "page_ad_id"
      | "status"
    >
  > {
  isShowDescription?: boolean;
  isSearchModal?: boolean;
}
