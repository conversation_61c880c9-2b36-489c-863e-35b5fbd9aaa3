import {
  AUDIENCE_FINDER_PATH,
  ROOT_PATH,
  DATAPROCESSING,
  FB_SERVICE_PATH,
  YOUR_AUDIENCES,
  USER_PROFILE_PATH,
} from "./Router";

export function path(root: string, sublink: string, isRootSymbol?: "symbol") {
  return `${isRootSymbol ? "/" : ""}${root}/${sublink}`;
}

export const PATH_DASHBOARD = {
  "": path(ROOT_PATH, ""),
  dashboard: path(ROOT_PATH, ""),
  audience_finder: {
    "": path(ROOT_PATH, "audience-finder"),
    social: path(ROOT_PATH, AUDIENCE_FINDER_PATH.SOCIAL),
    persona: path(ROOT_PATH, AUDIENCE_FINDER_PATH.PERSONA),
  },
  ads_analytics: {
    "": path(ROOT_PATH, FB_SERVICE_PATH.POST),
  },
  live_analysis: {
    "": path(ROOT_PATH, FB_SERVICE_PATH.LIVE),
    community_live: path(FB_SERVICE_PATH.LIVE, FB_SERVICE_PATH.LIVE_COMMUNITY),
    your_following: path(FB_SERVICE_PATH.LIVE, FB_SERVICE_PATH.LIVE_FOLLOWING),
    your_archives: path(FB_SERVICE_PATH.LIVE, FB_SERVICE_PATH.ARCHIVES),
  },
  your_data: {
    "": path(ROOT_PATH, "your-data"),
    your_audience: {
      "": path(ROOT_PATH, YOUR_AUDIENCES.ROOT),
      social: path(YOUR_AUDIENCES.ROOT, YOUR_AUDIENCES.SOCIAL),
      socialDataset: path(YOUR_AUDIENCES.ROOT, YOUR_AUDIENCES.SOCIAL_DATASET),
      persona: path(YOUR_AUDIENCES.ROOT, YOUR_AUDIENCES.PERSONA),
    },
    data_processing: {
      "": path(ROOT_PATH, DATAPROCESSING.ROOT),
      social: path(DATAPROCESSING.ROOT, DATAPROCESSING.SOCIAL),
      persona: path(DATAPROCESSING.ROOT, DATAPROCESSING.PERSONA),
    },
  },
  user: {
    "": path(ROOT_PATH, USER_PROFILE_PATH.ROOT),
    profile: path(USER_PROFILE_PATH.ROOT, USER_PROFILE_PATH.PROFILE),
    plan_overview: path(USER_PROFILE_PATH.ROOT, USER_PROFILE_PATH.PLAN),
    billing: path(USER_PROFILE_PATH.ROOT, USER_PROFILE_PATH.BILLING),
    transaction: path(USER_PROFILE_PATH.ROOT, USER_PROFILE_PATH.TRANSACTION),
  },
  plan: {
    "": path(ROOT_PATH, "plan/upgrade"),
  },
};
