export interface SocialPersonaPreview {
  fb_uid: string | null;
  phone: string;
  fullname: string;
  email: string;
  gender: string;
  city: string;
  dob: string;
  friends: number;
  followers: number;
  relationships: string;
  age: number;
  scores: {[key: string]: number};
}

export interface SocialPersonaSummarize {
  age_gender: { [key: string]: any };
  city: { [key: string]: string };
  follower_segment: { [key: string]: any };
  friend_segment: { [key: string]: any };
  gender: { [key: string]: any };
  relationships: { [key: string]: any };
}

export type SocialPersonaPreviewResponse = {
  data: {
    count: number,
    items: SocialPersonaPreview[]
  },
  message: string,
  status: number
}

export type SocialPersonaSummarizeResponse = {
  data: SocialPersonaSummarize,
  message: string,
  status: number
}

export interface TypeCreateAudience {
  name: string;
  description: string;
  datatype: 'AUDIENCE' | 'DATASET';
  filters: {
    [key: string]: string;
  };
  cancelToken?: any;
}

export interface SocialPersonaLog {
  crm_segment_id: number;
  date_created: string;
  date_import: string;
  segment_id: number;
  segment_name: string;
  status: number;
  user_id: number;
  type: string;
}

export type SocialPersonaLogResponse = {
  data: {
    count:number,
    items: SocialPersonaLog[]
  },
  message: string,
  status: number
}

export type SocialPersonaDimType = 'city' | 'relationships' | 'category' | 'category_ranking';
