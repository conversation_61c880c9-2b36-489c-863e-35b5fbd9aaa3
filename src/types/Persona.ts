import { FILTER_KEY } from "constants/persona";

type FilterKeys = (typeof FILTER_KEY)[keyof typeof FILTER_KEY];

type Dimensions = {
  [K in FilterKeys]: string;
};

export type SegmentParams = {
  id: string;
  query: string;
  body: {
    name: string;
    description: string;
  };
};
export interface TDataNumber {
  [key: string]: number;
}
export interface IDimensions extends Dimensions {
  [key: string]: string;
}
export interface FilterProps {
  [key: string]: any;
}

export interface CityValue {
  city: string;
  count: number;
}

export interface TypeCreateAudience {
  audience_name: string;
  description: string;
  datatype: 'AUDIENCE' | 'DATASET';
  filters: {
    [key: string]: string;
  };
  cancelToken?: any;
}

export interface GenderDistribution {
  F: number;
  M: number;
  NULL: number;
}

export interface AgeGenderDistribution {
  [ageRange: string]: GenderDistribution;
}

export interface RegionDistribution {
  [region: string]: number;
}

export interface ProvinceDistribution {
  [province: string]: number;
}

export interface Summarize {
  gender: GenderDistribution;
  age_gender: AgeGenderDistribution;
  person_region: RegionDistribution;
  person_province: ProvinceDistribution;
}

export interface Filters {
  person_region__in: string;
}

export interface PersonaAudienceItem {
  id: number;
  audience_name: string;
  datatype: 'AUDIENCE' | 'DATASET';
  name: string;
  description: string;
  filters: Filters;
  summarize: Summarize;
  date_created: string;
  date_updated: string;
  size: number;
  segment_size: number;
  last_added?: string;
}

export interface AudienceData {
  count: number;
  items: PersonaAudienceItem[];
}

export interface ApiResponse {
  status: number;
  message: string;
  data: AudienceData;
}
export interface PersonaAudiencePreview {
  uid: number;
  uid_fb: string | null;
  full_name: string;
  phone: string;
  gender: string;
  dob: string;
  person_district: string;
  person_province: string;
  position_department: string | null;
  position_level: string | null;
  company_name: string;
  company_province: string;
  company_size: string | null;
  person_address_latitude: number | null;
  person_address_longitude: number | null;
  position: any;
}
export interface PersonaSegment {
  id: number;
  name: string;
  description: string;
  segment_size: number;
  date_created: string;
  datatype: 'AUDIENCE' | 'DATASET';
}

export interface PersonaSegmentsResponse {
  status: number;
  message: string;
  data: {
    count: number;
    items: PersonaSegment[];
  };
}
export interface TAgeRange {
  label: string;
  value: {
    min: number;
    max: number;
  };
}
