export interface AudienceSegmentItem {
  id: number;
  name: string;
  description: string;
  segment_size: number;
  date_created: string;
  latest_audience: number;
  datatype: 'AUDIENCE' | 'DATASET';
}

export interface AudienceSegmentData {
  count: number;
  items: AudienceSegmentItem[];
}

export interface ApiResponse {
  status: number;
  message: string;
  data: AudienceSegmentData;
}
export interface ProcessedTree {
  data: {
    [key: string]: any;
  };
}
export type ProcessedTreeItem = {
  [key in "left" | "right"]: {
    audience: {
      id: number;
      name: string;
    };
    [key: string]: any;
  };
} & {
  op: 1 | 2 | 0;
};
