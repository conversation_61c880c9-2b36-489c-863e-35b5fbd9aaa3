import { SUB_TYPE_SOCIAL, TYPE_SOCIAL } from "constants/requestAudience";
import { AgeGroup, CityGroup, GenderGroup, Overview } from "./ResponseApi";

export interface TSocialFilterProps {
  isFilterPackage?: boolean;
  isFilterType?: boolean;
  isFilterCategory?: boolean;
  PageName?: string;
  className?: string;
}

export interface ICategoryItem {
  code: string;
  img_cover: string;
  audience_count: number;
  name: string;
  description?: string;
}

export interface IUserPreview {
  phone: string;
  fullname: string;
  fb_uid: string;
  email: null;
  username: null;
  gender: string;
  dob: string;
  city: string;
  location: string;
  relationships: string;
  friends: number;
  followers: number;
  scores: {
    category: number;
  };
}
export interface Summarize {
  age_gender: AgeGroup;
  city: CityGroup;
  gender: GenderGroup;
  overview: Overview;
  relationships: Record<string, number | undefined>;
}
export interface TSocialDetail {
  id: string;
  fb_uid: string;
  name: string;
  description: string;
  type: TYPE_SOCIAL;
  category: string;
  size: number;
  rating: number;
  package: string;
  date_created: string;
  date_updated: string;
  data_release: string;
  datatype: 'AUDIENCE' | 'DATASET';
  summarize: Summarize;
  latest_audience: number;
  actor_id: string;
  subtype: SUB_TYPE_SOCIAL;
  attachments?: string[];
  is_aud_added?: boolean;
  is_ds_added?: boolean;
  system?: boolean;
}

export interface TSocialSegment extends TSocialDetail {
  segment_size: number;
}

export interface TDataSummarize<T> {
  data: T;
  loading: boolean;
}

export interface TDataPreview {
  data: IUserPreview[];
  loading: boolean;
  total: number;
}
