import { TRange } from '../components/AgeRangeFilter';

export interface SelectProps {
  placeholder: string;
  options: TSelectOption[];
  selected?: string;
  defaultValue?: string;
  className?: string;
  onChange: (value: string) => void;
  disabled?: boolean;
}
export interface TSelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
  children?: TSelectOption[];
  [keys: string]: any;
}
export interface HandleSelectChangeProps {
  selectedNodes: TSelectOption[];
  key: string;
}

export type TSelectedOption = { [key: string]: string };

export type ValueSelectorType = "all" | (string | number)[] | string | number;

export type TRangeOption = {
  isRangeEnable?: boolean;
  rangeSelected: TRange;
  className?: string
  min: number;
  max: number;
  hideInput?: boolean;
  onRangeChange: (value: TRange) => void;
}

export interface TSelectOptionCheckbox {
  options: TSelectOption[];
  placeholderOptions: string;
  placeholderOptionsChildren?: string;
  defaultValue?: any;
  isSearchable?: boolean;
  onChange?: (currentNode: TSelectOption, selectedNodes: TSelectOption[]) => void;
  //custom
  isCustomOptions?: boolean;
  customOptions?: React.ReactNode | JSX.Element;
  //search keyword
  handleSetSearchKeyword?: (searchTerm: string) => void;
  searchCallback?: (nodes: TSelectOption[], searchTerm: string) => TSelectOption[];
  //permission
  isEnable?: boolean;
  //
  isSingleSelect?: boolean;
  //
  rangeOption?: TRangeOption;
  //
  isMobile?: boolean;
  //
  isHaveChildren?: boolean;
  children?: React.ReactNode | JSX.Element;
  className?: string;
}
