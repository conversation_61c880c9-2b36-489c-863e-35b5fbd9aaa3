export interface TRouterPath {
  [ROOT_PATH]: string;
}
export const ROOT_PATH = "";

export enum USER_PROFILE_PATH {
  ROOT = "account",
  PROFILE = "profile",
  PLAN = "plan-overview",
  BILLING = "billing",
  TRANSACTION = "transaction",
}

export enum AUDIENCE_FINDER_PATH {
  SOCIAL = "social-data",
  PERSONA = "work-persona",
  SOCIAL_DETAIL = "detail",
  SOCIAL_PERSONA = "social-persona",
}
export enum YOUR_AUDIENCES {
  ROOT = "your-audience",
  SOCIAL = "social-audience",
  SOCIAL_DATASET = "social-dataset",
  PERSONA = "work-persona-audience",
  ARCHIVE = "archive",
}
export enum YOUR_SEGMENTS {
  ROOT = "your-segments",
  SOCIAL = "social-segment",
  PERSONA = "work-persona-segment",
}
export enum DATAPROCESSING {
  ROOT = "data-processing",
  SOCIAL = "social-processing",
  PERSONA = "persona-processing",
}
export enum DATA_AUDIENCE {
  ROOT = "your-audience",
  SOCIAL = "social-audience",
  PERSONA = "work-persona-audience",
}

export enum DATA_SEGMENT {
  ROOT = "your-segments",
  SOCIAL = "social-segment",
  PERSONA = "work-persona-segment",
}

export enum FB_SERVICE_PATH {
  REQUEST_AUDIENCE = "request-audience",
  POST = "ads-post",
  LIVE = "live-post",
  DETAIL = "brand",
  LIVE_COMMUNITY = "community-live",
  LIVE_FOLLOWING = "your-following",
  ARCHIVES = "archives",
}

export enum ENRICHMENT_PATH {
  ENRICHMENT = "enrichment",
}
