import { TSocialDetail } from "./SocialData";

export interface IDataProps<T> {
  data: T[];
  loading: boolean;
  count: number;
}

export enum PLATFORM {
  FACEBOOK = "FACEBOOK",
  INSTAGRAM = "INSTAGRAM",
  AUDIENCE_NETWORK = "AUDIENCE_NETWORK",
  MESSENGER = "MESSENGER",
}

export interface IAttachment {
  type: string;
  path?: string;
  url: string;
  data: {
    title?: string;
    caption?: string;
    cta_type?: string;
    link_url?: string;
    link_description?: string;
  };
}
export interface ILivePostItem {
  post_id: string;
  is_live: boolean;
  publish_time: string;
  description: string;
  title: string;
  live_url: string;
  thumbnail: string;
  reaction_count: number;
  comment_count: number;
  share_count: number;
  play_count: number;
  playable_duration: number;
  audience_id?: string;
  actor_id: string;
  actor_name: string;
  created: string;
  updated?: string;
  attachments?: IAttachment[];
  status?: boolean;
  total_lead?: number;
}

export interface IAdsPostItem {
  is_active?: boolean;
  post_id: string;
  ad_id?: string;
  audience?: TSocialDetail;
  date_created?: string;
  date_updated?: string;
  publish_time?: string;
  content?: string;
  att_type?: string;
  att_thumbnail?: string;
  att_url?: string;
  att_height?: number;
  att_width?: number;
  count_reaction?: number;
  count_comment?: number;
  count_share?: number;
  actor_id: string;
  actor_name: string;
  page_transparency: string;
  publisher_platform?: string[];
  avatar?: string;
  attachments: IAttachment[];
}

export interface IQuickViewModal<T> {
  isOpen: boolean;
  id: string;
  data?: T;
  avatar?: string;
}

export interface IQuickViewItem<T> {
  id: string;
  post: T;
  avatar?: string;
}
export interface CommentDetail {
  comment_id: string;
  post_id: string;
  text_content: string;
  comment_time: string;
  phone_number: string;
  actor_id: string;
  actor_name: string;
  created: string;
  updated: string;
}

export interface IPageDetail {
  best_description?: string;
  category_name?: string;
  id: string;
  name?: string;
  page_likers?: number;
  profile_picture_uri?: string;
  url?: string;
  websites?: string[];
  email?: string;
  follow_count?: number;
  verification_status?: string;
}

export type ContainerType = "detail" | "community" | "page_follow";
