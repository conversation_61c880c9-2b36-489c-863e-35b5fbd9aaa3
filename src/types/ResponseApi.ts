export interface TMultiResponse<T> {
  next?: string | null;
  previous?: string | null;
  count?: number;
  results: T[];
  total?: any;
  data?: T[];
  error?: any;
}

export interface TUserBase {
  uuid: string;
  full_name: string;
  email: string;
  date_created: string;
  gender?: "Male" | "Female" | "Other";
  birthdate?: string;
  phone_number?: string;
  avatar?: string;
  credit: number;
}

export interface TBaseResponse<T> {
  data: T;
  message?: string;
  status?: number;
  error?: any;
  code: 0 | 1;
  response?: any;
}

export interface TLoginResponse
  extends TBaseResponse<{
    data: TUserBase;
    refresh_token: string;
    access_token: "string";
  }> {}

export interface TErrorResponse<T> {
  detail: T;
}
export interface TRefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}

export type ErrorType = any;

export const successResManyData = <T>(data: { items: T[]; total: number }) => {
  return { items: data.items, total: data.total };
};

export type TErrorName = "NETWORK_ERROR" | "SERVER_ERROR" | "VALIDATION_ERROR" | "CANCEL_REQUEST";

export const NETWORK_ERROR = "NETWORK_ERROR";
export const SERVER_ERROR = "SERVER_ERROR";
export const VALIDATION_ERROR = "VALIDATION_ERROR";
export const CANCEL_REQUEST = "CANCEL_REQUEST";

export class CValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = VALIDATION_ERROR;
  }
}

export class CServerError extends Error {
  constructor(message: string) {
    super(message);
    this.name = SERVER_ERROR;
  }
}

export class TNetwordError extends Error {
  constructor(message: string) {
    super(message);
    this.name = NETWORK_ERROR;
  }
}

export class TCancelRequest extends Error {
  constructor(message: string) {
    super(message);
    this.name = CANCEL_REQUEST;
  }
}

export interface AgeGroup {
  [key: string]: {
    F: number;
    M: number;
    NULL: number;
  };
}

export interface CityGroup {
  [key: string]: number;
}
export interface GenderGroup {
  F: number;
  M: number;
  NULL: number;
}
export interface Overview {
  c_dob: number;
  total: number;
  c_city: number;
  c_email: number;
  c_phone: number;
  c_gender: number;
  c_localtion: number;
  c_relationships: number;
}

export interface TRelationships {
  NULL: number;
  dating: number;
  single: number;
  married: number;
  widowed: number;
  divorced: number;
  separated: number;
  complicated: number;
  registered_partnership: number;
}
export interface SuccessResponse<TData> {
  success: boolean;
  data: TData;
}

export type AuthResponse = SuccessResponse<{
  refresh_token: string;
  access_token: string;
  data: any;
}>;
