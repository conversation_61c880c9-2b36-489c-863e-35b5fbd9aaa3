import { SUB_TYPE_SOCIAL, TYPE_SOCIAL } from "constants/requestAudience";
import { TSocialDetail } from "./SocialData";

export interface IRequestData {
  uid: string;
  actor_id: null;
  name: string;
  description: string;
  img: string;
  like_count: number;
  follow_count: number;
  member_count?: number;
  type: TYPE_SOCIAL | undefined;
  subtype: SUB_TYPE_SOCIAL | undefined;
  attachments?: string[];
  category?: string;
  status: 1 | 2;
}

export interface IAudienceExist {
  isExist: boolean | undefined;
  loading: boolean;
}

export interface TDataResponse {
  id: string;
  url: string;
  uid: string;
  audience_id: string;
  name: string;
  type: number;
  status: number;
  date_created: string;
  date_updated: string;
  eta_time: string;
  completed_time: string;
  audience: TSocialDetail;
  is_update: boolean;
}
