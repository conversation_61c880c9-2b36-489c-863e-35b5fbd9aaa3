export interface IYourAudienceItem {
  fb_uid: string;
  actor_id?: string;
  name: string;
  description: string | null;
  type: number;
  category: string | null;
  size: number;
  rating: number;
  package: string;
  date_created: Date;
  date_updated: Date;
  data_release: Date;
  datatype: 'AUDIENCE' | 'DATASET';
  is_aud_added: boolean;
  keywords: string | null;
}

export interface IYourAudience {
  status: number;
  message: string;
  data: {
    count: number;
    items: IYourAudienceItem[];
  };
}

export type keyDOBFilter = {
  from: string;
  to: string;
  month: string;
  year: string;
};
