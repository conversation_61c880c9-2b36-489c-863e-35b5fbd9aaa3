export type PLAN_CODE = "TRIAL" | "BASIC" | "PRO";
// export type CREDIT_CODE = "CREDIT_50" | "CREDIT_100" | "CREDIT_250" | "CREDIT_400";
export type PAYMENT_TYPE = "SUBSCRIPTION" | "CREDIT" | "CRM_CONTACT";
export type PAYMENT_TYPE_CREATE_LINK = "subscription" | "credit" | "crm_contact";
export interface InvoicesResponse {
  user_id: number;
  subscription_id: number;
  type: PAYMENT_TYPE;
  status: string;
  billing_date: string;
  total_amount: string;
  hosted_invoice_url: string;
  invoice_pdf: string;
  stripe_invoice_id: string;
  invoice_code?: string;
  id: number;
  date_created: string;
  date_updated: string;
  plan_code: PLAN_CODE;
}

export interface ICreditItem {
  count: number;
  price: number;
  code: string;
  discount: number;
}

export interface ICretePaymentRequest {
  plan_code?: PLAN_CODE;
  credit_code?: string;
  type: PAYMENT_TYPE_CREATE_LINK;
  currency?: string;
}

export interface IUpdateInfoInvoice {
  name_invoice?: string | null | undefined;
  address_line_1?: string | null | undefined;
  email_invoice?: string | null | undefined;
  tax_id?: string | null | undefined;
}

export interface InformationInvoice {
  address_line_1: string;
  address_line_2: string;
  city: string;
  country: string;
  name_invoice: string;
  email_invoice: string;
  phone_invoice: string;
  tax_id: string;
  user_id: number;
}
