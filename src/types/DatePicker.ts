interface Preset {
  name: string;
  label: string;
}
export interface PresetButtonProps {
  preset: string;
  label: string;
  isSelected: boolean;
  setPreset: (preset: string) => void;
}
export const PRESETS_OPTIONS: Preset[] = [
  { name: "today", label: "Today" },
  { name: "yesterday", label: "Yesterday" },
  { name: "last7", label: "Last 7 days" },
  { name: "last14", label: "Last 14 days" },
  { name: "last30", label: "Last 30 days" },
  { name: "thisWeek", label: "This Week" },
  { name: "lastWeek", label: "Last Week" },
  { name: "thisMonth", label: "This Month" },
  { name: "lastMonth", label: "Last Month" },
];

export interface DateRange {
  from: Date | string;
  to: Date | string | undefined;
}

export interface DateRangePickerProps {
  initialDateFrom?: Date | string;
  initialDateTo?: Date | string;
  align?: "start" | "center" | "end";
  locale?: string;
  className?: string;
  disableRemove?: boolean;
  onUpdate?: (values: { range: DateRange }) => void;
  onChange?: (value: any) => void;
  onClear?: () => void;
}
