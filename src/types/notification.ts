export type TNotificationAction = {
  your_audience_social: string,
  your_audience_work: string,
  your_segment_social: string,
  your_segment_work: string,
  request_audience: string,
  data_enrichment: string,
};

export const NotificationAction = {
  YOUR_AUDIENCE_SOCIAL: 'your_audience_social',
  YOUR_AUDIENCE_WORK: 'your_audience_work',
  YOUR_SEGMENT_SOCIAL: 'your_segment_social',
  YOUR_SEGMENT_WORK: 'your_segment_work',
  REQUEST_AUDIENCE: 'request_audience',
  DATA_ENRICHMENT: 'data_enrichment'
};

export const NotificationSystem = {
  SYSTEM: 'SYSTEM',
  USER_ACTION: 'USER_ACTION'
};

export type TNotificationData = {
  action: string,
  created_at: string,
  id: string,
  is_read: boolean,
  meta: {datatype: 'AUDIENCE' | 'DATASET'},
  ref_id: string,
  ref_name: string,
  title: string,
  type: string,
  user_id: number
}

export type TNotificationResponse = {
  data: {
    count: number,
    items: TNotificationData[]
  }
  message: string,
  status: number
}

export type TNotificationType = {
  request_audience: number;
  your_audience_work: number;
  your_audience_social: number;
  your_segment_work: number;
  your_segment_social: number;
  data_enrichment: number;
}


export type TNotificationUnReadTypeResponse = {
  data: {
    counts: TNotificationType,
  }
  message: string,
  status: number
}
