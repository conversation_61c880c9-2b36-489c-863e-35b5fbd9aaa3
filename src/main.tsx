import ReactDOM from "react-dom/client";
import React<PERSON> from "react-ga4";
import * as Sen<PERSON> from "@sentry/react";

import { store } from "./store";
import { Provider } from "react-redux";
import App from "./App";
import { Suspense } from "react";
import LoadingFavicon from "components/Loading/LoadingFavicon";
import Hotjar from "@hotjar/browser";
import { IS_PRODUCTION } from './layout/SidebarConfig';

const trackingID = import.meta.env.REACT_APP_TRACKING_ID;
const siteId = import.meta.env.REACT_APP_HOTJAR_ID;

ReactGA.initialize(trackingID);
ReactGA.send({ hitType: "pageview", page: window.location.pathname });

const hotjarVersion = 6;
Hotjar.init(siteId, hotjarVersion);

if (IS_PRODUCTION) {
  Sentry.init({
    dsn: import.meta.env.REACT_APP_SENTRY_DNS
  });
}

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <Provider store={store}>
    <Suspense fallback={<LoadingFavicon />}>
      <App />
    </Suspense>
  </Provider>
);
