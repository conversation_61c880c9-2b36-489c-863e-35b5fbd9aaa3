import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from "store";
import { ICategoryItem } from "types/SocialData";
import { SocialPersonaDimType } from '../../../types/SocialPersona';
import httpInstance from '../../../apis';
import { TAttribute } from '../../../types/Attribute';

export type CategoriesContextProps = {
  categories: ICategoryItem | undefined;
  isOpenSearchSuggest: boolean;
  city: {
    count: number;
    items: TAttribute[];
  };
};

const initialState: CategoriesContextProps = {
  categories: undefined,
  isOpenSearchSuggest: false,
  city: {
    count: 0,
    items: [],
  },
};

export const getDims = createAsyncThunk(
  "socialPersona/getDim",
  async (type: SocialPersonaDimType, { rejectWithValue, signal }) => {
    try {
      const dimsResponse = await httpInstance.get(`social-persona/dims/${ type }/`, { signal });
      return dimsResponse.data.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const socialDataSlice = createSlice({
  name: "social",
  initialState,
  reducers: {
    onOpenSearchSuggest: (state: CategoriesContextProps) => {
      state.isOpenSearchSuggest = true;
    },
    onCloseSearchSuggest: (state: CategoriesContextProps) => {
      state.isOpenSearchSuggest = false;
    },
  },
  extraReducers(builder) {
    builder.addCase(getDims.pending, (state) => {
      state.city = {
        count: 0,
        items: [],
      };
    }).addCase(getDims.fulfilled, (state, action) => {
      const data = action.payload as { count: number; items: string[] };
      state.city = {
        count: data.count, items: data.items.map((item) => {
          return {
            id: item,
            name: item,
          };
        }) as TAttribute[]
      };
    }).addCase(getDims.rejected, (state) => {
      state.city = {
        count: 0,
        items: [],
      };
    });
  }
});

export const { onOpenSearchSuggest, onCloseSearchSuggest } = socialDataSlice.actions;

export const socialStore = (state: RootState) => state.social;
export default socialDataSlice.reducer;
