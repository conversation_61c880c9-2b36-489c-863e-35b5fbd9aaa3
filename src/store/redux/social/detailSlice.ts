import { createSlice } from "@reduxjs/toolkit";
import { TBaseResponse } from "types/ResponseApi";
import { createAsyncThunk } from "@reduxjs/toolkit";
import httpInstance from "apis";
import { IUserPreview, TSocialDetail } from "types/SocialData";
import { yourSegmentApi } from "apis/yourSegment";
import { socialAPI } from "apis/socialData";

export type TSocialState = {
  social_detail: TSocialDetail | null;
  user_preview: {
    count: number;
    items: IUserPreview[];
  };

  loading: {
    social_detail: boolean;
    user_preview: boolean;
  };
  error: {};
  current_page: number;
};

const initialState: TSocialState = {
  social_detail: null,
  user_preview: {
    count: 0,
    items: [],
  },
  loading: {
    social_detail: false,
    user_preview: false,
  },
  error: {},
  current_page: 1,
};

export const detailSocialSlice = createSlice({
  name: "detail-social",
  initialState,
  reducers: {
    setCurrentPage: (state, action) => {
      state.current_page = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(getDataById.pending, (state) => {
        state.error = {};
        state.loading.social_detail = true;
      })
      .addCase(getUserPreview.pending, (state) => {
        state.loading.user_preview = true;
        state.user_preview.items = [];
        state.user_preview.count = 0;
      })
      .addCase(getDataBoughtById.pending, (state) => {
        state.error = {};
        state.loading.user_preview = true;
        state.user_preview.items = [];
        state.user_preview.count = 0;
      })
      .addCase(getSegmentSummarizeById.pending, (state) => {
        state.loading.social_detail = true;
        state.social_detail = null;
      })
      .addCase(getSegmentListUsers.pending, (state) => {
        state.loading.user_preview = true;
        state.user_preview.items = [];
      })
      .addCase(getSegmentSummarizeById.fulfilled, (state, action) => {
        state.loading.social_detail = false;
        state.social_detail = action.payload;
      })
      .addCase(getDataById.fulfilled, (state, action) => {
        state.loading.social_detail = false;
        state.social_detail = action.payload.data;
      })
      .addCase(getUserPreview.fulfilled, (state, action) => {
        state.loading.user_preview = false;
        state.user_preview.items = action.payload.data.items || [];
        state.user_preview.count = action.payload.data.count || 0;
      })
      .addCase(getDataBoughtById.fulfilled, (state, action) => {
        state.loading.user_preview = false;
        state.user_preview = action.payload;
      })
      .addCase(getSegmentListUsers.fulfilled, (state, action) => {
        state.loading.user_preview = false;
        state.user_preview = action.payload;
      })
      .addCase(getUserPreview.rejected, (state, action) => {
        state.loading.user_preview = false;
        state.error = action.payload ?? {};
        state.user_preview.items = [];
        state.user_preview.count = 0;
      })
      .addCase(getDataBoughtById.rejected, (state, action) => {
        state.loading.social_detail = false;
        state.error = action.payload ?? {};
        state.social_detail = null;
      })
      .addCase(getDataById.rejected, (state, action) => {
        state.loading.social_detail = false;
        state.error = action.payload ?? {};
        state.social_detail = null;
        state.user_preview.items = [];
        state.user_preview.count = 0;
      })
      .addCase(getSegmentSummarizeById.rejected, (state, action) => {
        state.loading.social_detail = false;
        state.error = action.payload ?? {};
        state.social_detail = null;
      })
      .addCase(getSegmentListUsers.rejected, (state, action) => {
        state.loading.user_preview = false;
        state.error = action.payload ?? {};
        state.user_preview.items = [];
        state.user_preview.count = 0;
      });
  },
});
export const { setCurrentPage } = detailSocialSlice.actions;
export default detailSocialSlice.reducer;

type IGetId = {
  id: string;
  page: number;
};

// action
export const getDataById = createAsyncThunk(
  "social/getDataById",
  async (id: string, { rejectWithValue, signal }) => {
    try {
      const preview = await httpInstance.get<TBaseResponse<TSocialDetail>>(
        `/social-data/audiences/${id}/`,
        { signal }
      );
      return preview.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);
export const getUserPreview = createAsyncThunk(
  "social/getUserPreview",
  async ({ id, page }: IGetId, { rejectWithValue, signal }) => {
    try {
      const userPreview = await httpInstance.get<
        TBaseResponse<{ count: number; items: IUserPreview[] }>
      >(`/social-data/audiences/${id}/preview/?page=${page}&limit=10`, { signal });
      return userPreview.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);
type PropsGetDataBoughtById = { id: string; query?: string; page: number };

export const getDataBoughtById = createAsyncThunk(
  "social/getDataBoughtById",
  async ({ id, query, page }: PropsGetDataBoughtById, { rejectWithValue }) => {
    try {
      const preview = await socialAPI.getDetailYourAudience({ id, query, page });
      return preview;
    } catch (error) {
      throw rejectWithValue(error);
    }
  }
);
export const getSegmentSummarizeById = createAsyncThunk(
  "social/getSegmentSummarizeById",
  async (id: string, { rejectWithValue }) => {
    try {
      const preview = await yourSegmentApi.getSummarizeDetail(id);
      return preview;
    } catch (error) {
      throw rejectWithValue(error);
    }
  }
);

export const getSegmentListUsers = createAsyncThunk(
  "social/getSegmentListUsers",
  async ({ id, page }: IGetId, { rejectWithValue }) => {
    try {
      const res = yourSegmentApi.getSegmentListUsers(id, page, 10);
      return res;
    } catch (error) {
      throw rejectWithValue(error);
    }
  }
);
