import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from 'store';
import {
  TNotificationAction,
  TNotificationResponse,
  TNotificationType,
  TNotificationUnReadTypeResponse
} from '../../../types/notification';
import { notificationApi } from '../../../apis/notification';

export type TReferenceNoti = {
  id: string,
  type: string,
}

export type TUnReadByType = {
  id: string
  ref_id: string
  meta: {datatype: 'AUDIENCE' | 'DATASET'},
}

export type NotificationContextProps = {
  countNoti: TNotificationType;
  unreadCount: number;
  isNewNoti: boolean;
  ref: TReferenceNoti;
  openDrawer: boolean;
  isReloadTable: boolean;
  unreadByType?: TUnReadByType[];
  timeReadNoti: string | undefined;
  isViewEnrich: boolean
};

const defaultArrNoti: TNotificationType = {
  request_audience: 0,
  data_enrichment: 0,
  your_audience_work: 0,
  your_audience_social: 0,
  your_segment_work: 0,
  your_segment_social: 0
};

const initialState: NotificationContextProps = {
  countNoti: defaultArrNoti,
  unreadCount: 0,
  isNewNoti: false,
  ref: {
    id: '',
    type: ''
  },
  openDrawer: false,
  isReloadTable: false,
  unreadByType:[],
  timeReadNoti: '',
  isViewEnrich: false
};

export const getCountUnreadType = createAsyncThunk(
  'notification/getCountUnreadType',
  async (_, { rejectWithValue }) => {
    try {
      const countUnreadResponse = await notificationApi.get<TNotificationUnReadTypeResponse>({
        endpoint: 'unread-count-by-type'
      });
      return countUnreadResponse?.data?.counts ?? defaultArrNoti;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getUnreadType = createAsyncThunk(
  'notification/getUnreadType',
  async (payload: {type: keyof TNotificationAction}, { rejectWithValue }) => {
    const { type } = payload;
    try {
      const countUnreadResponse = await notificationApi.get<TNotificationResponse>({
        endpoint: `unread-by-type/${type}`,
        params: {
          page: 1,
          limit: 50
        }
      });
      return countUnreadResponse?.data?.items ?? [];
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getCountUnread = createAsyncThunk(
  'notification/getCountUnread',
  async (_, { rejectWithValue }) => {
    try {
      const countUnreadResponse = await notificationApi.get<TNotificationResponse>({
        endpoint: 'unread-count'
      });
      return countUnreadResponse?.data?.count ?? 0;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    setIsNewNoti: (state, action) => {
      state.isNewNoti = action.payload;
    },
    setIsReloadTable: (state, action) => {
      state.isReloadTable = action.payload;
    },
    setIsViewEnrich: (state, action) => {
      state.isViewEnrich = action.payload;
    },
    setTimeReadNoti: (state, action) => {
      state.timeReadNoti = action.payload;
    },
    resetNoti: (state) => {
      state.countNoti = defaultArrNoti;
      state.unreadCount = 0;
      state.isNewNoti = false;
    },
    handleSaveRef: (state, action: {payload: TReferenceNoti}) => {
      state.ref = action.payload;
    },
    handleOpenDrawer: (state, action: {payload: boolean}) => {
      state.openDrawer = action.payload;
    }
  },
  extraReducers(builder) {
    builder.addCase(getCountUnreadType.pending, () => {
    }).addCase(getCountUnreadType.fulfilled, (state, action) => {
      state.countNoti = action.payload;
    }).addCase(getCountUnreadType.rejected, (state) => {
      state.countNoti = defaultArrNoti;
    })
    //   unread
    .addCase(getCountUnread.pending, () => {
    }).addCase(getCountUnread.fulfilled, (state, action) => {
      state.unreadCount = action.payload;
    }).addCase(getCountUnread.rejected, (state) => {
      state.unreadCount = 0;
    })
    //   unread by type
    .addCase(getUnreadType.pending, () => {
    }).addCase(getUnreadType.fulfilled, (state, action) => {
      state.unreadByType = action.payload;
    }).addCase(getUnreadType.rejected, (state) => {
      state.unreadByType = [];
    });
  }
});

export const { setIsNewNoti, setIsReloadTable, setTimeReadNoti, setIsViewEnrich, resetNoti, handleSaveRef, handleOpenDrawer } = notificationSlice.actions;

export const notificationStore = (state: RootState) => state.notification;
export default notificationSlice.reducer;
