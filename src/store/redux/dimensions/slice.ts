import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { personaAPI } from "apis/persona";
import { INITIAL_LOCATION } from "constants/persona";
import { dimensionFieldPersona } from "constants/socialPersona";
import sortEmployees from "utils/persona/sortEmployees";
import { getAgeValue } from "utils/utils";

export interface IDimensionsPersona {
  age: any;
  department: any;
  level: any;
  company_size: any;
  industry: any;
  location: any;
}

const initialState: IDimensionsPersona = {
  age: [],
  department: [],
  level: [],
  company_size: [],
  location: INITIAL_LOCATION,
  industry: [],
};

export const getDimensions = createAsyncThunk(
  "dimensions/get",
  async (field: string, { rejectWithValue }) => {
    try {
      const response = await personaAPI.getDimensions({ fields: field });
      if (!response.data || !response.data.items) {
        return rejectWithValue("No data received");
      }
      return response.data.items;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getLocationArea = createAsyncThunk(
  "dimensions/getLocationArea",
  async (_, { rejectWithValue }) => {
    const response = await personaAPI.get({ endpoint: "admin-area/" });
    return response.status >= 200 && response.status < 300
      ? response.data
      : rejectWithValue(response);
  }
);

export const dimensionsSlice = createSlice({
  name: "dimensions",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // .addCase(getDimensions.pending, (state) => {
      //   state.loading = true;
      //   state.error = null;
      // })
      .addCase(getDimensions.fulfilled, (state, action) => {
        const field = action.meta.arg; // Key gọi API
        const reduxField = dimensionFieldPersona[field]; //key lưu redux

        if (!reduxField) return;

        let newData = action.payload;

        if (field === "age_segment") {
          newData = action.payload.sort((a: string, b: string) => getAgeValue(a) - getAgeValue(b));
        }
        if (field === "company_size_segment") {
          newData = sortEmployees(action.payload);
        }

        state[reduxField] = newData;
      })
      .addCase(getDimensions.rejected, (state, action) => {
        state[action.meta.arg as keyof IDimensionsPersona] = [];
      })
      .addCase(getLocationArea.fulfilled, (state, action) => {
        state.location = action.payload;
      })
      .addCase(getLocationArea.rejected, (state) => {
        state.location = [];
      });
  },
});

export default dimensionsSlice.reducer;
