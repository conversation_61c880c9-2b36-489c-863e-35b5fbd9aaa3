import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import httpInstance from "apis";
export interface AudienceCategory {
  code: string;
  name: string;
  description: string;
  img_cover: string;
  audience_count: number;
  date_created: string;
  date_updated: string;
}
interface AudienceCategories {
  count: number;
  items: AudienceCategory[];
}
export interface CategoryState {
  data: AudienceCategories;
  loading: boolean;
  error: string | null;
}
const initialState: CategoryState = {
  data: {
    count: 0,
    items: [],
  },
  loading: false,
  error: null,
};
export const getCategories = createAsyncThunk(
  "category/getCategories",
  async (_, { rejectWithValue }) => {
    try {
      const res = (await httpInstance.get<{ data: AudienceCategories }>("/social-data/categories/"))
        .data;
      return res.data;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch categories");
    }
  }
);
export const categorySlice = createSlice({
  name: "category",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCategories.fulfilled, (state, action) => {
        const { count = 0, items = [] } = action.payload;
        const unCategory = items.find((item) => item.name === 'Uncategory');
        state.data = {
          count: count,
          items: items
          .filter((item) => item.name !== "Uncategory")
          .sort((a, b) => b.audience_count - a.audience_count)
          .concat(unCategory ? [unCategory] : []),        };
        state.loading = false;
        state.error = null;
      })
      .addCase(getCategories.rejected, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload || "An error occurred";
        state.data = { count: 0, items: [] };
      });
  },
});
export default categorySlice.reducer;
