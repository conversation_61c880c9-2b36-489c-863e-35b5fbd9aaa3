import { createSlice } from "@reduxjs/toolkit";
import { RootState } from "store";

export type CollapseDrawerContextProps = {
  collapse: boolean;
};

const initialState: CollapseDrawerContextProps = {
  collapse: false,
};

export const sidebarSlice = createSlice({
  name: "sidebar",
  initialState,
  reducers: {
    onToggleCollapse: (state: CollapseDrawerContextProps) => {
      state.collapse = !state.collapse;
    },
    disableCollapse: (state: CollapseDrawerContextProps) => {
      state.collapse = false;
    },
  },
});

export const sidebarStore = (state: RootState) => state.sidebar;
export default sidebarSlice.reducer;
export const { onToggleCollapse, disableCollapse } = sidebarSlice.actions;
