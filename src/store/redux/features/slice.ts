import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { subscriptionAPI } from "apis/subscription";

export interface IActiveFeature {
  plan_code: string;
  code: string;
  is_enabled: boolean;
  usage_limit: string | number | null;
  is_can_exceed: boolean;
  excess_usage_credit: string | number | null;
  extra_limit: { max_rows?: number; concurrent: number; [key: string]: any };
  is_overage: boolean;
  usage_count: number;
}

export interface ActiveFeatureState {
  features: IActiveFeature[];
  loading: boolean;
  error: string | null;
}

const initialState: ActiveFeatureState = {
  features: [],
  loading: false,
  error: null,
};

export const getFeatures = createAsyncThunk(
  "features/getActiveFeatures",
  async (_, { rejectWithValue }) => {
    const response = await subscriptionAPI.getActiveFeatures();
    if (response.status < 200 || response.status >= 300) {
      return rejectWithValue(response);
    }
    return response.data;
  }
);

export const featuresSlice = createSlice({
  name: "features",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getFeatures.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getFeatures.fulfilled, (state, action) => {
        state.features = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(getFeatures.rejected, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload || "An error occurred";
        state.features = [];
      });
  },
});

export default featuresSlice.reducer;
