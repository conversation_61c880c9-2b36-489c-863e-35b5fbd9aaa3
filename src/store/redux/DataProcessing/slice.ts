import { createSlice } from "@reduxjs/toolkit";
import { RootState } from "store";

export type TDataProcessing = {
  datatype: 'AUDIENCE' | 'DATASET' | string;
};

const initialState: TDataProcessing = {
  datatype: '',
};

export const dataProcessingSlice = createSlice({
  name: "dataProcessing",
  initialState,
  reducers: {
    setDatatype: (state: TDataProcessing, action: {payload: 'AUDIENCE' | 'DATASET' | string}) => {
      state.datatype = action.payload;
    },
  },
});

export const dataProcessingStore = (state: RootState) => state.sidebar;
export default dataProcessingSlice.reducer;
export const { setDatatype  } = dataProcessingSlice.actions;
