import { PayloadAction, createAsyncThunk, createSlice, isAnyOf } from "@reduxjs/toolkit";
import httpInstance from "apis";
import { TBaseResponse, TUserBase } from "types/ResponseApi";
import { clearLS, redirectTo } from 'utils/localStorage';
import { LoginBodyType, RegisterBodyType } from "validations/account";

export type IAuthState = {
  user: TUserBase;
  accessToken: string;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean | undefined;
};

const initialState: IAuthState = {
  user: {
    uuid: "",
    full_name: "",
    email: "",
    date_created: "",
    credit: 0,
  },
  accessToken: "",
  loading: false,
  error: null,
  isAuthenticated: undefined,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      state.isAuthenticated = false;
      state.accessToken = "";
      state.user = {
        uuid: "",
        full_name: "",
        email: "",
        date_created: "",
        credit: 0,
      };
      clearLS();
      redirectTo(document, '/login');
    },
    setEmailVerify: (state, action: PayloadAction<string>) => {
      state.user.email = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(login.fulfilled, (state, action) => {
        const data = action.payload.data;
        state.loading = false;
        state.accessToken = data.access_token;
        state.user = data.user;
        state.isAuthenticated = true;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.user = action.payload.data;
      })
      .addCase(getMe.fulfilled, (state, action) => {
        state.user = action.payload.data;
        state.isAuthenticated = true;
        state.loading = false;
      })

      .addMatcher(isAnyOf(login.pending, register.pending, getMe.pending), pendingAction)
      .addMatcher(isAnyOf(login.rejected, register.rejected, getMe.rejected), rejectAction);
  },
});
export const { logout, setEmailVerify } = authSlice.actions;

export default authSlice.reducer;

const pendingAction = (state: IAuthState) => {
  state.loading = true;
};

const rejectAction = (state: IAuthState, action: PayloadAction<any>) => {
  state.loading = false;
  state.error = action.payload?.detail;
};

export const login = createAsyncThunk(
  "auth/login",
  async (payload: LoginBodyType, { rejectWithValue }) => {
    try {
      const normalizedPayload = {
        ...payload,
        username: payload.username.toLowerCase().trim()
      };

      const res = await httpInstance.post("/auth/login/", normalizedPayload, {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          isShowToast: true,
        },
      });
      if (res.data) {
        return res.data;
      }

      return rejectWithValue(JSON.parse(res.request.response));
    } catch (error: any) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const register = createAsyncThunk(
  "auth/register",
  async (payload: RegisterBodyType, { rejectWithValue }) => {
    const { confirmPassword, ...rest } = payload;
    try {
      const res = await httpInstance.post<TBaseResponse<TUserBase>>("/auth/register/", rest);
      const data = res.data;
      return data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getMe = createAsyncThunk("auth/getMe", async (_, { rejectWithValue, signal }) => {
  try {
    const res = await httpInstance.get<TBaseResponse<TUserBase>>("/auth/me/", { signal });
    const resendEmail = localStorage.getItem('resendEmail');
    if (resendEmail && res.data.data.email.toLowerCase() === resendEmail.toLowerCase()) {
      localStorage.removeItem('resendEmail');
    }
    return res.data;
  } catch (error) {
    return rejectWithValue(error);
  }
});
