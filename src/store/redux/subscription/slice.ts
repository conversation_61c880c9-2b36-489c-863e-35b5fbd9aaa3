import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { subscriptionAPI } from "apis/subscription";
import { RootState } from "store";

export type ILastSub = {
  id: number;
  status: "ACTIVE" | "DISABLE" | "CANCELED" | "EXPIRED" | "PENDING";
  plan_code: string;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  plan_credit: number;
  plan_price: number;
  is_renewal?: boolean;
};

export type SubScriptionContextProps = {
  last_sub: ILastSub | null;
  is_active: boolean | null;
};

const initialState: SubScriptionContextProps = {
  last_sub: null,
  is_active: null,
};

export const fetchLastSubscription = createAsyncThunk(
  "subscription/fetchLastSubscription",
  async () => {
    const response = await subscriptionAPI.getLastSubscriptions();
    return response.data;
  }
);
export const fetchActiveSubscription = createAsyncThunk(
  "subscription/fetchActiveSubscription",
  async () => {
    const response = await subscriptionAPI.getActiveSubscriptions();
    if (response && response.data) {
      return true;
    }
    return false;
  }
);

export const subscriptionSlice = createSlice({
  name: "subscription",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchLastSubscription.fulfilled, (state, action) => {
      state.last_sub = action.payload;
    });
    builder.addCase(fetchActiveSubscription.fulfilled, (state, action) => {
      state.is_active = action.payload;
    });
  },
});

export default subscriptionSlice.reducer;
export const subscriptionStore = (state: RootState) => state.subscription;
