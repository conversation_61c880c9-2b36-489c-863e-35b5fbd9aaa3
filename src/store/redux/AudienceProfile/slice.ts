import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { personaCustomAPI } from "apis/personaCustom";
import { PersonaAudienceItem } from "types/Persona";

export interface AudienceProfilesState {
  data: PersonaAudienceItem | null;
  loading: boolean;
}
const initialState: AudienceProfilesState = {
  data: null,
  loading: false,
};

export const audienceProfilesSlice = createSlice({
  name: "audience profiles",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getAudienceProfilesSummarize.pending, (state) => {
        state.loading = true;
      })
      .addCase(getAudienceProfilesSummarize.fulfilled, (state, action) => {
        state.data = action.payload;
        state.loading = false;
      })
      .addCase(getAudienceProfilesSummarize.rejected, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.data = action.payload;
      });
  },
});
export default audienceProfilesSlice.reducer;

export const getAudienceProfilesSummarize = createAsyncThunk(
  "audience/getProfilesSummarize",
  async (_, { rejectWithValue }) => {
    try {
      const response: any = await personaCustomAPI.getProfilesSummarize();
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch Audience Profiles Summarize");
    }
  }
);
