import { createSlice } from '@reduxjs/toolkit';

export interface IAiAgentState {
  messageAiAgent: {role: string; content: string}[];
}

const initialState: IAiAgentState = {
  messageAiAgent: []
};

export const aiAgentSlice = createSlice({
  name: 'Ai Agent',
  initialState,
  reducers: {
    setMessageAI: (state, action) => {
      state.messageAiAgent = action.payload;
    }
  }
});
export default aiAgentSlice.reducer;
export const { setMessageAI } = aiAgentSlice.actions;
