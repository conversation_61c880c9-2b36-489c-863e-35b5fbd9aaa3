import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { SocialPersonaLog, SocialPersonaLogResponse } from '../../../types/SocialPersona';
import { socialPersonaApi } from '../../../apis/socialPersona';

export interface SegmentLogState {
  logSegment: {
    count: number,
    items: SocialPersonaLog[]
  };
  loading: boolean;
  error: string | null;
}
const initialState: SegmentLogState = {
  logSegment: {
    items: [],
    count: 0
  },
  loading: false,
  error: null,
};

export const segmentLogSlice = createSlice({
  name: "segment profiles",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(handleGetSegmentLog.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(handleGetSegmentLog.fulfilled, (state, action) => {
        state.logSegment = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(handleGetSegmentLog.rejected, (state, action: PayloadAction<any>) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch segment log";
      });
  },
});
export default segmentLogSlice.reducer;


export const handleGetSegmentLog = createAsyncThunk(
  "contact-crm/segments/logs",
  async (_, { rejectWithValue }) => {
    try {
      const response: any = await socialPersonaApi.getSegmentLogContactCRM<SocialPersonaLogResponse>();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch segment log");
    }
  }
);
