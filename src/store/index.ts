import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import sidebarReducer, { CollapseDrawerContextProps } from "store/redux/sidebar/slice";
import authSlice, { IAuthState } from "./redux/auth/slice";
import socialDataSlice, { CategoriesContextProps } from "./redux/social/slice";
import audienceProfilesSlice, { AudienceProfilesState } from "./redux/AudienceProfile/slice";
import detailSocialSlice, { TSocialState } from "./redux/social/detailSlice";
import categorySlice, { CategoryState } from "./redux/category/slice";
import featuresSlice, { ActiveFeatureState } from "./redux/features/slice";
import subscriptionSlice, { SubScriptionContextProps } from "./redux/subscription/slice";
import dimensionsSlice, { IDimensionsPersona } from "./redux/dimensions/slice";
import notificationSlice, { NotificationContextProps } from './redux/notification/slice';
import dataProcessingSlice, { TDataProcessing } from './redux/DataProcessing/slice';
import segmentLogSlice, { SegmentLogState } from './redux/SegmentLog/slice';
import aiAgentSlice, { IAiAgentState } from './redux/AIAgent/slice';

const reducers = {
  sidebar: sidebarReducer,
  detailSocial: detailSocialSlice,
  auth: authSlice,
  social: socialDataSlice,
  notification: notificationSlice,
  dimensions: dimensionsSlice,
  audienceProfiles: audienceProfilesSlice,
  category: categorySlice,
  features: featuresSlice,
  subscription: subscriptionSlice,
  dataProcessing:dataProcessingSlice,
  segmentLog: segmentLogSlice,
  aiAgent: aiAgentSlice
};
export default reducers;

export const store = configureStore({
  reducer: reducers,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

const newRootReducer = combineReducers(reducers);

store.replaceReducer(newRootReducer);

// Infer the `RootState` and `AppDispatch` types from the store itself
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<RootState>();

export type RootState = ReturnType<
  () => {
    sidebar: CollapseDrawerContextProps;
    auth: IAuthState;
    detailSocial: TSocialState;
    dimensions: IDimensionsPersona;
    social: CategoriesContextProps;
    category: CategoryState;
    notification: NotificationContextProps,
    dataProcessing: TDataProcessing,
    audienceProfiles: AudienceProfilesState;
    subscription: SubScriptionContextProps;
    features: ActiveFeatureState;
    segmentLog: SegmentLogState;
    aiAgent: IAiAgentState;
  }
>;
