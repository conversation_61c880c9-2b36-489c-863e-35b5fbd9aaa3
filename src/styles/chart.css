.chartjs-tooltip {
  background: white;
  border-radius: 6px;
  pointer-events: none;
  position: absolute;
  transform: translate(-20%, calc(-100% - 20px));
  border: 1px solid hsla(230, 13%, 9%, 0.1);
  box-shadow: 0px 3px 10px -2px hsla(230, 13%, 9%, 0.02),
    0px 10px 16px -3px hsla(230, 13%, 9%, 0.05);
  transition: all 0.2s ease;
}
.chartjs-tooltip-header {
  color: hsla(230, 12%, 9%, 1);
  font-size: 14px;
  font-weight: 500;
  background-color: hsla(240, 8%, 97%, 1);
  padding: 3.5px 12px 3.5px 12px;
}

.chartjs-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 12%;
  border-width: 10px;
  border-style: solid;
  border-color: white transparent transparent transparent;
}

.dots-tooltip {
  height: 10px;
  width: 10px;
  display: inline-block;
  border-radius: 50%;
  flex-shrink: 0;
}
.table-body-tooltips {
  display: flex;
  flex-direction: column;
}

.value-tooltips {
  display: flex;
  align-items: center;
  gap: 6px;
}
