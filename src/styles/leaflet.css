.leaflet-control-attribution {
  display: none;
}
.leaflet-tooltip.my-labels {
  background-color: transparent;
  border: transparent;
  box-shadow: none;
  font-size: 10px;
  font-weight: 500;
  color: white;
  text-shadow: 0 0 5px black;
}
.easy-button-button {
  background: white;
  padding: 8px;
  border-radius: 4px;
}

.leaflet-popup {
  left: calc(100% + 35px) !important;
}

.leaflet-popup-content-wrapper {
  padding: 0 !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

.leaflet-popup-content {
  margin: 0 !important;
  min-height: 150px !important;
  width: 100% !important;
}
.leaflet-popup-tip-container {
  left: 0 !important;
  margin-top: 0px !important;
  margin-left: -30px !important;
  top: 50%;
  transform: rotate(90deg);
}
.leaflet-top,
.leaflet-bottom {
  bottom: 115px !important;
  right: 16px !important;
  top: unset !important;
  left: unset !important;
}
.leaflet-control {
  margin: 0 !important;
}
.leaflet-control-zoom-in,
.leaflet-control-zoom-out {
  width: 100% !important;
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
.leaflet-control-zoom {
  width: 44px !important;
  border: none !important;
  border-radius: 10px !important;
  overflow: hidden !important;
}
