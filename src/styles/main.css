@import "./leaflet.css";
@import "./chart.css";
@import "slick-carousel/slick/slick.css";
@import "slick-carousel/slick/slick-theme.css";

@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --background: 0, 0%, 100%, 1;
    --foreground: 230, 12%, 9%, 1;

    --card: 0, 0%, 100%, 1;
    --card-foreground: 259, 63%, 59%, 1;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --background-primary: 266, 77%, 61%, 1;
    --background-primary-hover: 266, 77%, 50%, 1;
    --background-secondary: 0, 0%, 94%, 1;
    --background-active: 230, 61%, 10%, 0.08;

    --text-primary: 225, 16%, 15%, 1;
    --text-secondary: 226, 12%, 36%, 1;
    --text-tertiary: 210, 3.7%, 58% ,1;

    --radius: 10px;

    --primary-gradients: 90deg, rgba(112, 81, 240, 0.38) -2.57%, rgba(112, 81, 240, 0) 112.5%;

    --secondary: 229, 41%, 10%, 0.6;
    --secondary-foreground: 220.9 39.3% 11%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220, 8%, 88%, 1;
    --border-focus: 266, 77%, 74%, 1;
    --ring: 224 71.4% 4.1%;

    --shadow-xs: 0px 1px 2px 0px hsla(230, 13%, 9%, 0.05);
    --shadow-focus: 0px 0px 0px 2px hsla(257, 100%, 85%, 0.5);
    --color-shadow-light: hsl(226, 12%, 36%, 1);
    --color-shadow-medium: hsl(225, 18%, 4%, 0.1);
    --color-shadow-dark: hsl(225, 18%, 4%, 0.15);

    --border-primary: 222, 6%, 67%, 1;
    --border-secondary: 0, 0%, 99%, 1;
    --border-tertiary: 225, 16%, 15%, 1;
    --border-inverse: 0, 0%, 94%, 1;
    --border-disable: 210, 3%, 89%, 1;

    --dp-2xl: 4.5rem;
    --dp-2xl-line-height: 5.625rem;
    --dp-2xl-letter-spacing: -2%;

    --dp-xl: 3.75rem;
    --dp-xl-line-height: 4.5rem;
    --dp-xl-letter-spacing: -2%;

    --dp-lg: 3rem;
    --dp-lg-line-height: 3.75rem;
    --dp-lg-letter-spacing: -2%;

    --dp-md: 2.25rem;
    --dp-md-line-height: 2.75rem;
    --dp-md-letter-spacing: -2%;

    --dp-sm: 1.875rem;
    --dp-sm-line-height: 2.375rem;

    --dp-xs: 1.5rem;
    --dp-xs-line-height: 2rem;

    --text-xl: 1.25rem;
    --text-xl-line-height: 1.875rem;

    --text-lg: 1.125rem;
    --text-lg-line-height: 1.75rem;

    --text-md: 1rem;
    --text-md-line-height: 1.5rem;

    --text-sm: 0.875rem;
    --text-sm-line-height: 1.25rem;

    --text-xs: 0.75rem;
    --text-xs-line-height: 1.125rem;
  }

  .dark {
    --background: 0 0% 49.02%;
    --foreground: 0 0% 49.02%;

    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;

    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;

    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;

    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;

    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hidden {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
}

html {
  height: 100vh;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('https://rsms.me/inter/font-files/InterVariable.woff2?v=4.1') format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('https://rsms.me/inter/font-files/InterVariable-Italic.woff2?v=4.1') format('woff2');
}


body {
  box-sizing: border-box;
  height: 100%;
  font-family: 'Inter', sans-serif;
  font-feature-settings: "ss04", "cv05", "cv06", "cv10", "cv12", "cv13" off;
  font-weight: 400;
}

#root {
  height: 100%;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Track */
::-webkit-scrollbar-track {
  /* background: red; */
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 5px;
}

*:hover::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.scroll-bottom::-webkit-scrollbar-thumb {
  background: #888 !important;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  text-indent: 1px;
  text-overflow: "";
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input:-webkit-autofill {
  -webkit-background-clip: text;
}

.ag-body-horizontal-scroll-viewport::-webkit-scrollbar-thumb {
  background: #888 !important;
}

.custom-ag-header--center .ag-header-cell-comp-wrapper {
  justify-content: center;
}

.custom-ag-header--end .ag-header-cell-comp-wrapper {
  justify-content: flex-end;
}

.ProcessedTree-line {
  position: absolute;
  width: 2px;
  background: #ae81ef;
  height: 50%;
}

.ag-root-wrapper, .ag-header {
  border: none !important;
}

.ag-center-cols-viewport {
  padding: 0 !important;
}


.custom--dropdown-container {
  text-align: left;
  border: 1px solid #dee0e3;
  position: relative;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #6b7183;
  /* box-sizing: content-box; */
}

.custom--dropdown-container .dropdown-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  gap: 6px;
  padding: 8px;
  border-radius: 12px;
}

.custom--dropdown-container .dropdown-input .dropdown-selected-value.placeholder {
  color: #6b7183;
}

.custom--dropdown-container .dropdown-tool svg {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  transform: rotate(0deg);
}

.custom--dropdown-container .dropdown-tool svg.translate {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.custom--dropdown-container .dropdown-menu {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: 100%;
  padding: 12px 8px;
  position: absolute;
  border: 1px solid #e1e2e3;
  border-radius: 12px;
  overflow: auto;
  background-color: #fff;
  z-index: 99;
  max-height: 312px;
  min-height: 50px;
  left: 0;
  margin-top: 4px;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar {
  width: 5px;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-track {
  background: #fdfdfd;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-thumb {
  background: #888;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.custom--dropdown-container .dropdown-menu.alignment--left {
  left: 0;
}

.custom--dropdown-container .dropdown-menu.alignment--right {
  right: 0;
}

.custom--dropdown-container .dropdown-item {
  padding: 8px;
  cursor: pointer;
  color: #515667;
  -webkit-transition: background-color 0.35s ease;
  transition: background-color 0.35s ease;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  margin: 8px 0;
}

.custom--dropdown-container .dropdown-item:hover {
  background-color: #ecdffb;
  color: #5314a3;
}

.custom--dropdown-container .dropdown-item.selected {
  background-color: #ecdffb;
  color: #5314a3;
  font-weight: 600;
}

.custom--dropdown-container .search-box {
  padding: 8px;
}

.custom--dropdown-container .search-box input {
  width: 100%;
}

.custom--dropdown-container .dropdown-tags {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 5px;
}

.custom--dropdown-container .dropdown-tag-item {
  background-color: #ecdffb;
  color: #5314a3;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 4px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  z-index: 3;
}

.custom--dropdown-container .dropdown-tag-close {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 5px;
}

.post-carousel .slick-track,
.quickview-carousel .slick-track {
  display: flex;
}

.post-carousel .slick-slide {
  margin: 1px;
  height: 100%;
}

.post-carousel .slick-slide.slick-active {
  z-index: 10;
}

.post-carousel .slick-slide:not(.slick-active) {
  opacity: 0;
}

.quickview-carousel .slick-slide {
  margin: 5px;
}

@keyframes bounce-right-to-left {
  0%,
  100% {
    transform: translateX(25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateX(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce-folder-share {
  animation: bounce-right-to-left 1s infinite;
}
