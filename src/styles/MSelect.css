.custom--dropdown-container {
  text-align: left;
  border: 1px solid #dee0e3;
  position: relative;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #0f132499;
  box-sizing: border-box;
}

.custom--dropdown-container .dropdown-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  gap: 6px;
  padding: 8px 10px;
}

.custom--dropdown-container .dropdown-input .dropdown-selected-value.placeholder {
  color: #82868b;
}

.custom--dropdown-container .dropdown-tool svg {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  transform: rotate(0deg);
}

.custom--dropdown-container .dropdown-tool svg.translate {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.custom--dropdown-container .dropdown-menu {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: 100%;
  padding: 5px;
  position: absolute;
  -webkit-transform: translateY(6px);
  -ms-transform: translateY(6px);
  transform: translateY(6px);
  border: 1px solid #a7aab1;
  border-radius: 6px;
  overflow: auto;
  background-color: #fff;
  z-index: 99;
  max-height: 312px;
  min-height: 50px;
  left: 0;
  margin-top: 10px;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar {
  width: 5px;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-thumb {
  background: #888;
}

.custom--dropdown-container .dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.custom--dropdown-container .dropdown-menu.alignment--left {
  left: 0;
}

.custom--dropdown-container .dropdown-menu.alignment--right {
  right: 0;
}

.custom--dropdown-container .dropdown-item {
  padding: 7px 10px;
  cursor: pointer;
  -webkit-transition: background-color 0.35s ease;
  transition: background-color 0.35s ease;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  margin: 5px 0;
}

.custom--dropdown-container .dropdown-item:hover {
  background-color: #ecdffb;
  color: #5314a3;
}

.custom--dropdown-container .dropdown-item.selected {
  background-color: #ecdffb;
  color: #5314a3;
  font-weight: 600;
}

.custom--dropdown-container .search-box {
  padding: 5px;
}

.custom--dropdown-container .search-box input {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 5px;
  margin: 5px 0;
  border: 1px solid ##dee0e3;
  border-radius: 5px;
}

.custom--dropdown-container .dropdown-tags {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 5px;
}

.custom--dropdown-container .dropdown-tag-item {
  background-color: #ecdffb;
  color: #5314a3;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 4px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  z-index: 3;
}

.custom--dropdown-container .dropdown-tag-close {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 5px;
}
