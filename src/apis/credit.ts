import httpInstance from "apis";
import { ICreditCostProps } from "types/Transaction";

const getCreditCost = async <T = any>({ credit_type, n }: ICreditCostProps) => {
  try {
    const response = await httpInstance.get<T>(`credit/cost?inst_type=${credit_type}&n=${n}`);
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getPersonaCreditCost = async <T = any>({ params }: { params: any }) => {
  try {
    const query = new URLSearchParams(params).toString();
    const response = await httpInstance.get<T>(`credit/cost/persona/?${query}`);
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getTransactions = async <T = any>({ params }: { params?: any }) => {
  try {
    const response = await httpInstance.get<T>(`credit/transactions`, {
      params,
      headers: { isHiddenToast: true },
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getSummarize = async <T = any>() => {
  try {
    const response = await httpInstance.get<T>(`credit/balance`, {
      headers: { isHiddenToast: true },
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

export const creditAPI = { getCreditCost, getPersonaCreditCost, getTransactions, getSummarize };
