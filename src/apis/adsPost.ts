import httpInstance from "apis";
import { convertCancelToken } from "utils/utils";

const getListPost = async <T = any>({ params }: { params?: any }) => {
  try {
    const { cancelToken } = convertCancelToken(params);
    const response = await httpInstance.get<T>(`ads-post/`, { params, cancelToken });
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const getPagePreview = async <T = any>({ id, params }: { id?: string; params?: any }) => {
  try {
    const { cancelToken } = convertCancelToken(params);
    const response = await httpInstance.get<T>(`ads-post/page-preview/${id}/`, {
      params,
      cancelToken,
    });
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const getPostDetail = async <T = any>({ id }: { id?: string }) => {
  try {
    const response = await httpInstance.get<T>(`ads-post/${id}/`);
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const getSuggest = async <T = any>({ q }: { q: string }) => {
  try {
    const response = await httpInstance.get<T>(`ads-post/search-suggest/?q=${q}`);
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

export const adsPostAPI = {
  getListPost,
  getPagePreview,
  getPostDetail,
  getSuggest,
};
