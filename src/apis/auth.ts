import httpInstance from 'apis';
import { TBaseResponse, TUserBase } from 'types/ResponseApi';
import { RegisterBodyType } from 'validations/account';

export const callRegister = async (payload: RegisterBodyType) => {
  const { confirmPassword, ...rest } = payload;
  try {
    const res = (await httpInstance.post<TBaseResponse<TUserBase>>("/auth/register/", rest)).data;
    return res;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const forgotPassword = async (email: string) => {
  try {
    const res = (
      await httpInstance.post<TBaseResponse<{}>>("/auth/password-reset/", {
        email: email.toLowerCase().trim(),
      })
    ).data;
    return res;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const resendForgotPassword = async (password: string, token: string) => {
  try {
    const res = await httpInstance.post<TBaseResponse<{}>>(
      `auth/password-reset/complete/${token}/`,
      {
        password,
      }
    );
    if (res.data) return res.data;
    return JSON.parse(res.request.response);
  } catch (error) {
    return { error };
  }
};

export const verifyEmail = async ({ token }: { token: string }) => {
  try {
    const response = await httpInstance.get(`/auth/account-verification/${token}/`);
    return response;
  } catch (error: any) {
    return { error };
  }
};

export const resendAcctVerification = async (email: string) => {
  try {
    const res = (
      await httpInstance.post<TBaseResponse<{}>>("auth/resend-account-verification/", {
        email: email.toLowerCase().trim(),
      })
    ).data;
    return res;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const exchangeSession = async (session:string) => {
  try {
    const res = await httpInstance.get<TBaseResponse<TUserBase>>(`/auth/exchange-token?session_id=${session}`)
    return res
  } catch (error) {
    return Promise.reject(error);
  }
};
