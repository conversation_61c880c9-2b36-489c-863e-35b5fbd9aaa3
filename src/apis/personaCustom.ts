import httpInstance from "apis";

const addProfileCustom = async <T>(uid: number) => {
  try {
    const result = await httpInstance.post<T>(`/persona/audience-custom/${uid}/`);
    return result;
  } catch (error) {
    return { data: null, error };
  }
};

const getProfilesSummarize = async () => {
  try {
    const res: any = await httpInstance.get("/persona/audience-custom/");
    return res;
  } catch (error: any) {
    return error;
  }
};
const getAudienceProfiles = async (page: string, query?: string) => {
  try {
    const res = await httpInstance.get(
      `/persona/audience-custom/profiles/?page=${page}&limit=10${query}`
    );
    return res.data;
  } catch (error: any) {
    return error;
  }
};

const getAllPersonaSegment = async () => {
  try {
    const res: any = await httpInstance.get("/persona/segments/all/");
    return res.data;
  } catch (error: any) {
    return error;
  }
};

interface IParamsAddSegment {
  query?: string;
  payload: {
    is_full_profiles?: boolean;
    profile_uids?: number[];
    segment_id?: number;
    new_segment?: {
      name: string;
      description?: string;
    };
  };
}
const addSegmentProfileCustom = async <T>({ query, payload }: IParamsAddSegment) => {
  try {
    const result = await httpInstance.post<T>(`/persona/segments/audience-custom/?${query}`, {
      ...payload,
    });
    return result;
  } catch (error) {
    return { data: null, error };
  }
};
export const personaCustomAPI = {
  addProfileCustom,
  getProfilesSummarize,
  getAudienceProfiles,
  getAllPersonaSegment,
  addSegmentProfileCustom,
};
