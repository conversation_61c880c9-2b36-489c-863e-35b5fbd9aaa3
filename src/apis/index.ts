import axios, { AxiosRequestConfig } from "axios";
import { toast } from "components/ui/use-toast";
import { ERROR_STATUS } from "constants/httpStatusCode";
import { AuthResponse } from "types/ResponseApi";
import { clearLS, getTokens, setAccessTokenToLS, setTokensToLS } from "utils/localStorage";

const AXIOS_OPTIONS = {
  baseURL: import.meta.env.REACT_APP_API_URL + "/api/v1",
  // timeout: 10000,
  headers: {
    "Content-Type": "application/json",
    "ngrok-skip-browser-warning": "69420",
  },
};
const PATH_API = {
  register: "/auth/register",
  login: "/auth/login",
  refreshToken: "/auth/refresh/",
};
const UNAUTHORIZED_PATHS = ["/login"];

function createHttp(options: AxiosRequestConfig) {
  let isRefreshing = false;
  const instance = axios.create(options);

  const setupInterceptors = () => {
    instance.interceptors.request.use(
      (config) => {
        let { accessToken } = getTokens();
        if (config.headers?.isv2) {
          config.baseURL = config.baseURL?.replace('v1', 'v2') || config.baseURL;
          delete config.headers.isv2;
        } else {
          config.baseURL = config.baseURL?.replace('v2', 'v1') || config.baseURL;
        }
        if (
          accessToken &&
          config.headers &&
          UNAUTHORIZED_PATHS.every((path) => !config.url?.includes(path))
        ) {
          config.headers.authorization = `Bearer ${accessToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    instance.interceptors.response.use(handleResponse, handleError);
  };

  const handleResponse = (response: any) => {
    const { url } = response.config;
    if (url.includes(PATH_API.login)) {
      const data = response.data as AuthResponse;
      const access_token = data.data.access_token;
      const refresh_token = data.data.refresh_token;
      setTokens(access_token, refresh_token);
    }
    return response;
  };

  const handleError = (error: any) => {
    const _error = error?.response?.data || error?.response || error || {};
    if (error.config.headers.isHiddenToast) {
      return _error;
    }
    if (error.config.url?.includes(PATH_API.refreshToken)) {
      const pathName = window.location.pathname;
      window.location.href = `/login?redirect=${pathName}`;
    } else {

      switch (_error.code) {
        case 'ERR_NETWORK':
          toast({
            title: 'Something went wrong. Please try again later!!!',
            status: 'error',
            duration: 3000
          });
          break;
        case 1:
          toast({
            title: _error.error || _error.message,
            status: 'error',
            duration: 3000
          });
          break;
        case 5002:
          toast({
            title: _error.message ?? 'Subscription not found',
            status: 'error',
            duration: 3000
          });
          break;
        case 1001:
          if (error.config.headers.isShowToast) {
            toast({
              status: 'error',
              duration: 3000,
              description: error.response.data.message || 'Could not validate credentials'
            });
          }
          break;
      }

      switch (error.status || error.response.status) {
        case ERROR_STATUS.UNAUTHORIZED:
          if (error.config.url?.includes(PATH_API.refreshToken)) {
            clearLS();
            return Promise.reject(_error);
          }
          return tryRefreshToken(error);
        case ERROR_STATUS.BAD_REQUEST:
          toast({
            title: _error.message || _error.error,
            status: 'error',
            duration: 3000
          });
          break;
        case 422:
          toast({
            title: _error.error[0].msg || _error.message,
            status: 'error',
            duration: 3000
          });
          break;
        case ERROR_STATUS.NOT_FOUND:
          toast({
            title: _error.error || _error.message,
            status: 'error',
            duration: 3000
          });
          break;
        case ERROR_STATUS.TOO_MANY_REQUESTS:
          toast({
            title: 'You have performed too many actions, please try again in a few minutes!!!!',
            status: 'error',
            duration: 3000
          });
          break;

        case ERROR_STATUS.INTERNAL_SERVER_ERROR:
        case ERROR_STATUS.BAD_GATEWAY:
        case ERROR_STATUS.SERVICE_UNAVAILABLE:
          toast({
            title: 'Something went wrong. Please try again later!!!',
            status: 'error',
            duration: 3000
          });
          break;
        default:
          return Promise.reject(_error);
      }
    }
    return error;
  };

  const tryRefreshToken = async (error: any) => {
    let { refreshToken } = getTokens();
    if (refreshToken && !isRefreshing) {
      isRefreshing = true;
      try {
        const response = await instance.get(PATH_API.refreshToken, {
          headers: {
            "refresh-token": refreshToken,
          },
        });
        const { access_token } = response.data.data;
        setAccessTokenToLS(access_token);
        return instance(error.config);
      } catch (error) {
        clearLS();
        return Promise.reject(error);
      } finally {
        isRefreshing = false;
      }
    } else if (isRefreshing) {
      return new Promise((resolve) => {
        const intervalId = setInterval(() => {
          if (!isRefreshing) {
            clearInterval(intervalId);
            resolve(instance(error.config));
          }
        }, 200);
      });
    } else {
      // clearLS();
      return Promise.reject(error);
    }
  };

  //setToken
  const setTokens = (access_token: string, refresh_token: string) => {
    setTokensToLS(access_token, refresh_token);
  };

  setupInterceptors();
  return {
    getInstance: () => instance,
  };
}
const httpInstance = createHttp(AXIOS_OPTIONS).getInstance();
export default httpInstance;
