import {
  EnrichmentData,
  FileData,
  TypeEnrichmentPreview,
  TypeEnrichmentRecord,
  TypeEnrichmentRequestTable,
  TypeFieldsEnrich,
  TypePayloadDownload,
} from "types/Enrichment";

import httpInstance from "apis";

// get fields enrich transfer
const getFieldsEnrich = async (): Promise<TypeFieldsEnrich> => {
  try {
    const res = await httpInstance.get("/enrichment/fields-enrich/");
    return res.data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};

const getListRequestEnrich = async ({
  page,
  limit,
}: {
  page: number;
  limit: number;
}): Promise<TypeEnrichmentRequestTable> => {
  try {
    const res = await httpInstance.get(`/enrichment/?page=${page}&limit=${limit}`);
    return res.data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};

const createEnrichmentRequest = async (data: FormData): Promise<TypeEnrichmentRecord> => {
  try {
    const res = await httpInstance.post("/enrichment/", data, {
      headers: { "Content-Type": "multipart/form-data" },
    });
    return res.data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};
const detailRequestEnrichment = async (id: string): Promise<EnrichmentData> => {
  try {
    const res = await httpInstance.get(`/enrichment/${id}/`);
    return res.data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};
const previewRequestEnrichment = async (id: string): Promise<TypeEnrichmentPreview> => {
  try {
    const res = await httpInstance.get("/enrichment/" + id + "/preview/");
    return res.data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};
const downloadEnrichedFile = async ({ id }: TypePayloadDownload) => {
  try {
    const res = await httpInstance.post("/enrichment/" + id + "/download/");

    return res.data;
  } catch (error) {
    return error;
  }
};
const getFileInfo = async (id: string): Promise<FileData> => {
  try {
    const res = await httpInstance.get(`/enrichment/${id}/file-info/`);
    return res.data.data;
  } catch (error: any) {
    return error;
  }
};

export const enrichmentAPI = {
  getFieldsEnrich,
  createEnrichmentRequest,
  getListRequestEnrich,
  detailRequestEnrichment,
  previewRequestEnrichment,
  downloadEnrichedFile,
  getFileInfo,
};
