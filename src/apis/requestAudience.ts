import httpInstance from "apis";
import { toast } from "components/ui/use-toast";

const getFbPreview = async <T = any>({ payload }: { payload: {} }) => {
  try {
    const result = await httpInstance.post<T>("/social-data/request-audience/preview/", payload);
    return result.data;
  } catch (error) {
    return { data: null, error };
  }
};
const checkAudienceExists = async ({ audience_id }: { audience_id: string }) => {
  try {
    const response = await httpInstance.get<boolean>(
      `/social-data/audiences/${audience_id}/exists/`
    );
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const createRequest = async <T>({ payload }: { payload: {} }) => {
  try {
    const result = await httpInstance.post<T>("/social-data/request-audience/", payload);
    if (result.status === 200 || result.status === 201) {
      toast({
        title: "Request purchased successfully.",
        status: "success",
        duration: 3000,
        className: "bg-success-primary text-success-subtitle border border-custom-success",
      });
    }
    return result;
  } catch (error) {
    return { data: null, error };
  }
};

export const requestAudienceApi = { getFbPreview, createRequest, checkAudienceExists };
