import {
  ApiResponse,
  PersonaAudienceItem,
  PersonaAudiencePreview,
  PersonaSegmentsResponse,
  SegmentParams,
  TypeCreateAudience
} from 'types/Persona';

import { CreateSegmentFormType } from 'types/YourData';
import httpInstance from 'apis';
import { toast } from 'components/ui/use-toast';

const getDimensions = async <T = any>({ fields }: {fields: string}) => {
  try {
    const response = await httpInstance.get<T>(`/persona/dims/${fields}`);
    return response.data;
  } catch (error: any) {
    toast({
      title: error?.detail[0].msg,
      status: 'error',
      duration: 3000
    });
    return { data: null, error };
  }
};
const get = async <T = any>({
  params,
  endpoint = '',
  signal
}: {
  params?: any;
  endpoint?: string;
  signal?: AbortSignal;
}) => {
  try {
    const response = await httpInstance.get<T>(`/persona/${endpoint}`, { params, signal });
    return response.data;
  } catch (error: any) {
    toast({
      title: error?.detail[0].msg,
      status: 'error',
      duration: 3000
    });
    return { data: null, error };
  }
};
const savedFilter = async <T>({ payload }: {payload: {}}) => {
  try {
    const result = await httpInstance.post<T>('/persona/filter-histories/', payload);
    if (result.status === 200 || result.status === 201) {
      toast({
        title: 'Save Success',
        status: 'success',
        duration: 3000
      });
    }
    return result;
  } catch (error) {
    toast({
      title: 'Failed Save Search',
      description: 'Please check again! ',
      status: 'error',
      duration: 3000
    });
    return { data: null, error };
  }
};
const deleteFilter = async <T>({ id }: {id: string | number}) => {
  try {
    const result = await httpInstance.delete<T>(`persona/filter-histories/${id}/`);
    if (result.status === 200 || result.status === 201) {
      toast({
        title: 'Delete Success',
        status: 'success',
        duration: 3000
      });
    }
    return result;
  } catch {
    toast({
      title: 'Error!! Please check again!',
      status: 'error',
      duration: 3000
    });
  }
};
const previewMap = async <T = any>({ params }: {params: any}) => {
  try {
    const response = await httpInstance.get<T>('/persona/preview-map/', { params });
    return response.data;
  } catch (error: any) {
    throw error?.detail[0].msg;
  }
};
const detailUserMap = async <T = any>({ params, signal }: {params: any, signal?: AbortSignal}) => {
  try {
    const response = await httpInstance.get<T>(`/persona/preview-map/${params}/`, { signal });
    return response.data;
  } catch (error: any) {
    toast({
      title: error?.detail[0].msg,
      status: 'error',
      duration: 3000
    });
    return { data: null, error };
  }
};

//yourData
const createPersonaAudience = async (body: TypeCreateAudience) => {
  try {
    const { filters, ...rest } = body;
    const query = new URLSearchParams(filters).toString();
    const result = await httpInstance.post('persona/audiences/' + ( query && `?${query}` ), rest);
    return result.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getPersonaAudience = async (payload: {
  page: number,
  limit: number,
  signal?: AbortSignal
}): Promise<ApiResponse> => {
  try {
    const { page, limit, signal } = payload;
    const response = await httpInstance.get(`/persona/audiences/?page=${page}&limit=${limit}`, { signal });
    return response.data;
  } catch (error: any) {
    toast({
      title: 'error',
      description: error.detail || 'An error occurred',
      status: 'error'
    });
    throw error.detail;
  }
};
const getPersonaAudienceById = async (id: string): Promise<PersonaAudienceItem> => {
  try {
    const res: any = await httpInstance.get(`/persona/audiences/${id}/`);
    return res;
  } catch (error: any) {
    return error;
  }
};
const getPersonaAudiencePreviewById = async (
  payload: {
    id: string,
    page: string,
    query?: string, signal?: AbortSignal
  }
): Promise<{
  count: number;
  items: PersonaAudiencePreview[];
}> => {
  try {
    const { id, page, query, signal } = payload;
    const res = await httpInstance.get(
      `/persona/audiences/${id}/preview/?page=${page}&limit=10${query}`,
      { signal }
    );
    return res.data.data;
  } catch (error: any) {
    return { count: 0, items: [] };
  }
};
const AnalyzeProcessing = async <T = any>({ payload }: {payload: {}}) => {
  try {
    const result = await httpInstance.post<T>('/persona/segments-processing/analyze/', payload);
    return result.data;
  } catch (error) {
    toast({
      title: 'Failed Analyze',
      description: 'Please check again! ',
      status: 'error',
      duration: 3000
    });
    return { data: null, error };
  }
};
const createSegmentByProcessing = async <T>({ payload }: {payload: {}}) => {
  try {
    const result = await httpInstance.post<T>('/persona/segments-processing/create/', payload);
    if (result.status === 200 || result.status === 201) {
      toast({
        title: 'Create Segment Success',
        status: 'success',
        duration: 3000
      });
    }
    return result;
  } catch (error) {
    toast({
      title: 'Failed Create Segment',
      description: 'Please check again! ',
      status: 'error',
      duration: 3000
    });
    return { data: null, error };
  }
};

const CreatePersonaSegment = async (payload: SegmentParams) => {
  try {
    const res = await httpInstance.post(
      `/persona/segments/?audience_id=${payload.id}${payload.query && '&' + payload.query}`,
      payload.body
    );
    return res.data;
  } catch (error: any) {
    throw error.detail;
  }
};
const getPersonaSegment = async (data: {page: number, limit: number, signal?: AbortSignal}) => {
  try {
    const { page, limit, signal } = data;
    const response = await httpInstance.get<PersonaSegmentsResponse>(
      `/persona/segments/?page=${page}&limit=${limit}`,
      { signal }
    );
    return response.data.data;
  } catch (error: any) {
    throw error.detail;
  }
};
const getPersonaSegmentById = async (id: string) => {
  try {
    const response = await httpInstance.get(`/persona/segments/${id}/`);
    return response;
  } catch (error: any) {
    throw error.detail;
  }
};

const getPersonaSegmentPreview = async (payload: {id: string, page: number, limit: number, signal?: AbortSignal}) => {
  try {
    const { id, page, limit, signal } = payload;
    const res = await httpInstance.get(
      `/persona/segments/${id}/preview/?page=${page}&limit=${limit}`,{ signal }
    );
    return res.data.data;
  } catch (error: any) {
    throw error.detail;
  }
};
const deleteAudience = async (id: string) => {
  try {
    const response = await httpInstance.delete(`/persona/audiences/${id}/`);
    return response.data;
  } catch (error: any) {
    throw error.detail;
  }
};
const deleteSegment = async (id: string) => {
  try {
    const response = await httpInstance.delete(`/persona/segments/${id}/`);
    return response.data;
  } catch (error: any) {
    throw error.detail;
  }
};
const updateAudience = async (id: string, body: CreateSegmentFormType) => {
  try {
    const response = await httpInstance.patch(`/persona/audiences/${id}/`, {
      audience_name: body.name,
      description: body.description
    });
    return response.data?.data;
  } catch (error: any) {
    throw error.detail;
  }
};
const updateSegment = async (id: string, body: CreateSegmentFormType) => {
  try {
    const response = await httpInstance.patch(`/persona/segments/${id}/`, body);
    return response.data?.data;
  } catch (error: any) {
    throw error.detail;
  }
};

export const personaAPI = {
  get,
  getDimensions,
  savedFilter,
  deleteFilter,
  previewMap,
  detailUserMap,
  createPersonaAudience,
  getPersonaAudience,
  AnalyzeProcessing,
  createSegmentByProcessing,
  getPersonaAudienceById,
  getPersonaAudiencePreviewById,
  CreatePersonaSegment,
  getPersonaSegment,
  getPersonaSegmentPreview,
  getPersonaSegmentById,
  deleteAudience,
  deleteSegment,
  updateAudience,
  updateSegment
};
