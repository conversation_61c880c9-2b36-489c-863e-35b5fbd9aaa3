import httpInstance from 'apis';
import { toast } from 'components/ui/use-toast';
import { IYourAudience, IYourAudienceItem } from 'types/yourAudience';
import { CreateSegmentFormType } from '../types/YourData';

interface IHeaders {
  isv2: boolean;
}

const get = async <T = any>({
  params,
  endpoint = "",
  headers,
  signal
}: {
  params?: any;
  endpoint?: string;
  headers?: IHeaders;
  signal?: AbortSignal;
}) => {
  try {
    const response = await httpInstance.get<T>(`/social-data/${endpoint}`, {
      params,
      headers: {
        ...headers
      },
      signal
    });
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const getSummarizeDetail = async <T = any>(id: string) => {
  try {
    const response = await httpInstance.get<T>(`/social-data/audiences/${id}/`);
    return response;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getBoughtSummarizeDetail = async <T = any>(id: string, datatype: "AUDIENCE" | "DATASET") => {
  try {
    const response = await httpInstance.get<T>(
      `/social-data/user-audiences/${id}/?datatype=${datatype}`
    );
    return response;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getAvatar = async (fb_uid: string, type: string) => {
  try {
    const res = await httpInstance.get(`/social-data/fb-img/${fb_uid}/?type=${type}`);
    return res.data;
  } catch (error) {
    return null;
  }
};

const addAudiences = async <T>({ payload }: { payload: {} }) => {
  try {
    const result = await httpInstance.post<T>("/social-data/user-audiences/", payload);
    if (result.status === 200) {
      toast({
        title: "Your audience has been added successfully",
        status: "success",
        duration: 3000,
      });
    }
    return result;
  } catch (error) {
    return { data: null, error };
  }
};

//yourData
const AnalyzeProcessing = async <T = any>({ payload }: { payload: {} }) => {
  try {
    const result = await httpInstance.post<T>(
      "/social-data/segments-processing/analyze/",
      payload,
      {
        headers: {
          isv2: true,
        },
      }
    );
    return result.data;
  } catch (error) {
    toast({
      title: "Failed Analyze",
      description: "Please check again! ",
      status: "error",
      duration: 3000,
    });
    return { data: null, error };
  }
};

const createSegmentByProcessing = async <T>({ payload }: { payload: {} }) => {
  try {
    const result = await httpInstance.post<T>("/social-data/segments-processing/create/", payload, {
      headers: {
        isv2: true,
      },
    });
    if (result.status === 200 || result.status === 201) {
      toast({
        title: "Create Segment Success",
        status: "success",
        duration: 3000,
      });
    }
    return result;
  } catch (error) {
    toast({
      title: "Failed Create Segment",
      description: "Please check again! ",
      status: "error",
      duration: 3000,
    });
    return { data: null, error };
  }
};
const getListYourAudience = async ({
  page = 1,
  limit = 10,
}: {
  page?: number;
  limit?: number;
}): Promise<{
  count: number;
  items: IYourAudienceItem[];
}> => {
  try {
    const response = await httpInstance.get<IYourAudience>(
      `/social-data/user-audiences/?page=${page}&limit=${limit}`
    );
    return response.data.data;
  } catch (error: any) {
    toast({
      title: "error",
      description: error?.response?.data?.message || "An error occurred",
      status: "error",
      duration: 3000,
    });
    return { count: 0, items: [] };
  }
};

const getDetailYourAudience = async (payload:{id: string, page: number, query?: any, signal?: AbortSignal}) => {
  try {
    const { id, page, query, signal } = payload;
    const response = await httpInstance.get(
      `/social-data/user-audiences/${id}/profiles/?page=${page}&limit=10${query}`,
      {
        headers: {
          isv2: true,
        },
        signal
      }
    );
    return response.data.data;
  } catch (error: any) {
    // toast({
    //   title: error?.response?.data?.message || "An error occurred",
    //   status: "error",
    //   duration: 3000,
    // });
    return { data: null, error };
  }
};

const deleteSegment = async (id: string) => {
  try {
    const response = await httpInstance.delete(`/social-data/segments/${id}/`);
    return response.data;
  } catch (error: any) {
    throw error.detail;
  }
};
// @ts-ignore
const deleteAudience = async (id: string) => {
  throw new Error("Feature not implemented");
};
const getSocialCityAudience = async (audience_id: string, dim?: string) => {
  try {
    const response = await httpInstance.get(
      `/social-data/user-audiences/${audience_id}/dims/${dim}/`
    );
    return response.data.data;
  } catch (error: any) {
    throw error.detail;
  }
};

const updateSocialSegment = async(id: string, body: CreateSegmentFormType) => {
  try {
    const result = await httpInstance.patch(`social-data/segments/${id}/`, body);
    return result.data.data;
  } catch (error) {
    return { data: null, error };
  }
};

const updateData = async <T = any>({ id }: { id: string }) => {
  try {
    const result = await httpInstance.post<T>(`social-data/audiences/${id}/update_data/`, {
      isHiddenToast: true,
    });
    return result.data;
  } catch (error) {
    return { data: null, error };
  }
};

export const socialAPI = {
  get,
  getSummarizeDetail,
  addAudiences,
  getAvatar,
  AnalyzeProcessing,
  createSegmentByProcessing,
  getListYourAudience,
  getDetailYourAudience,
  deleteSegment,
  deleteAudience,
  getSocialCityAudience,
  updateData,
  updateSocialSegment,
  getBoughtSummarizeDetail,
};
