import httpInstance from './index.ts';

const get = async <T = any>({ params, endpoint = "" }: { params?: any; endpoint?: string }) => {
  try {
    const response = await httpInstance.get<T>(`notifications/${endpoint}`, { params });
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const handleMarkAllAsRead = async () => {
  try {
    return await httpInstance.put(`notifications/read-all`)
  } catch (error) {
    return { data: null, error };
  }
}

const handleMarkAsRead = async (id: string) => {
  try {
    return await httpInstance.put(`notifications/${id}/read`)
  } catch (error) {
    return { data: null, error };
  }
}

const handleMarkAsReadByType = async (type: string) => {
  try {
    return await httpInstance.put(`notifications/read-by-type/${type}`)
  } catch (error) {
    return { data: null, error };
  }
}

export const notificationApi = {
  get,
  handleMarkAllAsRead,
  handleMarkAsRead,
  handleMarkAsReadByType
};
