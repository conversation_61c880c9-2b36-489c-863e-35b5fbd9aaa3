import httpInstance from './index.ts';

const checkValidReferral = async <T = any>({ referral_code = "" }: { referral_code?: string }) => {
  try {
    const response = await httpInstance.get<T>(`referral/${referral_code}/valid`);
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const getReferralCode = async <T = any>() => {
  try {
    const response = await httpInstance.get<T>(`referral/my-referrer`);
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const appleReferralCode = async <T = any>({ referral_code = "" }: { referral_code?: string }) => {
  try {
    const response = await httpInstance.post<T>(`referral/${referral_code}/apply`);
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};


export const referralApi = {
  checkValidReferral,
  getReferralCode,
  appleReferralCode
};
