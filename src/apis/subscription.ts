import httpInstance from "apis";

const getActiveSubscriptions = async <T = any>() => {
  try {
    const response = await httpInstance.get<T>(`subscriptions/active`, {
      headers: { isHiddenToast: true },
    });
    return response;
  } catch (error: any) {
    return { data: null, error };
  }
};
const getLastSubscriptions = async <T = any>() => {
  try {
    const response = await httpInstance.get<T>(`subscriptions/last`, {
      headers: { isHiddenToast: true },
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getActiveFeatures = async <T = any>() => {
  try {
    const response = await httpInstance.get<T>(`subscriptions/active/features`, {
      headers: { isHiddenToast: true },
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

// https://devapi.big360.ai/api/v1/subscriptions/update-renewal
const updateRenewal = async <T>({ payload }: { payload: { is_renewal: boolean } }) => {
  try {
    const response = await httpInstance.post<T>(`subscriptions/update-renewal`, { ...payload });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

export const subscriptionAPI = {
  getLastSubscriptions,
  getActiveSubscriptions,
  getActiveFeatures,
  updateRenewal,
};
