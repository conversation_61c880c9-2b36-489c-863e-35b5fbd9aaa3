import httpInstance from "apis";
import { toast } from "components/ui/use-toast";

const getListPage = async <T = any>({ params, signal }: { params?: any, signal?: AbortSignal }) => {
  try {
    const response = await httpInstance.get<T>(`page/`, {
      params: { limit: 10, page: 1, ...params },
      signal
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getListPageFollow = async <T = any>({ params }: { params?: any }) => {
  try {
    const response = await httpInstance.get<T>(`page/following/`, {
      params: { page: 1, ...params },
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getListPageArchives = async <T = any>({ params }: { params?: any }) => {
  try {
    const response = await httpInstance.get<T>(`page/archive/`, {
      params: { page: 1, ...params },
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getFollowedPost = async <T = any>({ id, params }: { id: string; params?: any }) => {
  try {
    const response = await httpInstance.get<T>(`page/${id}/followed-posts/`, { params });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getPageById = async <T = any>({ id }: { id: string }) => {
  try {
    const response = await httpInstance.get<T>(`page/${id}/`, {
      headers: { isHiddenToast: true },
    });
    return response.data;
  } catch (error: any) {
    throw error;
    // return { data: null, error };
  }
};

const createPageFromPreview = async <T>({ payload }: { payload: {} }) => {
  try {
    const result = await httpInstance.post<T>("/page/from-preview/", payload);
    return result.data;
  } catch (error) {
    return { data: null, error };
  }
};
const followPage = async <T>({ page_id, days }: { page_id: string; days: number }) => {
  try {
    const result = await httpInstance.post<T>(`/page/${page_id}/follow/`, {
      days: days,
    });
    if (result.status === 200 || result.status === 201) {
      toast({
        title: "Request Success",
        status: "success",
        duration: 3000,
      });
    }
    return result.data;
  } catch (error) {
    return { data: null, error };
  }
};

const updateFollow = async <T>({ page_id, days }: { page_id: string; days: number }) => {
  try {
    const result: any = await httpInstance.post<T>(`/page/${page_id}/follow/`, {
      days: days,
    });
    return result;
  } catch (error) {
    return { data: null, error };
  }
};
const unfollow = async <T>({ page_id }: { page_id: string }) => {
  try {
    const result = await httpInstance.patch<T>(`/page/${page_id}/unfollow/`);
    return result.status;
  } catch (error) {
    return { data: null, error };
  }
};

const removePage = async <T>({ page_id }: { page_id: string }) => {
  try {
    const result = await httpInstance.delete<T>(`/page/${page_id}/clear-sessions/`);

    if (result.status == 200 || result.status == 204) {
      toast({
        title: "Remove Page Success",
        status: "success",
        duration: 3000,
      });
    }
    return result;
  } catch (error) {
    return { data: null, error };
  }
};
const getFbPreview = async <T = any>({ payload }: { payload: {} }) => {
  try {
    const result = await httpInstance.post<T>("/page/preview/", payload);
    return result.data;
  } catch (error) {
    return { data: null, error };
  }
};
export const pageAPI = {
  getListPage,
  getPageById,
  followPage,
  updateFollow,
  unfollow,
  removePage,
  getListPageFollow,
  getListPageArchives,
  createPageFromPreview,
  getFbPreview,
  getFollowedPost,
};
