import httpInstance from "apis";
import { ICretePaymentRequest, IUpdateInfoInvoice } from "types/Payment";

const getPlanDetail = async <T = any>({ plan_code }: { plan_code: string }) => {
  try {
    const response = await httpInstance.get<T>(`/plans/${plan_code}`);
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getListInvoices = async <T = any>({ params }: { params?: any }) => {
  try {
    const response = await httpInstance.get<T>(`/payment/invoice/`, { params });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getInfoInvoice = async <T = any>() => {
  try {
    const response = await httpInstance.get<T>(`/payment/invoice/info/`);
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const createPaymentLink = async <T = any>(payload: ICretePaymentRequest) => {
  try {
    const response = await httpInstance.post<T>(`/payment/payment-links/`, {
      ...payload,
      currency: "usd",
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};
const updateInforInvoice = async <T = any>(payload: IUpdateInfoInvoice) => {
  try {
    const response = await httpInstance.post<T>(`/payment/update-invoice/`, {
      ...payload,
    });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

export const paymentAPI = {
  getListInvoices,
  createPaymentLink,
  updateInforInvoice,
  getInfoInvoice,
};

export const planAPI = {
  getPlanDetail,
};
