import httpInstance from "apis";
import { toast } from "components/ui/use-toast";
import { ApiResponse, AudienceSegmentData } from "types/yourSegment";

const getList = async (page: number, limit: number): Promise<AudienceSegmentData> => {
  try {
    const response = await httpInstance.get<ApiResponse>(
      `/social-data/segments/?page=${page}&limit=${limit}`
    );
    return response.data.data;
  } catch (error: any) {
    toast({
      title: error.detail || "An error occurred",
      status: "error",
      duration: 3000,
    });
    throw error.detail;
  }
};

type AddYourSegment = {
  name: string;
  description: string;
  audience_id: string;
  query?: string;
  datatype?:'AUDIENCE'|'DATASET'
};

const createSocialSegment = async ({
  name,
  description,
  audience_id,
  query = "",
  datatype
}: AddYourSegment) => {
  try {
    const res = await httpInstance.post(
      `/social-data/segments/?audience_id=${audience_id}${datatype?`&datatype=${datatype}`:''}&${query}`,
      { name,  description, },
      { headers: { isv2: true }
      }
    );
    if (res.data && res.data.code == 0) {
      toast({
        title: res.data.message,
        status: "success",
        duration: 3000,
      });
    }
    return res.data;
  } catch (error) {
    return { error: error, data: null };
  }
};
const createPersonaSegment = async ({
  name,
  description,
  audience_id,
  query = "",
}: AddYourSegment) => {
  try {
    const res = await httpInstance.post(`/persona/segments/?audience_id=${audience_id}&${query}`, {
      name,
      description,
    });
    if (res.data && res.data.code == 0) {
      toast({
        title: res.data.message,
        status: "success",
        duration: 3000,
      });
    }
    return res.data;
  } catch (error) {
    return { error: error, data: null };
  }
};
const getSummarizeDetail = async (id: string) => {
  try {
    const response = await httpInstance.get(`/social-data/segments/${id}/`);
    return response.data;
  } catch (error: any) {
    return { error: error, data: null };
  }
};
const getSegmentListUsers = async (id: string, page: number, limit: number) => {
  try {
    const response = await httpInstance.get(
      `/social-data/segments/${id}/profiles/?page=${page}&limit=${limit}`,{
        headers:{
          isv2: true,
        }
      }
    );
    return response.data.data;
  } catch (error: any) {
    throw error.detail;
  }
};
const getProcessedTree = async (id: string) => {
  try {
    const res = await httpInstance.get(`/social-data/segments/${id}/tree/`);
    return res.data.data;
  } catch (error: any) {
    throw error.detail;
  }
};

export const yourSegmentApi = {
  getList,
  createSocialSegment,
  createPersonaSegment,
  getSummarizeDetail,
  getSegmentListUsers,
  getProcessedTree,
};
