import httpInstance from './index.ts';
import { SocialPersonaDimType, TypeCreateAudience } from '../types/SocialPersona';

const get = async <T = any>({
  params,
  endpoint = '',
  signal
}: {
  params?: any;
  endpoint?: string;
  signal?: AbortSignal;
}) => {
  try {
    const response = await httpInstance.get<T>(`social-persona/${endpoint}`, {
      params,
      signal
    });
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

//yourData
const createPersonaAudience = async (body: TypeCreateAudience) => {
  try {
    const { filters, ...rest } = body;
    const query = new URLSearchParams(filters).toString();
    const result = await httpInstance.post('social-persona/audiences/' + ( query && `?${query}` ), rest);
    return result.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getSocialPersonaDim = async (type: SocialPersonaDimType) => {
  try {
    const response = await httpInstance.get(`social-persona/dims/${type}/`);
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const addListContactToCRM = async (payload: {id: number, typeAdd: string, color: string}) => {
  const { id, typeAdd, color } = payload;
  try {
    const response: any = await httpInstance.post(`contact-crm/${typeAdd}/segments/${id}/`, undefined, {
      params: { color }
    });
    const { response: responseData } = response;
    return responseData?.data || response.data;
  } catch (error) {
    return { data: null, error };
  }
};

const getSegmentLogContactCRM = async <T = any>() => {
  try {
    const response = await httpInstance.get<T>(`contact-crm/segments/logs/`);
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

export const socialPersonaApi = {
  get,
  createPersonaAudience,
  getSocialPersonaDim,
  addListContactToCRM,
  getSegmentLogContactCRM
};
