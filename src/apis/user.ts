import httpInstance from "apis";
import { TBaseResponse } from "types/ResponseApi";

interface UpdateProfileProps {
  full_name?: string;
  gender?: string | null;
  birthdate?: string;
  phone_number?: string;
}
const updateUserProfile = async ({ payload }: { payload: UpdateProfileProps }) => {
  try {
    const response = await httpInstance.patch<TBaseResponse<{}>>("auth/update/", payload);
    return response.data;
  } catch (error) {
    return { error: error };
  }
};

const uploadAvatar = async ({ formData }: { formData: any }) => {
  try {
    const response = await httpInstance.patch<TBaseResponse<{}>>("auth/update/avatar/", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
    return response.data;
  } catch (error) {
    return { error: error };
  }
};

//change pass
export interface ChangePasswordProps {
  password: string;
  old_password: string;
}
const changePassword = async ({ payload }: { payload: ChangePasswordProps }) => {
  const response = await httpInstance.patch<TBaseResponse<{}>>("auth/change-password/", payload, {
    headers: { isHiddenToast: true },
  });
  return response;
};

export const userAPI = {
  updateUserProfile,
  uploadAvatar,
  changePassword,
};
