import httpInstance from "apis";

const getListPost = async <T = any>({ params }: { params?: any }) => {
  try {
    const response = await httpInstance.get<T>(`live-post/`, { params });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};
const getListComment = async <T = any>({ id, params }: { id?: string; params?: any }) => {
  try {
    const response = await httpInstance.get<T>(`live-post/${id}/comments/`, { params });
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getSuggest = async <T = any>({ q }: { q: string }) => {
  try {
    const response = await httpInstance.get<T>(`live-post/search-suggest/?q=${q}`);
    return response.data;
  } catch (error: any) {
    return { data: null, error };
  }
};

const getPostDetail = async <T = any>({ id }: { id?: string }) => {
  try {
    const response = await httpInstance.get<T>(`live-post/${id}/`);
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

export const liveCommentAPI = {
  getListPost,
  getListComment,
  getSuggest,
  getPostDetail,
};
