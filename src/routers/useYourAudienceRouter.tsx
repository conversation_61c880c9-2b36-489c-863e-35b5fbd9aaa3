import { Navigate } from "react-router-dom";
import PrivateRouter from "./PrivateRouter";
import Loadable from "components/Loading/Loadable";
import { lazyWithRetry } from "utils/loadingWithRetry";
import { DATA_AUDIENCE, ROOT_PATH, YOUR_AUDIENCES } from "types/Router";
import { AudienceProfilesProvider } from "views/YourAudience/context/AudienceProfilesContext";

const useYourAudienceRouter = () => [
  {
    path: `${ROOT_PATH}/${DATA_AUDIENCE.ROOT}`,
    element: <PrivateRouter children={<YourAudienceView />} />,
    children: [
      {
        path: `${ROOT_PATH}/${DATA_AUDIENCE.ROOT}`,
        element: (
          <Navigate to={`${ROOT_PATH}/${DATA_AUDIENCE.ROOT}/${DATA_AUDIENCE.SOCIAL}`} replace />
        ),
      },
      {
        path: `${ROOT_PATH}/${DATA_AUDIENCE.ROOT}/${DATA_AUDIENCE.SOCIAL}`,
        element: <PrivateRouter children={<SocialTable />} />,
      },
      {
        path: `${ROOT_PATH}/${DATA_AUDIENCE.ROOT}/${DATA_AUDIENCE.PERSONA}`,
        element: <PrivateRouter children={<PersonaTable />} />,
      },
    ],
  },
  {
    path: `/${YOUR_AUDIENCES.ROOT}/${YOUR_AUDIENCES.SOCIAL}/:id`,
    element: <PrivateRouter children={<SocialAudience />} />,
  },
  {
    path: `/${YOUR_AUDIENCES.ROOT}/${YOUR_AUDIENCES.SOCIAL_DATASET}/:id`,
    element: <PrivateRouter children={<SocialAudience />} />,
  },
  {
    path: `/${YOUR_AUDIENCES.ROOT}/${YOUR_AUDIENCES.PERSONA}/:id`,
    element: <PrivateRouter children={<PersonaAudience />} />,
  },
  {
    path: `/${YOUR_AUDIENCES.ROOT}/${YOUR_AUDIENCES.PERSONA}/${YOUR_AUDIENCES.ARCHIVE}`,
    element: (
      <PrivateRouter
        children={
          <AudienceProfilesProvider>
            <AudienceProfiles />
          </AudienceProfilesProvider>
        }
      />
    ),
  },
];

export default useYourAudienceRouter;

const YourAudienceView = Loadable(lazyWithRetry(() => import("views/YourAudience/index")));
const PersonaAudience = Loadable(lazyWithRetry(() => import("views/YourAudience/PersonaAudience")));
const SocialAudience = Loadable(lazyWithRetry(() => import("views/YourAudience/SocialAudience")));
const AudienceProfiles = Loadable(
  lazyWithRetry(() => import("views/YourAudience/AudienceProfiles"))
);

const PersonaTable = Loadable(lazyWithRetry(() => import("views/YourAudience/tabs/PersonaTable")));
const SocialTable = Loadable(lazyWithRetry(() => import("views/YourAudience/tabs/SocialTable")));
