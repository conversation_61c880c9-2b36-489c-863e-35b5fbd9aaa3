import Loadable from "components/Loading/Loadable";
import { lazyWithRetry } from "utils/loadingWithRetry";
import { isRemoteAvailable } from "utils/remoteLoader";
import PrivateRouter from "./PrivateRouter";
import { Maintenance } from '../components/Maintenance';

const CRM360_URL = process.env.VITE_CRM360_URL || '';

const Crm360 = Loadable(
  lazyWithRetry(async () => {
    try {
      // Check if remote is available first
      const remoteUrl = `${CRM360_URL}/assets/remoteEntry.js`;
      const isAvailable = await isRemoteAvailable(remoteUrl);

      if (!isAvailable) {
        console.warn('CRM360 remote is not available, showing maintenance page');
        return { default: () => <Maintenance /> };
      }

      // Try to import with cache busting in production
      const cacheBuster = process.env.NODE_ENV === 'production' ? `?v=${Date.now()}` : '';
      const moduleUrl = `crm360/App${cacheBuster}`;

      return await import(/* @vite-ignore */ moduleUrl);
    } catch (error) {
      console.error('Failed to load CRM360 module:', error);
      return { default: () => <Maintenance /> };
    }
  })
)

const useCRM360Router = () => {
  return [
    {
      path: "/crm360/*",
      element: <PrivateRouter children={<Crm360 />} />,
    },
  ];
};
export default useCRM360Router;
