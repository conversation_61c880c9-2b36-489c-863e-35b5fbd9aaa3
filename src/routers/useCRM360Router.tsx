import Loadable from "components/Loading/Loadable";
import { lazyWithRetry } from "utils/loadingWithRetry";
import PrivateRouter from "./PrivateRouter";
import { Maintenance } from '../components/Maintenance';

const Crm360 = Loadable(
  lazyWithRetry(() =>
    import('crm360/App').catch(() => {
      return { default: () => <Maintenance /> };
    })
  )
)

const useCRM360Router = () => {
  return [
    {
      path: "/crm360/*",
      element: <PrivateRouter children={<Crm360 />} />,
    },
  ];
};
export default useCRM360Router;
