import { Navigate } from "react-router-dom";

import { ITab } from "components/Tabs/TabRouteWrap";
import Loadable from "components/Loading/Loadable";
import UserPlanView from "views/UserPlan";
import PrivateRouter from "./PrivateRouter";
import UserTransactionView from "views/UserTransaction";

import { lazyWithRetry } from "utils/loadingWithRetry";
import { ROOT_PATH, USER_PROFILE_PATH } from "types/Router";
import { RiAccountBoxLine, RiCurrencyLine, RiFileList3Line, RiTodoLine } from "@remixicon/react";

const ProfileView = Loadable(lazyWithRetry(() => import("views/UserProfile/index")));
const BillingView = Loadable(lazyWithRetry(() => import("views/UserBillingView/index")));
const PlanOverview = Loadable(lazyWithRetry(() => import("views/UserPlan/index")));
const TransactionView = Loadable(lazyWithRetry(() => import("views/UserTransaction/index")));

export const USER_TABS: ITab[] = [
  {
    title: "Profile",
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.PROFILE}`,
    content: <ProfileView />,
    tab_icon: RiAccountBoxLine,
  },
  {
    title: "Plan Overview",
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.PLAN}`,
    content: <UserPlanView />,
    tab_icon: RiTodoLine,
  },
  {
    title: "Billing",
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.BILLING}`,
    content: <BillingView />,
    tab_icon: RiFileList3Line,
  },
  {
    title: "Transaction",
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.TRANSACTION}`,
    content: <UserTransactionView />,
    tab_icon: RiCurrencyLine,
  },
];

const useUserProfileRouter = () => [
  {
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}`,
    element: (
      <Navigate
        to={`${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.PROFILE}`}
        replace
      />
    ),
  },
  {
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.PROFILE}`,
    element: <PrivateRouter children={<ProfileView />} isCheckSubscription={false} />,
  },
  {
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.PLAN}`,
    element: <PrivateRouter children={<PlanOverview />} isCheckSubscription={false} />,
  },
  {
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.BILLING}`,
    element: <PrivateRouter children={<BillingView />} isCheckSubscription={false} />,
  },
  {
    path: `${ROOT_PATH}/${USER_PROFILE_PATH.ROOT}/${USER_PROFILE_PATH.TRANSACTION}`,
    element: <PrivateRouter children={<TransactionView />} isCheckSubscription={false} />,
  },
  //
];

export default useUserProfileRouter;
