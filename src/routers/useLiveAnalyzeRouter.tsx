import { Navigate } from "react-router-dom";

import PrivateRouter from "./PrivateRouter";
import Loadable from "components/Loading/Loadable";

import { FB_SERVICE_PATH, ROOT_PATH } from "types/Router";
import { lazyWithRetry } from "utils/loadingWithRetry";

const useLiveAnalyzeRouter = () => [
  {
    path: `${ROOT_PATH}/${FB_SERVICE_PATH.LIVE}`,
    element: <LivePostsView />,
    children: [
      {
        path: "",
        element: (
          <Navigate
            to={`${ROOT_PATH}/${FB_SERVICE_PATH.LIVE}/${FB_SERVICE_PATH.LIVE_COMMUNITY}`}
            replace
          />
        ),
      },
      {
        path: `${ROOT_PATH}/${FB_SERVICE_PATH.LIVE}/${FB_SERVICE_PATH.LIVE_COMMUNITY}`,
        element: <PrivateRouter children={<ListLiveTab />} />,
      },
      {
        path: `${ROOT_PATH}/${FB_SERVICE_PATH.LIVE}/${FB_SERVICE_PATH.LIVE_FOLLOWING}`,
        element: <PrivateRouter children={<FollowPagesTab />} />,
      },
      {
        path: `${ROOT_PATH}/${FB_SERVICE_PATH.LIVE}/${FB_SERVICE_PATH.ARCHIVES}`,
        element: <PrivateRouter children={<ArchivesTab />} />,
      },
    ],
  },
  {
    path: `${ROOT_PATH}/${FB_SERVICE_PATH.LIVE}/${FB_SERVICE_PATH.LIVE_COMMUNITY}/:id`,
    element: <PrivateRouter children={<PostDetail />} />,
  },
  {
    path: `${ROOT_PATH}/${FB_SERVICE_PATH.LIVE}/${FB_SERVICE_PATH.LIVE_FOLLOWING}/:id`,
    element: <PrivateRouter children={<FollowPage />} />,
  },
];

export default useLiveAnalyzeRouter;

const LivePostsView = Loadable(lazyWithRetry(() => import("views/LivePostView/index")));
const ListLiveTab = Loadable(lazyWithRetry(() => import("views/LivePostView/tabs/CommunityLive")));

const PostDetail = Loadable(lazyWithRetry(() => import("views/LivePostDetail")));

const ArchivesTab = Loadable(lazyWithRetry(() => import("views/LivePostView/tabs/Archives")));
const FollowPagesTab = Loadable(
  lazyWithRetry(() => import("views/LivePostView/tabs/YourFollowTab"))
);
const FollowPage = Loadable(lazyWithRetry(() => import("views/YourPageView")));
