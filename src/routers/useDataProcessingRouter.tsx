import { ROOT_PATH, DATAPROCESSING } from "types/Router";
import PrivateRouter from "./PrivateRouter";
import { Navigate } from "react-router-dom";
import Loadable from "components/Loading/Loadable";
import { lazyWithRetry } from "utils/loadingWithRetry";

const useDataProcessingRouter = () => [
  {
    path: `${ROOT_PATH}/${DATAPROCESSING.ROOT}`,
    element: <PrivateRouter children={<DataProcessingView />} />,
    children: [
      {
        path: "",
        element: (
          <Navigate to={`${ROOT_PATH}/${DATAPROCESSING.ROOT}/${DATAPROCESSING.SOCIAL}`} replace />
        ),
      },
      {
        path: `${ROOT_PATH}/${DATAPROCESSING.ROOT}/${DATAPROCESSING.SOCIAL}`,
        element: <PrivateRouter children={<SocialTab />} />,
      },
      {
        path: `${ROOT_PATH}/${DATAPROCESSING.ROOT}/${DATAPROCESSING.PERSONA}`,
        element: <PrivateRouter children={<PersonaTab />} />,
      },
    ],
  },
];

export default useDataProcessingRouter;

const DataProcessingView = Loadable(lazyWithRetry(() => import("views/DataProcessing/index")));
const SocialTab = Loadable(lazyWithRetry(() => import("views/DataProcessing/tabs/Social")));
const PersonaTab = Loadable(lazyWithRetry(() => import("views/DataProcessing/tabs/Persona")));
