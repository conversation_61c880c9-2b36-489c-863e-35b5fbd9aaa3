import { useEffect } from 'react';
import { useSelector } from "react-redux";
import { Navigate, useLocation } from "react-router-dom";
import { useAppSelector } from "store";
import { subscriptionStore } from "store/redux/subscription/slice";
import { clearLS, getAccessTokenFromLS } from "utils/localStorage";
import useRedirectURL from '../hooks/useRedirectURL';

type Props = {
  children: React.ReactNode;
  isCheckSubscription?: boolean;
};

const PrivateRouter = ({ children, isCheckSubscription = true }: Props) => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const { is_active } = useSelector(subscriptionStore);
  const accessToken = getAccessTokenFromLS();
  const location = useLocation();
  const { params } = useRedirectURL();
  const pathName = location.pathname;
  const searchParams = new URLSearchParams(params).toString();

  useEffect(() => {
    if (!accessToken) {
      clearLS();
    }
  }, [accessToken]);

  if (!accessToken) {
    return <Navigate to={`/login?redirect=${pathName}${searchParams?'&'+searchParams:''}`} replace />;
  }

  if (isAuthenticated === false) {
    return <Navigate to={`/login?redirect=${pathName}${searchParams?'&'+searchParams:''}`} replace />;
  }

  if (
    is_active !== null && !is_active && isCheckSubscription &&
    location.pathname !== "/plan/upgrade"
  ) {
    return <Navigate to="/plan/upgrade" replace />;
  }

  return <>{children}</>;
};

export default PrivateRouter;
