import LandingLayout from "layout/LandingLayout";
import PublicRouter from "./PublicRouter";
import Login from "views/AuthenticationView/Login";
import Signup from "views/AuthenticationView/Signup";
import Verify from "views/AuthenticationView/Verify";
import ForgotPassword from "views/AuthenticationView/ForgotPassword";
import { LoginGoogle } from '../views/AuthenticationView/LoginGoogle';

const useAuthentication = () => ({
  path: "",
  element: <LandingLayout />,
  children: [
    {
      path: "/login",
      element: (
        <PublicRouter>
          <Login />
        </PublicRouter>
      ),
    },
    {
      path: "/auth/oauth-callback/",
      element: (
        <PublicRouter>
          <LoginGoogle />
        </PublicRouter>
      ),
    },
    {
      path: "/signup",
      element: (
        <PublicRouter>
          <Signup />
        </PublicRouter>
      ),
    },
    {
      path: "/authen/verify-email/:token/",
      element: (
        <PublicRouter>
          <Verify />
        </PublicRouter>
      ),
    },
    {
      path: "/authen/forgot-password/:token/",
      element: (
        <PublicRouter>
          <ForgotPassword />
        </PublicRouter>
      ),
    },
  ],
});

export default useAuthentication;
