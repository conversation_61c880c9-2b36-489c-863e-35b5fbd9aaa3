import PrivateRouter from "./PrivateRouter";
import Loadable from "components/Loading/Loadable";
import { lazyWithRetry } from "utils/loadingWithRetry";
import { AUDIENCE_FINDER_PATH } from "types/Router";
import { SocialPersonaProvider } from "../views/SocialPersona/Context/SocialPersonaContext.tsx";

const useSocialRouter = () => [
  {
    path: AUDIENCE_FINDER_PATH.SOCIAL,
    element: <PrivateRouter children={<SocialView />} />,
  },
  {
    path: AUDIENCE_FINDER_PATH.SOCIAL_PERSONA,
    element: <PrivateRouter
      children={<SocialPersonaProvider>
        <SocialPersonaView />
      </SocialPersonaProvider>}
    />
  },
  {
    path: `/${AUDIENCE_FINDER_PATH.SOCIAL}/${AUDIENCE_FINDER_PATH.SOCIAL_DETAIL}/:id`,
    element: <PrivateRouter children={<SocialDetail />} />,
  },
];

export default useSocialRouter;

const SocialView = Loadable(lazyWithRetry(() => import("views/SocialDataView/index")));
const SocialPersonaView = Loadable(lazyWithRetry(() => import("views/SocialPersona/")));
const SocialDetail = Loadable(lazyWithRetry(() => import("views/SocialDataDetail/index")));
