import { Navigate, useNavigate } from 'react-router-dom';
import { getTokens } from 'utils/localStorage';
import useRedirectURL from '../hooks/useRedirectURL';
import React from 'react';

type Props = {
  children: React.ReactNode;
};

const PublicRouter = (props: Props) => {
  const { accessToken, refreshToken } = getTokens();
  const { paramsNoReDirect, reDirect } = useRedirectURL();
  const navigate = useNavigate();

  if (!!reDirect && reDirect !== '/' && refreshToken && accessToken) {
    const reDirectURL = reDirect
      ? `${reDirect}?${new URLSearchParams(paramsNoReDirect).toString()}`
      : null;
    navigate(reDirectURL ?? '/');
  }

  return <>{!refreshToken && !accessToken ? <>{props.children}</> : <Navigate to="/" replace />}</>;
};
export default PublicRouter;
