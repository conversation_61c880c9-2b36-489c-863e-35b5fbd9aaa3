import PrivateRouter from "./PrivateRouter";
import Loadable from "components/Loading/Loadable";
import { RequestAudienceProvider } from "views/RequestAudienceView/RequestContext";

import { FB_SERVICE_PATH, ROOT_PATH } from "types/Router";
import { lazyWithRetry } from "utils/loadingWithRetry";

const useRequestAudienceRouter = () => [
  {
    path: `${ROOT_PATH}/${FB_SERVICE_PATH.REQUEST_AUDIENCE}`,
    element: (
      <PrivateRouter
        children={
          <RequestAudienceProvider>
            <RequestAudienceView />
          </RequestAudienceProvider>
        }
      />
    ),
  },
];

export default useRequestAudienceRouter;

const RequestAudienceView = Loadable(lazyWithRetry(() => import("views/RequestAudienceView")));
