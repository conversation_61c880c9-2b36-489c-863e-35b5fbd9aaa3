import { MapProvider } from "views/Persona/context/MapProvider";
import PrivateRouter from "./PrivateRouter";
import Loadable from "components/Loading/Loadable";
import { lazyWithRetry } from "utils/loadingWithRetry";
import { AUDIENCE_FINDER_PATH } from "types/Router";
import { PersonaProvider } from "views/Persona/context/PersonaContext";

const usePersonaRouter = () => [
  {
    path: AUDIENCE_FINDER_PATH.PERSONA,
    element: (
      <PrivateRouter
        children={
          <PersonaProvider>
            <MapProvider>
              <PersonaView />
            </MapProvider>
          </PersonaProvider>
        }
      />
    ),
  },
];

export default usePersonaRouter;

const PersonaView = Loadable(lazyWithRetry(() => import("views/Persona/index")));
