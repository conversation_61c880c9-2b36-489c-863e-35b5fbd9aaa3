import { ENRICHMENT_PATH, ROOT_PATH } from "types/Router";
import PrivateRouter from "./PrivateRouter";
import Loadable from "components/Loading/Loadable";
import { lazyWithRetry } from "utils/loadingWithRetry";

const useEnrichmentRouter = () => [
  {
    path: `${ROOT_PATH}/${ENRICHMENT_PATH.ENRICHMENT}`,
    element: <PrivateRouter children={<EnrichmentView />} />,
  },
];

export default useEnrichmentRouter;

const EnrichmentView = Loadable(lazyWithRetry(() => import("views/EnrichmentView/index")));
