import Loadable from "components/Loading/Loadable";
import PrivateRouter from "./PrivateRouter";
import { lazyWithRetry } from "utils/loadingWithRetry";
import { FB_SERVICE_PATH, ROOT_PATH } from "types/Router";
import { Maintenance } from '../components/Maintenance';

const useAdsPostAnalyzeRouter = () => {
  return [
    {
      path: `${ROOT_PATH}/${FB_SERVICE_PATH.POST}`,
      element: (
        <PrivateRouter
          // permissionCode={FEATURE_PERMISSION_KEY.SCD_VIEW_ADPOST_COMMUNITY}
          children={<ListAdsPost />}
        />
      ),
    },
    {
      path: `/${FB_SERVICE_PATH.POST}/:id`,
      element: <PrivateRouter children={<PostDetail />} />,
    },
  ];
};

export default useAdsPostAnalyzeRouter;

const ListAdsPost = Loadable(lazyWithRetry(() => import('views/AdsPostView').catch(() => {
  return { default: () => <Maintenance /> };
})));
const PostDetail = Loadable(lazyWithRetry(() => import('views/AdsPostDetail').catch(() => {
  return { default: () => <Maintenance /> };
})));
