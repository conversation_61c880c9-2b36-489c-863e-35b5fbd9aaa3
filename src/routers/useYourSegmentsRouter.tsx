import { Navigate } from "react-router-dom";
import Loadable from "components/Loading/Loadable";
import PrivateRouter from "./PrivateRouter";
import { lazyWithRetry } from "utils/loadingWithRetry";
import { DATA_SEGMENT, ROOT_PATH, YOUR_SEGMENTS } from "types/Router";

const useYourSegmentsRouter = () => [
  {
    path: `${ROOT_PATH}/${YOUR_SEGMENTS.ROOT}`,
    element: <PrivateRouter children={<YourSegmentView />} />,
    children: [
      {
        path: `${ROOT_PATH}/${DATA_SEGMENT.ROOT}`,
        element: (
          <Navigate to={`${ROOT_PATH}/${DATA_SEGMENT.ROOT}/${DATA_SEGMENT.SOCIAL}`} replace />
        ),
      },
      {
        path: `${ROOT_PATH}/${DATA_SEGMENT.ROOT}/${DATA_SEGMENT.SOCIAL}`,
        element: <PrivateRouter children={<SocialTable />} />,
      },
      {
        path: `${ROOT_PATH}/${DATA_SEGMENT.ROOT}/${DATA_SEGMENT.PERSONA}`,
        element: <PrivateRouter children={<PersonaTable />} />,
      },
    ],
  },
  {
    path: `${ROOT_PATH}/${YOUR_SEGMENTS.ROOT}/${YOUR_SEGMENTS.PERSONA}/:id`,
    element: <PrivateRouter children={<PersonaSegments />} />,
  },
  {
    path: `${ROOT_PATH}/${YOUR_SEGMENTS.ROOT}/${YOUR_SEGMENTS.SOCIAL}/:id`,
    element: <PrivateRouter children={<SocialSegments />} />,
  },
];

export default useYourSegmentsRouter;

const YourSegmentView = Loadable(lazyWithRetry(() => import("views/YourSegment/YourSegment")));
const PersonaSegments = Loadable(lazyWithRetry(() => import("views/YourSegment/PersonaSegment")));
const SocialSegments = Loadable(lazyWithRetry(() => import("views/YourSegment/SocialSegment")));

const SocialTable = Loadable(lazyWithRetry(() => import("views/YourSegment/Tabs/SocialTab")));
const PersonaTable = Loadable(lazyWithRetry(() => import("views/YourSegment/Tabs/PersonaTab")));
