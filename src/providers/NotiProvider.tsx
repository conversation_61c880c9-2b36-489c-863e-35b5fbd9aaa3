import { createContext, ReactNode, useEffect } from 'react';
import useWebSocket from '../hooks/useWebSocket';
import { useAppDispatch, useAppSelector } from '../store';
import {
  getCountUnread,
  getCountUnreadType,
  setIsNewNoti,
  setIsReloadTable,
  setTimeReadNoti
} from '../store/redux/notification/slice';

type NotiProviderProps = {children: React.ReactNode} | null;

const NotiContext = createContext<NotiProviderProps>(null);

const NotiProvider = ({ children }: {children: ReactNode}) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { isConnected, message, connectSocket, disconnectSocket } = useWebSocket(user.uuid);

  useEffect(() => {
    if (!!user.uuid) {
      if (!isConnected) {
        connectSocket();
      }
      return () => {
        disconnectSocket();
      };
    }
  }, [connectSocket, disconnectSocket]);

  useEffect(() => {
    if (isConnected) {
      dispatch(getCountUnreadType()).unwrap();
      dispatch(getCountUnread()).unwrap();
      if (message && typeof message === 'string') {
        dispatch(setIsNewNoti(true));
        // const dataParse: {data?: {[key: string]: string}, event?: string} = JSON.parse(message);
        dispatch(setIsReloadTable(true));
        dispatch(setTimeReadNoti(Date.now()));
      }
    }
  }, [isConnected, message]);

  return <NotiContext.Provider value={{ children }}>{children}</NotiContext.Provider>;
};
export { NotiProvider, NotiContext };
