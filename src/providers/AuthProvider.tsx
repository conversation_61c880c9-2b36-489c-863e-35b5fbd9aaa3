import getUserInfor from "hooks/getUserInfor";
import { createContext, ReactNode } from "react";

type AuthProviderProps = { children: React.ReactNode } | null;

const AuthContext = createContext<AuthProviderProps>(null);

const AuthProvider = ({ children }: { children: ReactNode }) => {
  getUserInfor();
  return <AuthContext.Provider value={{ children }}>{children}</AuthContext.Provider>;
};
export { AuthProvider, AuthContext };
