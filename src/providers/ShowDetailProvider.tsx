import { createContext, ReactNode, useState } from "react";

interface IShowDetail {
  isShow: boolean;
  id: string;
}

type ShowDetailContextProps = {
  showDetail: IShowDetail;
  setShowDetail: React.Dispatch<React.SetStateAction<IShowDetail>>;
} | null;

const ShowDetailContext = createContext<ShowDetailContextProps>(null);

const ShowDetailProvider = ({ children }: { children: ReactNode }) => {
  const [showDetail, setShowDetail] = useState<IShowDetail>({
    isShow: false,
    id: "",
  });

  return (
    <ShowDetailContext.Provider
      value={{
        showDetail,
        setShowDetail,
      }}
    >
      {children}
    </ShowDetailContext.Provider>
  );
};
export { ShowDetailProvider, ShowDetailContext };
