import { createContext, ReactNode, useState } from "react";

interface IDialogProps {
  isOpen: boolean;
  title?: string;
  message?: string;
  content?: JSX.Element | React.ReactNode | string;
  footer?: JSX.Element | React.ReactNode | string;
  isShowTitle?: boolean;
  isPopupVideo?: boolean;
  buttonSubmit?: React.ElementType;
  buttonVariant?: string;
  buttonText?: string;
  className?: string;
  handleSubmit?: () => void;
  handleCancel?: () => void;
}

export type ModalContextProps = {
  dataDialog: IDialogProps;
  setDataDialog: React.Dispatch<React.SetStateAction<IDialogProps>>;
} | null;

const ModalContext = createContext<ModalContextProps>(null);

const ModalAppProvider = ({ children }: { children: ReactNode }) => {
  const [dataDialog, setDataDialog] = useState<IDialogProps>({
    isOpen: false,
    isShowTitle: true,
    isPopupVideo: false
  });

  return (
    <ModalContext.Provider
      value={{
        dataDialog,
        setDataDialog,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};
export { ModalAppProvider, ModalContext };
