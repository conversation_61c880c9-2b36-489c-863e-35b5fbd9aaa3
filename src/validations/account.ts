import { ERROR_PASSWORD_MESSAGE } from "constants/message/password.message";
import * as yup from "yup";

// RegisterBody schema
export const RegisterBody = yup.object().shape({
  full_name: yup
    .string()
    .trim()
    .min(2, "Full name must be at least 2 characters")
    .max(256)
    .required("Full name is required"),
  email: yup
    .string()
    .email()
    .required()
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, "Invalid email")
    .transform((value) => value ? value.toLowerCase().trim() : value),
  password: yup
    .string()
    .required(ERROR_PASSWORD_MESSAGE.required)
    .min(8, ERROR_PASSWORD_MESSAGE.minLength)
    .max(20, ERROR_PASSWORD_MESSAGE.maxLength)
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})/,
      "Must Contain 8 Characters, One Uppercase, One Lowercase, One Number and One Special Case Character"
    ),
  term: yup
  .boolean()
  .oneOf([true], "You must accept the terms and conditions")
  .required(),
  confirmPassword: yup
    .string()
    .min(8)
    .max(100)
    .oneOf([yup.ref("password"), ""], ERROR_PASSWORD_MESSAGE.confirmPassword)
    .required(),
  referral_code: yup.string().nullable(),
});

// TypeScript type for RegisterBody schema
export type RegisterBodyType = yup.InferType<typeof RegisterBody>;

// LoginBody schema
export const LoginBody = yup.object().shape({
  username: yup.string().email().required().transform((value) => value ? value.toLowerCase().trim() : value),
  password: yup.string().required(),
});

// TypeScript type for LoginBody schema
export type LoginBodyType = yup.InferType<typeof LoginBody>;

// ForgotPasswordBody schema
export const ForgotPasswordBody = yup.object().shape({
  email: yup.string().email().required().transform((value) => value ? value.toLowerCase().trim() : value),
  password: yup
    .string()
    .required(ERROR_PASSWORD_MESSAGE.required)
    .min(8, ERROR_PASSWORD_MESSAGE.minLength)
    .max(20, ERROR_PASSWORD_MESSAGE.maxLength)
    .matches(/[a-z]/, ERROR_PASSWORD_MESSAGE.pattern.lowercase)
    .matches(/[A-Z]/, ERROR_PASSWORD_MESSAGE.pattern.uppercase)
    .matches(/\d/, ERROR_PASSWORD_MESSAGE.pattern.number)
    .matches(/[@$!%*?&#]/, ERROR_PASSWORD_MESSAGE.pattern.special),
});

// TypeScript type for ForgotPasswordBody schema
export type ForgotPasswordBodyType = yup.InferType<typeof ForgotPasswordBody>;

export type TReferral ={
  benefit_credits: number
  benefit_trial_days: number
  code: string
  description: string
}
