import * as yup from "yup";

export type UpdateAccountSchemaType = yup.InferType<typeof UpdateAccountSchema>;

export const UpdateAccountSchema = yup.object().shape({
  formType: yup.string().required(),

  // Full Name
  full_name: yup
    .string()
    .trim()
    .min(2, "Full name must be at least 2 characters")
    .max(50, "Full name must not exceed 50 characters")
    .when("formType", {
      is: "full_name_gender",
      then: (schema) => schema.required("Full name is required"),
      otherwise: (schema) => schema.notRequired(),
    }),

  // Gender
  gender: yup
    .string()
    .nullable()
    .when("formType", {
      is: "full_name_gender",
      then: (schema) => schema.notRequired(),
      otherwise: (schema) => schema.notRequired(),
    }),

  // Phone Number
  phone_number: yup
    .string()
    .nullable()
    .notRequired()
    .test(
      "is-valid-phone",
      "Phone number must start with 0 or +84 and contain 10 or 12 digits",
      (value) => {
        if (!value) return true;
        return /^(?:\+84|0)(3[2-9]|5[2689]|7[06-9]|8[1-9]|9[0-9])\d{7}$/.test(value);
      }
    ),
});

export type UpdatePasswordSchemaType = yup.InferType<typeof UpdatePasswordSchema>;

export const UpdatePasswordSchema = yup.object().shape({
  currentPassword: yup.string().required("Current password is required"),
  newPassword: yup
    .string()
    .required("New password is required")
    .min(8, "Password must be at least 8 characters")
    .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
    .matches(/[a-z]/, "Password must contain at least one lowercase letter")
    .matches(/[0-9]/, "Password must contain at least one number")
    .matches(/[@$!%*?&]/, "Password must contain at least one special character"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("newPassword")], "Passwords do not match")
    .required("Confirm password is required"),
});
