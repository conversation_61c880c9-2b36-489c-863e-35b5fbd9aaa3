import * as yup from "yup";

export type BillingInfoSchemaType = yup.InferType<typeof BillingInfoSchema>;

export const BillingInfoSchema = yup.object().shape({
  name_invoice: yup.string().nullable(),
  address_line_1: yup.string().nullable(),
  email_invoice: yup
    .string()
    .nullable()
    .test("is-valid-email", "Invalid email", (value) => {
      if (!value) return true;
      return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
    })
    .transform((value) => value ? value.toLowerCase().trim() : value),
  tax_id: yup
    .string()
    .nullable()
    .test("is-valid-tax", "Tax must be exactly 10 characters", (value) => {
      if (!value) return true;
      return /^[a-zA-Z0-9]{10}$/.test(value);
    }),
});
