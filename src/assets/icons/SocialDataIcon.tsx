const SocialDataIcon = () => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.3175 11.2698C16.0626 11.2698 16.6667 10.6658 16.6667 9.92062C16.6667 9.17547 16.0626 8.57141 15.3175 8.57141C14.5723 8.57141 13.9683 9.17547 13.9683 9.92062C13.9683 10.6658 14.5723 11.2698 15.3175 11.2698Z"
        stroke="#6C6E79"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.3175 5.19841C16.0626 5.19841 16.6667 4.59435 16.6667 3.84921C16.6667 3.10406 16.0626 2.5 15.3175 2.5C14.5723 2.5 13.9683 3.10406 13.9683 3.84921C13.9683 4.59435 14.5723 5.19841 15.3175 5.19841Z"
        stroke="#6C6E79"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.3175 16.6667C16.0626 16.6667 16.6667 16.0626 16.6667 15.3175C16.6667 14.5723 16.0626 13.9683 15.3175 13.9683C14.5723 13.9683 13.9683 14.5723 13.9683 15.3175C13.9683 16.0626 14.5723 16.6667 15.3175 16.6667Z"
        stroke="#6C6E79"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.84921 11.2698C4.59435 11.2698 5.19841 10.6658 5.19841 9.92062C5.19841 9.17547 4.59435 8.57141 3.84921 8.57141C3.10406 8.57141 2.5 9.17547 2.5 9.92062C2.5 10.6658 3.10406 11.2698 3.84921 11.2698Z"
        stroke="#6C6E79"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.19849 9.92065H13.9683"
        stroke="#6C6E79"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.9683 3.84924H11.2699C9.9207 3.84924 9.24609 4.56601 9.24609 5.99954V13.1672C9.24609 14.6007 9.9207 15.3175 11.2699 15.3175H13.9683"
        stroke="#6C6E79"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SocialDataIcon;
