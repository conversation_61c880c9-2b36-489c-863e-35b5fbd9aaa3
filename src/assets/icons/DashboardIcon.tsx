const DashboardIcon = () => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.00008 0.666626C13.6026 0.666626 17.3334 4.39746 17.3334 8.99996C17.3334 13.6025 13.6026 17.3333 9.00008 17.3333C4.39758 17.3333 0.666748 13.6025 0.666748 8.99996C0.666748 4.39746 4.39758 0.666626 9.00008 0.666626ZM9.00008 2.33329C5.31841 2.33329 2.33341 5.31829 2.33341 8.99996C2.33341 12.6816 5.31841 15.6666 9.00008 15.6666C12.6817 15.6666 15.6667 12.6816 15.6667 8.99996C15.6667 5.31829 12.6817 2.33329 9.00008 2.33329ZM12.1942 5.11413C12.3917 4.97579 12.6601 4.99913 12.8301 5.16996C13.0001 5.33996 13.0217 5.60829 12.8826 5.80329C11.0659 8.34163 10.0659 9.70163 9.88425 9.88413C9.39592 10.3716 8.60425 10.3716 8.11592 9.88413C7.62842 9.39579 7.62842 8.60413 8.11592 8.11579C8.42758 7.80496 9.78675 6.80413 12.1942 5.11413ZM13.5834 8.16663C14.0434 8.16663 14.4167 8.53996 14.4167 8.99996C14.4167 9.45996 14.0434 9.83329 13.5834 9.83329C13.1234 9.83329 12.7501 9.45996 12.7501 8.99996C12.7501 8.53996 13.1234 8.16663 13.5834 8.16663ZM4.41675 8.16663C4.87675 8.16663 5.25008 8.53996 5.25008 8.99996C5.25008 9.45996 4.87675 9.83329 4.41675 9.83329C3.95675 9.83329 3.58341 9.45996 3.58341 8.99996C3.58341 8.53996 3.95675 8.16663 4.41675 8.16663ZM6.34841 5.16996C6.67341 5.49496 6.67341 6.02246 6.34841 6.34829C6.02341 6.67329 5.49508 6.67329 5.17008 6.34829C4.84508 6.02329 4.84508 5.49496 5.17008 5.16996C5.49508 4.84496 6.02258 4.84496 6.34841 5.16996ZM9.00008 3.58329C9.46008 3.58329 9.83342 3.95663 9.83342 4.41663C9.83342 4.87663 9.46008 5.24996 9.00008 5.24996C8.54008 5.24996 8.16675 4.87663 8.16675 4.41663C8.16675 3.95663 8.54008 3.58329 9.00008 3.58329Z"
        fill="#0F1324"
        fillOpacity="0.6"
      />
    </svg>
  );
};

export default DashboardIcon;
