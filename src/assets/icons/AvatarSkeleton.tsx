const AvatarSkeleton = () => {
  return (
    <svg
      width="331"
      height="142"
      viewBox="0 0 331 142"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="animate-pulse"
    >
      <g filter="url(#filter0_dd_304_3783)">
        <path
          d="M13 29C13 15.7452 23.7452 5 37 5H294C307.255 5 318 15.7452 318 29V95C318 108.255 307.255 119 294 119H37C23.7452 119 13 108.255 13 95V29Z"
          fill="white"
        />
        <path
          d="M65.1016 86.4844C51.9297 86.4844 41.1953 75.75 41.1953 62.5781C41.1953 49.3828 51.9297 38.6719 65.1016 38.6719C78.2969 38.6719 89.0078 49.3828 89.0078 62.5781C89.0078 75.75 78.2969 86.4844 65.1016 86.4844ZM65.0781 66.5859C69.6016 66.6328 73.1172 62.7891 73.1172 57.75C73.1172 53.0156 69.5781 49.0781 65.0781 49.0781C60.6016 49.0781 57.0156 53.0156 57.0391 57.75C57.0391 62.7891 60.5781 66.5391 65.0781 66.5859ZM65.0781 82.9219C70.4688 82.9219 75.8828 80.7188 79.4922 76.8984C76.9375 72.8672 71.3828 70.5703 65.0781 70.5703C58.7266 70.5703 53.2188 72.9141 50.6875 76.8984C54.2734 80.7188 59.7109 82.9219 65.0781 82.9219Z"
          fill="#924FE8"
        />
        <path
          d="M113.367 46.3281C108.977 46.3281 105.398 42.75 105.398 38.3594C105.398 33.9609 108.977 30.3906 113.367 30.3906C117.766 30.3906 121.336 33.9609 121.336 38.3594C121.336 42.75 117.766 46.3281 113.367 46.3281ZM112.492 42.1797C112.758 42.1797 112.984 42.0547 113.148 41.7969L116.82 36.0156C116.914 35.8516 117.016 35.6719 117.016 35.5C117.016 35.1328 116.695 34.8984 116.352 34.8984C116.148 34.8984 115.945 35.0234 115.797 35.2578L112.461 40.6172L110.875 38.5703C110.68 38.3125 110.508 38.2422 110.281 38.2422C109.93 38.2422 109.656 38.5312 109.656 38.8828C109.656 39.0625 109.727 39.2344 109.844 39.3906L111.805 41.7969C112.008 42.0703 112.227 42.1797 112.492 42.1797Z"
          fill="#924FE8"
        />
        <path
          d="M113.367 70.3987C108.977 70.3987 105.398 66.8206 105.398 62.4299C105.398 58.0315 108.977 54.4612 113.367 54.4612C117.766 54.4612 121.336 58.0315 121.336 62.4299C121.336 66.8206 117.766 70.3987 113.367 70.3987ZM110.742 65.7112C110.922 65.7112 111.086 65.6487 111.211 65.5237L113.367 63.3518L115.531 65.5237C115.648 65.6487 115.812 65.7112 115.992 65.7112C116.359 65.7112 116.648 65.4221 116.648 65.0627C116.648 64.8831 116.586 64.719 116.453 64.6018L114.289 62.4377L116.461 60.2659C116.602 60.1252 116.656 59.9846 116.656 59.8049C116.656 59.4456 116.367 59.1643 116.008 59.1643C115.836 59.1643 115.695 59.219 115.562 59.3518L113.367 61.5315L111.188 59.3596C111.062 59.2424 110.922 59.1799 110.742 59.1799C110.383 59.1799 110.102 59.4534 110.102 59.8206C110.102 59.9924 110.164 60.1487 110.289 60.2737L112.453 62.4377L110.289 64.6096C110.164 64.7268 110.102 64.8909 110.102 65.0627C110.102 65.4221 110.383 65.7112 110.742 65.7112Z"
          fill="#0A0F29"
          fillOpacity="0.25"
        />
        <path
          d="M113.367 93.6928C108.977 93.6928 105.398 90.1147 105.398 85.7241C105.398 81.3257 108.977 77.7553 113.367 77.7553C117.766 77.7553 121.336 81.3257 121.336 85.7241C121.336 90.1147 117.766 93.6928 113.367 93.6928ZM112.492 89.5444C112.758 89.5444 112.984 89.4194 113.148 89.1616L116.82 83.3803C116.914 83.2163 117.016 83.0366 117.016 82.8647C117.016 82.4975 116.695 82.2632 116.352 82.2632C116.148 82.2632 115.945 82.3882 115.797 82.6225L112.461 87.9819L110.875 85.935C110.68 85.6772 110.508 85.6069 110.281 85.6069C109.93 85.6069 109.656 85.896 109.656 86.2475C109.656 86.4272 109.727 86.5991 109.844 86.7553L111.805 89.1616C112.008 89.435 112.227 89.5444 112.492 89.5444Z"
          fill="#924FE8"
        />
        <rect
          x="125.764"
          y="32.8823"
          width="168.236"
          height="10.8706"
          rx="5.43529"
          fill="#0A0F29"
          fillOpacity="0.08"
        />
        <rect
          x="125.65"
          y="56.9531"
          width="134.05"
          height="10.8706"
          rx="5.43529"
          fill="#0A0F29"
          fillOpacity="0.08"
        />
        <rect
          x="125.65"
          y="80.2471"
          width="151.534"
          height="10.8706"
          rx="5.43529"
          fill="#0A0F29"
          fillOpacity="0.08"
        />
      </g>
      <defs>
        <filter
          id="filter0_dd_304_3783"
          x="0"
          y="0"
          width="331"
          height="142"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_304_3783"
          />
          <feOffset dy="3" />
          <feGaussianBlur stdDeviation="5" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0784314 0 0 0 0 0.0823529 0 0 0 0 0.101961 0 0 0 0.02 0"
          />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_304_3783" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="3"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_304_3783"
          />
          <feOffset dy="10" />
          <feGaussianBlur stdDeviation="8" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0784314 0 0 0 0 0.0823529 0 0 0 0 0.101961 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_304_3783"
            result="effect2_dropShadow_304_3783"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_304_3783"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export default AvatarSkeleton;
