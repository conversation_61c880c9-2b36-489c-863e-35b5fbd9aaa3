const FileCSV = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="41" height="40" viewBox="0 0 41 40" fill="none">
      <path
        d="M35.5 13.333V34.9883C35.5 35.9165 34.7587 36.6663 33.8443 36.6663H7.15567C6.24158 36.6663 5.5 35.9263 5.5 35.0133V4.98601C5.5 4.09186 6.24783 3.33301 7.17035 3.33301H25.4947L35.5 13.333ZM32.1667 14.9997H23.8333V6.66634H8.83333V33.333H32.1667V14.9997Z"
        fill="#27923A"
      />
      <path
        d="M22.3569 17.975V19.6513H18.3029C18.0994 19.6513 17.9344 19.8747 17.9344 20.117C17.9344 20.3324 18.0647 20.5634 18.2367 20.6005L18.3029 20.6076H21.2513C22.269 20.6076 23.094 21.5899 23.094 22.8015C23.094 24.0131 22.269 24.9953 21.2513 24.9953H17.1973V23.3428H21.2513C21.4548 23.3428 21.6263 23.0806 21.6263 22.8383C21.6263 22.6229 21.4894 22.4069 21.3175 22.3698L21.2513 22.3627H18.3029C17.2852 22.3627 16.4602 21.3805 16.4602 20.1689C16.4602 18.9572 17.2852 17.975 18.3029 17.975H22.3569Z"
        fill="#27923A"
      />
      <path
        d="M28.644 17.975L27.0036 22.4821L25.3631 17.975H23.49L26.122 24.9953H27.8851L30.5172 17.975H28.644Z"
        fill="#27923A"
      />
      <path
        d="M12.0174 21.4852C12.0174 22.5489 12.4736 23.5913 13.7374 23.5913C14.1693 23.5913 14.5647 23.4353 14.8704 23.1766L16.0645 23.9727C15.4871 24.6013 14.6582 24.9953 13.7374 24.9953C11.722 24.9953 10.5975 23.315 10.5975 21.4852C10.5975 19.6553 11.7221 17.975 13.7374 17.975C14.6582 17.975 15.4871 18.3691 16.0645 18.9977L14.8705 19.7938C14.5647 19.5351 14.1693 19.3791 13.7374 19.3791C12.4736 19.3791 12.0174 20.4214 12.0174 21.4852Z"
        fill="#27923A"
      />
    </svg>
  );
};

export default FileCSV;
