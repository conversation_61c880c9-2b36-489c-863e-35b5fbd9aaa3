type Props = {
  type: "increase" | "decrease";
};

const TrendingIcon: React.FC<Props> = ({ type }) => {
  const map = {
    increase: (
      <div className="h-6 w-6 rounded-full flex justify-center items-center bg-[#22C55E29]">
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M14 4.66667C14.0063 4.62021 14.0063 4.57312 14 4.52667C13.9942 4.48753 13.9829 4.44938 13.9666 4.41333C13.949 4.38075 13.929 4.34955 13.9066 4.32C13.8813 4.27784 13.8498 4.23965 13.8133 4.20667L13.7333 4.16C13.6948 4.1313 13.6521 4.10881 13.6066 4.09333H13.4733C13.4327 4.054 13.3852 4.02237 13.3333 4H9.99996C9.63177 4 9.33329 4.29848 9.33329 4.66667C9.33329 5.03486 9.63177 5.33333 9.99996 5.33333H11.8866L9.21995 8.47333L6.33995 6.76C6.05815 6.59239 5.6963 6.65458 5.48662 6.90667L2.15329 10.9067C2.03992 11.0427 1.98534 11.2183 2.0016 11.3946C2.01786 11.571 2.10362 11.7336 2.23995 11.8467C2.3599 11.9461 2.51085 12.0003 2.66662 12C2.86491 12.0003 3.05305 11.9124 3.17995 11.76L6.14662 8.2L8.99329 9.90667C9.27202 10.072 9.62928 10.0129 9.83996 9.76667L12.6666 6.46667V8C12.6666 8.36819 12.9651 8.66667 13.3333 8.66667C13.7015 8.66667 14 8.36819 14 8V4.66667Z"
            fill="#118D57"
          />
        </svg>
      </div>
    ),
    decrease: (
      <div className="h-6 bg-[#FF563029] w-6 rounded-full flex justify-center items-center">
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M14.0002 7.99987C14.0002 7.63168 13.7018 7.3332 13.3336 7.3332C12.9654 7.3332 12.6669 7.63168 12.6669 7.99987V9.5332L9.84023 6.19987C9.62955 5.95362 9.2723 5.89454 8.99357 6.05987L6.1469 7.79987L3.18023 4.23987C3.0278 4.05647 2.7891 3.96833 2.55406 4.00864C2.31902 4.04896 2.12334 4.2116 2.04073 4.43531C1.95812 4.65902 2.00113 4.90981 2.15357 5.0932L5.4869 9.0932C5.69657 9.34529 6.05842 9.40748 6.34023 9.23987L9.19356 7.52654L11.8602 10.6665H10.0002C9.63204 10.6665 9.33356 10.965 9.33356 11.3332C9.33356 11.7014 9.63204 11.9999 10.0002 11.9999H13.3336C13.4156 11.9979 13.4967 11.9821 13.5736 11.9532L13.6669 11.8999C13.7017 11.8824 13.7351 11.8623 13.7669 11.8399C13.8034 11.8069 13.8349 11.7687 13.8602 11.7265C13.8826 11.697 13.9026 11.6658 13.9202 11.6332C13.9365 11.5972 13.9478 11.559 13.9536 11.5199C13.9806 11.4611 13.9964 11.3978 14.0002 11.3332V7.99987Z"
            fill="#B71D18"
          />
        </svg>
      </div>
    ),
  };

  return map[type];
};

export default TrendingIcon;
