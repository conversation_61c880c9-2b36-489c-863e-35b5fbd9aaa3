type Props = {
  width?: string;
  height?: string;
  color?: string;
};

const WarningIcon = ({ width, height, color }: Props) => {
  return (
    <svg
      width={width || "20"}
      height={height || "20"}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.0001 18.3333C5.39758 18.3333 1.66675 14.6025 1.66675 9.99996C1.66675 5.39746 5.39758 1.66663 10.0001 1.66663C14.6026 1.66663 18.3334 5.39746 18.3334 9.99996C18.3334 14.6025 14.6026 18.3333 10.0001 18.3333ZM10.0001 16.6666C11.7682 16.6666 13.4639 15.9642 14.7141 14.714C15.9644 13.4638 16.6667 11.7681 16.6667 9.99996C16.6667 8.23185 15.9644 6.53616 14.7141 5.28591C13.4639 4.03567 11.7682 3.33329 10.0001 3.33329C8.23197 3.33329 6.53628 4.03567 5.28604 5.28591C4.03579 6.53616 3.33341 8.23185 3.33341 9.99996C3.33341 11.7681 4.03579 13.4638 5.28604 14.714C6.53628 15.9642 8.23197 16.6666 10.0001 16.6666ZM9.16675 5.83329H10.8334V7.49996H9.16675V5.83329ZM9.16675 9.16663H10.8334V14.1666H9.16675V9.16663Z"
        fill={color || "#0F1324"}
        fillOpacity="0.6"
      />
    </svg>
  );
};

export default WarningIcon;
