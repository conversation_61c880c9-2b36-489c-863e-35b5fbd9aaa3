type Props = {
  width?: string;
  height?: string;
};

export const GoogleIcon = (props: Props) => {
  return (
    <svg
      width={props.width || "17px"}
      height={props.height || "16px"}
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_37_108647)">
        <path
          d="M16.3442 8.18429C16.3442 7.64047 16.3001 7.09371 16.206 6.55872H8.66016V9.63937H12.9813C12.802 10.6329 12.2258 11.5119 11.3822 12.0704V14.0693H13.9602C15.4741 12.6759 16.3442 10.6182 16.3442 8.18429Z"
          fill="#4285F4"
        />
        <path
          d="M8.65999 16.0006C10.8176 16.0006 12.6372 15.2922 13.9629 14.0693L11.385 12.0704C10.6677 12.5584 9.74174 12.8347 8.66293 12.8347C6.57584 12.8347 4.80623 11.4266 4.17128 9.53357H1.51099V11.5942C2.86906 14.2956 5.63518 16.0006 8.65999 16.0006V16.0006Z"
          fill="#34A853"
        />
        <path
          d="M4.16852 9.53356C3.83341 8.53999 3.83341 7.46411 4.16852 6.47054V4.40991H1.51116C0.376489 6.67043 0.376489 9.33367 1.51116 11.5942L4.16852 9.53356V9.53356Z"
          fill="#FBBC04"
        />
        <path
          d="M8.65999 3.16644C9.80053 3.1488 10.9029 3.57798 11.7289 4.36578L14.0129 2.08174C12.5667 0.72367 10.6471 -0.0229773 8.65999 0.000539111C5.63518 0.000539111 2.86906 1.70548 1.51099 4.40987L4.16834 6.4705C4.80035 4.57449 6.5729 3.16644 8.65999 3.16644V3.16644Z"
          fill="#EA4335"
        />
      </g>
      <defs>
        <clipPath id="clip0_37_108647">
          <rect width="16" height="16" fill="white" transform="translate(0.5)" />
        </clipPath>
      </defs>
    </svg>
  );
};
