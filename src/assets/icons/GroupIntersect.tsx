const GroupIntersect = () => {
  return (
    <svg
      width="25"
      height="100%"
      viewBox="0 0 25 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.7141 14.9659C14.9617 13.5824 16.4602 11.0995 16.4602 8.26666C16.4602 5.43377 14.9617 2.95088 12.7141 1.56738C10.4666 2.95088 8.96808 5.43377 8.96808 8.26666C8.96808 11.0995 10.4666 13.5824 12.7141 14.9659Z"
        fill="#8F5CFF"
      />
      <circle cx="8.60501" cy="8.27419" r="7.12673" stroke="#5A18BF" strokeWidth="1.5" />
      <circle cx="16.8515" cy="8.27419" r="7.12673" stroke="#5A18BF" strokeWidth="1.5" />
    </svg>
  );
};

export default GroupIntersect;
