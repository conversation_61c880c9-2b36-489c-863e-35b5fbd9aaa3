const Star = ({ filled }: { filled: number }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
    <path
      d="M10.892 3.26401C11.3019 2.27846 12.6981 2.27846 13.108 3.26401L14.8926 7.55465C15.0654 7.97014 15.4561 8.25402 15.9047 8.28998L20.5368 8.66133C21.6007 8.74663 22.0322 10.0744 21.2215 10.7688L17.6924 13.792C17.3506 14.0847 17.2014 14.544 17.3058 14.9817L18.384 19.5019C18.6317 20.5402 17.5022 21.3608 16.5912 20.8044L12.6255 18.3822C12.2415 18.1476 11.7585 18.1476 11.3745 18.3822L7.40876 20.8044C6.49784 21.3608 5.36834 20.5402 5.61601 19.5019L6.69423 14.9817C6.79864 14.544 6.64939 14.0847 6.30765 13.792L2.77847 10.7688C1.96782 10.0744 2.39926 8.74663 3.46324 8.66133L8.09535 8.28998C8.5439 8.25402 8.93463 7.97014 9.10744 7.55465L10.892 3.26401Z"
      fill="#ccc"
    />
    <path
      d="M10.892 3.26401C11.3019 2.27846 12.6981 2.27846 13.108 3.26401L14.8926 7.55465C15.0654 7.97014 15.4561 8.25402 15.9047 8.28998L20.5368 8.66133C21.6007 8.74663 22.0322 10.0744 21.2215 10.7688L17.6924 13.792C17.3506 14.0847 17.2014 14.544 17.3058 14.9817L18.384 19.5019C18.6317 20.5402 17.5022 21.3608 16.5912 20.8044L12.6255 18.3822C12.2415 18.1476 11.7585 18.1476 11.3745 18.3822L7.40876 20.8044C6.49784 21.3608 5.36834 20.5402 5.61601 19.5019L6.69423 14.9817C6.79864 14.544 6.64939 14.0847 6.30765 13.792L2.77847 10.7688C1.96782 10.0744 2.39926 8.74663 3.46324 8.66133L8.09535 8.28998C8.5439 8.25402 8.93463 7.97014 9.10744 7.55465L10.892 3.26401Z"
      fill="#ffc107"
      style={{ clipPath: `inset(0 ${100 - filled}% 0 0)` }}
    />
  </svg>
);

const StarRating = ({ rating }: { rating: number }) => {
  const fullStars = Math.floor(rating);
  const partialStar = rating - fullStars;
  const stars = Array.from({ length: 5 }, (_, i) => (
    <Star key={i} filled={i < fullStars ? 100 : i === fullStars ? partialStar * 100 : 0} />
  ));

  return <div style={{ display: "flex" }}>{stars}</div>;
};

export default StarRating;
