interface Props {
  width?: number;
  height?: number;
  color?: string;
}

const LogoSvg = ({ width = 70, height = 18, color = "#000040" }: Props) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 70 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M49.595 7.05687C49.595 2.99331 51.5569 0.5 54.7306 0.5C57.9043 0.5 59.8589 2.98436 59.8589 7.04792C59.8589 11.1115 57.9221 13.6495 54.7306 13.6495C51.5392 13.6495 49.595 11.1279 49.595 7.05985V7.05687ZM57.1823 7.05687C57.1823 4.18777 56.2753 2.61007 54.7321 2.61007C53.1889 2.61007 52.273 4.19821 52.273 7.05687C52.273 9.91553 53.18 11.538 54.7321 11.538C56.2842 11.538 57.1823 9.94088 57.1823 7.05687ZM38.8162 7.17915C38.8162 8.64502 39.0766 9.89316 39.556 10.8684L39.5589 10.8669H39.5619C40.4866 12.7145 42.0742 13.648 44.0717 13.648C46.8799 13.648 48.9188 11.7915 48.9188 9.18484C48.9188 6.82275 47.2542 5.08698 44.8203 5.08698C43.1203 5.08698 41.8819 5.94144 41.3936 7.10907H41.3418V6.97933C41.3581 4.29515 42.2754 2.58472 44.0702 2.58472C44.9357 2.58472 45.5453 2.90831 46.1209 3.63155C46.5026 4.07743 46.8178 4.30409 47.3401 4.30409C48.0266 4.30409 48.4379 3.85076 48.4379 3.26621C48.4379 3.03209 48.3743 2.80542 48.253 2.56086C47.6597 1.3828 46.1594 0.501495 44.0746 0.501495C40.8047 0.501495 38.8162 3.01121 38.8162 7.17915ZM44.0539 7.0345C45.3811 7.0345 46.3487 7.97546 46.3487 9.29966V9.30264C46.3399 10.5657 45.3204 11.5603 44.0287 11.5603C42.7371 11.5603 41.725 10.5299 41.725 9.24896C41.725 7.96801 42.7267 7.0345 44.0539 7.0345ZM62.1685 6.2024C62.0857 6.45143 62.0516 6.60354 62.0516 6.73476C62.0516 7.0673 62.3091 7.28651 62.6568 7.28651C63.0045 7.28651 63.1983 7.10607 63.3344 6.64678L63.7029 5.51346H66.0243L66.3972 6.64678C66.5274 7.1016 66.7316 7.28651 67.0852 7.28651C67.4581 7.28651 67.7051 7.06283 67.7051 6.7109C67.7051 6.57371 67.6711 6.41266 67.6031 6.2024L65.937 1.38727C65.7299 0.762449 65.4428 0.508942 64.891 0.508942C64.3391 0.508942 64.0432 0.766922 63.8345 1.38727L62.1685 6.2024ZM64.8998 1.83612L65.7388 4.53224H65.7373H63.9929L64.8362 1.83612H64.8998ZM68.2394 1.22623V6.56328C68.2394 7.01214 68.485 7.27608 68.8978 7.27608C69.3106 7.27608 69.5473 7.01214 69.5473 6.56328V1.22623C69.5473 0.777372 69.3106 0.513428 68.8934 0.513428C68.4761 0.513428 68.2394 0.777372 68.2394 1.22623ZM28.7048 11.3725C28.5657 11.0861 28.5035 10.8058 28.5035 10.5448C28.5035 9.86483 28.94 9.43834 29.6635 9.43834C30.1858 9.43834 30.5173 9.64861 30.7806 10.1273C31.226 11.0086 31.9066 11.5305 33.2145 11.5305C34.5225 11.5305 35.3955 10.7819 35.3955 9.7351C35.4043 8.51528 34.5047 7.83529 32.9793 7.83529H32.4289C31.7941 7.83529 31.4242 7.461 31.4242 6.89433C31.4242 6.32767 31.7897 5.96083 32.4289 5.96083H32.9423C34.2577 5.96083 35.1143 5.24654 35.1143 4.2176C35.1143 3.18867 34.4337 2.53551 33.1598 2.53551C32.096 2.53551 31.4494 2.98139 31.041 3.86866C30.754 4.46067 30.4477 4.67988 29.8559 4.67988C29.1235 4.67988 28.7492 4.2519 28.7492 3.61664C28.7492 3.3184 28.8113 3.06639 28.9415 2.77113C29.5008 1.54087 30.9301 0.513428 33.1539 0.513428C35.8142 0.513428 37.6104 1.82868 37.6104 3.8448C37.6104 5.44189 36.4667 6.48723 34.9871 6.79144V6.84214C36.8617 7.01512 38.0735 8.11564 38.0735 9.85141C38.0735 12.1374 36.0938 13.654 33.1716 13.654C30.7717 13.654 29.2996 12.6161 28.7077 11.3799L28.7048 11.371V11.3725ZM15.2657 2.99035C15.8353 2.4207 15.8353 1.49764 15.2657 0.927996C14.696 0.358352 13.7728 0.358352 13.2016 0.927996C12.632 1.49764 12.632 2.4207 13.2016 2.99035C13.7713 3.55999 14.6946 3.55999 15.2657 2.99035ZM0.907729 12.2181V1.93317C0.907729 1.03247 1.41819 0.501601 2.28375 0.501601H6.63374C9.10465 0.501601 10.6553 1.76764 10.6553 3.78376C10.6553 5.22428 9.57811 6.42769 8.18434 6.63646V6.71102C9.97316 6.84672 11.2826 8.15153 11.2826 9.89178C11.2826 12.2076 9.5426 13.6571 6.73287 13.6571H2.28227C1.41765 13.6571 0.907361 13.1304 0.90625 12.2196L0.907729 12.2181ZM5.64093 5.97436C7.10868 5.97436 7.94761 5.32866 7.94761 4.21622C7.94761 3.10377 7.20929 2.55649 5.92205 2.55649H3.66272V5.97436H5.64093ZM6.03302 11.6082C7.61914 11.6082 8.4773 10.9326 8.4773 9.67555C8.4773 8.41846 7.59399 7.79066 5.96792 7.79066H3.66124V11.6097H6.03302V11.6082ZM17.4407 14.9693C17.4407 15.2141 17.4963 15.4589 17.6398 15.7038L17.6389 15.7029C18.2678 16.7349 19.7562 17.4909 21.7522 17.4999C24.8978 17.5178 26.7399 15.9878 26.7399 13.4438V5.2048C26.7399 4.30261 26.1821 3.85525 25.4171 3.85525C24.6522 3.85525 24.0766 4.3041 24.0766 5.2048V5.74163H24.0234C23.5144 4.63813 22.4491 3.89998 21.0435 3.89998C18.5252 3.89998 17.0308 5.79532 17.0308 8.77774C17.0308 11.7602 18.5652 13.6197 20.9991 13.6197C22.4136 13.6197 23.5514 12.9203 24.0086 11.8705H24.0618V13.4303C24.0618 14.6516 23.2052 15.4524 21.7463 15.4435C20.863 15.4345 20.169 15.0602 19.4485 14.3758C19.1466 14.1044 18.8729 13.994 18.5267 13.994C17.8594 13.994 17.4407 14.4116 17.4407 14.9693ZM24.0885 8.81652C24.0885 7.05837 23.2318 5.92654 21.9002 5.92654C20.5685 5.92654 19.7562 7.03899 19.7562 8.80757C19.7562 10.5761 20.5774 11.6424 21.9002 11.6424C23.2229 11.6424 24.0885 10.5836 24.0885 8.81652ZM12.7637 7.27908V13.6555C14.3853 13.6555 15.6992 12.3596 15.6992 10.7581V4.38165C14.0776 4.38165 12.7637 5.67751 12.7637 7.27908Z"
        fill={color}
        fillOpacity="0.6"
      />
    </svg>
  );
};

export default LogoSvg;
