import { cn } from "utils/utils";

const BuyAudienceIcon = ({ isLimitFilter }: { isLimitFilter?: boolean }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M7.99967 14.6666C4.31767 14.6666 1.33301 11.682 1.33301 7.99998C1.33301 4.31798 4.31767 1.33331 7.99967 1.33331C11.6817 1.33331 14.6663 4.31798 14.6663 7.99998C14.6663 11.682 11.6817 14.6666 7.99967 14.6666ZM7.99967 13.3333C9.41416 13.3333 10.7707 12.7714 11.7709 11.7712C12.7711 10.771 13.333 9.41447 13.333 7.99998C13.333 6.58549 12.7711 5.22894 11.7709 4.22874C10.7707 3.22855 9.41416 2.66665 7.99967 2.66665C6.58519 2.66665 5.22863 3.22855 4.22844 4.22874C3.22824 5.22894 2.66634 6.58549 2.66634 7.99998C2.66634 9.41447 3.22824 10.771 4.22844 11.7712C5.22863 12.7714 6.58519 13.3333 7.99967 13.3333ZM7.99967 4.69998L11.2997 7.99998L7.99967 11.3L4.69967 7.99998L7.99967 4.69998ZM7.99967 6.58598L6.58567 7.99998L7.99967 9.41398L9.41368 7.99998L7.99967 6.58598Z"
      fill={cn(isLimitFilter ? "gray" : "white")}
    />
  </svg>
);

export default BuyAudienceIcon;
