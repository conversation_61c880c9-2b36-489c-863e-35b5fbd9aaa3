const AttachmentIcon = ({ className, color }: { className?: string; color?: string }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M9.99341 11.6667H11.0001C13.0134 11.6667 14.6667 10.02 14.6667 8.00001C14.6667 5.98668 13.0201 4.33334 11.0001 4.33334H9.99341"
        stroke={color ? color : "#7D7D7D"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.99992 4.33334H4.99992C2.97992 4.33334 1.33325 5.98001 1.33325 8.00001C1.33325 10.0133 2.97992 11.6667 4.99992 11.6667H5.99992"
        stroke={color ? color : "#7D7D7D"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.33325 8H10.6666"
        stroke={color ? color : "#7D7D7D"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default AttachmentIcon;
