const ZaloIcon = () => {
  return (
    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.64978 5.2002H10.206C10.203 6.28357 10.203 7.36738 10.206 8.45075C10.0246 8.42532 9.72276 8.54129 9.65453 8.3003C9.64331 7.26736 9.65367 6.23356 9.64978 5.2002Z" fill="#20232C"/>
      <path d="M3.98228 5.23865C4.84423 5.23779 5.70532 5.23219 6.56684 5.23822C6.5608 5.40679 6.5513 5.59044 6.43815 5.72667C5.84912 6.45869 5.27262 7.2002 4.68402 7.93222C5.30976 7.9361 5.93549 7.93351 6.56123 7.93351C6.54957 8.07966 6.60096 8.24995 6.50595 8.37669C6.44636 8.4586 6.3384 8.45127 6.24901 8.45214C5.46608 8.44783 4.68316 8.45602 3.90067 8.44783C3.90282 8.28271 3.90455 8.09992 4.02029 7.96843C4.6024 7.23943 5.19143 6.5156 5.77096 5.78487C5.17546 5.78573 4.57952 5.78099 3.98401 5.78746C3.98012 5.60467 3.98142 5.42144 3.98228 5.23865Z" fill="#20232C"/>
      <path d="M11.5848 5.95592C12.205 5.81754 12.8778 6.22407 13.0475 6.83366C13.2604 7.46782 12.8717 8.22183 12.2296 8.41496C11.6842 8.60681 11.0312 8.35633 10.7484 7.85452C10.5264 7.48377 10.513 6.99834 10.7147 6.61681C10.8857 6.28012 11.2161 6.03395 11.5848 5.95592ZM11.5745 6.50515C11.1962 6.63837 10.9924 7.09793 11.1401 7.46911C11.2623 7.82003 11.6738 8.03473 12.0314 7.92695C12.4395 7.82952 12.686 7.35228 12.5414 6.96127C12.4205 6.57413 11.9519 6.35082 11.5745 6.50515Z" fill="#20232C"/>
      <path d="M6.99096 6.40745C7.28936 6.03843 7.81188 5.8565 8.27222 5.98799C8.42423 6.02635 8.56199 6.10223 8.695 6.18285C8.6937 6.14232 8.69068 6.06171 8.68938 6.02118C8.86341 6.02032 9.03701 6.02075 9.21104 6.01989C9.21018 6.83037 9.20932 7.64085 9.21148 8.45177C9.08365 8.44832 8.95367 8.46341 8.828 8.43754C8.73991 8.40176 8.7127 8.30562 8.67772 8.22716C8.19752 8.59964 7.44309 8.51514 7.04407 8.06162C6.61871 7.62232 6.59669 6.87391 6.99096 6.40745ZM7.69399 6.51609C7.29411 6.65663 7.10021 7.17095 7.30275 7.5417C7.47548 7.90297 7.96173 8.05472 8.30936 7.85555C8.63195 7.68655 8.78093 7.26708 8.64231 6.93168C8.50801 6.56696 8.05501 6.36262 7.69399 6.51609Z" fill="#20232C"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M4.69397 0.836861C4.2319 0.826553 3.76684 0.826824 3.30952 0.898981C2.65874 0.989313 2.03988 1.30941 1.60247 1.80122C1.05059 2.382 0.842854 3.20095 0.833344 3.98383V11.011C0.851074 11.5183 0.917579 12.0361 1.13992 12.4992C1.50702 13.3002 2.27567 13.8995 3.14153 14.0671C3.82947 14.1968 4.53379 14.1645 5.22989 14.1542C5.47179 14.1667 5.71451 14.1648 5.9573 14.1629C6.11198 14.1615 6.26673 14.1601 6.42134 14.1626C7.8028 14.162 9.18427 14.1626 10.5657 14.162C11.0148 14.1677 11.467 14.1474 11.9079 14.0565C12.6938 13.8891 13.3925 13.3667 13.7777 12.6633C14.109 12.0635 14.1819 11.3604 14.1642 10.6866L14.1301 10.7237C13.5147 11.4109 12.6873 11.8761 11.8223 12.1769C10.6568 12.5735 9.40837 12.7013 8.18322 12.608C6.95814 12.5027 5.72348 12.1834 4.68446 11.5074C4.15331 11.7432 3.56733 11.8587 2.98563 11.811L2.92388 11.7464C3.23996 11.4092 3.47358 10.9649 3.45503 10.4932C3.45245 10.2734 3.28704 10.1134 3.18772 9.93245C2.59441 8.92062 2.31453 7.7501 2.25495 6.5861C2.1949 5.15652 2.45963 3.68164 3.18602 2.43462C3.54831 1.81152 4.04971 1.26872 4.65423 0.873482L4.69397 0.836861Z" fill="#20232C"/>
    </svg>
  );
};

export default ZaloIcon;
