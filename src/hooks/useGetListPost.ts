import { useEffect, useState } from "react";
import useInfiniteScroll from "./useLoadInfinity";
import { adsPostAPI } from "apis/adsPost";
import { pageAPI } from "apis/pagePost";
import { liveCommentAPI } from "apis/livePost";

interface Props {
  page_id?: string;
  type: "ads" | "live" | "your-pages";
  paramsNoPage: any
}
const useGetListPost = ({ page_id, type, paramsNoPage }: Props) => {
  const [page, setPage] = useState<number>(1);
  const [data, setData] = useState<{
    posts: any[];
    total: number;
    loading: boolean;
  }>({
    posts: [],
    total: 0,
    loading: false,
  });

  useEffect(() => {
    setPage(1);
    setData((prev) => ({ ...prev, posts: [] }));
    fetchData(1);
  }, [paramsNoPage]);

  const { lastElementRef } = useInfiniteScroll({
    fetchNextPage: async () => {
      await fetchData(page + 1).then(()=>setPage((prev) => prev + 1));
    },
    hasNextPage: data.posts.length < data.total,
    isFetching: data.loading,
    autoScrollAfterFetch: true, // Bật auto-scroll cho window scroll
  });

  const fetchData = async (pageToFetch: number) => {
    const _params =
      type == "ads"
        ? {
            ...paramsNoPage,
            actor_id: page_id,
            page: pageToFetch,
            limit: 9,
          }
        : {
            ...paramsNoPage,
            actor_id: page_id,
            page: pageToFetch,
            limit: 6,
          };
    setData((prev) => ({ ...prev, loading: true }));
    try {
      let response: any = {};
      switch (type) {
        case "your-pages":
          response = await pageAPI.getFollowedPost({
            id: page_id || "",
            params: { ...paramsNoPage, page: pageToFetch, limit: 6 },
          });
          break;
        case "live":
          response = await liveCommentAPI.getListPost({ params: _params });
          break;
        default:
          response = await adsPostAPI.getListPost({ params: _params });
          break;
      }

      if (response && response.data) {
        setData((prev) => ({
          ...prev,
          total: response.data.count,
          posts: [...prev.posts, ...response.data.items],
        }));
      }
    } catch (_error) {}
    setData((prev) => ({ ...prev, loading: false }));
  };
  return { data, lastElementRef };
};

export default useGetListPost;
