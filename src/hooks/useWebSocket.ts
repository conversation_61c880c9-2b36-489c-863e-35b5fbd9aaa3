import { useCallback, useEffect, useRef, useState } from 'react';
import { useAppDispatch } from '../store';
import { setTimeReadNoti } from '../store/redux/notification/slice';
import { getTokens } from '../utils/localStorage';

const useWebSocket = (clientId: string) => {
  const [isConnected, setIsConnected] = useState(false);
  const { accessToken } = getTokens();
  const [message, setMessage] = useState<{message: string; status: string} | null>(null);
  const socketRef = useRef<WebSocket | null>(null);
  const channel = useRef(new BroadcastChannel('big360-web-socket-channel'));
  const channelClosed = useRef(false);
  const dispatch = useAppDispatch();

  const WS_URL = `wss://${import.meta.env.REACT_APP_API_URL.replace(
    /^https?:\/\//,
    ''
  )}/ws/${clientId}/?token=${accessToken}`;

  const connectSocket = useCallback(() => {
    if (socketRef.current) {
      console.warn('Socket is already connected');
      return;
    }
    socketRef.current = new WebSocket(WS_URL);
    console.log('Connecting to WebSocket');

    socketRef.current.onopen = () => {
      console.log('WebSocket connected');
      console.log('WebSocket readyState (onopen)');
      setIsConnected(true);
      if (!channelClosed.current && channel.current) {
        try {
          channel.current.postMessage({ type: 'status', isConnected: true });
        } catch (error) {
          console.error('Error posting message to BroadcastChannel:', error);
        }
      }
    };

    socketRef.current.onmessage = (event) => {
      setMessage(event.data);
      if (!channelClosed.current && channel.current) {
        try {
          channel.current.postMessage({ type: 'message', data: event.data });
        } catch (error) {
          console.error('Error posting message to BroadcastChannel:', error);
        }
      }
    };

    socketRef.current.onclose = () => {
      setIsConnected(false);
      if (!channelClosed.current && channel.current) {
        try {
          channel.current.postMessage({ type: 'status', isConnected: false });
        } catch (error) {
          console.error('Error posting message to BroadcastChannel:', error);
        }
      }
      socketRef.current = null;
    };

    socketRef.current.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }, [WS_URL]);

  const disconnectSocket = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
    if (!channelClosed.current && channel.current) {
      try {
        channel.current.postMessage({ type: 'status', isConnected: false });
      } catch (error) {
        console.error('Error posting message to BroadcastChannel',);
      }
    }
  }, []);

  useEffect(() => {
    channel.current.onmessage = (event) => {
      if (event.data.type === 'message') {
        setMessage(event.data.data);
        const isRead = JSON.parse(event.data.data).data.type === 'read';
        if (isRead) {
          dispatch(setTimeReadNoti(Date.now()));
        }
      } else if (event.data.type === 'status') {
        setIsConnected(event.data.isConnected);
      }
    };

    return () => {
      if (channel.current && !channelClosed.current) {
        try {
          channel.current.close();
          channelClosed.current = true;
        } catch (error) {
          console.error('Error closing BroadcastChannel:', error);
        }
      }
    };
  }, []);

  return { isConnected, message, connectSocket, disconnectSocket, setMessage };
};

export default useWebSocket;
