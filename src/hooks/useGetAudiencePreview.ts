import { useEffect, useState } from "react";
import { socialAPI } from "apis/socialData";
import { TDataPreview } from "types/SocialData";
import { useAbortController } from './useAbortController';

interface Props {
  id: string;
  page: number | string;
  params?: { [key: string]: string };
}
const useGetAudiencePreview = ({ id, page, params }: Props) => {
  const { newAbortController } = useAbortController();
  const [preview, setPreview] = useState<TDataPreview>({
    data: [],
    total: 0,
    loading: false,
  });

  useEffect(() => {
    if (id !== "") {
      getPreview();
    }
  }, [params, id, page]);
  const getPreview = async () => {
    const controller = newAbortController();
    setPreview((prev) => ({ ...prev, loading: true }));
    const currentPage = isNaN(Number(page)) || Number(page) <= 0 ? 1 : Number(page) > 10 ? 10 : Number(page);
    const response = await socialAPI.get({
      endpoint: `audiences/${id}/preview/`,
      params: { ...params, page: currentPage.toString(), limit: "10" },
      headers: { isv2: true },
      signal: controller.signal
    });
    if (response && response.data) {
      setPreview((prev) => ({
        ...prev,
        data: response.data.items,
        total: response.data.count,
      }));
    }
    setPreview((prev) => ({ ...prev, loading: false }));
  };

  return preview;
};

export default useGetAudiencePreview;
