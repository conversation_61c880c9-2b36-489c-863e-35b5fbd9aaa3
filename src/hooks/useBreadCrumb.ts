import SidebarConfig from 'layout/SidebarConfig';
import { useLocation } from 'react-router-dom';
import { USER_PROFILE_PATH } from '../types/Router';

export const useBreadCrumb = (endRouter?: string) => {
  const sidebarConfig = SidebarConfig();
  const { pathname } = useLocation();

  const checkIsAccount = pathname.includes(USER_PROFILE_PATH.ROOT);

  const arr = pathname.split("/").filter((item) => item !== "");

  const breadcrumbPage = sidebarConfig
    .map((item, index) => {
      const check = item.items.find((item) => item.path.split("/").includes(arr[0]));
      if (check) return index;
    })
    .filter((item) => item !== undefined);
  const targetRouter = breadcrumbPage[0];
  const items = arr.map((item, index) => {
    const title = sidebarConfig[targetRouter ?? 0].items
      .map((sidebar) => {
        if (sidebar.path === `/${item}`) return sidebar.title;
      })
      .filter((item) => item !== undefined)[0];

    if (endRouter && index === arr.length - 1) {
      return {
        title: endRouter,
        pathname: "/" + arr.slice(0, index + 1).join("/"),
      };
    }
    const currentPath = getPathName(arr.slice(0, index + 1).join("/"));

    const handleConvertTitle = (item: string) => {
      switch (item) {
        case 'social-dataset':
          return 'social-audience';
        case 'persona-processing':
          return 'work-persona-processing';
        default:
          return item;
      }
    };

    const handleConvertPathName = (currentPath: string) => {
      switch (currentPath) {
        case 'your-audience/social-dataset':
          return 'your-audience/social-audience';
        default:
          return currentPath;
      }
    };
    const icon = sidebarConfig[targetRouter ?? 0].items;
    return {
      title: title ?? handleConvertTitle(item),
      pathname: handleConvertPathName(currentPath),
      icon: (icon[0] as any)?.icon,
    };
  });
  return {
    icon: checkIsAccount ? '' : sidebarConfig[targetRouter ?? 0].icon,
    items: items.filter((item) => item.title !== '' && item.title !== 'detail'),
    subHeader: checkIsAccount ? '' : sidebarConfig[targetRouter ?? 0].subheader
  };
};

const getPathName = (input: string): string => {
  const currentInput = input.split("/").filter((item) => item !== "");

  const sidebarConfig = SidebarConfig();
  const currentPath = sidebarConfig
    .map((item) => {
    return item.items.find((item) =>
        item.path.split("/").includes(currentInput[currentInput.length - 1])
      );
    })
    .filter((item) => item !== undefined);
  return currentPath.length > 0 && typeof currentPath[0] === "object" ? currentPath[0].path : input;
};
