import { useCallback, useEffect, useState } from "react";

const formatPhoneNumber = (phone: string) => {
  return phone.replace(/\d(?=\d{3})/g, "*");
};

export const useHiddenPhone = (numberPhone: string, rowIndex: number, current_page: number) => {
  const [hiddenPhone, setHiddenPhone] = useState(numberPhone);

  useEffect(() => {
    if ((!numberPhone.includes("*") && rowIndex > 4) || current_page > 5) {
      setHiddenPhone(formatPhoneNumber(numberPhone));
    }
  }, [numberPhone]);

  const handleSetHiddenPhone = useCallback((input: string) => {
    if (!input.includes("*")) {
      setHiddenPhone(formatPhoneNumber(input));
    } else {
      setHiddenPhone(numberPhone);
    }
  }, []);

  return { number: hiddenPhone, onHiddenNumber: handleSetHiddenPhone };
};
