import omit from "lodash/omit";
import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from "react-router-dom";
import searchParamSchema from "utils/persona/searchParamSchema";

/**
 * Hook lấy query params filter cho [Persona]
 * @returns
 * filterKey: key api
 * params: params call api
 * paramsNoPage: params omit page (dùng cho api Summarize)
 * searchSchema: list filter dạng Array ex: age__in: [1,2,3,4]
 */
const useFilters = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const params = useMemo(() => Object.fromEntries(searchParams.entries()), [searchParams]);

  const paramsNoPage = useMemo(() => omit(params, ["page", "order_by"]), [params]);

  const searchSchema = useMemo(() => omit(searchParamSchema(params), ["page"]), [params]);

  const [filterKey, setFilterKey] = useState(0);

  useEffect(() => {
    setFilterKey((prev) => prev + 1);
    const page = searchParams.get("page");
    if (isNaN(Number(page))) {
      setSearchParams({ ...Object.fromEntries(searchParams.entries()), page: '1' });
    }
  }, [params]);

  return {
    filterKey,
    params,
    paramsNoPage,
    searchSchema,
    searchParams,
    setSearchParams,
  };
};

export default useFilters;
