import { buildTreeProcess, treeProcessArray } from "utils/yourData";
import { useEffect, useState } from "react";

import { FlatTreeNode } from "types/YourData";
import httpInstance from "apis";

type Props = {
  id: string;
  endpoint?: string;
};

export const useProcessedSource = ({ id, endpoint }: Props) => {
  const [tree, setTree] = useState<FlatTreeNode[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      setLoading(true);
      try {
        const res = await httpInstance.get(endpoint || `/social-data/segments/${id}/tree/`);
        const result = res.data.data;
        const treeProcess = treeProcessArray(result);
        setTree(treeProcess);
        setLoading(false);
      } catch (error) {
        setTree([]);
        setLoading(false);
      }
    };
    fetchData();
  }, [id]);

  const buildTree = buildTreeProcess(tree);

  return {
    flatTree: tree,
    buildTree,
    loading,
  };
};
