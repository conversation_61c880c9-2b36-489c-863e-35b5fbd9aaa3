import { planAPI } from "apis/plansAndPayment";
import { useEffect, useState } from "react";

interface IFeature {
  code: string;
  name: string;
  description: string;
  is_enabled: boolean;
  usage_limit: null;
  is_can_exceed: boolean;
  excess_usage_credit: null;
  extra_limit: {};
}
interface IPlanFeature {
  TRIAL: IFeature[];
  BASIC: IFeature[];
  PRO: IFeature[];
}
const useGetPlanDetail = ({ code }: { code: string[] }) => {
  const [features, setFeatures] = useState<IPlanFeature>({ TRIAL: [], BASIC: [], PRO: [] });

  useEffect(() => {
    code.map((key) => getPlanDetail(key));
  }, []);

  const getPlanDetail = async (key: string) => {
    const response = await planAPI.getPlanDetail({ plan_code: key });
    response &&
      response.data &&
      setFeatures((prev) => ({ ...prev, [key]: response.data.features }));
  };
  return { features, getPlanDetail };
};

export default useGetPlanDetail;
