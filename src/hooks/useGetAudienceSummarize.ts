import { socialAPI } from "apis/socialData";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { TDataSummarize, TSocialDetail } from "types/SocialData";

const useGetAudienceSummarize = (id: string) => {
  const navigate = useNavigate();
  const [summarize, setSummarize] = useState<TDataSummarize<TSocialDetail>>({
    data: {} as any,
    loading: false,
  });

  useEffect(() => {
    if (id !== "") {
      getSummarize();
    }
  }, [id]);

  const getSummarize = async () => {
    try {
      setSummarize((prev) => ({ ...prev, loading: true }));
      const response: any = await socialAPI.getSummarizeDetail(id);

      if (response.status === 422 || response.status === 404) {
        navigate("/404");
        setSummarize((prev) => ({ ...prev, loading: false }));
        return;
      }

      if (response && response?.data?.data) {
        setSummarize((prev) => ({ ...prev, data: response.data.data, loading: false }));
      } else {
        setSummarize((prev) => ({ ...prev, data: {} as any, loading: false }));
      }
    } catch (error) {
      console.error("Error fetching audience summarize:", error);
      setSummarize((prev) => ({ ...prev, loading: false }));
    }
  };
  return { summarize: summarize.data, loading: summarize.loading };
};

export default useGetAudienceSummarize;
