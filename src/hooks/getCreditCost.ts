import { useEffect, useState } from "react";
import { creditAPI } from "apis/credit";
import { CREDIT_TYPE_ENUM } from "types/Transaction";

const getCreditCost = ({ params, creditType }: { params: any; creditType: CREDIT_TYPE_ENUM }) => {
  const [credit, setCredit] = useState({
    loading: false,
    cost: 0,
  });

  useEffect(() => {
    handleGetCost();
  }, []);

  const handleGetCost = async () => {
    setCredit((prev) => ({ ...prev, loading: true }));
    const response = await creditAPI.getCreditCost({
      credit_type: creditType,
      n: params,
    });
    if (response && response.data) {
      setCredit({ loading: false, cost: response.data.credit_cost });
    }
    setCredit((prev) => ({ ...prev, loading: false }));
  };
  return {
    credit,
  };
};

export default getCreditCost;
