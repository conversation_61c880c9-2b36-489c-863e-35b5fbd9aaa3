import { useEffect, useState } from 'react';
import { useAppDispatch } from 'store';

import { getTokens } from 'utils/localStorage';
import { refetchAfterReferral } from '../utils/refetchAfterReferral';

const getUserInfor = (skip = false) => {
  const dispatch = useAppDispatch();
  const { accessToken, refreshToken } = getTokens();

  const [isReady, setIsReady] = useState<boolean>(false);

  useEffect(() => {
    if (!skip) {
      fetchData();
    }
  }, [dispatch, accessToken, refreshToken, skip]);

  const fetchData = async () => {
    setIsReady(false);

    if (!refreshToken || !accessToken) {
      setIsReady(true);
      return;
    }

    try {
      await refetchAfterReferral(dispatch);
      // await getAllCity();
      setIsReady(true);
    } catch (error) {
      console.error('Error fetching data:', error);
      setIsReady(true);
    }
  };
  return { isReady };
};

export default getUserInfor;
