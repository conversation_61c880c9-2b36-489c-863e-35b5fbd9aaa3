import { useState } from "react";
// import isEqual from "lodash/isEqual";

// import usePrevious from "./usePrevious";

import { requestAudienceApi } from "apis/requestAudience";
import { IRequestData } from "types/RequestAudience";
import { pageAPI } from "apis/pagePost";

interface IData {
  data: IRequestData | undefined;
  loading: boolean;
}
type TypeAPI = "livePost" | "request";
const usePreviewFB = ({ type = "request" }: { type?: TypeAPI }) => {
  const [requestURL, setRequestURL] = useState<string>("");
  const [requestData, setRequestData] = useState<IData>({ data: undefined, loading: false });

  // const prevRequestURL = usePrevious(requestURL.trim());
  const rootAPI = type == "livePost" ? pageAPI : requestAudienceApi;
  const handleGetPreview = async () => {
    if (requestURL.trim() != "") {
      setRequestData((prev) => ({ ...prev, data: undefined, loading: true }));

      const res = await rootAPI.getFbPreview({ payload: { url: requestURL.trim() } });
      if (res && res.data) {
        setRequestData((prev) => ({ ...prev, data: res.data, loading: false }));
      } else {
        setRequestData((prev) => ({ ...prev, loading: false }));
      }
      return { data: res.data, url: requestURL.trim() };
    }
    return;
  };

  return { requestData, requestURL, setRequestURL, setRequestData, handleGetPreview };
};

export default usePreviewFB;
