import { useMemo } from "react";
import { useAppSelector } from "store";
import { FEATURE_PERMISSION_KEY } from "constants/permission/permissionPlan";

const useFeatures = (key_feature: FEATURE_PERMISSION_KEY) => {
  const { features } = useAppSelector((state) => state.features);

  /**
   * @features : active features
   * @key_feature : code feature (BE set)
   */
  const feature = useMemo(() => {
    return features?.find((feature) => feature.code === key_feature) || null;
  }, [features, key_feature]);

  return feature;
};

export default useFeatures;
