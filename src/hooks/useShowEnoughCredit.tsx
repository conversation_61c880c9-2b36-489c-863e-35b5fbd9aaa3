import InsufficientCreditModal from "components/Transaction/InsufficientCreditModal";
import { ModalContext } from "providers/Modal";
import { useContext } from "react";

const useShowEnoughCredit = () => {
  const modal = useContext(ModalContext);

  const handleShowEnoughCredit = async () => {
    modal?.setDataDialog((prev) => ({
      ...prev,
      title: "",
      content: <InsufficientCreditModal />,
      footer: "",
    }));
  };

  return { handleShowEnoughCredit };
};

export default useShowEnoughCredit;
