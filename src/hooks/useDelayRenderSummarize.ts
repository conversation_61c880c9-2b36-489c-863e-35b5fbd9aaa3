import { useEffect, useState } from "react";

const useDelayRenderSummarize = (paramsNoPage: { [key: string]: string }, isDisable?: boolean) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isRendered, setIsRendered] = useState(false);

  useEffect(() => {
    if (isDisable || Object.keys(paramsNoPage).length === 0) {
      setIsRendered(true);
      setTimeout(() => setIsVisible(true), 10);
    } else {
      setIsVisible(false); // Ẩn ChartSummarize
      //unmount
      setTimeout(() => setIsRendered(false), 500);
    }
  }, [isDisable, paramsNoPage]);

  return { isVisible, isRendered };
};

export default useDelayRenderSummarize;
