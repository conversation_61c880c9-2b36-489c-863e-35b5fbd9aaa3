import { socialAPI } from "apis/socialData";
import { useEffect, useState } from "react";

type Props = {
  type: "profile" | "group" | "page" | "post";
  uid: string[];
};

const useListAvatar = ({ type, uid }: Props) => {
  const [listAvatar, setListAvatar] = useState<string[]>([]);

  const getAvatar = async (uid: string) => {
    try {
      const res = await socialAPI.getAvatar(uid, type);
      return res;
    } catch (error) {
      return "";
    }
  };
  useEffect(() => {
    if (uid.length === 0) return;
    const fetchData = async () => {
      for (const node of uid) {
        const avatar = await getAvatar(node);
        setListAvatar((prev) => [...prev, avatar]);
      }
    };

    fetchData();
  }, [uid.length]);
  return { listAvatar };
};
export default useListAvatar;
