import { socialAPI } from "apis/socialData";

import { useEffect, useState } from "react";
import { AVATAR_TYPE } from "types";

type Props = {
  type: AVATAR_TYPE;
  uid: string;
};
interface IAvatar {
  loading: boolean;
  url: string;
}

const useAvatar = ({ type, uid }: Props) => {
  const [avatar, setAvatar] = useState<IAvatar>({
    loading: false,
    url: "",
  });

  useEffect(() => {
    if (uid) {
      fetchAvatar();
    }
  }, [uid]);

  const fetchAvatar = async () => {
    setAvatar({ url: "", loading: true });
    const res = await socialAPI.getAvatar(uid, type);
    if (res) {
      setAvatar({ url: res, loading: false });
    } else {
      setAvatar({ url: "", loading: false });
    }
  };

  return { avatar };
};
export default useAvatar;
