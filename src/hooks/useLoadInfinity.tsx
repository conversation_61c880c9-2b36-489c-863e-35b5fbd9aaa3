import { useCallback, useEffect, useRef, useState } from "react";
import { getAccessTokenFromLS } from '../utils/localStorage';

type Props = {
  fetchNextPage: () => void;
  hasNextPage: boolean;
  isFetching: boolean;
  scrollContainer?: HTMLElement | null;
  autoScrollAfterFetch?: boolean;
};

const useInfiniteScroll = ({
  fetchNextPage,
  hasNextPage,
  isFetching,
  scrollContainer,
  autoScrollAfterFetch = true
}: Props) => {
  const observer = useRef<IntersectionObserver>();
  const lastFetchTime = useRef<number>(0);
  const [previousScrollHeight, setPreviousScrollHeight] = useState<number>(0);
  const token = getAccessTokenFromLS();

  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);

  const autoScrollToNewContent = useCallback(() => {
    if (!autoScrollAfterFetch) return;

    const container = scrollContainer || document.documentElement;
    const currentScrollHeight = container.scrollHeight;

    if (previousScrollHeight > 0 && currentScrollHeight > previousScrollHeight) {
      const scrollDifference = currentScrollHeight - previousScrollHeight;
      const currentScrollTop = container.scrollTop || window.pageYOffset;

      // Scroll xuống một chút để hiển thị nội dung mới nhưng không đến cuối
      const newScrollPosition = currentScrollTop + Math.min(scrollDifference * 0.3, 100);

      if (scrollContainer) {
        scrollContainer.scrollTo({
          top: newScrollPosition,
          behavior: 'smooth'
        });
      } else {
        window.scrollTo({
          top: newScrollPosition,
          behavior: 'smooth'
        });
      }
    }

    setPreviousScrollHeight(currentScrollHeight);
  }, [scrollContainer, autoScrollAfterFetch, previousScrollHeight]);

  const handleFetch = useCallback(() => {
    const now = Date.now();

    if (now - lastFetchTime.current < (isIOS ? 800 : 500) || isFetching) {
      return;
    }

    lastFetchTime.current = now;

    if (autoScrollAfterFetch) {
      const container = scrollContainer || document.documentElement;
      setPreviousScrollHeight(container.scrollHeight);
    }

    fetchNextPage();
  }, [fetchNextPage, isFetching, isIOS, scrollContainer, autoScrollAfterFetch]);

  const lastElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (isFetching) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry?.isIntersecting && hasNextPage && token) {
            handleFetch();
          }
        },
        {
          threshold: 0.1,
          rootMargin: isIOS ? '100px 0px' : '50px 0px',
          root: scrollContainer
        }
      );

      if (node) observer.current.observe(node);
    },
    [hasNextPage, isFetching, token, handleFetch, scrollContainer, isIOS]
  );

  useEffect(() => {
    if (!isFetching && autoScrollAfterFetch && previousScrollHeight > 0) {
      const timeoutId = setTimeout(() => {
        autoScrollToNewContent();
      }, 150);

      return () => clearTimeout(timeoutId);
    }
  }, [isFetching, autoScrollToNewContent, autoScrollAfterFetch, previousScrollHeight]);

  useEffect(() => {
    return () => {
      if (observer.current) observer.current.disconnect();
    };
  }, []);

  return { lastElementRef };
};

export default useInfiniteScroll;
