import omit from 'lodash/omit';
import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

/**
 * Hook lấy query params filter cho [Persona]
 * @returns
 * filterKey: key api
 * params: params call api
 * paramsNoPage: params omit page (dùng cho api Summarize)
 * searchSchema: list filter dạng Array ex: age__in: [1,2,3,4]
 */
const useRedirectURL = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const params = useMemo(() => Object.fromEntries(searchParams.entries()), [searchParams]);

  const paramsNoReDirect = useMemo(() => omit(params, ['redirect', 'session']), [params]);

  const reDirect = useMemo(() => params.redirect, [params]);

  const [filterKey, setFilterKey] = useState(0);

  useEffect(() => {
    setFilterKey((prev) => prev + 1);
  }, [params]);

  return {
    filterKey,
    params,
    searchParams,
    reDirect,
    paramsNoReDirect,
    setSearchParams
  };
};

export default useRedirectURL;
