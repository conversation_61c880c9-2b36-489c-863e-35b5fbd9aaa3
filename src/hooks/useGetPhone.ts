import { persona<PERSON><PERSON> } from "apis/persona";
import { socialAP<PERSON> } from "apis/socialData";
import { toast } from "components/ui/use-toast";
import { useEffect, useState } from "react";

interface Props {
  defaultValue: string;
  endpoint?: string;
  type: string;
}
interface DataProps {
  phone: string;
  loading: boolean;
  showed: boolean;
}
const useGetPhone = ({ defaultValue, endpoint, type }: Props) => {
  const [data, setData] = useState<DataProps>({
    phone: defaultValue,
    loading: false,
    showed: false,
  });

  const rootAPI = type === "persona" ? personaAPI : socialAPI;

  useEffect(() => {
    if (data.showed) return;
    setData({ phone: defaultValue, loading: false, showed: false });
  }, [defaultValue]);

  const handleGetPhone = async (uid: string) => {
    if (+uid === 0 || data.showed) return;
    setData({ phone: data.phone, loading: true, showed: false });
    try {
      const res = await rootAPI.get({ endpoint: `${endpoint ?? "phonenumber"}/${uid}/` });
      setData({ phone: res.data.phone, loading: false, showed: true });
    } catch (error) {
      setData({ phone: data.phone, loading: false, showed: false });
      toast({
        title: "Error",
        description: "Failed to fetch user data",
        status: "error",
      });
    }
  };
  return { handleGetPhone, phone: data };
};
export default useGetPhone;
